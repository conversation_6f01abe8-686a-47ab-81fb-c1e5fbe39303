import globals from 'globals'
import pluginJs from '@eslint/js'
import tseslint from 'typescript-eslint'
import pluginReact from 'eslint-plugin-react'
import reactHooks from 'eslint-plugin-react-hooks'
import reactRefresh from 'eslint-plugin-react-refresh'
import eslintPluginPrettierRecommended from 'eslint-plugin-prettier/recommended'

export default [
  {
    ignores: ['.prettierrc.*', 'dist/*', 'node_modules/*', '.gitignore', '.vite-env.*', '.env.*']
  },
  { files: ['**/*.{js,mjs,cjs,ts,jsx,tsx}'] },
  { languageOptions: { globals: { ...globals.browser, ...globals.node } } },
  {
    plugins: {
      'react-hooks': reactHooks,
      'react-refresh': reactRefresh
    }
  },
  pluginJs.configs.recommended,
  ...tseslint.configs.recommended,
  {
    rules: {
      '@typescript-eslint/no-explicit-any': 0,
      '@typescript-eslint/no-unsafe-function-type': 0,
      '@typescript-eslint/no-unused-vars': 0,
      '@typescript-eslint/ban-ts-commet': 'off'
    }
  },
  pluginReact.configs.flat.recommended,
  {
    rules: {
      'react/react-in-jsx-scope': 0,
      'react/prop-types': 0,
      'no-constant-binary-expression': 0,
      'no-useless-escape': 0,
      'no-empty': 0
    }
  },
  {
    settings: {
      react: {
        version: 'detect'
      }
    }
  },
  eslintPluginPrettierRecommended
]
