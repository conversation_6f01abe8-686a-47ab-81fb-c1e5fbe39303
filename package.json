{"name": "zxl_front_pc", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "fix": "eslint --fix", "preview": "vite preview", "pre-check": "tsc -b && pnpm lint-staged", "postinstall": "husky"}, "dependencies": {"@ant-design/cssinjs": "^1.21.1", "@ant-design/icons": "^5.4.0", "antd": "^5.21.0", "axios": "^1.7.7", "buffer": "^6.0.3", "classnames": "^2.5.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "echarts": "^5.5.1", "js-md5": "^0.8.3", "jsencrypt": "^3.3.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-resizable": "^3.0.5", "react-router-dom": "^6.26.2", "tailwindcss": "^3.4.10", "zustand": "^4.5.5"}, "devDependencies": {"@commitlint/cli": "^19.4.1", "@commitlint/config-conventional": "^19.4.1", "@eslint/js": "^9.10.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.20", "eslint": "^9.10.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.35.2", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "husky": "^9.1.5", "lint-staged": "^15.2.10", "postcss": "^8.4.45", "prettier": "^3.3.3", "sass": "^1.78.0", "typescript": "^5.5.3", "typescript-eslint": "^8.4.0", "vite": "^5.4.1", "vite-plugin-svg-icons": "^2.0.1"}, "lint-staged": {"src/**/*.{ts,tsx}": []}}