import{F as o,r,u as c,d as Ue,j as e,R as v,C as s,y as m,S as Q,B as f,a4 as ze,T as He}from"./index-De_f0oL2.js";import{p as h}from"./service-ClCV2GnI.js";import{s as j}from"./Index.module-B93HR2f3.js";import{R as Pe,o as X,d as Me}from"./down-BCLNnN1h.js";import{e as qe}from"./service-DcPXuTuP.js";import{R as Ve}from"./index-BmnYJy3v.js";import{C as Ye}from"./index-DZyVV6rP.js";import{S as Z}from"./index-BWJehDyc.js";import{R as Be,a as Ge}from"./FullscreenOutlined-DzCTibKW.js";import{R as Je,U as Ke,a as Qe}from"./index-BH5uxjwl.js";import{S as Xe}from"./Table-D-iLeFE-.js";import{M as Ze}from"./index-Dck5cc4J.js";import{T as Oe}from"./index-CwwLkcJF.js";import{s as p}from"./index-BcPP1N8I.js";import"./useMultipleSelect-B0dEIXT-.js";import"./DeleteOutlined-D-FcgX8f.js";const{Dragger:et}=Ke,ft=()=>{var B;const[x]=o.useForm(),[C]=o.useForm(),[O,_]=r.useState(!1),[ee,$]=r.useState(!1),[i,E]=r.useState(null),[te,R]=r.useState(!1),[y,ae]=r.useState([]),[W,U]=r.useState(!0),[u,z]=r.useState([]),[ne,le]=r.useState([]),[S,se]=r.useState([]),[oe,re]=r.useState([]),[ie,me]=r.useState([]),[de,ce]=r.useState([]),[he,pe]=r.useState([]),[d,H]=r.useState({total:0,pageNum:1,pageSize:50}),P=r.useRef(null),M=r.useRef(null),q=r.useRef(null),[xe,ue]=r.useState(0),{runAsync:g}=c(h.getEnumType,{manual:!0}),{runAsync:ge}=c(h.getEmployeePostPag,{manual:!0}),{runAsync:fe}=c(h.downloadEmployeeTemplate,{manual:!0}),{runAsync:je}=c(h.downloadEmployeeCompare,{manual:!0}),{runAsync:ye}=c(h.exportEmployeeExcel,{manual:!0}),{runAsync:Te}=c(h.uploadEmployeeExcel,{manual:!0}),{runAsync:we}=c(h.updateEmployeePost,{manual:!0}),{runAsync:Se}=c(qe.build4LevelOrgTree,{manual:!0}),be={正式:"blue",紧密型外包:"cyan",其他:"orange"},ke=[{title:"员工编码",width:75,minWidth:75,dataIndex:"employeeId",key:"employeeId",align:"center",fixed:"left"},{title:"员工名称",width:75,minWidth:75,dataIndex:"employeeName",key:"employeeName",align:"center",fixed:"left"},{title:"所属单位",dataIndex:"orgName5",key:"orgName5",align:"center",width:150,minWidth:150,render:(t,a)=>e.jsx(He,{title:t,children:e.jsx("div",{className:j.over_ellipsis,children:t})})},{title:"所属区域",dataIndex:"region",key:"region",align:"center",width:90,minWidth:90},{title:"年龄",dataIndex:"age",key:"age",align:"center",width:50,minWidth:50},{title:"性别",dataIndex:"gender",key:"gender",align:"center",width:50,minWidth:50},{title:"最高学历",dataIndex:"edu",key:"edu",align:"center",width:75,minWidth:75},{title:"用工类型",dataIndex:"employeeType",key:"employeeType",align:"center",width:75,minWidth:75,render:(t,a)=>e.jsx(Oe,{color:be[t]||"purple",children:t})},{title:"岗位名称",dataIndex:"postName",key:"postName",align:"center",width:120,minWidth:120},{title:"岗级",dataIndex:"postLevel",key:"postLevel",align:"center",width:50,minWidth:50},{title:"备注1",dataIndex:"remarkOne",key:"remarkOne",align:"center",width:100,minWidth:100},{title:"备注2",dataIndex:"remarkTwo",key:"remarkTwo",align:"center",width:100,minWidth:100},{title:"备注3",dataIndex:"remarkThree",key:"remarkThree",align:"center",width:100,minWidth:100},{title:"操作",dataIndex:"action",key:"action",fixed:"right",align:"center",width:100,minWidth:100,render:(t,a)=>e.jsx("span",{className:"cursor-pointer",style:{color:"#F14846"},onClick:()=>Ee(a),children:"编辑"})}];r.useEffect(()=>(Ae(),console.log(oe,ie,de),window.addEventListener("resize",V),()=>{window.removeEventListener("resize",V)}),[]);const V=()=>{b()};r.useEffect(()=>{b()},[(document.querySelector(".position_people_table .ant-table-header")||{}).offsetHeight,(document.querySelector(".position_people_table .ant-table-pagination")||{}).offsetHeight]),r.useEffect(()=>{if(!i&&(S==null?void 0:S.length)>0){const t={tag:"1",...x.getFieldsValue()};k(t)}},[i]);const b=()=>{var l;const t=(document.querySelector(".position_people_table .ant-table-header")||{}).offsetHeight||0,a=(document.querySelector(".position_people_table .ant-table-pagination")||{}).offsetHeight||26;t&&a&&ue(((l=M.current)==null?void 0:l.offsetHeight)-(q.current.offsetHeight+t+a))},Y=async t=>{try{X("正在导出",0,"loading");let a=null;if(t===1)a=await fe({tag:"1"});else if(t===2){const l=x.getFieldsValue(),n=u?u[u.length-1]:{};a=await ye({...l,tag:"1",unit:void 0,org4:Number(n==null?void 0:n.level)===4?n==null?void 0:n.orgId:"",org5:Number(n==null?void 0:n.level)===5?n==null?void 0:n.orgId:"",org6:Number(n==null?void 0:n.level)===6?n==null?void 0:n.orgId:""})}else t===3&&(a=await je({tag:"1"}));Me(a)}catch(a){X("导出失败",1,"error"),console.error("Download failed:",a)}},Ie=t=>{console.log("Success:",t),k({tag:"1",...t})},Ne=async t=>{console.log("Success:",t),$(!0);const[a,l]=await we({tag:"1",postName:t==null?void 0:t.postName,postLevel:t==null?void 0:t.postLevel,remarkOne:t==null?void 0:t.remark1,remarkTwo:t==null?void 0:t.remark2,remarkThree:t==null?void 0:t.remark3,employeeId:i==null?void 0:i.employeeId});if($(!1),a){p.error("修改失败");return}l.STATUS==="0000"?(l.DATA==="修改成功!"?p.success(l.DATA):p.error(l.DATA),E(null)):p.error(l==null?void 0:l.MESSAGE)},Ae=async()=>{var K;const[[t,a],[l,n],[I,T],[N,A],[D,w],[F,G],[$e,J]]=await Promise.all([g({code:"1002",region:""}),g({code:"1003",region:""}),Se({monthId:Ue().format("YYYYMM"),tag:"1"}),g({code:"1009",region:""}),g({code:"1006",region:""}),g({code:"1007",region:""}),g({code:"1008",region:""})]);if(t||l||I||N||D||F||$e)return;a.STATUS,n.STATUS,T.STATUS==="0000"&&le((K=T.DATA)==null?void 0:K.filter(L=>(L==null?void 0:L.orgId)!=="49757")),A.STATUS==="0000"&&se(A.DATA),w.STATUS==="0000"&&re(w.DATA),G.STATUS==="0000"&&me(G.DATA),J.STATUS==="0000"&&ce(J.DATA);const We={tag:"1",...x.getFieldsValue()};k(We)},k=async t=>{var I,T,N;_(!0);const a=u?u[u.length-1]:{},[l,n]=await ge({...t,...d,code:(I=t==null?void 0:t.code)==null?void 0:I.trim(),name:(T=t==null?void 0:t.name)==null?void 0:T.trim(),unit:void 0,org4:Number(a==null?void 0:a.level)===4?a==null?void 0:a.orgId:"",org5:Number(a==null?void 0:a.level)===5?a==null?void 0:a.orgId:"",org6:Number(a==null?void 0:a.level)===6?a==null?void 0:a.orgId:""});if(_(!1),!l&&n.STATUS==="0000"){const{DATA:{data:A}}=n,D=A.map((w,F)=>({...w,key:w.id||F}));pe(D),H({...d,total:(N=n.DATA)==null?void 0:N.totalCount})}},Ce=async()=>{const t=new FormData;y.map(n=>n==null?void 0:n.originFileObj).forEach(n=>{t.append("file",n)}),t.append("tag","1");const[a,l]=await Te(t);a||(l.STATUS==="0000"?(R(!1),p.success(l==null?void 0:l.DATA)):p.error(l==null?void 0:l.MESSAGE))},Ee=t=>{E(t),C.setFieldsValue({...t,remark1:(t==null?void 0:t.remarkOne)||(t==null?void 0:t.remark1),remark2:(t==null?void 0:t.remarkTwo)||(t==null?void 0:t.remark2),remark3:(t==null?void 0:t.remarkThree)||(t==null?void 0:t.remark3)})},Re=()=>{(y==null?void 0:y.length)>0?Ce():p.error("请先选择文件上传")},De=()=>{R(!1)},Fe=(t,a)=>{z(a)},Le=()=>{z([]),x.resetFields()},ve=t=>{const a={total:t==null?void 0:t.total,pageNum:t==null?void 0:t.current,pageSize:t==null?void 0:t.pageSize};H(a)};r.useEffect(()=>{if((d==null?void 0:d.total)>0){const t={tag:"1",...x.getFieldsValue()};k(t)}},[d.pageNum,d.pageSize]);const _e=(t,a)=>a.some(l=>l.orgName.toLowerCase().indexOf(t.trim().toLowerCase())>-1);return e.jsxs("div",{className:`pt-[0.5rem] px-[0rem] h-full flex flex-col ${j.position_page}`,children:[i?e.jsx("div",{className:"bg-white h-[calc(100%-1rem)] pt-[1rem] px-[2.8rem]",children:e.jsxs(Xe,{spinning:ee,children:[e.jsx("div",{className:"text-[0.8rem] font-bold mb-[0.8rem]",children:"员工信息"}),e.jsx(o,{form:C,labelCol:{span:6},onFinish:Ne,initialValues:{...i},children:e.jsxs(v,{children:[e.jsx(s,{span:6,children:e.jsx(o.Item,{name:"employeeName",label:"员工姓名",children:e.jsx("span",{children:i.employeeName})})}),e.jsx(s,{span:6,children:e.jsx(o.Item,{name:"employeeId",label:"员工编号",children:e.jsx("span",{children:i.employeeId})})}),e.jsx(s,{span:6,children:e.jsx(o.Item,{name:"unit",label:"所属单位",children:e.jsx("span",{children:i.orgName5})})}),e.jsx(s,{span:6,children:e.jsx(o.Item,{name:"region",label:"所属区域",children:e.jsx("span",{children:i.region})})}),e.jsx(s,{span:6,children:e.jsx(o.Item,{name:"age",label:"年龄",children:e.jsx("span",{children:i.age})})}),e.jsx(s,{span:6,children:e.jsx(o.Item,{name:"gender",label:"性别",children:e.jsx("span",{children:i.gender})})}),e.jsx(s,{span:6,children:e.jsx(o.Item,{name:"edu",label:"学历",children:e.jsx("span",{children:i.edu})})}),e.jsx(s,{span:6,children:e.jsx(o.Item,{name:"employeeType",label:"用工类型",children:e.jsx("span",{children:i.employeeType})})}),e.jsx(s,{span:6,children:e.jsx(o.Item,{name:"postName",label:"岗位名称",rules:[{required:!0,message:"请输入岗位名称!"}],children:e.jsx(m,{placeholder:"请输入岗位名称",allowClear:!0})})}),e.jsx(s,{span:6,children:e.jsx(o.Item,{name:"postLevel",label:"岗级",rules:[{pattern:/^[+]{0,1}(\d+)$/,message:"只支持数字，请正确输入！"},{required:!0,message:"请输入岗级!"}],children:e.jsx(m,{placeholder:"请输入岗级",allowClear:!0})})}),e.jsx(s,{span:6}),e.jsx(s,{span:6}),e.jsx(s,{span:6,children:e.jsx(o.Item,{name:"remark1",label:"备注1",rules:[{required:!1,message:"请选择备注1!"}],children:e.jsx(m,{placeholder:"请输入",allowClear:!0})})}),e.jsx(s,{span:6,children:e.jsx(o.Item,{name:"remark2",label:"备注2",children:e.jsx(m,{placeholder:"请输入",allowClear:!0})})}),e.jsx(s,{span:6,children:e.jsx(o.Item,{name:"remark3",label:"备注3",children:e.jsx(m,{placeholder:"请输入",allowClear:!0})})}),e.jsx(s,{span:24,className:"text-center mt-[2rem]",children:e.jsxs(Q,{children:[e.jsx(f,{onClick:()=>{E(null),C.resetFields()},children:"返回"}),e.jsx(f,{type:"primary",htmlType:"submit",children:"保存"})]})})]})})]})}):e.jsxs(e.Fragment,{children:[e.jsx("div",{ref:P,className:"bg-white pt-[0.2rem] px-[0.75rem] mb-[0.5rem]",children:e.jsx(o,{form:x,labelCol:{span:6},onFinish:Ie,initialValues:{},children:e.jsxs(v,{gutter:24,children:[e.jsx(s,{span:6,children:e.jsx(o.Item,{name:"unit",label:"组织",children:e.jsx(Ye,{allowClear:!0,changeOnSelect:!0,expandTrigger:"hover",displayRender:t=>t[t.length-1],options:ne,onChange:Fe,fieldNames:{value:"orgId",label:"orgName",children:"children"},placeholder:"请选择组织",showSearch:{filter:_e},onSearch:t=>console.log(t)})})}),e.jsx(s,{span:6,children:e.jsx(o.Item,{name:"code",label:"员工编码",children:e.jsx(m,{placeholder:"请输入员工编码",autoComplete:"off",allowClear:!0})})}),e.jsx(s,{span:6,children:e.jsx(o.Item,{name:"name",label:"员工姓名",children:e.jsx(m,{placeholder:"请输入员工姓名",autoComplete:"off",allowClear:!0})})}),e.jsx(s,{span:6,children:e.jsx(o.Item,{name:"type",label:"用工类型",children:e.jsx(Z,{placeholder:"请选择用工类型",allowClear:!0,children:S.map(t=>{const{enumId:a,enumName:l}=t;return e.jsx(Z.Option,{value:l,children:l},a)})})})}),e.jsx(s,{span:6,children:e.jsx(o.Item,{name:"postName",label:"岗位",children:e.jsx(m,{placeholder:"请输入",allowClear:!0})})}),e.jsx(s,{span:6,children:e.jsx(o.Item,{name:"level",label:"岗级",rules:[{pattern:/^[+]{0,1}(\d+)$/,message:"只支持数字，请正确输入！"}],children:e.jsx(m,{placeholder:"请输入",allowClear:!0})})}),e.jsx(s,{span:12,children:e.jsx("div",{className:"text-right",children:e.jsxs(Q,{children:[e.jsx(f,{type:"primary",htmlType:"submit",children:"查询"}),e.jsx(f,{onClick:()=>Le(),children:"重置"}),e.jsx(f,{icon:e.jsx(Pe,{}),onClick:()=>Y(2),children:"导出"})]})})})]})})}),e.jsxs("div",{ref:M,className:"relative bg-white px-5 pt-2.5",style:{height:`calc(100% - ${((B=P.current)==null?void 0:B.offsetHeight)+15}px)`},children:[e.jsxs("div",{ref:q,className:`flex justify-between items-center mb-2 overflow-hidden ${j.animation_box} ${W?"h-[1.6rem]":"h-0"}`,children:[e.jsxs("div",{className:"flex ",children:[W?e.jsx(Be,{className:`${j.shousuo_icon} text-[1rem]`,onClick:()=>{U(!1),setTimeout(()=>{b()},200)}}):e.jsx(Ge,{className:`${j.shousuo_icon} absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10`,onClick:()=>{U(!0),setTimeout(()=>{b()},200)}}),e.jsx("div",{className:"font-bold text-[0.8rem] ml-3",children:"数据列表"})]}),e.jsxs("div",{className:"flex gap-x-[0.75rem]",children:[e.jsxs("div",{className:"flex items-center gap-x-[0.25rem]",children:[e.jsx(ze,{name:"excel",width:20,height:20}),e.jsx("span",{className:"text-[#E60027] cursor-pointer",onClick:()=>Y(1),children:"下载导入模版"})]}),e.jsx(f,{className:"ml-[0.4rem]",danger:!0,ghost:!0,icon:e.jsx(Je,{}),onClick:()=>R(!0),children:"导入"})]})]}),e.jsx(Ve,{className:"position_people_table",rowClassName:(t,a)=>a%2===1?"customRow odd":"customRow even",columns:ke,dataSource:he,loading:O,onChange:ve,bordered:!0,scroll:{y:`calc(${xe}px - 0.625rem - 1.6rem - 0.5rem)`},pagination:{...d,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50"]}})]})]}),e.jsx(Ze,{title:"文件上传",destroyOnClose:!0,open:te,centered:!0,className:j.detail_modal,okText:"上传",onOk:Re,onCancel:De,children:e.jsx("div",{className:"mt-4 mb-8",children:e.jsx(v,{children:e.jsx(s,{span:22,offset:1,className:"h-[10rem]",children:e.jsxs(et,{action:"",maxCount:1,multiple:!1,fileList:y,beforeUpload(t,a){return console.log(t,a),!1},onChange(t){const{status:a}=t.file;a!=="uploading"&&(console.log(t.file,t.fileList),ae(t.fileList))},children:[e.jsx("p",{className:"ant-upload-drag-icon",children:e.jsx(Qe,{style:{color:"#F14846"}})}),e.jsx("p",{className:"ant-upload-text",children:"点击或将文件拖拽到这里上传"}),e.jsx("p",{className:"ant-upload-hint",children:"支持excel格式的文件。"})]})})})})})]})};export{ft as default};
