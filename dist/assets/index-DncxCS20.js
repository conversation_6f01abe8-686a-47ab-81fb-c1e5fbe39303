import{r as n,F as c,u as d,d as Te,j as s,R as Ce,C as p,S as Ne,B as C,T as H}from"./index-De_f0oL2.js";import{e as u}from"./service-DcPXuTuP.js";import{R as be,o as P,d as Se}from"./down-BCLNnN1h.js";import{s as h}from"./Index.module-CQ0G-enU.js";import{R as je}from"./index-BmnYJy3v.js";import{s as Ie}from"./index-BcPP1N8I.js";import{D as Re}from"./index-CdSZ9YgQ.js";import{C as Ae}from"./index-DZyVV6rP.js";import{S as N}from"./index-BWJehDyc.js";import{R as _e,a as Ee}from"./FullscreenOutlined-DzCTibKW.js";import{T as V}from"./index-CwwLkcJF.js";import"./Table-D-iLeFE-.js";import"./useMultipleSelect-B0dEIXT-.js";const Le={公众线:"cyan",政企线:"blue",网络线:"green",职能线:"geekblue"},ke={专业线:"cyan",岗位:"blue",重点岗位:"green"},We=()=>{var D;const q=[{title:"单位",key:"orgName5",dataIndex:"orgName5",align:"center",width:150,render:(e,o)=>s.jsx(H,{title:e,children:s.jsx("div",{className:h.over_ellipsis,children:e})})},{title:"部门",key:"orgName6",dataIndex:"orgName6",align:"center",width:200,render:(e,o)=>s.jsx(H,{title:e,children:s.jsx("div",{className:h.over_ellipsis,children:e&&e.startsWith("天津市分公司")?e.slice(6):e})})},{title:"员工编号",key:"employeeId",dataIndex:"employeeId",align:"center",width:100},{title:"员工姓名",key:"employeeName",dataIndex:"employeeName",align:"center",width:100},{title:"当前岗位",key:"postName",dataIndex:"postName",align:"center",width:100},{title:"用工类型",key:"employeeType",dataIndex:"employeeType",align:"center",width:100},{title:"分类",key:"oneClass",dataIndex:"oneClass",align:"center",width:100,render:(e,o)=>e==="-"?e:s.jsx(V,{color:ke[e]||"purple",style:{marginRight:"0"},children:e})},{title:"角色",key:"twoClass",dataIndex:"twoClass",align:"center",width:100,render:(e,o)=>e==="-"?e:s.jsx(V,{color:Le[e]||"purple",style:{marginRight:"0"},children:e})}],b=n.useRef(null),S=n.useRef(null),j=n.useRef(null),[l]=c.useForm(),[I,R]=n.useState(!0),[U,A]=n.useState(!1),[B,W]=n.useState(0),[f,G]=n.useState([]),[J,K]=n.useState([]),[m,x]=n.useState([]),[w,Q]=n.useState(0),[i,_]=n.useState({total:0,pageNum:1,pageSize:50}),[E,X]=n.useState([]),[Z,O]=n.useState([]),[ee,te]=n.useState([]),{runAsync:oe}=d(u.exportEmployeeOrgDetailExcel,{manual:!0}),{runAsync:se}=d(u.build4LevelOrgTree,{manual:!0}),{runAsync:ne}=d(u.getEmployeeOrgDetailPag,{manual:!0});n.useEffect(()=>(v(),window.addEventListener("resize",L),()=>{window.removeEventListener("resize",L)}),[]);const L=()=>{g()};n.useEffect(()=>{g()},[(document.querySelector(".report_org_detail_table .ant-table-header")||{}).offsetHeight]),n.useEffect(()=>{w>0&&v()},[w]),n.useEffect(()=>{(f==null?void 0:f.length)>0&&g()},[f]),n.useEffect(()=>{(i==null?void 0:i.total)>0&&T()},[i.pageNum,i.pageSize]);const g=()=>{var t;const e=(document.querySelector(".report_org_detail_table .ant-table-header")||{}).offsetHeight||1,o=(document.querySelector(".report_org_detail_table .ant-table-pagination")||{}).offsetHeight||26;W(((t=S.current)==null?void 0:t.offsetHeight)-(j.current.offsetHeight+e+o))},k=async()=>{var t;const[e,o]=await se({monthId:(t=l.getFieldValue("monthId"))==null?void 0:t.format("YYYYMM"),tag:"1"});e||o.STATUS==="0000"&&(K(o.DATA),x([]),l.setFieldValue("unit",""))},T=async e=>{var z,Y;A(!0);const o=l.getFieldsValue(),t=m?m[m.length-1]:{},a=i,[y,r]=await ne({monthId:(z=o==null?void 0:o.monthId)==null?void 0:z.format("YYYYMM"),org4:Number(t==null?void 0:t.level)===4?t==null?void 0:t.orgId:"",org5:Number(t==null?void 0:t.level)===5?t==null?void 0:t.orgId:"",org6:Number(t==null?void 0:t.level)===6?t==null?void 0:t.orgId:"",oneClass:o==null?void 0:o.oneClass,twoClass:o==null?void 0:o.role,type:o==null?void 0:o.employeeType,pageNum:a.pageNum,pageSize:a.pageSize});if(A(!1),!y)if(r.STATUS==="0000"){const{DATA:{data:xe}}=r,$=xe.map((M,we)=>({...M,key:M.id||we}));console.log("@@@1",$),G($),_({...i,total:(Y=r.DATA)==null?void 0:Y.totalCount})}else Ie.error(r==null?void 0:r.MESSAGE)},v=()=>{const e=Te();l.setFieldsValue({monthId:e,unit:[]}),k(),he(!0),ge()},ae=()=>{T()},le=(e,o)=>{x(o)},re=async()=>{var e;try{P("正在导出",0,"loading");const o=l.getFieldsValue(),t=m?m[m.length-1]:{},a=await oe({monthId:(e=o==null?void 0:o.monthId)==null?void 0:e.format("YYYYMM"),org4:Number(t==null?void 0:t.level)===4?t==null?void 0:t.orgId:"",org5:Number(t==null?void 0:t.level)===5?t==null?void 0:t.orgId:"",org6:Number(t==null?void 0:t.level)===6?t==null?void 0:t.orgId:"",oneClass:o==null?void 0:o.oneClass,twoClass:o==null?void 0:o.role,type:o==null?void 0:o.employeeType});Se(a)}catch(o){P("导出失败",1,"error"),console.error("Download failed:",o)}},ie=()=>{const e=w+1;l.resetFields(),x([]),Q(e)},ce=(e,o)=>o.some(t=>t.orgName.toLowerCase().indexOf(e.toLowerCase())>-1),me=e=>{},de=e=>{const o={total:e==null?void 0:e.total,pageNum:e==null?void 0:e.current,pageSize:e==null?void 0:e.pageSize};_(o)},pe=e=>{if(e.monthId)k();else if(e.oneClass){const o=E.filter(t=>t.enumName===e.oneClass)[0];l.setFieldValue("role",""),F(o.enumId)}},{runAsync:ue}=d(u.getEnumType,{manual:!0}),he=async e=>{const[o,t]=await ue({code:"1030",region:""});if(!o&&t.STATUS==="0000"){const a=t.DATA;X(a),a.length>0&&(l.setFieldsValue({oneClass:a[0].enumName}),F(a[0].enumId,e))}},{runAsync:fe}=d(u.getEnumType,{manual:!0}),ge=async()=>{const[e,o]=await fe({code:"1032",region:""});e||o.STATUS==="0000"&&te(o.DATA.map(t=>({key:t.enumId,label:t.enumName,value:t.enumName})))},{runAsync:ye}=d(u.getEnumType,{manual:!0}),F=async(e,o)=>{const[t,a]=await ye({code:"1031",region:e});if(!t&&a.STATUS==="0000"){const y=a.DATA.map(r=>({key:r.enumId,label:r.enumName,value:r.enumName}));O(y),y.length>0&&l.setFieldsValue({role:y[0].value}),o&&T()}};return s.jsxs("div",{className:`h-full pt-[0.5rem] flex flex-col ${h.employment_page}`,children:[s.jsx("div",{ref:b,className:"bg-white pt-[0.5rem] px-8 mb-[0.5rem]",children:s.jsx(c,{form:l,initialValues:{tag:""},onFinish:ae,onValuesChange:pe,autoComplete:"off",children:s.jsxs(Ce,{gutter:24,children:[s.jsx(p,{span:6,children:s.jsx(c.Item,{label:"月份",name:"monthId",wrapperCol:{span:20},children:s.jsx(Re,{className:"w-full",allowClear:!1,onChange:me,picker:"month"})})}),s.jsx(p,{span:6,children:s.jsx(c.Item,{label:"组织",name:"unit",wrapperCol:{span:20},children:s.jsx(Ae,{allowClear:!0,changeOnSelect:!0,expandTrigger:"hover",displayRender:e=>e[e.length-1],options:J,onChange:le,fieldNames:{value:"orgId",label:"orgName",children:"children"},placeholder:"请选择",showSearch:{filter:ce},onSearch:e=>console.log(e)})})}),s.jsx(p,{span:6,children:s.jsx(c.Item,{label:"人员分类",name:"oneClass",wrapperCol:{span:20},children:s.jsx(N,{placeholder:"请选择",className:"w-full",options:E.map(e=>({label:e.enumName,value:e.enumName,key:e.enumId}))})})}),s.jsx(p,{span:6,children:s.jsx(c.Item,{label:"角色",name:"role",wrapperCol:{span:20},children:s.jsx(N,{placeholder:"请选择",className:"w-full",options:Z})})}),s.jsx(p,{span:6,children:s.jsx(c.Item,{label:"用工类型",name:"employeeType",wrapperCol:{span:20},children:s.jsx(N,{placeholder:"请选择",allowClear:!0,options:ee})})}),s.jsx(p,{span:6,children:s.jsx(c.Item,{labelCol:{span:0},wrapperCol:{span:24},children:s.jsxs(Ne,{size:"small",children:[s.jsx(C,{type:"primary",htmlType:"submit",children:"查询"}),s.jsx(C,{htmlType:"button",onClick:()=>ie(),children:"重置"})]})})})]})})}),s.jsxs("div",{ref:S,className:"relative bg-white px-5 pt-2.5",style:{height:`calc(100% - ${((D=b.current)==null?void 0:D.offsetHeight)+15}px)`},children:[s.jsxs("div",{ref:j,className:`flex justify-between items-center mb-2 overflow-hidden ${h.animation_box} ${I?"h-[1.6rem]":"h-0"}`,children:[s.jsxs("div",{className:"flex ",children:[I?s.jsx(_e,{className:`${h.shousuo_icon} text-[1rem]`,onClick:()=>{R(!1),setTimeout(()=>{g()},200)}}):s.jsx(Ee,{className:`${h.shousuo_icon} absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10`,onClick:()=>{R(!0),setTimeout(()=>{g()},200)}}),s.jsx("div",{className:"font-bold text-[0.8rem] ml-3",children:"数据列表"})]}),s.jsx(C,{danger:!0,ghost:!0,icon:s.jsx(be,{}),onClick:()=>re(),children:"导出"})]}),s.jsx(je,{className:"report_org_detail_table",rowClassName:(e,o)=>o%2===1?"customRow odd":"customRow even",columns:q,dataSource:f,loading:U,bordered:!0,scroll:{y:`calc(${B}px - 0.625rem - 1.6rem - 0.5rem)`},onChange:de,pagination:{...i,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50"]}})]})]})};export{We as default};
