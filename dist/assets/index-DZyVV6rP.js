import{r,p as re,a8 as ue,al as G,a7 as ln,a5 as ne,c as ge,K as ve,_ as an,w as en,i as He,a9 as Mn,q as sn,m as Nn,Y as $n,X as Dn,a as cn,J as Rn,b as Ln,b7 as An,c9 as Tn,ax as un,aB as nn,v as dn,k as Wn,$ as Hn,bg as Kn,L as Fn,M as jn,bi as Bn,a0 as zn,br as rn,a1 as Xn,bk as Un}from"./index-De_f0oL2.js";import{c as Gn,d as Jn,B as Yn,D as vn,e as qn,f as Zn,u as Qn,m as et}from"./index-BWJehDyc.js";import{c as nt,a as tn}from"./Table-D-iLeFE-.js";import{g as tt}from"./useMultipleSelect-B0dEIXT-.js";var Oe=r.createContext({}),ye="__rc_cascader_search_mark__",at=function(n,t,a){var o=a.label,i=o===void 0?"":o;return t.some(function(s){return String(s[i]).toLowerCase().includes(n.toLowerCase())})},ot=function(n,t,a,o){return t.map(function(i){return i[o.label]}).join(" / ")},rt=function(n,t,a,o,i,s){var l=i.filter,c=l===void 0?at:l,u=i.render,g=u===void 0?ot:u,f=i.limit,d=f===void 0?50:f,S=i.sort;return r.useMemo(function(){var h=[];if(!n)return[];function p(C,_){var $=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;C.forEach(function(m){if(!(!S&&d!==!1&&d>0&&h.length>=d)){var k=[].concat(re(_),[m]),L=m[a.children],x=$||m.disabled;if((!L||L.length===0||s)&&c(n,k,{label:a.label})){var b;h.push(ue(ue({},m),{},(b={disabled:x},G(b,a.label,g(n,k,o,a)),G(b,ye,k),G(b,a.children,void 0),b)))}L&&p(m[a.children],k,x)}})}return p(t,[]),S&&h.sort(function(C,_){return S(C[ye],_[ye],n,a)}),d!==!1&&d>0?h.slice(0,d):h},[n,t,a,o,g,s,c,S,d])},on="__RC_CASCADER_SPLIT__",pn="SHOW_PARENT",fn="SHOW_CHILD";function se(e){return e.join(on)}function xe(e){return e.map(se)}function lt(e){return e.split(on)}function mn(e){var n=e||{},t=n.label,a=n.value,o=n.children,i=a||"value";return{label:t||"label",value:i,key:i,children:o||"children"}}function $e(e,n){var t,a;return(t=e.isLeaf)!==null&&t!==void 0?t:!((a=e[n.children])!==null&&a!==void 0&&a.length)}function it(e){var n=e.parentElement;if(n){var t=e.offsetTop-n.offsetTop;t-n.scrollTop<0?n.scrollTo({top:t}):t+e.offsetHeight-n.scrollTop>n.offsetHeight&&n.scrollTo({top:t+e.offsetHeight-n.offsetHeight})}}function hn(e,n){return e.map(function(t){var a;return(a=t[ye])===null||a===void 0?void 0:a.map(function(o){return o[n.value]})})}function st(e){return Array.isArray(e)&&Array.isArray(e[0])}function Ke(e){return e?st(e)?e:(e.length===0?[]:[e]).map(function(n){return Array.isArray(n)?n:[n]}):[]}function gn(e,n,t){var a=new Set(e),o=n();return e.filter(function(i){var s=o[i],l=s?s.parent:null,c=s?s.children:null;return s&&s.node.disabled?!0:t===fn?!(c&&c.some(function(u){return u.key&&a.has(u.key)})):!(l&&!l.node.disabled&&a.has(l.key))})}function we(e,n,t){for(var a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1,o=n,i=[],s=function(){var u,g,f,d=e[l],S=(u=o)===null||u===void 0?void 0:u.findIndex(function(p){var C=p[t.value];return a?String(C)===String(d):C===d}),h=S!==-1?(g=o)===null||g===void 0?void 0:g[S]:null;i.push({value:(f=h==null?void 0:h[t.value])!==null&&f!==void 0?f:d,index:S,option:h}),o=h==null?void 0:h[t.children]},l=0;l<e.length;l+=1)s();return i}const ct=function(e,n,t,a,o){return r.useMemo(function(){var i=o||function(s){var l=a?s.slice(-1):s,c=" / ";return l.every(function(u){return["string","number"].includes(ln(u))})?l.join(c):l.reduce(function(u,g,f){var d=r.isValidElement(g)?r.cloneElement(g,{key:f}):g;return f===0?[d]:[].concat(re(u),[c,d])},[])};return e.map(function(s){var l,c=we(s,n,t),u=i(c.map(function(f){var d,S=f.option,h=f.value;return(d=S==null?void 0:S[t.label])!==null&&d!==void 0?d:h}),c.map(function(f){var d=f.option;return d})),g=se(s);return{label:u,value:g,key:g,valueCells:s,disabled:(l=c[c.length-1])===null||l===void 0||(l=l.option)===null||l===void 0?void 0:l.disabled}})},[e,n,t,o,a])};function Cn(e,n){return r.useCallback(function(t){var a=[],o=[];return t.forEach(function(i){var s=we(i,e,n);s.every(function(l){return l.option})?o.push(i):a.push(i)}),[o,a]},[e,n])}const ut=function(e,n){var t=r.useRef({options:[],info:{keyEntities:{},pathKeyEntities:{}}}),a=r.useCallback(function(){return t.current.options!==e&&(t.current.options=e,t.current.info=nt(e,{fieldNames:n,initWrapper:function(i){return ue(ue({},i),{},{pathKeyEntities:{}})},processEntity:function(i,s){var l=i.nodes.map(function(c){return c[n.value]}).join(on);s.pathKeyEntities[l]=i,i.key=l}})),t.current.info.pathKeyEntities},[n,e]);return a};function Sn(e,n){var t=r.useMemo(function(){return n||[]},[n]),a=ut(t,e),o=r.useCallback(function(i){var s=a();return i.map(function(l){var c=s[l].nodes;return c.map(function(u){return u[e.value]})})},[a,e]);return[t,a,o]}function dt(e){return r.useMemo(function(){if(!e)return[!1,{}];var n={matchInputWidth:!0,limit:50};return e&&ln(e)==="object"&&(n=ue(ue({},n),e)),n.limit<=0&&(n.limit=!1),[!0,n]},[e])}function bn(e,n,t,a,o,i,s,l){return function(c){if(!e)n(c);else{var u=se(c),g=xe(t),f=xe(a),d=g.includes(u),S=o.some(function(x){return se(x)===u}),h=t,p=o;if(S&&!d)p=o.filter(function(x){return se(x)!==u});else{var C=d?g.filter(function(x){return x!==u}):[].concat(re(g),[u]),_=i(),$;if(d){var m=tn(C,{halfCheckedKeys:f},_);$=m.checkedKeys}else{var k=tn(C,!0,_);$=k.checkedKeys}var L=gn($,i,l);h=s(L)}n([].concat(re(p),re(h)))}}}function yn(e,n,t,a,o){return r.useMemo(function(){var i=o(n),s=ne(i,2),l=s[0],c=s[1];if(!e||!n.length)return[l,[],c];var u=xe(l),g=t(),f=tn(u,!0,g),d=f.checkedKeys,S=f.halfCheckedKeys;return[a(d),a(S),c]},[e,n,t,a,o])}var vt=r.memo(function(e){var n=e.children;return n},function(e,n){return!n.open});function pt(e){var n,t=e.prefixCls,a=e.checked,o=e.halfChecked,i=e.disabled,s=e.onClick,l=e.disableCheckbox,c=r.useContext(Oe),u=c.checkable,g=typeof u!="boolean"?u:null;return r.createElement("span",{className:ge("".concat(t),(n={},G(n,"".concat(t,"-checked"),a),G(n,"".concat(t,"-indeterminate"),!a&&o),G(n,"".concat(t,"-disabled"),i||l),n)),onClick:s},g)}var xn="__cascader_fix_label__";function ft(e){var n=e.prefixCls,t=e.multiple,a=e.options,o=e.activeValue,i=e.prevValuePath,s=e.onToggleOpen,l=e.onSelect,c=e.onActive,u=e.checkedSet,g=e.halfCheckedSet,f=e.loadingKeys,d=e.isSelectable,S=e.disabled,h="".concat(n,"-menu"),p="".concat(n,"-menu-item"),C=r.useContext(Oe),_=C.fieldNames,$=C.changeOnSelect,m=C.expandTrigger,k=C.expandIcon,L=C.loadingIcon,x=C.dropdownMenuColumnStyle,b=C.optionRender,I=m==="hover",P=function(M){return S||M},D=r.useMemo(function(){return a.map(function(v){var M,N=v.disabled,H=v.disableCheckbox,K=v[ye],W=(M=v[xn])!==null&&M!==void 0?M:v[_.label],F=v[_.value],z=$e(v,_),Y=K?K.map(function(Z){return Z[_.value]}):[].concat(re(i),[F]),w=se(Y),A=f.includes(w),X=u.has(w),j=g.has(w);return{disabled:N,label:W,value:F,isLeaf:z,isLoading:A,checked:X,halfChecked:j,option:v,disableCheckbox:H,fullPath:Y,fullPathKey:w}})},[a,u,_,g,f,i]);return r.createElement("ul",{className:h,role:"menu"},D.map(function(v){var M,N=v.disabled,H=v.label,K=v.value,W=v.isLeaf,F=v.isLoading,z=v.checked,Y=v.halfChecked,w=v.option,A=v.fullPath,X=v.fullPathKey,j=v.disableCheckbox,Z=function(){if(!P(N)){var B=re(A);I&&W&&B.pop(),c(B)}},Q=function(){d(w)&&!P(N)&&l(A,W)},te;return typeof w.title=="string"?te=w.title:typeof H=="string"&&(te=H),r.createElement("li",{key:X,className:ge(p,(M={},G(M,"".concat(p,"-expand"),!W),G(M,"".concat(p,"-active"),o===K||o===X),G(M,"".concat(p,"-disabled"),P(N)),G(M,"".concat(p,"-loading"),F),M)),style:x,role:"menuitemcheckbox",title:te,"aria-checked":z,"data-path-key":X,onClick:function(){Z(),!j&&(!t||W)&&Q()},onDoubleClick:function(){$&&s(!1)},onMouseEnter:function(){I&&Z()},onMouseDown:function(B){B.preventDefault()}},t&&r.createElement(pt,{prefixCls:"".concat(n,"-checkbox"),checked:z,halfChecked:Y,disabled:P(N)||j,disableCheckbox:j,onClick:function(B){j||(B.stopPropagation(),Q())}}),r.createElement("div",{className:"".concat(p,"-content")},b?b(w):H),!F&&k&&!W&&r.createElement("div",{className:"".concat(p,"-expand-icon")},k),F&&L&&r.createElement("div",{className:"".concat(p,"-loading-icon")},L))}))}var mt=function(n,t){var a=r.useContext(Oe),o=a.values,i=o[0],s=r.useState([]),l=ne(s,2),c=l[0],u=l[1];return r.useEffect(function(){n||u(i||[])},[t,i]),[c,u]};const ht=function(e,n,t,a,o,i,s){var l=s.direction,c=s.searchValue,u=s.toggleOpen,g=s.open,f=l==="rtl",d=r.useMemo(function(){for(var x=-1,b=n,I=[],P=[],D=a.length,v=hn(n,t),M=function(F){var z=b.findIndex(function(Y,w){return(v[w]?se(v[w]):Y[t.value])===a[F]});if(z===-1)return 1;x=z,I.push(x),P.push(a[F]),b=b[x][t.children]},N=0;N<D&&b&&!M(N);N+=1);for(var H=n,K=0;K<I.length-1;K+=1)H=H[I[K]][t.children];return[P,x,H,v]},[a,t,n]),S=ne(d,4),h=S[0],p=S[1],C=S[2],_=S[3],$=function(b){o(b)},m=function(b){var I=C.length,P=p;P===-1&&b<0&&(P=I);for(var D=0;D<I;D+=1){P=(P+b+I)%I;var v=C[P];if(v&&!v.disabled){var M=h.slice(0,-1).concat(_[P]?se(_[P]):v[t.value]);$(M);return}}},k=function(){if(h.length>1){var b=h.slice(0,-1);$(b)}else u(!1)},L=function(){var b,I=((b=C[p])===null||b===void 0?void 0:b[t.children])||[],P=I.find(function(v){return!v.disabled});if(P){var D=[].concat(re(h),[P[t.value]]);$(D)}};r.useImperativeHandle(e,function(){return{onKeyDown:function(b){var I=b.which;switch(I){case ve.UP:case ve.DOWN:{var P=0;I===ve.UP?P=-1:I===ve.DOWN&&(P=1),P!==0&&m(P);break}case ve.LEFT:{if(c)break;f?L():k();break}case ve.RIGHT:{if(c)break;f?k():L();break}case ve.BACKSPACE:{c||k();break}case ve.ENTER:{if(h.length){var D=C[p],v=(D==null?void 0:D[ye])||[];v.length?i(v.map(function(M){return M[t.value]}),v[v.length-1]):i(h,C[p])}break}case ve.ESC:u(!1),g&&b.stopPropagation()}},onKeyUp:function(){}}})};var wn=r.forwardRef(function(e,n){var t,a,o,i=e.prefixCls,s=e.multiple,l=e.searchValue,c=e.toggleOpen,u=e.notFoundContent,g=e.direction,f=e.open,d=e.disabled,S=r.useRef(null),h=g==="rtl",p=r.useContext(Oe),C=p.options,_=p.values,$=p.halfValues,m=p.fieldNames,k=p.changeOnSelect,L=p.onSelect,x=p.searchOptions,b=p.dropdownPrefixCls,I=p.loadData,P=p.expandTrigger,D=b||i,v=r.useState([]),M=ne(v,2),N=M[0],H=M[1],K=function(O){if(!(!I||l)){var E=we(O,C,m),y=E.map(function(oe){var q=oe.option;return q}),V=y[y.length-1];if(V&&!$e(V,m)){var ae=se(O);H(function(oe){return[].concat(re(oe),[ae])}),I(y)}}};r.useEffect(function(){N.length&&N.forEach(function(R){var O=lt(R),E=we(O,C,m,!0).map(function(V){var ae=V.option;return ae}),y=E[E.length-1];(!y||y[m.children]||$e(y,m))&&H(function(V){return V.filter(function(ae){return ae!==R})})})},[C,N,m]);var W=r.useMemo(function(){return new Set(xe(_))},[_]),F=r.useMemo(function(){return new Set(xe($))},[$]),z=mt(s,f),Y=ne(z,2),w=Y[0],A=Y[1],X=function(O){A(O),K(O)},j=function(O){if(d)return!1;var E=O.disabled,y=$e(O,m);return!E&&(y||k||s)},Z=function(O,E){var y=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;L(O),!s&&(E||k&&(P==="hover"||y))&&c(!1)},Q=r.useMemo(function(){return l?x:C},[l,x,C]),te=r.useMemo(function(){for(var R=[{options:Q}],O=Q,E=hn(O,m),y=function(){var oe=w[V],q=O.find(function(fe,Ce){return(E[Ce]?se(E[Ce]):fe[m.value])===oe}),ie=q==null?void 0:q[m.children];if(!(ie!=null&&ie.length))return 1;O=ie,R.push({options:ie})},V=0;V<w.length&&!y();V+=1);return R},[Q,w,m]),T=function(O,E){j(E)&&Z(O,$e(E,m),!0)};ht(n,Q,m,w,X,T,{direction:g,searchValue:l,toggleOpen:c,open:f}),r.useEffect(function(){if(!l)for(var R=0;R<w.length;R+=1){var O,E=w.slice(0,R+1),y=se(E),V=(O=S.current)===null||O===void 0?void 0:O.querySelector('li[data-path-key="'.concat(y.replace(/\\{0,2}"/g,'\\"'),'"]'));V&&it(V)}},[w,l]);var B=!((t=te[0])!==null&&t!==void 0&&(t=t.options)!==null&&t!==void 0&&t.length),le=[(a={},G(a,m.value,"__EMPTY__"),G(a,xn,u),G(a,"disabled",!0),a)],pe=ue(ue({},e),{},{multiple:!B&&s,onSelect:Z,onActive:X,onToggleOpen:c,checkedSet:W,halfCheckedSet:F,loadingKeys:N,isSelectable:j}),ce=B?[{options:le}]:te,J=ce.map(function(R,O){var E=w.slice(0,O),y=w[O];return r.createElement(ft,an({key:O},pe,{prefixCls:D,options:R.options,prevValuePath:E,activeValue:y}))});return r.createElement(vt,{open:f},r.createElement("div",{className:ge("".concat(D,"-menus"),(o={},G(o,"".concat(D,"-menu-empty"),B),G(o,"".concat(D,"-rtl"),h),o)),ref:S},J))}),gt=r.forwardRef(function(e,n){var t=Gn();return r.createElement(wn,an({},e,t,{ref:n}))});function Ct(){}function On(e){var n,t=e,a=t.prefixCls,o=a===void 0?"rc-cascader":a,i=t.style,s=t.className,l=t.options,c=t.checkable,u=t.defaultValue,g=t.value,f=t.fieldNames,d=t.changeOnSelect,S=t.onChange,h=t.showCheckedStrategy,p=t.loadData,C=t.expandTrigger,_=t.expandIcon,$=_===void 0?">":_,m=t.loadingIcon,k=t.direction,L=t.notFoundContent,x=L===void 0?"Not Found":L,b=t.disabled,I=!!c,P=en(u,{value:g,postState:Ke}),D=ne(P,2),v=D[0],M=D[1],N=r.useMemo(function(){return mn(f)},[JSON.stringify(f)]),H=Sn(N,l),K=ne(H,3),W=K[0],F=K[1],z=K[2],Y=Cn(W,N),w=yn(I,v,F,z,Y),A=ne(w,3),X=A[0],j=A[1],Z=A[2],Q=He(function(ce){if(M(ce),S){var J=Ke(ce),R=J.map(function(y){return we(y,W,N).map(function(V){return V.option})}),O=I?J:J[0],E=I?R:R[0];S(O,E)}}),te=bn(I,Q,X,j,Z,F,z,h),T=He(function(ce){te(ce)}),B=r.useMemo(function(){return{options:W,fieldNames:N,values:X,halfValues:j,changeOnSelect:d,onSelect:T,checkable:c,searchOptions:[],dropdownPrefixCls:void 0,loadData:p,expandTrigger:C,expandIcon:$,loadingIcon:m,dropdownMenuColumnStyle:void 0}},[W,N,X,j,d,T,c,p,C,$,m]),le="".concat(o,"-panel"),pe=!W.length;return r.createElement(Oe.Provider,{value:B},r.createElement("div",{className:ge(le,(n={},G(n,"".concat(le,"-rtl"),k==="rtl"),G(n,"".concat(le,"-empty"),pe),n),s),style:i},pe?x:r.createElement(wn,{prefixCls:o,searchValue:"",multiple:I,toggleOpen:Ct,open:!0,direction:k,disabled:b})))}var St=["id","prefixCls","fieldNames","defaultValue","value","changeOnSelect","onChange","displayRender","checkable","autoClearSearchValue","searchValue","onSearch","showSearch","expandTrigger","options","dropdownPrefixCls","loadData","popupVisible","open","popupClassName","dropdownClassName","dropdownMenuColumnStyle","dropdownStyle","popupPlacement","placement","onDropdownVisibleChange","onPopupVisibleChange","onOpenChange","expandIcon","loadingIcon","children","dropdownMatchSelectWidth","showCheckedStrategy","optionRender"],De=r.forwardRef(function(e,n){var t=e.id,a=e.prefixCls,o=a===void 0?"rc-cascader":a,i=e.fieldNames,s=e.defaultValue,l=e.value,c=e.changeOnSelect,u=e.onChange,g=e.displayRender,f=e.checkable,d=e.autoClearSearchValue,S=d===void 0?!0:d,h=e.searchValue,p=e.onSearch,C=e.showSearch,_=e.expandTrigger,$=e.options,m=e.dropdownPrefixCls,k=e.loadData,L=e.popupVisible,x=e.open,b=e.popupClassName,I=e.dropdownClassName,P=e.dropdownMenuColumnStyle,D=e.dropdownStyle,v=e.popupPlacement,M=e.placement,N=e.onDropdownVisibleChange,H=e.onPopupVisibleChange,K=e.onOpenChange,W=e.expandIcon,F=W===void 0?">":W,z=e.loadingIcon,Y=e.children,w=e.dropdownMatchSelectWidth,A=w===void 0?!1:w,X=e.showCheckedStrategy,j=X===void 0?pn:X,Z=e.optionRender,Q=Mn(e,St),te=Jn(t),T=!!f,B=en(s,{value:l,postState:Ke}),le=ne(B,2),pe=le[0],ce=le[1],J=r.useMemo(function(){return mn(i)},[JSON.stringify(i)]),R=Sn(J,$),O=ne(R,3),E=O[0],y=O[1],V=O[2],ae=en("",{value:h,postState:function(U){return U||""}}),oe=ne(ae,2),q=oe[0],ie=oe[1],fe=function(U,de){ie(U),de.source!=="blur"&&p&&p(U)},Ce=dt(C),Pe=ne(Ce,2),Le=Pe[0],Se=Pe[1],Ie=rt(q,E,J,m||o,Se,c||T),Fe=Cn(E,J),je=yn(T,pe,y,V,Fe),Ee=ne(je,3),me=Ee[0],_e=Ee[1],Ve=Ee[2],Be=r.useMemo(function(){var ee=xe(me),U=gn(ee,y,j);return[].concat(re(Ve),re(V(U)))},[me,y,V,Ve,j]),ze=ct(Be,E,J,T,g),Ae=He(function(ee){if(ce(ee),u){var U=Ke(ee),de=U.map(function(Ze){return we(Ze,E,J).map(function(he){return he.option})}),Me=T?U:U[0],Ne=T?de:de[0];u(Me,Ne)}}),be=bn(T,Ae,me,_e,Ve,y,V,j),ke=He(function(ee){(!T||S)&&ie(""),be(ee)}),Te=function(U,de){if(de.type==="clear"){Ae([]);return}var Me=de.values[0],Ne=Me.valueCells;ke(Ne)},Xe=x!==void 0?x:L,Ue=I||b,Ge=M||v,Je=function(U){K==null||K(U),N==null||N(U),H==null||H(U)},Ye=r.useMemo(function(){return{options:E,fieldNames:J,values:me,halfValues:_e,changeOnSelect:c,onSelect:ke,checkable:f,searchOptions:Ie,dropdownPrefixCls:m,loadData:k,expandTrigger:_,expandIcon:F,loadingIcon:z,dropdownMenuColumnStyle:P,optionRender:Z}},[E,J,me,_e,c,ke,f,Ie,m,k,_,F,z,P,Z]),We=!(q?Ie:E).length,qe=q&&Se.matchInputWidth||We?{}:{minWidth:"auto"};return r.createElement(Oe.Provider,{value:Ye},r.createElement(Yn,an({},Q,{ref:n,id:te,prefixCls:o,autoClearSearchValue:S,dropdownMatchSelectWidth:A,dropdownStyle:ue(ue({},qe),D),displayValues:ze,onDisplayValuesChange:Te,mode:T?"multiple":void 0,searchValue:q,onSearch:fe,showSearch:Le,OptionList:gt,emptyOptions:We,open:Xe,dropdownClassName:Ue,placement:Ge,onDropdownVisibleChange:Je,getRawInputElement:function(){return Y}})))});De.SHOW_PARENT=pn;De.SHOW_CHILD=fn;De.Panel=On;function Pn(e,n){const{getPrefixCls:t,direction:a,renderEmpty:o}=r.useContext(sn),i=n||a,s=t("select",e),l=t("cascader",e);return[s,l,i,o]}function In(e,n){return r.useMemo(()=>n?r.createElement("span",{className:`${e}-checkbox-inner`}):!1,[n])}const En=(e,n,t)=>{let a=t;t||(a=n?r.createElement($n,null):r.createElement(Dn,null));const o=r.createElement("span",{className:`${e}-menu-item-loading-icon`},r.createElement(Nn,{spin:!0}));return r.useMemo(()=>[a,o],[a])},_n=e=>{const{prefixCls:n,componentCls:t}=e,a=`${t}-menu-item`,o=`
  &${a}-expand ${a}-expand-icon,
  ${a}-loading-icon
`;return[tt(`${n}-checkbox`,e),{[t]:{"&-checkbox":{top:0,marginInlineEnd:e.paddingXS,pointerEvents:"unset"},"&-menus":{display:"flex",flexWrap:"nowrap",alignItems:"flex-start",[`&${t}-menu-empty`]:{[`${t}-menu`]:{width:"100%",height:"auto",[a]:{color:e.colorTextDisabled}}}},"&-menu":{flexGrow:1,flexShrink:0,minWidth:e.controlItemWidth,height:e.dropdownHeight,margin:0,padding:e.menuPadding,overflow:"auto",verticalAlign:"top",listStyle:"none","-ms-overflow-style":"-ms-autohiding-scrollbar","&:not(:last-child)":{borderInlineEnd:`${cn(e.lineWidth)} ${e.lineType} ${e.colorSplit}`},"&-item":Object.assign(Object.assign({},Rn),{display:"flex",flexWrap:"nowrap",alignItems:"center",padding:e.optionPadding,lineHeight:e.lineHeight,cursor:"pointer",transition:`all ${e.motionDurationMid}`,borderRadius:e.borderRadiusSM,"&:hover":{background:e.controlItemBgHover},"&-disabled":{color:e.colorTextDisabled,cursor:"not-allowed","&:hover":{background:"transparent"},[o]:{color:e.colorTextDisabled}},[`&-active:not(${a}-disabled)`]:{"&, &:hover":{color:e.optionSelectedColor,fontWeight:e.optionSelectedFontWeight,backgroundColor:e.optionSelectedBg}},"&-content":{flex:"auto"},[o]:{marginInlineStart:e.paddingXXS,color:e.colorIcon,fontSize:e.fontSizeIcon},"&-keyword":{color:e.colorHighlight}})}}}]},bt=e=>{const{componentCls:n,antCls:t}=e;return[{[n]:{width:e.controlWidth}},{[`${n}-dropdown`]:[{[`&${t}-select-dropdown`]:{padding:0}},_n(e)]},{[`${n}-dropdown-rtl`]:{direction:"rtl"}},An(e)]},Vn=e=>{const n=Math.round((e.controlHeight-e.fontSize*e.lineHeight)/2);return{controlWidth:184,controlItemWidth:111,dropdownHeight:180,optionSelectedBg:e.controlItemBgActive,optionSelectedFontWeight:e.fontWeightStrong,optionPadding:`${n}px ${e.paddingSM}px`,menuPadding:e.paddingXXS,optionSelectedColor:e.colorText}},kn=Ln("Cascader",e=>[bt(e)],Vn),yt=e=>{const{componentCls:n}=e;return{[`${n}-panel`]:[_n(e),{display:"inline-flex",border:`${cn(e.lineWidth)} ${e.lineType} ${e.colorSplit}`,borderRadius:e.borderRadiusLG,overflowX:"auto",maxWidth:"100%",[`${n}-menus`]:{alignItems:"stretch"},[`${n}-menu`]:{height:"auto"},"&-empty":{padding:e.paddingXXS}}]}},xt=Tn(["Cascader","Panel"],e=>yt(e),Vn);function wt(e){const{prefixCls:n,className:t,multiple:a,rootClassName:o,notFoundContent:i,direction:s,expandIcon:l,disabled:c}=e,u=r.useContext(un),g=c??u,[f,d,S,h]=Pn(n,s),p=nn(d),[C,_,$]=kn(d,p);xt(d);const m=S==="rtl",[k,L]=En(f,m,l),x=i||(h==null?void 0:h("Cascader"))||r.createElement(vn,{componentName:"Cascader"}),b=In(d,a);return C(r.createElement(On,Object.assign({},e,{checkable:b,prefixCls:d,className:ge(t,_,o,$,p),notFoundContent:x,direction:S,expandIcon:k,loadingIcon:L,disabled:g})))}var Ot=function(e,n){var t={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&n.indexOf(a)<0&&(t[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,a=Object.getOwnPropertySymbols(e);o<a.length;o++)n.indexOf(a[o])<0&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(t[a[o]]=e[a[o]]);return t};const{SHOW_CHILD:Pt,SHOW_PARENT:It}=De;function Et(e,n,t){const a=e.toLowerCase().split(n).reduce((s,l,c)=>c===0?[l]:[].concat(re(s),[n,l]),[]),o=[];let i=0;return a.forEach((s,l)=>{const c=i+s.length;let u=e.slice(i,c);i=c,l%2===1&&(u=r.createElement("span",{className:`${t}-menu-item-keyword`,key:`separator-${l}`},u)),o.push(u)}),o}const _t=(e,n,t,a)=>{const o=[],i=e.toLowerCase();return n.forEach((s,l)=>{l!==0&&o.push(" / ");let c=s[a.label];const u=typeof c;(u==="string"||u==="number")&&(c=Et(String(c),i,t)),o.push(c)}),o},Re=r.forwardRef((e,n)=>{var t,a,o,i;const{prefixCls:s,size:l,disabled:c,className:u,rootClassName:g,multiple:f,bordered:d=!0,transitionName:S,choiceTransitionName:h="",popupClassName:p,dropdownClassName:C,expandIcon:_,placement:$,showSearch:m,allowClear:k=!0,notFoundContent:L,direction:x,getPopupContainer:b,status:I,showArrow:P,builtinPlacements:D,style:v,variant:M,dropdownRender:N,onDropdownVisibleChange:H,dropdownMenuColumnStyle:K,popupRender:W,dropdownStyle:F,popupMenuColumnStyle:z,onOpenChange:Y,styles:w,classNames:A}=e,X=Ot(e,["prefixCls","size","disabled","className","rootClassName","multiple","bordered","transitionName","choiceTransitionName","popupClassName","dropdownClassName","expandIcon","placement","showSearch","allowClear","notFoundContent","direction","getPopupContainer","status","showArrow","builtinPlacements","style","variant","dropdownRender","onDropdownVisibleChange","dropdownMenuColumnStyle","popupRender","dropdownStyle","popupMenuColumnStyle","onOpenChange","styles","classNames"]),j=dn(X,["suffixIcon"]),{getPrefixCls:Z,getPopupContainer:Q,className:te,style:T,classNames:B,styles:le}=Wn("cascader"),{popupOverflow:pe}=r.useContext(sn),{status:ce,hasFeedback:J,isFormItemInput:R,feedbackIcon:O}=r.useContext(Hn),E=Xn(ce,I),[y,V,ae,oe]=Pn(s,x),q=ae==="rtl",ie=Z(),fe=nn(y),[Ce,Pe,Le]=qn(y,fe),Se=nn(V),[Ie]=kn(V,Se),{compactSize:Fe,compactItemClassnames:je}=Kn(y,x),[Ee,me]=Fn("cascader",M,d),_e=L||(oe==null?void 0:oe("Cascader"))||r.createElement(vn,{componentName:"Cascader"}),Ve=ge(((t=A==null?void 0:A.popup)===null||t===void 0?void 0:t.root)||((a=B.popup)===null||a===void 0?void 0:a.root)||p||C,`${V}-dropdown`,{[`${V}-dropdown-rtl`]:ae==="rtl"},g,fe,B.root,A==null?void 0:A.root,Se,Pe,Le),Be=W||N,ze=z||K,Ae=Y||H,be=((o=w==null?void 0:w.popup)===null||o===void 0?void 0:o.root)||((i=le.popup)===null||i===void 0?void 0:i.root)||F,ke=r.useMemo(()=>{if(!m)return m;let he={render:_t};return typeof m=="object"&&(he=Object.assign(Object.assign({},he),m)),he},[m]),Te=jn(he=>{var Qe;return(Qe=l??Fe)!==null&&Qe!==void 0?Qe:he}),Xe=r.useContext(un),Ue=c??Xe,[Ge,Je]=En(y,q,_),Ye=In(V,f),We=Zn(e.suffixIcon,P),{suffixIcon:qe,removeIcon:ee,clearIcon:U}=Qn(Object.assign(Object.assign({},e),{hasFeedback:J,feedbackIcon:O,showSuffixIcon:We,multiple:f,prefixCls:y,componentName:"Cascader"})),de=r.useMemo(()=>$!==void 0?$:q?"bottomRight":"bottomLeft",[$,q]),Me=k===!0?{clearIcon:U}:k,[Ne]=Bn("SelectLike",be==null?void 0:be.zIndex),Ze=r.createElement(De,Object.assign({prefixCls:y,className:ge(!s&&V,{[`${y}-lg`]:Te==="large",[`${y}-sm`]:Te==="small",[`${y}-rtl`]:q,[`${y}-${Ee}`]:me,[`${y}-in-form-item`]:R},zn(y,E,J),je,te,u,g,A==null?void 0:A.root,B.root,fe,Se,Pe,Le),disabled:Ue,style:Object.assign(Object.assign(Object.assign(Object.assign({},le.root),w==null?void 0:w.root),T),v)},j,{builtinPlacements:et(D,pe),direction:ae,placement:de,notFoundContent:_e,allowClear:Me,showSearch:ke,expandIcon:Ge,suffixIcon:qe,removeIcon:ee,loadingIcon:Je,checkable:Ye,dropdownClassName:Ve,dropdownPrefixCls:s||V,dropdownStyle:Object.assign(Object.assign({},be),{zIndex:Ne}),dropdownRender:Be,dropdownMenuColumnStyle:ze,onOpenChange:Ae,choiceTransitionName:rn(ie,"",h),transitionName:rn(ie,"slide-up",S),getPopupContainer:b||Q,ref:n}));return Ie(Ce(Ze))}),Vt=Un(Re,"dropdownAlign",e=>dn(e,["visible"]));Re.SHOW_PARENT=It;Re.SHOW_CHILD=Pt;Re.Panel=wt;Re._InternalPanelDoNotUseOrYouWillBeFired=Vt;export{Re as C};
