import{aY as _t,a6 as oo,r as i,b as rn,E as on,a as W,G as St,bs as Ln,q as Et,l as da,$ as ua,aB as _n,ax as fa,c as G,aJ as va,bt as pa,bu as ma,w as ao,M as lo,ao as Ft,a8 as F,af as mt,a7 as lt,v as io,p as fe,t as so,a9 as rt,bv as hr,n as dt,f as En,ab as zn,k as ga,I as it,_ as he,a5 as Se,ay as ha,i as gt,bw as At,bx as yr,bp as co,by as Hn,al as B,x as Fn,a_ as ya,bz as br,bA as An,bB as xr,bC as Cr,V as ge,bD as jn,W as uo,au as ba,at as xa,ai as Ca,aj as Sa,an as wa,ak as Ea,am as de,K as pt,ar as $a,m as ka,av as Na,bq as fo,Q as Ra,U as Ia,B as Sr,bE as vo,bF as Ka,bG as Oa,T as wr,J as Ta,o as Pa,H as Da,ac as Lt,aA as Ma,P as Ba}from"./index-De_f0oL2.js";import{L as po,a as La,E as Er,D as _a}from"./index-BWJehDyc.js";import{a as za,b as Ha,u as Fa,C as Zt,g as Aa,P as ja}from"./useMultipleSelect-B0dEIXT-.js";function $n(e){return e!=null&&e===e.window}const Wa=e=>{var t,r;if(typeof window>"u")return 0;let n=0;return $n(e)?n=e.pageYOffset:e instanceof Document?n=e.documentElement.scrollTop:(e instanceof HTMLElement||e)&&(n=e.scrollTop),e&&!$n(e)&&typeof n!="number"&&(n=(r=((t=e.ownerDocument)!==null&&t!==void 0?t:e).documentElement)===null||r===void 0?void 0:r.scrollTop),n};function Va(e,t,r,n){const o=r-t;return e/=n/2,e<1?o/2*e*e*e+t:o/2*((e-=2)*e*e+2)+t}function qa(e,t={}){const{getContainer:r=()=>window,callback:n,duration:o=450}=t,a=r(),d=Wa(a),l=Date.now(),c=()=>{const v=Date.now()-l,u=Va(v>o?o:v,d,e,o);$n(a)?a.scrollTo(window.pageXOffset,u):a instanceof Document||a.constructor.name==="HTMLDocument"?a.documentElement.scrollTop=u:a.scrollTop=u,v<o?_t(c):typeof n=="function"&&n()};_t(c)}var mo=function(t){if(oo()&&window.document.documentElement){var r=Array.isArray(t)?t:[t],n=window.document.documentElement;return r.some(function(o){return o in n.style})}return!1},Xa=function(t,r){if(!mo(t))return!1;var n=document.createElement("div"),o=n.style[t];return n.style[t]=r,n.style[t]!==o};function Ga(e,t){return!Array.isArray(e)&&t!==void 0?Xa(e,t):mo(e)}const go=i.createContext(null),Ua=go.Provider,ho=i.createContext(null),Ya=ho.Provider,Ja=e=>{const{componentCls:t,antCls:r}=e,n=`${t}-group`;return{[n]:Object.assign(Object.assign({},St(e)),{display:"inline-block",fontSize:0,[`&${n}-rtl`]:{direction:"rtl"},[`&${n}-block`]:{display:"flex"},[`${r}-badge ${r}-badge-count`]:{zIndex:1},[`> ${r}-badge:not(:first-child) > ${r}-button-wrapper`]:{borderInlineStart:"none"}})}},Qa=e=>{const{componentCls:t,wrapperMarginInlineEnd:r,colorPrimary:n,radioSize:o,motionDurationSlow:a,motionDurationMid:d,motionEaseInOutCirc:l,colorBgContainer:c,colorBorder:s,lineWidth:v,colorBgContainerDisabled:u,colorTextDisabled:f,paddingXS:p,dotColorDisabled:g,lineType:b,radioColor:m,radioBgColor:y,calc:C}=e,x=`${t}-inner`,S=C(o).sub(C(4).mul(2)),E=C(1).mul(o).equal({unit:!0});return{[`${t}-wrapper`]:Object.assign(Object.assign({},St(e)),{display:"inline-flex",alignItems:"baseline",marginInlineStart:0,marginInlineEnd:r,cursor:"pointer","&:last-child":{marginInlineEnd:0},[`&${t}-wrapper-rtl`]:{direction:"rtl"},"&-disabled":{cursor:"not-allowed",color:e.colorTextDisabled},"&::after":{display:"inline-block",width:0,overflow:"hidden",content:'"\\a0"'},"&-block":{flex:1,justifyContent:"center"},[`${t}-checked::after`]:{position:"absolute",insetBlockStart:0,insetInlineStart:0,width:"100%",height:"100%",border:`${W(v)} ${b} ${n}`,borderRadius:"50%",visibility:"hidden",opacity:0,content:'""'},[t]:Object.assign(Object.assign({},St(e)),{position:"relative",display:"inline-block",outline:"none",cursor:"pointer",alignSelf:"center",borderRadius:"50%"}),[`${t}-wrapper:hover &,
        &:hover ${x}`]:{borderColor:n},[`${t}-input:focus-visible + ${x}`]:Object.assign({},Ln(e)),[`${t}:hover::after, ${t}-wrapper:hover &::after`]:{visibility:"visible"},[`${t}-inner`]:{"&::after":{boxSizing:"border-box",position:"absolute",insetBlockStart:"50%",insetInlineStart:"50%",display:"block",width:E,height:E,marginBlockStart:C(1).mul(o).div(-2).equal({unit:!0}),marginInlineStart:C(1).mul(o).div(-2).equal({unit:!0}),backgroundColor:m,borderBlockStart:0,borderInlineStart:0,borderRadius:E,transform:"scale(0)",opacity:0,transition:`all ${a} ${l}`,content:'""'},boxSizing:"border-box",position:"relative",insetBlockStart:0,insetInlineStart:0,display:"block",width:E,height:E,backgroundColor:c,borderColor:s,borderStyle:"solid",borderWidth:v,borderRadius:"50%",transition:`all ${d}`},[`${t}-input`]:{position:"absolute",inset:0,zIndex:1,cursor:"pointer",opacity:0},[`${t}-checked`]:{[x]:{borderColor:n,backgroundColor:y,"&::after":{transform:`scale(${e.calc(e.dotSize).div(o).equal()})`,opacity:1,transition:`all ${a} ${l}`}}},[`${t}-disabled`]:{cursor:"not-allowed",[x]:{backgroundColor:u,borderColor:s,cursor:"not-allowed","&::after":{backgroundColor:g}},[`${t}-input`]:{cursor:"not-allowed"},[`${t}-disabled + span`]:{color:f,cursor:"not-allowed"},[`&${t}-checked`]:{[x]:{"&::after":{transform:`scale(${C(S).div(o).equal()})`}}}},[`span${t} + *`]:{paddingInlineStart:p,paddingInlineEnd:p}})}},Za=e=>{const{buttonColor:t,controlHeight:r,componentCls:n,lineWidth:o,lineType:a,colorBorder:d,motionDurationSlow:l,motionDurationMid:c,buttonPaddingInline:s,fontSize:v,buttonBg:u,fontSizeLG:f,controlHeightLG:p,controlHeightSM:g,paddingXS:b,borderRadius:m,borderRadiusSM:y,borderRadiusLG:C,buttonCheckedBg:x,buttonSolidCheckedColor:w,colorTextDisabled:S,colorBgContainerDisabled:E,buttonCheckedBgDisabled:N,buttonCheckedColorDisabled:M,colorPrimary:h,colorPrimaryHover:O,colorPrimaryActive:T,buttonSolidCheckedBg:R,buttonSolidCheckedHoverBg:K,buttonSolidCheckedActiveBg:k,calc:$}=e;return{[`${n}-button-wrapper`]:{position:"relative",display:"inline-block",height:r,margin:0,paddingInline:s,paddingBlock:0,color:t,fontSize:v,lineHeight:W($(r).sub($(o).mul(2)).equal()),background:u,border:`${W(o)} ${a} ${d}`,borderBlockStartWidth:$(o).add(.02).equal(),borderInlineStartWidth:0,borderInlineEndWidth:o,cursor:"pointer",transition:[`color ${c}`,`background ${c}`,`box-shadow ${c}`].join(","),a:{color:t},[`> ${n}-button`]:{position:"absolute",insetBlockStart:0,insetInlineStart:0,zIndex:-1,width:"100%",height:"100%"},"&:not(:first-child)":{"&::before":{position:"absolute",insetBlockStart:$(o).mul(-1).equal(),insetInlineStart:$(o).mul(-1).equal(),display:"block",boxSizing:"content-box",width:1,height:"100%",paddingBlock:o,paddingInline:0,backgroundColor:d,transition:`background-color ${l}`,content:'""'}},"&:first-child":{borderInlineStart:`${W(o)} ${a} ${d}`,borderStartStartRadius:m,borderEndStartRadius:m},"&:last-child":{borderStartEndRadius:m,borderEndEndRadius:m},"&:first-child:last-child":{borderRadius:m},[`${n}-group-large &`]:{height:p,fontSize:f,lineHeight:W($(p).sub($(o).mul(2)).equal()),"&:first-child":{borderStartStartRadius:C,borderEndStartRadius:C},"&:last-child":{borderStartEndRadius:C,borderEndEndRadius:C}},[`${n}-group-small &`]:{height:g,paddingInline:$(b).sub(o).equal(),paddingBlock:0,lineHeight:W($(g).sub($(o).mul(2)).equal()),"&:first-child":{borderStartStartRadius:y,borderEndStartRadius:y},"&:last-child":{borderStartEndRadius:y,borderEndEndRadius:y}},"&:hover":{position:"relative",color:h},"&:has(:focus-visible)":Object.assign({},Ln(e)),[`${n}-inner, input[type='checkbox'], input[type='radio']`]:{width:0,height:0,opacity:0,pointerEvents:"none"},[`&-checked:not(${n}-button-wrapper-disabled)`]:{zIndex:1,color:h,background:x,borderColor:h,"&::before":{backgroundColor:h},"&:first-child":{borderColor:h},"&:hover":{color:O,borderColor:O,"&::before":{backgroundColor:O}},"&:active":{color:T,borderColor:T,"&::before":{backgroundColor:T}}},[`${n}-group-solid &-checked:not(${n}-button-wrapper-disabled)`]:{color:w,background:R,borderColor:R,"&:hover":{color:w,background:K,borderColor:K},"&:active":{color:w,background:k,borderColor:k}},"&-disabled":{color:S,backgroundColor:E,borderColor:d,cursor:"not-allowed","&:first-child, &:hover":{color:S,backgroundColor:E,borderColor:d}},[`&-disabled${n}-button-wrapper-checked`]:{color:M,backgroundColor:N,borderColor:d,boxShadow:"none"},"&-block":{flex:1,textAlign:"center"}}}},el=e=>{const{wireframe:t,padding:r,marginXS:n,lineWidth:o,fontSizeLG:a,colorText:d,colorBgContainer:l,colorTextDisabled:c,controlItemBgActiveDisabled:s,colorTextLightSolid:v,colorPrimary:u,colorPrimaryHover:f,colorPrimaryActive:p,colorWhite:g}=e,b=4,m=a,y=t?m-b*2:m-(b+o)*2;return{radioSize:m,dotSize:y,dotColorDisabled:c,buttonSolidCheckedColor:v,buttonSolidCheckedBg:u,buttonSolidCheckedHoverBg:f,buttonSolidCheckedActiveBg:p,buttonBg:l,buttonCheckedBg:l,buttonColor:d,buttonCheckedBgDisabled:s,buttonCheckedColorDisabled:c,buttonPaddingInline:r-o,wrapperMarginInlineEnd:n,radioColor:t?u:g,radioBgColor:t?l:u}},yo=rn("Radio",e=>{const{controlOutline:t,controlOutlineWidth:r}=e,n=`0 0 0 ${W(r)} ${t}`,a=on(e,{radioFocusShadow:n,radioButtonFocusShadow:n});return[Ja(a),Qa(a),Za(a)]},el,{unitless:{radioSize:!0,dotSize:!0}});var tl=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const nl=(e,t)=>{var r,n;const o=i.useContext(go),a=i.useContext(ho),{getPrefixCls:d,direction:l,radio:c}=i.useContext(Et),s=i.useRef(null),v=da(t,s),{isFormItemInput:u}=i.useContext(ua),f=I=>{var P,_;(P=e.onChange)===null||P===void 0||P.call(e,I),(_=o==null?void 0:o.onChange)===null||_===void 0||_.call(o,I)},{prefixCls:p,className:g,rootClassName:b,children:m,style:y,title:C}=e,x=tl(e,["prefixCls","className","rootClassName","children","style","title"]),w=d("radio",p),S=((o==null?void 0:o.optionType)||a)==="button",E=S?`${w}-button`:w,N=_n(w),[M,h,O]=yo(w,N),T=Object.assign({},x),R=i.useContext(fa);o&&(T.name=o.name,T.onChange=f,T.checked=e.value===o.value,T.disabled=(r=T.disabled)!==null&&r!==void 0?r:o.disabled),T.disabled=(n=T.disabled)!==null&&n!==void 0?n:R;const K=G(`${E}-wrapper`,{[`${E}-wrapper-checked`]:T.checked,[`${E}-wrapper-disabled`]:T.disabled,[`${E}-wrapper-rtl`]:l==="rtl",[`${E}-wrapper-in-form-item`]:u,[`${E}-wrapper-block`]:!!(o!=null&&o.block)},c==null?void 0:c.className,g,b,h,O,N),[k,$]=za(T.onClick);return M(i.createElement(va,{component:"Radio",disabled:T.disabled},i.createElement("label",{className:K,style:Object.assign(Object.assign({},c==null?void 0:c.style),y),onMouseEnter:e.onMouseEnter,onMouseLeave:e.onMouseLeave,title:C,onClick:k},i.createElement(Ha,Object.assign({},T,{className:G(T.className,{[pa]:!S}),type:"radio",prefixCls:E,ref:v,onClick:$})),m!==void 0?i.createElement("span",{className:`${E}-label`},m):null)))},en=i.forwardRef(nl),rl=i.forwardRef((e,t)=>{const{getPrefixCls:r,direction:n}=i.useContext(Et),o=ma(),{prefixCls:a,className:d,rootClassName:l,options:c,buttonStyle:s="outline",disabled:v,children:u,size:f,style:p,id:g,optionType:b,name:m=o,defaultValue:y,value:C,block:x=!1,onChange:w,onMouseEnter:S,onMouseLeave:E,onFocus:N,onBlur:M}=e,[h,O]=ao(y,{value:C}),T=i.useCallback(Y=>{const te=h,ye=Y.target.value;"value"in e||O(ye),ye!==te&&(w==null||w(Y))},[h,O,w]),R=r("radio",a),K=`${R}-group`,k=_n(R),[$,I,P]=yo(R,k);let _=u;c&&c.length>0&&(_=c.map(Y=>typeof Y=="string"||typeof Y=="number"?i.createElement(en,{key:Y.toString(),prefixCls:R,disabled:v,value:Y,checked:h===Y},Y):i.createElement(en,{key:`radio-group-value-options-${Y.value}`,prefixCls:R,disabled:Y.disabled||v,value:Y.value,checked:h===Y.value,title:Y.title,style:Y.style,className:Y.className,id:Y.id,required:Y.required},Y.label)));const D=lo(f),V=G(K,`${K}-${s}`,{[`${K}-${D}`]:D,[`${K}-rtl`]:n==="rtl",[`${K}-block`]:x},d,l,I,P,k),Q=i.useMemo(()=>({onChange:T,value:h,disabled:v,name:m,optionType:b,block:x}),[T,h,v,m,b,x]);return $(i.createElement("div",Object.assign({},Ft(e,{aria:!0,data:!0}),{className:V,style:p,onMouseEnter:S,onMouseLeave:E,onFocus:N,onBlur:M,id:g,ref:t}),i.createElement(Ua,{value:Q},_)))}),ol=i.memo(rl);var al=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const ll=(e,t)=>{const{getPrefixCls:r}=i.useContext(Et),{prefixCls:n}=e,o=al(e,["prefixCls"]),a=r("radio",n);return i.createElement(Ya,{value:"button"},i.createElement(en,Object.assign({prefixCls:a},o,{type:"radio",ref:t})))},il=i.forwardRef(ll),Wt=en;Wt.Button=il;Wt.Group=ol;Wt.__ANT_RADIO=!0;function sl(e,t,r){var n=r||{},o=n.noTrailing,a=o===void 0?!1:o,d=n.noLeading,l=d===void 0?!1:d,c=n.debounceMode,s=c===void 0?void 0:c,v,u=!1,f=0;function p(){v&&clearTimeout(v)}function g(m){var y=m||{},C=y.upcomingOnly,x=C===void 0?!1:C;p(),u=!x}function b(){for(var m=arguments.length,y=new Array(m),C=0;C<m;C++)y[C]=arguments[C];var x=this,w=Date.now()-f;if(u)return;function S(){f=Date.now(),t.apply(x,y)}function E(){v=void 0}!l&&s&&!v&&S(),p(),s===void 0&&w>e?l?(f=Date.now(),a||(v=setTimeout(s?E:S,e))):S():a!==!0&&(v=setTimeout(s?E:S,s===void 0?e-w:e))}return b.cancel=g,b}function cl(e,t,r){var n={},o=n.atBegin,a=o===void 0?!1:o;return sl(e,t,{debounceMode:a!==!1})}function Je(e,t){return e[t]}var dl=["children"];function bo(e,t){return"".concat(e,"-").concat(t)}function ul(e){return e&&e.type&&e.type.isTreeNode}function Vt(e,t){return e??t}function Ot(e){var t=e||{},r=t.title,n=t._title,o=t.key,a=t.children,d=r||"title";return{title:d,_title:n||[d],key:o||"key",children:a||"children"}}function xo(e){function t(r){var n=so(r);return n.map(function(o){if(!ul(o))return mt(!o,"Tree/TreeNode can only accept TreeNode as children."),null;var a=o.key,d=o.props,l=d.children,c=rt(d,dl),s=F({key:a},c),v=t(l);return v.length&&(s.children=v),s}).filter(function(o){return o})}return t(e)}function hn(e,t,r){var n=Ot(r),o=n._title,a=n.key,d=n.children,l=new Set(t===!0?[]:t),c=[];function s(v){var u=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;return v.map(function(f,p){for(var g=bo(u?u.pos:"0",p),b=Vt(f[a],g),m,y=0;y<o.length;y+=1){var C=o[y];if(f[C]!==void 0){m=f[C];break}}var x=Object.assign(io(f,[].concat(fe(o),[a,d])),{title:m,key:b,parent:u,pos:g,children:null,data:f,isStart:[].concat(fe(u?u.isStart:[]),[p===0]),isEnd:[].concat(fe(u?u.isEnd:[]),[p===v.length-1])});return c.push(x),t===!0||l.has(b)?x.children=s(f[d]||[],x):x.children=[],x})}return s(e),c}function fl(e,t,r){var n={};lt(r)==="object"?n=r:n={externalGetKey:r},n=n||{};var o=n,a=o.childrenPropName,d=o.externalGetKey,l=o.fieldNames,c=Ot(l),s=c.key,v=c.children,u=a||v,f;d?typeof d=="string"?f=function(b){return b[d]}:typeof d=="function"&&(f=function(b){return d(b)}):f=function(b,m){return Vt(b[s],m)};function p(g,b,m,y){var C=g?g[u]:e,x=g?bo(m.pos,b):"0",w=g?[].concat(fe(y),[g]):[];if(g){var S=f(g,x),E={node:g,index:b,pos:x,key:S,parentPos:m.node?m.pos:null,level:m.level+1,nodes:w};t(E)}C&&C.forEach(function(N,M){p(N,M,{node:g,pos:x,level:m?m.level+1:-1},w)})}p(null)}function Wn(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=t.initWrapper,n=t.processEntity,o=t.onProcessFinished,a=t.externalGetKey,d=t.childrenPropName,l=t.fieldNames,c=arguments.length>2?arguments[2]:void 0,s=a||c,v={},u={},f={posEntities:v,keyEntities:u};return r&&(f=r(f)||f),fl(e,function(p){var g=p.node,b=p.index,m=p.pos,y=p.key,C=p.parentPos,x=p.level,w=p.nodes,S={node:g,nodes:w,index:b,key:y,pos:m,level:x},E=Vt(y,m);v[m]=S,u[E]=S,S.parent=v[C],S.parent&&(S.parent.children=S.parent.children||[],S.parent.children.push(S)),n&&n(S,f)},{externalGetKey:s,childrenPropName:d,fieldNames:l}),o&&o(f),f}function zt(e,t){var r=t.expandedKeys,n=t.selectedKeys,o=t.loadedKeys,a=t.loadingKeys,d=t.checkedKeys,l=t.halfCheckedKeys,c=t.dragOverNodeKey,s=t.dropPosition,v=t.keyEntities,u=Je(v,e),f={eventKey:e,expanded:r.indexOf(e)!==-1,selected:n.indexOf(e)!==-1,loaded:o.indexOf(e)!==-1,loading:a.indexOf(e)!==-1,checked:d.indexOf(e)!==-1,halfChecked:l.indexOf(e)!==-1,pos:String(u?u.pos:""),dragOver:c===e&&s===0,dragOverGapTop:c===e&&s===-1,dragOverGapBottom:c===e&&s===1};return f}function Me(e){var t=e.data,r=e.expanded,n=e.selected,o=e.checked,a=e.loaded,d=e.loading,l=e.halfChecked,c=e.dragOver,s=e.dragOverGapTop,v=e.dragOverGapBottom,u=e.pos,f=e.active,p=e.eventKey,g=F(F({},t),{},{expanded:r,selected:n,checked:o,loaded:a,loading:d,halfChecked:l,dragOver:c,dragOverGapTop:s,dragOverGapBottom:v,pos:u,active:f,key:p});return"props"in g||Object.defineProperty(g,"props",{get:function(){return mt(!1,"Second param return from event is node data instead of TreeNode instance. Please read value directly instead of reading from `props`."),e}}),g}function Co(e,t){var r=new Set;return e.forEach(function(n){t.has(n)||r.add(n)}),r}function vl(e){var t=e||{},r=t.disabled,n=t.disableCheckbox,o=t.checkable;return!!(r||n)||o===!1}function pl(e,t,r,n){for(var o=new Set(e),a=new Set,d=0;d<=r;d+=1){var l=t.get(d)||new Set;l.forEach(function(u){var f=u.key,p=u.node,g=u.children,b=g===void 0?[]:g;o.has(f)&&!n(p)&&b.filter(function(m){return!n(m.node)}).forEach(function(m){o.add(m.key)})})}for(var c=new Set,s=r;s>=0;s-=1){var v=t.get(s)||new Set;v.forEach(function(u){var f=u.parent,p=u.node;if(!(n(p)||!u.parent||c.has(u.parent.key))){if(n(u.parent.node)){c.add(f.key);return}var g=!0,b=!1;(f.children||[]).filter(function(m){return!n(m.node)}).forEach(function(m){var y=m.key,C=o.has(y);g&&!C&&(g=!1),!b&&(C||a.has(y))&&(b=!0)}),g&&o.add(f.key),b&&a.add(f.key),c.add(f.key)}})}return{checkedKeys:Array.from(o),halfCheckedKeys:Array.from(Co(a,o))}}function ml(e,t,r,n,o){for(var a=new Set(e),d=new Set(t),l=0;l<=n;l+=1){var c=r.get(l)||new Set;c.forEach(function(f){var p=f.key,g=f.node,b=f.children,m=b===void 0?[]:b;!a.has(p)&&!d.has(p)&&!o(g)&&m.filter(function(y){return!o(y.node)}).forEach(function(y){a.delete(y.key)})})}d=new Set;for(var s=new Set,v=n;v>=0;v-=1){var u=r.get(v)||new Set;u.forEach(function(f){var p=f.parent,g=f.node;if(!(o(g)||!f.parent||s.has(f.parent.key))){if(o(f.parent.node)){s.add(p.key);return}var b=!0,m=!1;(p.children||[]).filter(function(y){return!o(y.node)}).forEach(function(y){var C=y.key,x=a.has(C);b&&!x&&(b=!1),!m&&(x||d.has(C))&&(m=!0)}),b||a.delete(p.key),m&&d.add(p.key),s.add(p.key)}})}return{checkedKeys:Array.from(a),halfCheckedKeys:Array.from(Co(d,a))}}function It(e,t,r,n){var o=[],a;n?a=n:a=vl;var d=new Set(e.filter(function(v){var u=!!Je(r,v);return u||o.push(v),u})),l=new Map,c=0;Object.keys(r).forEach(function(v){var u=r[v],f=u.level,p=l.get(f);p||(p=new Set,l.set(f,p)),p.add(u),c=Math.max(c,f)}),mt(!o.length,"Tree missing follow keys: ".concat(o.slice(0,100).map(function(v){return"'".concat(v,"'")}).join(", ")));var s;return t===!0?s=pl(d,l,c,a):s=ml(d,t.halfCheckedKeys,l,c,a),s}function $r(e,t,r,n){var o=hr.unstable_batchedUpdates?function(d){hr.unstable_batchedUpdates(r,d)}:r;return e!=null&&e.addEventListener&&e.addEventListener(t,o,n),{remove:function(){e!=null&&e.removeEventListener&&e.removeEventListener(t,o,n)}}}const tn=100,So=tn/5,wo=tn/2-So/2,yn=wo*2*Math.PI,kr=50,Nr=e=>{const{dotClassName:t,style:r,hasCircleCls:n}=e;return i.createElement("circle",{className:G(`${t}-circle`,{[`${t}-circle-bg`]:n}),r:wo,cx:kr,cy:kr,strokeWidth:So,style:r})},gl=({percent:e,prefixCls:t})=>{const r=`${t}-dot`,n=`${r}-holder`,o=`${n}-hidden`,[a,d]=i.useState(!1);dt(()=>{e!==0&&d(!0)},[e!==0]);const l=Math.max(Math.min(e,100),0);if(!a)return null;const c={strokeDashoffset:`${yn/4}`,strokeDasharray:`${yn*l/100} ${yn*(100-l)/100}`};return i.createElement("span",{className:G(n,`${r}-progress`,l<=0&&o)},i.createElement("svg",{viewBox:`0 0 ${tn} ${tn}`,role:"progressbar","aria-valuemin":0,"aria-valuemax":100,"aria-valuenow":l},i.createElement(Nr,{dotClassName:r,hasCircleCls:!0}),i.createElement(Nr,{dotClassName:r,style:c})))};function hl(e){const{prefixCls:t,percent:r=0}=e,n=`${t}-dot`,o=`${n}-holder`,a=`${o}-hidden`;return i.createElement(i.Fragment,null,i.createElement("span",{className:G(o,r>0&&a)},i.createElement("span",{className:G(n,`${t}-dot-spin`)},[1,2,3,4].map(d=>i.createElement("i",{className:`${t}-dot-item`,key:d})))),i.createElement(gl,{prefixCls:t,percent:r}))}function yl(e){const{prefixCls:t,indicator:r,percent:n}=e,o=`${t}-dot`;return r&&i.isValidElement(r)?En(r,{className:G(r.props.className,o),percent:n}):i.createElement(hl,{prefixCls:t,percent:n})}const bl=new zn("antSpinMove",{to:{opacity:1}}),xl=new zn("antRotate",{to:{transform:"rotate(405deg)"}}),Cl=e=>{const{componentCls:t,calc:r}=e;return{[t]:Object.assign(Object.assign({},St(e)),{position:"absolute",display:"none",color:e.colorPrimary,fontSize:0,textAlign:"center",verticalAlign:"middle",opacity:0,transition:`transform ${e.motionDurationSlow} ${e.motionEaseInOutCirc}`,"&-spinning":{position:"relative",display:"inline-block",opacity:1},[`${t}-text`]:{fontSize:e.fontSize,paddingTop:r(r(e.dotSize).sub(e.fontSize)).div(2).add(2).equal()},"&-fullscreen":{position:"fixed",width:"100vw",height:"100vh",backgroundColor:e.colorBgMask,zIndex:e.zIndexPopupBase,inset:0,display:"flex",alignItems:"center",flexDirection:"column",justifyContent:"center",opacity:0,visibility:"hidden",transition:`all ${e.motionDurationMid}`,"&-show":{opacity:1,visibility:"visible"},[t]:{[`${t}-dot-holder`]:{color:e.colorWhite},[`${t}-text`]:{color:e.colorTextLightSolid}}},"&-nested-loading":{position:"relative",[`> div > ${t}`]:{position:"absolute",top:0,insetInlineStart:0,zIndex:4,display:"block",width:"100%",height:"100%",maxHeight:e.contentHeight,[`${t}-dot`]:{position:"absolute",top:"50%",insetInlineStart:"50%",margin:r(e.dotSize).mul(-1).div(2).equal()},[`${t}-text`]:{position:"absolute",top:"50%",width:"100%",textShadow:`0 1px 2px ${e.colorBgContainer}`},[`&${t}-show-text ${t}-dot`]:{marginTop:r(e.dotSize).div(2).mul(-1).sub(10).equal()},"&-sm":{[`${t}-dot`]:{margin:r(e.dotSizeSM).mul(-1).div(2).equal()},[`${t}-text`]:{paddingTop:r(r(e.dotSizeSM).sub(e.fontSize)).div(2).add(2).equal()},[`&${t}-show-text ${t}-dot`]:{marginTop:r(e.dotSizeSM).div(2).mul(-1).sub(10).equal()}},"&-lg":{[`${t}-dot`]:{margin:r(e.dotSizeLG).mul(-1).div(2).equal()},[`${t}-text`]:{paddingTop:r(r(e.dotSizeLG).sub(e.fontSize)).div(2).add(2).equal()},[`&${t}-show-text ${t}-dot`]:{marginTop:r(e.dotSizeLG).div(2).mul(-1).sub(10).equal()}}},[`${t}-container`]:{position:"relative",transition:`opacity ${e.motionDurationSlow}`,"&::after":{position:"absolute",top:0,insetInlineEnd:0,bottom:0,insetInlineStart:0,zIndex:10,width:"100%",height:"100%",background:e.colorBgContainer,opacity:0,transition:`all ${e.motionDurationSlow}`,content:'""',pointerEvents:"none"}},[`${t}-blur`]:{clear:"both",opacity:.5,userSelect:"none",pointerEvents:"none","&::after":{opacity:.4,pointerEvents:"auto"}}},"&-tip":{color:e.spinDotDefault},[`${t}-dot-holder`]:{width:"1em",height:"1em",fontSize:e.dotSize,display:"inline-block",transition:`transform ${e.motionDurationSlow} ease, opacity ${e.motionDurationSlow} ease`,transformOrigin:"50% 50%",lineHeight:1,color:e.colorPrimary,"&-hidden":{transform:"scale(0.3)",opacity:0}},[`${t}-dot-progress`]:{position:"absolute",inset:0},[`${t}-dot`]:{position:"relative",display:"inline-block",fontSize:e.dotSize,width:"1em",height:"1em","&-item":{position:"absolute",display:"block",width:r(e.dotSize).sub(r(e.marginXXS).div(2)).div(2).equal(),height:r(e.dotSize).sub(r(e.marginXXS).div(2)).div(2).equal(),background:"currentColor",borderRadius:"100%",transform:"scale(0.75)",transformOrigin:"50% 50%",opacity:.3,animationName:bl,animationDuration:"1s",animationIterationCount:"infinite",animationTimingFunction:"linear",animationDirection:"alternate","&:nth-child(1)":{top:0,insetInlineStart:0,animationDelay:"0s"},"&:nth-child(2)":{top:0,insetInlineEnd:0,animationDelay:"0.4s"},"&:nth-child(3)":{insetInlineEnd:0,bottom:0,animationDelay:"0.8s"},"&:nth-child(4)":{bottom:0,insetInlineStart:0,animationDelay:"1.2s"}},"&-spin":{transform:"rotate(45deg)",animationName:xl,animationDuration:"1.2s",animationIterationCount:"infinite",animationTimingFunction:"linear"},"&-circle":{strokeLinecap:"round",transition:["stroke-dashoffset","stroke-dasharray","stroke","stroke-width","opacity"].map(n=>`${n} ${e.motionDurationSlow} ease`).join(","),fillOpacity:0,stroke:"currentcolor"},"&-circle-bg":{stroke:e.colorFillSecondary}},[`&-sm ${t}-dot`]:{"&, &-holder":{fontSize:e.dotSizeSM}},[`&-sm ${t}-dot-holder`]:{i:{width:r(r(e.dotSizeSM).sub(r(e.marginXXS).div(2))).div(2).equal(),height:r(r(e.dotSizeSM).sub(r(e.marginXXS).div(2))).div(2).equal()}},[`&-lg ${t}-dot`]:{"&, &-holder":{fontSize:e.dotSizeLG}},[`&-lg ${t}-dot-holder`]:{i:{width:r(r(e.dotSizeLG).sub(e.marginXXS)).div(2).equal(),height:r(r(e.dotSizeLG).sub(e.marginXXS)).div(2).equal()}},[`&${t}-show-text ${t}-text`]:{display:"block"}})}},Sl=e=>{const{controlHeightLG:t,controlHeight:r}=e;return{contentHeight:400,dotSize:t/2,dotSizeSM:t*.35,dotSizeLG:r}},wl=rn("Spin",e=>{const t=on(e,{spinDotDefault:e.colorTextDescription});return[Cl(t)]},Sl),El=200,Rr=[[30,.05],[70,.03],[96,.01]];function $l(e,t){const[r,n]=i.useState(0),o=i.useRef(null),a=t==="auto";return i.useEffect(()=>(a&&e&&(n(0),o.current=setInterval(()=>{n(d=>{const l=100-d;for(let c=0;c<Rr.length;c+=1){const[s,v]=Rr[c];if(d<=s)return d+l*v}return d})},El)),()=>{clearInterval(o.current)}),[a,e]),a?r:t}var kl=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};let Eo;function Nl(e,t){return!!e&&!!t&&!Number.isNaN(Number(t))}const $o=e=>{var t;const{prefixCls:r,spinning:n=!0,delay:o=0,className:a,rootClassName:d,size:l="default",tip:c,wrapperClassName:s,style:v,children:u,fullscreen:f=!1,indicator:p,percent:g}=e,b=kl(e,["prefixCls","spinning","delay","className","rootClassName","size","tip","wrapperClassName","style","children","fullscreen","indicator","percent"]),{getPrefixCls:m,direction:y,className:C,style:x,indicator:w}=ga("spin"),S=m("spin",r),[E,N,M]=wl(S),[h,O]=i.useState(()=>n&&!Nl(n,o)),T=$l(h,g);i.useEffect(()=>{if(n){const _=cl(o,()=>{O(!0)});return _(),()=>{var D;(D=_==null?void 0:_.cancel)===null||D===void 0||D.call(_)}}O(!1)},[o,n]);const R=i.useMemo(()=>typeof u<"u"&&!f,[u,f]),K=G(S,C,{[`${S}-sm`]:l==="small",[`${S}-lg`]:l==="large",[`${S}-spinning`]:h,[`${S}-show-text`]:!!c,[`${S}-rtl`]:y==="rtl"},a,!f&&d,N,M),k=G(`${S}-container`,{[`${S}-blur`]:h}),$=(t=p??w)!==null&&t!==void 0?t:Eo,I=Object.assign(Object.assign({},x),v),P=i.createElement("div",Object.assign({},b,{style:I,className:K,"aria-live":"polite","aria-busy":h}),i.createElement(yl,{prefixCls:S,indicator:$,percent:T}),c&&(R||f)?i.createElement("div",{className:`${S}-text`},c):null);return E(R?i.createElement("div",Object.assign({},b,{className:G(`${S}-nested-loading`,s,N,M)}),h&&i.createElement("div",{key:"loading"},P),i.createElement("div",{className:k,key:"container"},u)):f?i.createElement("div",{className:G(`${S}-fullscreen`,{[`${S}-fullscreen-show`]:h},d,N,M)},P):P)};$o.setDefaultIndicator=e=>{Eo=e};var Rl={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"}}]},name:"caret-down",theme:"filled"},Il=function(t,r){return i.createElement(it,he({},t,{ref:r,icon:Rl}))},Kl=i.forwardRef(Il),Ol={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"}}]},name:"caret-down",theme:"outlined"},Tl=function(t,r){return i.createElement(it,he({},t,{ref:r,icon:Ol}))},Pl=i.forwardRef(Tl),Dl={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"}}]},name:"caret-up",theme:"outlined"},Ml=function(t,r){return i.createElement(it,he({},t,{ref:r,icon:Dl}))},Bl=i.forwardRef(Ml),Ll={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494z"}}]},name:"file",theme:"outlined"},_l=function(t,r){return i.createElement(it,he({},t,{ref:r,icon:Ll}))},ko=i.forwardRef(_l),zl={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M349 838c0 17.7 14.2 32 31.8 32h262.4c17.6 0 31.8-14.3 31.8-32V642H349v196zm531.1-684H143.9c-24.5 0-39.8 26.7-27.5 48l221.3 376h348.8l221.3-376c12.1-21.3-3.2-48-27.7-48z"}}]},name:"filter",theme:"filled"},Hl=function(t,r){return i.createElement(it,he({},t,{ref:r,icon:zl}))},Fl=i.forwardRef(Hl),Al={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 444H820V330.4c0-17.7-14.3-32-32-32H473L355.7 186.2a8.15 8.15 0 00-5.5-2.2H96c-17.7 0-32 14.3-32 32v592c0 17.7 14.3 32 32 32h698c13 0 24.8-7.9 29.7-20l134-332c1.5-3.8 2.3-7.9 2.3-12 0-17.7-14.3-32-32-32zM136 256h188.5l119.6 114.4H748V444H238c-13 0-24.8 7.9-29.7 20L136 643.2V256zm635.3 512H159l103.3-256h612.4L771.3 768z"}}]},name:"folder-open",theme:"outlined"},jl=function(t,r){return i.createElement(it,he({},t,{ref:r,icon:Al}))},Wl=i.forwardRef(jl),Vl={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 298.4H521L403.7 186.2a8.15 8.15 0 00-5.5-2.2H144c-17.7 0-32 14.3-32 32v592c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V330.4c0-17.7-14.3-32-32-32zM840 768H184V256h188.5l119.6 114.4H840V768z"}}]},name:"folder",theme:"outlined"},ql=function(t,r){return i.createElement(it,he({},t,{ref:r,icon:Vl}))},Xl=i.forwardRef(ql),Gl={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M300 276.5a56 56 0 1056-97 56 56 0 00-56 97zm0 284a56 56 0 1056-97 56 56 0 00-56 97zM640 228a56 56 0 10112 0 56 56 0 00-112 0zm0 284a56 56 0 10112 0 56 56 0 00-112 0zM300 844.5a56 56 0 1056-97 56 56 0 00-56 97zM640 796a56 56 0 10112 0 56 56 0 00-112 0z"}}]},name:"holder",theme:"outlined"},Ul=function(t,r){return i.createElement(it,he({},t,{ref:r,icon:Gl}))},Yl=i.forwardRef(Ul),Jl={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M328 544h368c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"}},{tag:"path",attrs:{d:"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"}}]},name:"minus-square",theme:"outlined"},Ql=function(t,r){return i.createElement(it,he({},t,{ref:r,icon:Jl}))},Zl=i.forwardRef(Ql),ei={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M328 544h152v152c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V544h152c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H544V328c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v152H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"}},{tag:"path",attrs:{d:"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"}}]},name:"plus-square",theme:"outlined"},ti=function(t,r){return i.createElement(it,he({},t,{ref:r,icon:ei}))},ni=i.forwardRef(ti),ct={},qt="rc-table-internal-hook";function Vn(e){var t=i.createContext(void 0),r=function(o){var a=o.value,d=o.children,l=i.useRef(a);l.current=a;var c=i.useState(function(){return{getValue:function(){return l.current},listeners:new Set}}),s=Se(c,1),v=s[0];return dt(function(){ha.unstable_batchedUpdates(function(){v.listeners.forEach(function(u){u(a)})})},[a]),i.createElement(t.Provider,{value:v},d)};return{Context:t,Provider:r,defaultValue:e}}function He(e,t){var r=gt(typeof t=="function"?t:function(u){if(t===void 0)return u;if(!Array.isArray(t))return u[t];var f={};return t.forEach(function(p){f[p]=u[p]}),f}),n=i.useContext(e==null?void 0:e.Context),o=n||{},a=o.listeners,d=o.getValue,l=i.useRef();l.current=r(n?d():e==null?void 0:e.defaultValue);var c=i.useState({}),s=Se(c,2),v=s[1];return dt(function(){if(!n)return;function u(f){var p=r(f);At(l.current,p,!0)||v({})}return a.add(u),function(){a.delete(u)}},[n]),l.current}function ri(){var e=i.createContext(null);function t(){return i.useContext(e)}function r(o,a){var d=yr(o),l=function(s,v){var u=d?{ref:v}:{},f=i.useRef(0),p=i.useRef(s),g=t();return g!==null?i.createElement(o,he({},s,u)):((!a||a(p.current,s))&&(f.current+=1),p.current=s,i.createElement(e.Provider,{value:f.current},i.createElement(o,he({},s,u))))};return d?i.forwardRef(l):l}function n(o,a){var d=yr(o),l=function(s,v){var u=d?{ref:v}:{};return t(),i.createElement(o,he({},s,u))};return d?i.memo(i.forwardRef(l),a):i.memo(l,a)}return{makeImmutable:r,responseImmutable:n,useImmutableMark:t}}var qn=ri(),No=qn.makeImmutable,Tt=qn.responseImmutable,oi=qn.useImmutableMark,Ge=Vn(),Ro=i.createContext({renderWithProps:!1}),ai="RC_TABLE_KEY";function li(e){return e==null?[]:Array.isArray(e)?e:[e]}function an(e){var t=[],r={};return e.forEach(function(n){for(var o=n||{},a=o.key,d=o.dataIndex,l=a||li(d).join("-")||ai;r[l];)l="".concat(l,"_next");r[l]=!0,t.push(l)}),t}function kn(e){return e!=null}function ii(e){return typeof e=="number"&&!Number.isNaN(e)}function si(e){return e&&lt(e)==="object"&&!Array.isArray(e)&&!i.isValidElement(e)}function ci(e,t,r,n,o,a){var d=i.useContext(Ro),l=oi(),c=co(function(){if(kn(n))return[n];var s=t==null||t===""?[]:Array.isArray(t)?t:[t],v=Hn(e,s),u=v,f=void 0;if(o){var p=o(v,e,r);si(p)?(u=p.children,f=p.props,d.renderWithProps=!0):u=p}return[u,f]},[l,e,n,t,o,r],function(s,v){if(a){var u=Se(s,2),f=u[1],p=Se(v,2),g=p[1];return a(g,f)}return d.renderWithProps?!0:!At(s,v,!0)});return c}function di(e,t,r,n){var o=e+t-1;return e<=n&&o>=r}function ui(e,t){return He(Ge,function(r){var n=di(e,t||1,r.hoverStartRow,r.hoverEndRow);return[n,r.onHover]})}var fi=function(t){var r=t.ellipsis,n=t.rowType,o=t.children,a,d=r===!0?{showTitle:!0}:r;return d&&(d.showTitle||n==="header")&&(typeof o=="string"||typeof o=="number"?a=o.toString():i.isValidElement(o)&&typeof o.props.children=="string"&&(a=o.props.children)),a};function vi(e){var t,r,n,o,a,d,l,c,s=e.component,v=e.children,u=e.ellipsis,f=e.scope,p=e.prefixCls,g=e.className,b=e.align,m=e.record,y=e.render,C=e.dataIndex,x=e.renderIndex,w=e.shouldCellUpdate,S=e.index,E=e.rowType,N=e.colSpan,M=e.rowSpan,h=e.fixLeft,O=e.fixRight,T=e.firstFixLeft,R=e.lastFixLeft,K=e.firstFixRight,k=e.lastFixRight,$=e.appendNode,I=e.additionalProps,P=I===void 0?{}:I,_=e.isSticky,D="".concat(p,"-cell"),V=He(Ge,["supportSticky","allColumnsFixedLeft","rowHoverable"]),Q=V.supportSticky,Y=V.allColumnsFixedLeft,te=V.rowHoverable,ye=ci(m,C,x,v,y,w),Ce=Se(ye,2),we=Ce[0],J=Ce[1],ee={},Ee=typeof h=="number"&&Q,ve=typeof O=="number"&&Q;Ee&&(ee.position="sticky",ee.left=h),ve&&(ee.position="sticky",ee.right=O);var q=(t=(r=(n=J==null?void 0:J.colSpan)!==null&&n!==void 0?n:P.colSpan)!==null&&r!==void 0?r:N)!==null&&t!==void 0?t:1,X=(o=(a=(d=J==null?void 0:J.rowSpan)!==null&&d!==void 0?d:P.rowSpan)!==null&&a!==void 0?a:M)!==null&&o!==void 0?o:1,H=ui(S,X),j=Se(H,2),Z=j[0],le=j[1],pe=gt(function(be){var ie;m&&le(S,S+X-1),P==null||(ie=P.onMouseEnter)===null||ie===void 0||ie.call(P,be)}),ke=gt(function(be){var ie;m&&le(-1,-1),P==null||(ie=P.onMouseLeave)===null||ie===void 0||ie.call(P,be)});if(q===0||X===0)return null;var Fe=(l=P.title)!==null&&l!==void 0?l:fi({rowType:E,ellipsis:u,children:we}),Re=G(D,g,(c={},B(B(B(B(B(B(B(B(B(B(c,"".concat(D,"-fix-left"),Ee&&Q),"".concat(D,"-fix-left-first"),T&&Q),"".concat(D,"-fix-left-last"),R&&Q),"".concat(D,"-fix-left-all"),R&&Y&&Q),"".concat(D,"-fix-right"),ve&&Q),"".concat(D,"-fix-right-first"),K&&Q),"".concat(D,"-fix-right-last"),k&&Q),"".concat(D,"-ellipsis"),u),"".concat(D,"-with-append"),$),"".concat(D,"-fix-sticky"),(Ee||ve)&&_&&Q),B(c,"".concat(D,"-row-hover"),!J&&Z)),P.className,J==null?void 0:J.className),L={};b&&(L.textAlign=b);var A=F(F(F(F({},J==null?void 0:J.style),ee),L),P.style),re=we;return lt(re)==="object"&&!Array.isArray(re)&&!i.isValidElement(re)&&(re=null),u&&(R||K)&&(re=i.createElement("span",{className:"".concat(D,"-content")},re)),i.createElement(s,he({},J,P,{className:Re,style:A,title:Fe,scope:f,onMouseEnter:te?pe:void 0,onMouseLeave:te?ke:void 0,colSpan:q!==1?q:null,rowSpan:X!==1?X:null}),$,re)}const Pt=i.memo(vi);function Xn(e,t,r,n,o){var a=r[e]||{},d=r[t]||{},l,c;a.fixed==="left"?l=n.left[o==="rtl"?t:e]:d.fixed==="right"&&(c=n.right[o==="rtl"?e:t]);var s=!1,v=!1,u=!1,f=!1,p=r[t+1],g=r[e-1],b=p&&!p.fixed||g&&!g.fixed||r.every(function(w){return w.fixed==="left"});if(o==="rtl"){if(l!==void 0){var m=g&&g.fixed==="left";f=!m&&b}else if(c!==void 0){var y=p&&p.fixed==="right";u=!y&&b}}else if(l!==void 0){var C=p&&p.fixed==="left";s=!C&&b}else if(c!==void 0){var x=g&&g.fixed==="right";v=!x&&b}return{fixLeft:l,fixRight:c,lastFixLeft:s,firstFixRight:v,lastFixRight:u,firstFixLeft:f,isSticky:n.isSticky}}var Io=i.createContext({});function pi(e){var t=e.className,r=e.index,n=e.children,o=e.colSpan,a=o===void 0?1:o,d=e.rowSpan,l=e.align,c=He(Ge,["prefixCls","direction"]),s=c.prefixCls,v=c.direction,u=i.useContext(Io),f=u.scrollColumnIndex,p=u.stickyOffsets,g=u.flattenColumns,b=r+a-1,m=b+1===f?a+1:a,y=Xn(r,r+m-1,g,p,v);return i.createElement(Pt,he({className:t,index:r,component:"td",prefixCls:s,record:null,dataIndex:null,align:l,colSpan:m,rowSpan:d,render:function(){return n}},y))}var mi=["children"];function gi(e){var t=e.children,r=rt(e,mi);return i.createElement("tr",r,t)}function ln(e){var t=e.children;return t}ln.Row=gi;ln.Cell=pi;function hi(e){var t=e.children,r=e.stickyOffsets,n=e.flattenColumns,o=He(Ge,"prefixCls"),a=n.length-1,d=n[a],l=i.useMemo(function(){return{stickyOffsets:r,flattenColumns:n,scrollColumnIndex:d!=null&&d.scrollbar?a:null}},[d,n,a,r]);return i.createElement(Io.Provider,{value:l},i.createElement("tfoot",{className:"".concat(o,"-summary")},t))}const Jt=Tt(hi);var Ko=ln;function yi(e){return null}function bi(e){return null}function Oo(e,t,r,n,o,a,d){e.push({record:t,indent:r,index:d});var l=a(t),c=o==null?void 0:o.has(l);if(t&&Array.isArray(t[n])&&c)for(var s=0;s<t[n].length;s+=1)Oo(e,t[n][s],r+1,n,o,a,s)}function To(e,t,r,n){var o=i.useMemo(function(){if(r!=null&&r.size){for(var a=[],d=0;d<(e==null?void 0:e.length);d+=1){var l=e[d];Oo(a,l,0,t,r,n,d)}return a}return e==null?void 0:e.map(function(c,s){return{record:c,indent:0,index:s}})},[e,t,r,n]);return o}function Po(e,t,r,n){var o=He(Ge,["prefixCls","fixedInfoList","flattenColumns","expandableType","expandRowByClick","onTriggerExpand","rowClassName","expandedRowClassName","indentSize","expandIcon","expandedRowRender","expandIconColumnIndex","expandedKeys","childrenColumnName","rowExpandable","onRow"]),a=o.flattenColumns,d=o.expandableType,l=o.expandedKeys,c=o.childrenColumnName,s=o.onTriggerExpand,v=o.rowExpandable,u=o.onRow,f=o.expandRowByClick,p=o.rowClassName,g=d==="nest",b=d==="row"&&(!v||v(e)),m=b||g,y=l&&l.has(t),C=c&&e&&e[c],x=gt(s),w=u==null?void 0:u(e,r),S=w==null?void 0:w.onClick,E=function(O){f&&m&&s(e,O);for(var T=arguments.length,R=new Array(T>1?T-1:0),K=1;K<T;K++)R[K-1]=arguments[K];S==null||S.apply(void 0,[O].concat(R))},N;typeof p=="string"?N=p:typeof p=="function"&&(N=p(e,r,n));var M=an(a);return F(F({},o),{},{columnsKey:M,nestExpandable:g,expanded:y,hasNestChildren:C,record:e,onTriggerExpand:x,rowSupportExpand:b,expandable:m,rowProps:F(F({},w),{},{className:G(N,w==null?void 0:w.className),onClick:E})})}function Do(e){var t=e.prefixCls,r=e.children,n=e.component,o=e.cellComponent,a=e.className,d=e.expanded,l=e.colSpan,c=e.isEmpty,s=He(Ge,["scrollbarSize","fixHeader","fixColumn","componentWidth","horizonScroll"]),v=s.scrollbarSize,u=s.fixHeader,f=s.fixColumn,p=s.componentWidth,g=s.horizonScroll,b=r;return(c?g&&p:f)&&(b=i.createElement("div",{style:{width:p-(u&&!c?v:0),position:"sticky",left:0,overflow:"hidden"},className:"".concat(t,"-expanded-row-fixed")},b)),i.createElement(n,{className:a,style:{display:d?null:"none"}},i.createElement(Pt,{component:o,prefixCls:t,colSpan:l},b))}function xi(e){var t=e.prefixCls,r=e.record,n=e.onExpand,o=e.expanded,a=e.expandable,d="".concat(t,"-row-expand-icon");if(!a)return i.createElement("span",{className:G(d,"".concat(t,"-row-spaced"))});var l=function(s){n(r,s),s.stopPropagation()};return i.createElement("span",{className:G(d,B(B({},"".concat(t,"-row-expanded"),o),"".concat(t,"-row-collapsed"),!o)),onClick:l})}function Ci(e,t,r){var n=[];function o(a){(a||[]).forEach(function(d,l){n.push(t(d,l)),o(d[r])})}return o(e),n}function Mo(e,t,r,n){return typeof e=="string"?e:typeof e=="function"?e(t,r,n):""}function Bo(e,t,r,n,o){var a=e.record,d=e.prefixCls,l=e.columnsKey,c=e.fixedInfoList,s=e.expandIconColumnIndex,v=e.nestExpandable,u=e.indentSize,f=e.expandIcon,p=e.expanded,g=e.hasNestChildren,b=e.onTriggerExpand,m=l[r],y=c[r],C;r===(s||0)&&v&&(C=i.createElement(i.Fragment,null,i.createElement("span",{style:{paddingLeft:"".concat(u*n,"px")},className:"".concat(d,"-row-indent indent-level-").concat(n)}),f({prefixCls:d,expanded:p,expandable:g,record:a,onExpand:b})));var x;return t.onCell&&(x=t.onCell(a,o)),{key:m,fixedInfo:y,appendCellNode:C,additionalCellProps:x||{}}}function Si(e){var t=e.className,r=e.style,n=e.record,o=e.index,a=e.renderIndex,d=e.rowKey,l=e.indent,c=l===void 0?0:l,s=e.rowComponent,v=e.cellComponent,u=e.scopeCellComponent,f=Po(n,d,o,c),p=f.prefixCls,g=f.flattenColumns,b=f.expandedRowClassName,m=f.expandedRowRender,y=f.rowProps,C=f.expanded,x=f.rowSupportExpand,w=i.useRef(!1);w.current||(w.current=C);var S=Mo(b,n,o,c),E=i.createElement(s,he({},y,{"data-row-key":d,className:G(t,"".concat(p,"-row"),"".concat(p,"-row-level-").concat(c),y==null?void 0:y.className,B({},S,c>=1)),style:F(F({},r),y==null?void 0:y.style)}),g.map(function(h,O){var T=h.render,R=h.dataIndex,K=h.className,k=Bo(f,h,O,c,o),$=k.key,I=k.fixedInfo,P=k.appendCellNode,_=k.additionalCellProps;return i.createElement(Pt,he({className:K,ellipsis:h.ellipsis,align:h.align,scope:h.rowScope,component:h.rowScope?u:v,prefixCls:p,key:$,record:n,index:o,renderIndex:a,dataIndex:R,render:T,shouldCellUpdate:h.shouldCellUpdate},I,{appendNode:P,additionalProps:_}))})),N;if(x&&(w.current||C)){var M=m(n,o,c+1,C);N=i.createElement(Do,{expanded:C,className:G("".concat(p,"-expanded-row"),"".concat(p,"-expanded-row-level-").concat(c+1),S),prefixCls:p,component:s,cellComponent:v,colSpan:g.length,isEmpty:!1},M)}return i.createElement(i.Fragment,null,E,N)}const wi=Tt(Si);function Ei(e){var t=e.columnKey,r=e.onColumnResize,n=i.useRef();return dt(function(){n.current&&r(t,n.current.offsetWidth)},[]),i.createElement(Fn,{data:t},i.createElement("td",{ref:n,style:{padding:0,border:0,height:0}},i.createElement("div",{style:{height:0,overflow:"hidden"}}," ")))}function $i(e){var t=e.prefixCls,r=e.columnsKey,n=e.onColumnResize,o=i.useRef(null);return i.createElement("tr",{"aria-hidden":"true",className:"".concat(t,"-measure-row"),style:{height:0,fontSize:0},ref:o},i.createElement(Fn.Collection,{onBatchResize:function(d){ya(o.current)&&d.forEach(function(l){var c=l.data,s=l.size;n(c,s.offsetWidth)})}},r.map(function(a){return i.createElement(Ei,{key:a,columnKey:a,onColumnResize:n})})))}function ki(e){var t=e.data,r=e.measureColumnWidth,n=He(Ge,["prefixCls","getComponent","onColumnResize","flattenColumns","getRowKey","expandedKeys","childrenColumnName","emptyNode"]),o=n.prefixCls,a=n.getComponent,d=n.onColumnResize,l=n.flattenColumns,c=n.getRowKey,s=n.expandedKeys,v=n.childrenColumnName,u=n.emptyNode,f=To(t,v,s,c),p=i.useRef({renderWithProps:!1}),g=a(["body","wrapper"],"tbody"),b=a(["body","row"],"tr"),m=a(["body","cell"],"td"),y=a(["body","cell"],"th"),C;t.length?C=f.map(function(w,S){var E=w.record,N=w.indent,M=w.index,h=c(E,S);return i.createElement(wi,{key:h,rowKey:h,record:E,index:S,renderIndex:M,rowComponent:b,cellComponent:m,scopeCellComponent:y,indent:N})}):C=i.createElement(Do,{expanded:!0,className:"".concat(o,"-placeholder"),prefixCls:o,component:b,cellComponent:m,colSpan:l.length,isEmpty:!0},u);var x=an(l);return i.createElement(Ro.Provider,{value:p.current},i.createElement(g,{className:"".concat(o,"-tbody")},r&&i.createElement($i,{prefixCls:o,columnsKey:x,onColumnResize:d}),C))}const Ni=Tt(ki);var Ri=["expandable"],Ht="RC_TABLE_INTERNAL_COL_DEFINE";function Ii(e){var t=e.expandable,r=rt(e,Ri),n;return"expandable"in e?n=F(F({},r),t):n=r,n.showExpandColumn===!1&&(n.expandIconColumnIndex=-1),n}var Ki=["columnType"];function Lo(e){for(var t=e.colWidths,r=e.columns,n=e.columCount,o=He(Ge,["tableLayout"]),a=o.tableLayout,d=[],l=n||r.length,c=!1,s=l-1;s>=0;s-=1){var v=t[s],u=r&&r[s],f=void 0,p=void 0;if(u&&(f=u[Ht],a==="auto"&&(p=u.minWidth)),v||p||f||c){var g=f||{};g.columnType;var b=rt(g,Ki);d.unshift(i.createElement("col",he({key:s,style:{width:v,minWidth:p}},b))),c=!0}}return i.createElement("colgroup",null,d)}var Oi=["className","noData","columns","flattenColumns","colWidths","columCount","stickyOffsets","direction","fixHeader","stickyTopOffset","stickyBottomOffset","stickyClassName","onScroll","maxContentScroll","children"];function Ti(e,t){return i.useMemo(function(){for(var r=[],n=0;n<t;n+=1){var o=e[n];if(o!==void 0)r[n]=o;else return null}return r},[e.join("_"),t])}var Pi=i.forwardRef(function(e,t){var r=e.className,n=e.noData,o=e.columns,a=e.flattenColumns,d=e.colWidths,l=e.columCount,c=e.stickyOffsets,s=e.direction,v=e.fixHeader,u=e.stickyTopOffset,f=e.stickyBottomOffset,p=e.stickyClassName,g=e.onScroll,b=e.maxContentScroll,m=e.children,y=rt(e,Oi),C=He(Ge,["prefixCls","scrollbarSize","isSticky","getComponent"]),x=C.prefixCls,w=C.scrollbarSize,S=C.isSticky,E=C.getComponent,N=E(["header","table"],"table"),M=S&&!v?0:w,h=i.useRef(null),O=i.useCallback(function(_){br(t,_),br(h,_)},[]);i.useEffect(function(){var _;function D(V){var Q=V,Y=Q.currentTarget,te=Q.deltaX;te&&(g({currentTarget:Y,scrollLeft:Y.scrollLeft+te}),V.preventDefault())}return(_=h.current)===null||_===void 0||_.addEventListener("wheel",D,{passive:!1}),function(){var V;(V=h.current)===null||V===void 0||V.removeEventListener("wheel",D)}},[]);var T=i.useMemo(function(){return a.every(function(_){return _.width})},[a]),R=a[a.length-1],K={fixed:R?R.fixed:null,scrollbar:!0,onHeaderCell:function(){return{className:"".concat(x,"-cell-scrollbar")}}},k=i.useMemo(function(){return M?[].concat(fe(o),[K]):o},[M,o]),$=i.useMemo(function(){return M?[].concat(fe(a),[K]):a},[M,a]),I=i.useMemo(function(){var _=c.right,D=c.left;return F(F({},c),{},{left:s==="rtl"?[].concat(fe(D.map(function(V){return V+M})),[0]):D,right:s==="rtl"?_:[].concat(fe(_.map(function(V){return V+M})),[0]),isSticky:S})},[M,c,S]),P=Ti(d,l);return i.createElement("div",{style:F({overflow:"hidden"},S?{top:u,bottom:f}:{}),ref:O,className:G(r,B({},p,!!p))},i.createElement(N,{style:{tableLayout:"fixed",visibility:n||P?null:"hidden"}},(!n||!b||T)&&i.createElement(Lo,{colWidths:P?[].concat(fe(P),[M]):[],columCount:l+1,columns:$}),m(F(F({},y),{},{stickyOffsets:I,columns:k,flattenColumns:$}))))});const Ir=i.memo(Pi);var Di=function(t){var r=t.cells,n=t.stickyOffsets,o=t.flattenColumns,a=t.rowComponent,d=t.cellComponent,l=t.onHeaderRow,c=t.index,s=He(Ge,["prefixCls","direction"]),v=s.prefixCls,u=s.direction,f;l&&(f=l(r.map(function(g){return g.column}),c));var p=an(r.map(function(g){return g.column}));return i.createElement(a,f,r.map(function(g,b){var m=g.column,y=Xn(g.colStart,g.colEnd,o,n,u),C;return m&&m.onHeaderCell&&(C=g.column.onHeaderCell(m)),i.createElement(Pt,he({},g,{scope:m.title?g.colSpan>1?"colgroup":"col":null,ellipsis:m.ellipsis,align:m.align,component:d,prefixCls:v,key:p[b]},y,{additionalProps:C,rowType:"header"}))}))};function Mi(e){var t=[];function r(d,l){var c=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0;t[c]=t[c]||[];var s=l,v=d.filter(Boolean).map(function(u){var f={key:u.key,className:u.className||"",children:u.title,column:u,colStart:s},p=1,g=u.children;return g&&g.length>0&&(p=r(g,s,c+1).reduce(function(b,m){return b+m},0),f.hasSubColumns=!0),"colSpan"in u&&(p=u.colSpan),"rowSpan"in u&&(f.rowSpan=u.rowSpan),f.colSpan=p,f.colEnd=f.colStart+p-1,t[c].push(f),s+=p,p});return v}r(e,0);for(var n=t.length,o=function(l){t[l].forEach(function(c){!("rowSpan"in c)&&!c.hasSubColumns&&(c.rowSpan=n-l)})},a=0;a<n;a+=1)o(a);return t}var Bi=function(t){var r=t.stickyOffsets,n=t.columns,o=t.flattenColumns,a=t.onHeaderRow,d=He(Ge,["prefixCls","getComponent"]),l=d.prefixCls,c=d.getComponent,s=i.useMemo(function(){return Mi(n)},[n]),v=c(["header","wrapper"],"thead"),u=c(["header","row"],"tr"),f=c(["header","cell"],"th");return i.createElement(v,{className:"".concat(l,"-thead")},s.map(function(p,g){var b=i.createElement(Di,{key:g,flattenColumns:o,cells:p,stickyOffsets:r,rowComponent:u,cellComponent:f,onHeaderRow:a,index:g});return b}))};const Kr=Tt(Bi);function Or(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"";return typeof t=="number"?t:t.endsWith("%")?e*parseFloat(t)/100:null}function Li(e,t,r){return i.useMemo(function(){if(t&&t>0){var n=0,o=0;e.forEach(function(f){var p=Or(t,f.width);p?n+=p:o+=1});var a=Math.max(t,r),d=Math.max(a-n,o),l=o,c=d/o,s=0,v=e.map(function(f){var p=F({},f),g=Or(t,p.width);if(g)p.width=g;else{var b=Math.floor(c);p.width=l===1?d:b,d-=b,l-=1}return s+=p.width,p});if(s<a){var u=a/s;d=a,v.forEach(function(f,p){var g=Math.floor(f.width*u);f.width=p===v.length-1?d:g,d-=g})}return[v,Math.max(s,a)]}return[e,t]},[e,t,r])}var _i=["children"],zi=["fixed"];function Gn(e){return so(e).filter(function(t){return i.isValidElement(t)}).map(function(t){var r=t.key,n=t.props,o=n.children,a=rt(n,_i),d=F({key:r},a);return o&&(d.children=Gn(o)),d})}function _o(e){return e.filter(function(t){return t&&lt(t)==="object"&&!t.hidden}).map(function(t){var r=t.children;return r&&r.length>0?F(F({},t),{},{children:_o(r)}):t})}function Nn(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"key";return e.filter(function(r){return r&&lt(r)==="object"}).reduce(function(r,n,o){var a=n.fixed,d=a===!0?"left":a,l="".concat(t,"-").concat(o),c=n.children;return c&&c.length>0?[].concat(fe(r),fe(Nn(c,l).map(function(s){return F({fixed:d},s)}))):[].concat(fe(r),[F(F({key:l},n),{},{fixed:d})])},[])}function Hi(e){return e.map(function(t){var r=t.fixed,n=rt(t,zi),o=r;return r==="left"?o="right":r==="right"&&(o="left"),F({fixed:o},n)})}function Fi(e,t){var r=e.prefixCls,n=e.columns,o=e.children,a=e.expandable,d=e.expandedKeys,l=e.columnTitle,c=e.getRowKey,s=e.onTriggerExpand,v=e.expandIcon,u=e.rowExpandable,f=e.expandIconColumnIndex,p=e.direction,g=e.expandRowByClick,b=e.columnWidth,m=e.fixed,y=e.scrollWidth,C=e.clientWidth,x=i.useMemo(function(){var R=n||Gn(o)||[];return _o(R.slice())},[n,o]),w=i.useMemo(function(){if(a){var R=x.slice();if(!R.includes(ct)){var K=f||0;K>=0&&(K||m==="left"||!m)&&R.splice(K,0,ct),m==="right"&&R.splice(x.length,0,ct)}var k=R.indexOf(ct);R=R.filter(function(_,D){return _!==ct||D===k});var $=x[k],I;m?I=m:I=$?$.fixed:null;var P=B(B(B(B(B(B({},Ht,{className:"".concat(r,"-expand-icon-col"),columnType:"EXPAND_COLUMN"}),"title",l),"fixed",I),"className","".concat(r,"-row-expand-icon-cell")),"width",b),"render",function(D,V,Q){var Y=c(V,Q),te=d.has(Y),ye=u?u(V):!0,Ce=v({prefixCls:r,expanded:te,expandable:ye,record:V,onExpand:s});return g?i.createElement("span",{onClick:function(J){return J.stopPropagation()}},Ce):Ce});return R.map(function(_){return _===ct?P:_})}return x.filter(function(_){return _!==ct})},[a,x,c,d,v,p]),S=i.useMemo(function(){var R=w;return t&&(R=t(R)),R.length||(R=[{render:function(){return null}}]),R},[t,w,p]),E=i.useMemo(function(){return p==="rtl"?Hi(Nn(S)):Nn(S)},[S,p,y]),N=i.useMemo(function(){for(var R=-1,K=E.length-1;K>=0;K-=1){var k=E[K].fixed;if(k==="left"||k===!0){R=K;break}}if(R>=0)for(var $=0;$<=R;$+=1){var I=E[$].fixed;if(I!=="left"&&I!==!0)return!0}var P=E.findIndex(function(V){var Q=V.fixed;return Q==="right"});if(P>=0)for(var _=P;_<E.length;_+=1){var D=E[_].fixed;if(D!=="right")return!0}return!1},[E]),M=Li(E,y,C),h=Se(M,2),O=h[0],T=h[1];return[S,O,T,N]}function Ai(e,t,r){var n=Ii(e),o=n.expandIcon,a=n.expandedRowKeys,d=n.defaultExpandedRowKeys,l=n.defaultExpandAllRows,c=n.expandedRowRender,s=n.onExpand,v=n.onExpandedRowsChange,u=n.childrenColumnName,f=o||xi,p=u||"children",g=i.useMemo(function(){return c?"row":e.expandable&&e.internalHooks===qt&&e.expandable.__PARENT_RENDER_ICON__||t.some(function(S){return S&&lt(S)==="object"&&S[p]})?"nest":!1},[!!c,t]),b=i.useState(function(){return d||(l?Ci(t,r,p):[])}),m=Se(b,2),y=m[0],C=m[1],x=i.useMemo(function(){return new Set(a||y||[])},[a,y]),w=i.useCallback(function(S){var E=r(S,t.indexOf(S)),N,M=x.has(E);M?(x.delete(E),N=fe(x)):N=[].concat(fe(x),[E]),C(N),s&&s(!M,S),v&&v(N)},[r,x,t,s,v]);return[n,g,x,f,p,w]}function ji(e,t,r){var n=e.map(function(o,a){return Xn(a,a,e,t,r)});return co(function(){return n},[n],function(o,a){return!At(o,a)})}function Wi(e){var t=i.useRef(e),r=i.useState({}),n=Se(r,2),o=n[1],a=i.useRef(null),d=i.useRef([]);function l(c){d.current.push(c);var s=Promise.resolve();a.current=s,s.then(function(){if(a.current===s){var v=d.current,u=t.current;d.current=[],v.forEach(function(f){t.current=f(t.current)}),a.current=null,u!==t.current&&o({})}})}return i.useEffect(function(){return function(){a.current=null}},[]),[t.current,l]}function Vi(e){var t=i.useRef(null),r=i.useRef();function n(){window.clearTimeout(r.current)}function o(d){t.current=d,n(),r.current=window.setTimeout(function(){t.current=null,r.current=void 0},100)}function a(){return t.current}return i.useEffect(function(){return n},[]),[o,a]}function qi(){var e=i.useState(-1),t=Se(e,2),r=t[0],n=t[1],o=i.useState(-1),a=Se(o,2),d=a[0],l=a[1],c=i.useCallback(function(s,v){n(s),l(v)},[]);return[r,d,c]}var Tr=oo()?window:null;function Xi(e,t){var r=lt(e)==="object"?e:{},n=r.offsetHeader,o=n===void 0?0:n,a=r.offsetSummary,d=a===void 0?0:a,l=r.offsetScroll,c=l===void 0?0:l,s=r.getContainer,v=s===void 0?function(){return Tr}:s,u=v()||Tr,f=!!e;return i.useMemo(function(){return{isSticky:f,stickyClassName:f?"".concat(t,"-sticky-holder"):"",offsetHeader:o,offsetSummary:d,offsetScroll:c,container:u}},[f,c,o,d,t,u])}function Gi(e,t,r){var n=i.useMemo(function(){var o=t.length,a=function(s,v,u){for(var f=[],p=0,g=s;g!==v;g+=u)f.push(p),t[g].fixed&&(p+=e[g]||0);return f},d=a(0,o,1),l=a(o-1,-1,-1).reverse();return r==="rtl"?{left:l,right:d}:{left:d,right:l}},[e,t,r]);return n}function Pr(e){var t=e.className,r=e.children;return i.createElement("div",{className:t},r)}function Dr(e){var t=An(e),r=t.getBoundingClientRect(),n=document.documentElement;return{left:r.left+(window.pageXOffset||n.scrollLeft)-(n.clientLeft||document.body.clientLeft||0),top:r.top+(window.pageYOffset||n.scrollTop)-(n.clientTop||document.body.clientTop||0)}}var Ui=function(t,r){var n,o,a=t.scrollBodyRef,d=t.onScroll,l=t.offsetScroll,c=t.container,s=t.direction,v=He(Ge,"prefixCls"),u=((n=a.current)===null||n===void 0?void 0:n.scrollWidth)||0,f=((o=a.current)===null||o===void 0?void 0:o.clientWidth)||0,p=u&&f*(f/u),g=i.useRef(),b=Wi({scrollLeft:0,isHiddenScrollBar:!0}),m=Se(b,2),y=m[0],C=m[1],x=i.useRef({delta:0,x:0}),w=i.useState(!1),S=Se(w,2),E=S[0],N=S[1],M=i.useRef(null);i.useEffect(function(){return function(){_t.cancel(M.current)}},[]);var h=function(){N(!1)},O=function($){$.persist(),x.current.delta=$.pageX-y.scrollLeft,x.current.x=0,N(!0),$.preventDefault()},T=function($){var I,P=$||((I=window)===null||I===void 0?void 0:I.event),_=P.buttons;if(!E||_===0){E&&N(!1);return}var D=x.current.x+$.pageX-x.current.x-x.current.delta,V=s==="rtl";D=Math.max(V?p-f:0,Math.min(V?0:f-p,D));var Q=!V||Math.abs(D)+Math.abs(p)<f;Q&&(d({scrollLeft:D/f*(u+2)}),x.current.x=$.pageX)},R=function(){_t.cancel(M.current),M.current=_t(function(){if(a.current){var $=Dr(a.current).top,I=$+a.current.offsetHeight,P=c===window?document.documentElement.scrollTop+window.innerHeight:Dr(c).top+c.clientHeight;I-xr()<=P||$>=P-l?C(function(_){return F(F({},_),{},{isHiddenScrollBar:!0})}):C(function(_){return F(F({},_),{},{isHiddenScrollBar:!1})})}})},K=function($){C(function(I){return F(F({},I),{},{scrollLeft:$/u*f||0})})};return i.useImperativeHandle(r,function(){return{setScrollLeft:K,checkScrollBarVisible:R}}),i.useEffect(function(){var k=$r(document.body,"mouseup",h,!1),$=$r(document.body,"mousemove",T,!1);return R(),function(){k.remove(),$.remove()}},[p,E]),i.useEffect(function(){if(a.current){for(var k=[],$=An(a.current);$;)k.push($),$=$.parentElement;return k.forEach(function(I){return I.addEventListener("scroll",R,!1)}),window.addEventListener("resize",R,!1),window.addEventListener("scroll",R,!1),c.addEventListener("scroll",R,!1),function(){k.forEach(function(I){return I.removeEventListener("scroll",R)}),window.removeEventListener("resize",R),window.removeEventListener("scroll",R),c.removeEventListener("scroll",R)}}},[c]),i.useEffect(function(){y.isHiddenScrollBar||C(function(k){var $=a.current;return $?F(F({},k),{},{scrollLeft:$.scrollLeft/$.scrollWidth*$.clientWidth}):k})},[y.isHiddenScrollBar]),u<=f||!p||y.isHiddenScrollBar?null:i.createElement("div",{style:{height:xr(),width:f,bottom:l},className:"".concat(v,"-sticky-scroll")},i.createElement("div",{onMouseDown:O,ref:g,className:G("".concat(v,"-sticky-scroll-bar"),B({},"".concat(v,"-sticky-scroll-bar-active"),E)),style:{width:"".concat(p,"px"),transform:"translate3d(".concat(y.scrollLeft,"px, 0, 0)")}}))};const Yi=i.forwardRef(Ui);var zo="rc-table",Ji=[],Qi={};function Zi(){return"No Data"}function es(e,t){var r=F({rowKey:"key",prefixCls:zo,emptyText:Zi},e),n=r.prefixCls,o=r.className,a=r.rowClassName,d=r.style,l=r.data,c=r.rowKey,s=r.scroll,v=r.tableLayout,u=r.direction,f=r.title,p=r.footer,g=r.summary,b=r.caption,m=r.id,y=r.showHeader,C=r.components,x=r.emptyText,w=r.onRow,S=r.onHeaderRow,E=r.onScroll,N=r.internalHooks,M=r.transformColumns,h=r.internalRefs,O=r.tailor,T=r.getContainerWidth,R=r.sticky,K=r.rowHoverable,k=K===void 0?!0:K,$=l||Ji,I=!!$.length,P=N===qt,_=i.useCallback(function(se,ue){return Hn(C,se)||ue},[C]),D=i.useMemo(function(){return typeof c=="function"?c:function(se){var ue=se&&se[c];return ue}},[c]),V=_(["body"]),Q=qi(),Y=Se(Q,3),te=Y[0],ye=Y[1],Ce=Y[2],we=Ai(r,$,D),J=Se(we,6),ee=J[0],Ee=J[1],ve=J[2],q=J[3],X=J[4],H=J[5],j=s==null?void 0:s.x,Z=i.useState(0),le=Se(Z,2),pe=le[0],ke=le[1],Fe=Fi(F(F(F({},r),ee),{},{expandable:!!ee.expandedRowRender,columnTitle:ee.columnTitle,expandedKeys:ve,getRowKey:D,onTriggerExpand:H,expandIcon:q,expandIconColumnIndex:ee.expandIconColumnIndex,direction:u,scrollWidth:P&&O&&typeof j=="number"?j:null,clientWidth:pe}),P?M:null),Re=Se(Fe,4),L=Re[0],A=Re[1],re=Re[2],be=Re[3],ie=re??j,Ne=i.useMemo(function(){return{columns:L,flattenColumns:A}},[L,A]),Te=i.useRef(),Le=i.useRef(),me=i.useRef(),ce=i.useRef();i.useImperativeHandle(t,function(){return{nativeElement:Te.current,scrollTo:function(ue){var _e;if(me.current instanceof HTMLElement){var tt=ue.index,ze=ue.top,Rt=ue.key;if(ii(ze)){var xt;(xt=me.current)===null||xt===void 0||xt.scrollTo({top:ze})}else{var Ct,Bt=Rt??D($[tt]);(Ct=me.current.querySelector('[data-row-key="'.concat(Bt,'"]')))===null||Ct===void 0||Ct.scrollIntoView()}}else(_e=me.current)!==null&&_e!==void 0&&_e.scrollTo&&me.current.scrollTo(ue)}}});var U=i.useRef(),z=i.useState(!1),$e=Se(z,2),Ie=$e[0],ne=$e[1],Pe=i.useState(!1),xe=Se(Pe,2),De=xe[0],Ue=xe[1],Ve=i.useState(new Map),ut=Se(Ve,2),Ke=ut[0],$t=ut[1],kt=an(A),qe=kt.map(function(se){return Ke.get(se)}),nt=i.useMemo(function(){return qe},[qe.join("_")]),Qe=Gi(nt,A,u),Xe=s&&kn(s.y),Ae=s&&kn(ie)||!!ee.fixed,Ye=Ae&&A.some(function(se){var ue=se.fixed;return ue}),bt=i.useRef(),ft=Xi(R,n),ot=ft.isSticky,cn=ft.offsetHeader,dn=ft.offsetSummary,Xt=ft.offsetScroll,un=ft.stickyClassName,oe=ft.container,ae=i.useMemo(function(){return g==null?void 0:g($)},[g,$]),Oe=(Xe||ot)&&i.isValidElement(ae)&&ae.type===ln&&ae.props.fixed,Be,je,Ze;Xe&&(je={overflowY:I?"scroll":"auto",maxHeight:s.y}),Ae&&(Be={overflowX:"auto"},Xe||(je={overflowY:"hidden"}),Ze={width:ie===!0?"auto":ie,minWidth:"100%"});var et=i.useCallback(function(se,ue){$t(function(_e){if(_e.get(se)!==ue){var tt=new Map(_e);return tt.set(se,ue),tt}return _e})},[]),We=Vi(),tr=Se(We,2),ea=tr[0],nr=tr[1];function Gt(se,ue){ue&&(typeof ue=="function"?ue(se):ue.scrollLeft!==se&&(ue.scrollLeft=se,ue.scrollLeft!==se&&setTimeout(function(){ue.scrollLeft=se},0)))}var Nt=gt(function(se){var ue=se.currentTarget,_e=se.scrollLeft,tt=u==="rtl",ze=typeof _e=="number"?_e:ue.scrollLeft,Rt=ue||Qi;if(!nr()||nr()===Rt){var xt;ea(Rt),Gt(ze,Le.current),Gt(ze,me.current),Gt(ze,U.current),Gt(ze,(xt=bt.current)===null||xt===void 0?void 0:xt.setScrollLeft)}var Ct=ue||Le.current;if(Ct){var Bt=P&&O&&typeof ie=="number"?ie:Ct.scrollWidth,gn=Ct.clientWidth;if(Bt===gn){ne(!1),Ue(!1);return}tt?(ne(-ze<Bt-gn),Ue(-ze>0)):(ne(ze>0),Ue(ze<Bt-gn))}}),ta=gt(function(se){Nt(se),E==null||E(se)}),rr=function(){if(Ae&&me.current){var ue;Nt({currentTarget:An(me.current),scrollLeft:(ue=me.current)===null||ue===void 0?void 0:ue.scrollLeft})}else ne(!1),Ue(!1)},na=function(ue){var _e,tt=ue.width;(_e=bt.current)===null||_e===void 0||_e.checkScrollBarVisible();var ze=Te.current?Te.current.offsetWidth:tt;P&&T&&Te.current&&(ze=T(Te.current,ze)||ze),ze!==pe&&(rr(),ke(ze))},or=i.useRef(!1);i.useEffect(function(){or.current&&rr()},[Ae,l,L.length]),i.useEffect(function(){or.current=!0},[]);var ra=i.useState(0),ar=Se(ra,2),Ut=ar[0],lr=ar[1],oa=i.useState(!0),ir=Se(oa,2),sr=ir[0],aa=ir[1];dt(function(){(!O||!P)&&(me.current instanceof Element?lr(Cr(me.current).width):lr(Cr(ce.current).width)),aa(Ga("position","sticky"))},[]),i.useEffect(function(){P&&h&&(h.body.current=me.current)});var la=i.useCallback(function(se){return i.createElement(i.Fragment,null,i.createElement(Kr,se),Oe==="top"&&i.createElement(Jt,se,ae))},[Oe,ae]),ia=i.useCallback(function(se){return i.createElement(Jt,se,ae)},[ae]),cr=_(["table"],"table"),Yt=i.useMemo(function(){return v||(Ye?ie==="max-content"?"auto":"fixed":Xe||ot||A.some(function(se){var ue=se.ellipsis;return ue})?"fixed":"auto")},[Xe,Ye,A,v,ot]),fn,vn={colWidths:nt,columCount:A.length,stickyOffsets:Qe,onHeaderRow:S,fixHeader:Xe,scroll:s},dr=i.useMemo(function(){return I?null:typeof x=="function"?x():x},[I,x]),ur=i.createElement(Ni,{data:$,measureColumnWidth:Xe||Ae||ot}),fr=i.createElement(Lo,{colWidths:A.map(function(se){var ue=se.width;return ue}),columns:A}),vr=b!=null?i.createElement("caption",{className:"".concat(n,"-caption")},b):void 0,sa=Ft(r,{data:!0}),pr=Ft(r,{aria:!0});if(Xe||ot){var pn;typeof V=="function"?(pn=V($,{scrollbarSize:Ut,ref:me,onScroll:Nt}),vn.colWidths=A.map(function(se,ue){var _e=se.width,tt=ue===A.length-1?_e-Ut:_e;return typeof tt=="number"&&!Number.isNaN(tt)?tt:0})):pn=i.createElement("div",{style:F(F({},Be),je),onScroll:ta,ref:me,className:G("".concat(n,"-body"))},i.createElement(cr,he({style:F(F({},Ze),{},{tableLayout:Yt})},pr),vr,fr,ur,!Oe&&ae&&i.createElement(Jt,{stickyOffsets:Qe,flattenColumns:A},ae)));var mr=F(F(F({noData:!$.length,maxContentScroll:Ae&&ie==="max-content"},vn),Ne),{},{direction:u,stickyClassName:un,onScroll:Nt});fn=i.createElement(i.Fragment,null,y!==!1&&i.createElement(Ir,he({},mr,{stickyTopOffset:cn,className:"".concat(n,"-header"),ref:Le}),la),pn,Oe&&Oe!=="top"&&i.createElement(Ir,he({},mr,{stickyBottomOffset:dn,className:"".concat(n,"-summary"),ref:U}),ia),ot&&me.current&&me.current instanceof Element&&i.createElement(Yi,{ref:bt,offsetScroll:Xt,scrollBodyRef:me,onScroll:Nt,container:oe,direction:u}))}else fn=i.createElement("div",{style:F(F({},Be),je),className:G("".concat(n,"-content")),onScroll:Nt,ref:me},i.createElement(cr,he({style:F(F({},Ze),{},{tableLayout:Yt})},pr),vr,fr,y!==!1&&i.createElement(Kr,he({},vn,Ne)),ur,ae&&i.createElement(Jt,{stickyOffsets:Qe,flattenColumns:A},ae)));var mn=i.createElement("div",he({className:G(n,o,B(B(B(B(B(B(B(B(B(B({},"".concat(n,"-rtl"),u==="rtl"),"".concat(n,"-ping-left"),Ie),"".concat(n,"-ping-right"),De),"".concat(n,"-layout-fixed"),v==="fixed"),"".concat(n,"-fixed-header"),Xe),"".concat(n,"-fixed-column"),Ye),"".concat(n,"-fixed-column-gapped"),Ye&&be),"".concat(n,"-scroll-horizontal"),Ae),"".concat(n,"-has-fix-left"),A[0]&&A[0].fixed),"".concat(n,"-has-fix-right"),A[A.length-1]&&A[A.length-1].fixed==="right")),style:d,id:m,ref:Te},sa),f&&i.createElement(Pr,{className:"".concat(n,"-title")},f($)),i.createElement("div",{ref:ce,className:"".concat(n,"-container")},fn),p&&i.createElement(Pr,{className:"".concat(n,"-footer")},p($)));Ae&&(mn=i.createElement(Fn,{onResize:na},mn));var gr=ji(A,Qe,u),ca=i.useMemo(function(){return{scrollX:ie,prefixCls:n,getComponent:_,scrollbarSize:Ut,direction:u,fixedInfoList:gr,isSticky:ot,supportSticky:sr,componentWidth:pe,fixHeader:Xe,fixColumn:Ye,horizonScroll:Ae,tableLayout:Yt,rowClassName:a,expandedRowClassName:ee.expandedRowClassName,expandIcon:q,expandableType:Ee,expandRowByClick:ee.expandRowByClick,expandedRowRender:ee.expandedRowRender,onTriggerExpand:H,expandIconColumnIndex:ee.expandIconColumnIndex,indentSize:ee.indentSize,allColumnsFixedLeft:A.every(function(se){return se.fixed==="left"}),emptyNode:dr,columns:L,flattenColumns:A,onColumnResize:et,hoverStartRow:te,hoverEndRow:ye,onHover:Ce,rowExpandable:ee.rowExpandable,onRow:w,getRowKey:D,expandedKeys:ve,childrenColumnName:X,rowHoverable:k}},[ie,n,_,Ut,u,gr,ot,sr,pe,Xe,Ye,Ae,Yt,a,ee.expandedRowClassName,q,Ee,ee.expandRowByClick,ee.expandedRowRender,H,ee.expandIconColumnIndex,ee.indentSize,dr,L,A,et,te,ye,Ce,ee.rowExpandable,w,D,ve,X,k]);return i.createElement(Ge.Provider,{value:ca},mn)}var ts=i.forwardRef(es);function Ho(e){return No(ts,e)}var Dt=Ho();Dt.EXPAND_COLUMN=ct;Dt.INTERNAL_HOOKS=qt;Dt.Column=yi;Dt.ColumnGroup=bi;Dt.Summary=Ko;var Un=Vn(null),Fo=Vn(null);function ns(e,t,r){var n=t||1;return r[e+n]-(r[e]||0)}function rs(e){var t=e.rowInfo,r=e.column,n=e.colIndex,o=e.indent,a=e.index,d=e.component,l=e.renderIndex,c=e.record,s=e.style,v=e.className,u=e.inverse,f=e.getHeight,p=r.render,g=r.dataIndex,b=r.className,m=r.width,y=He(Fo,["columnsOffset"]),C=y.columnsOffset,x=Bo(t,r,n,o,a),w=x.key,S=x.fixedInfo,E=x.appendCellNode,N=x.additionalCellProps,M=N.style,h=N.colSpan,O=h===void 0?1:h,T=N.rowSpan,R=T===void 0?1:T,K=n-1,k=ns(K,O,C),$=O>1?m-k:0,I=F(F(F({},M),s),{},{flex:"0 0 ".concat(k,"px"),width:"".concat(k,"px"),marginRight:$,pointerEvents:"auto"}),P=i.useMemo(function(){return u?R<=1:O===0||R===0||R>1},[R,O,u]);P?I.visibility="hidden":u&&(I.height=f==null?void 0:f(R));var _=P?function(){return null}:p,D={};return(R===0||O===0)&&(D.rowSpan=1,D.colSpan=1),i.createElement(Pt,he({className:G(b,v),ellipsis:r.ellipsis,align:r.align,scope:r.rowScope,component:d,prefixCls:t.prefixCls,key:w,record:c,index:a,renderIndex:l,dataIndex:g,render:_,shouldCellUpdate:r.shouldCellUpdate},S,{appendNode:E,additionalProps:F(F({},N),{},{style:I},D)}))}var os=["data","index","className","rowKey","style","extra","getHeight"],as=i.forwardRef(function(e,t){var r=e.data,n=e.index,o=e.className,a=e.rowKey,d=e.style,l=e.extra,c=e.getHeight,s=rt(e,os),v=r.record,u=r.indent,f=r.index,p=He(Ge,["prefixCls","flattenColumns","fixColumn","componentWidth","scrollX"]),g=p.scrollX,b=p.flattenColumns,m=p.prefixCls,y=p.fixColumn,C=p.componentWidth,x=He(Un,["getComponent"]),w=x.getComponent,S=Po(v,a,n,u),E=w(["body","row"],"div"),N=w(["body","cell"],"div"),M=S.rowSupportExpand,h=S.expanded,O=S.rowProps,T=S.expandedRowRender,R=S.expandedRowClassName,K;if(M&&h){var k=T(v,n,u+1,h),$=Mo(R,v,n,u),I={};y&&(I={style:B({},"--virtual-width","".concat(C,"px"))});var P="".concat(m,"-expanded-row-cell");K=i.createElement(E,{className:G("".concat(m,"-expanded-row"),"".concat(m,"-expanded-row-level-").concat(u+1),$)},i.createElement(Pt,{component:N,prefixCls:m,className:G(P,B({},"".concat(P,"-fixed"),y)),additionalProps:I},k))}var _=F(F({},d),{},{width:g});l&&(_.position="absolute",_.pointerEvents="none");var D=i.createElement(E,he({},O,s,{"data-row-key":a,ref:M?null:t,className:G(o,"".concat(m,"-row"),O==null?void 0:O.className,B({},"".concat(m,"-row-extra"),l)),style:F(F({},_),O==null?void 0:O.style)}),b.map(function(V,Q){return i.createElement(rs,{key:Q,component:N,rowInfo:S,column:V,colIndex:Q,indent:u,index:n,renderIndex:f,record:v,inverse:l,getHeight:c})}));return M?i.createElement("div",{ref:t},D,K):D}),Mr=Tt(as),ls=i.forwardRef(function(e,t){var r=e.data,n=e.onScroll,o=He(Ge,["flattenColumns","onColumnResize","getRowKey","prefixCls","expandedKeys","childrenColumnName","scrollX","direction"]),a=o.flattenColumns,d=o.onColumnResize,l=o.getRowKey,c=o.expandedKeys,s=o.prefixCls,v=o.childrenColumnName,u=o.scrollX,f=o.direction,p=He(Un),g=p.sticky,b=p.scrollY,m=p.listItemHeight,y=p.getComponent,C=p.onScroll,x=i.useRef(),w=To(r,v,c,l),S=i.useMemo(function(){var K=0;return a.map(function(k){var $=k.width,I=k.key;return K+=$,[I,$,K]})},[a]),E=i.useMemo(function(){return S.map(function(K){return K[2]})},[S]);i.useEffect(function(){S.forEach(function(K){var k=Se(K,2),$=k[0],I=k[1];d($,I)})},[S]),i.useImperativeHandle(t,function(){var K,k={scrollTo:function(I){var P;(P=x.current)===null||P===void 0||P.scrollTo(I)},nativeElement:(K=x.current)===null||K===void 0?void 0:K.nativeElement};return Object.defineProperty(k,"scrollLeft",{get:function(){var I;return((I=x.current)===null||I===void 0?void 0:I.getScrollInfo().x)||0},set:function(I){var P;(P=x.current)===null||P===void 0||P.scrollTo({left:I})}}),k});var N=function(k,$){var I,P=(I=w[$])===null||I===void 0?void 0:I.record,_=k.onCell;if(_){var D,V=_(P,$);return(D=V==null?void 0:V.rowSpan)!==null&&D!==void 0?D:1}return 1},M=function(k){var $=k.start,I=k.end,P=k.getSize,_=k.offsetY;if(I<0)return null;for(var D=a.filter(function(q){return N(q,$)===0}),V=$,Q=function(X){if(D=D.filter(function(H){return N(H,X)===0}),!D.length)return V=X,1},Y=$;Y>=0&&!Q(Y);Y-=1);for(var te=a.filter(function(q){return N(q,I)!==1}),ye=I,Ce=function(X){if(te=te.filter(function(H){return N(H,X)!==1}),!te.length)return ye=Math.max(X-1,I),1},we=I;we<w.length&&!Ce(we);we+=1);for(var J=[],ee=function(X){var H=w[X];if(!H)return 1;a.some(function(j){return N(j,X)>1})&&J.push(X)},Ee=V;Ee<=ye;Ee+=1)ee(Ee);var ve=J.map(function(q){var X=w[q],H=l(X.record,q),j=function(pe){var ke=q+pe-1,Fe=l(w[ke].record,ke),Re=P(H,Fe);return Re.bottom-Re.top},Z=P(H);return i.createElement(Mr,{key:q,data:X,rowKey:H,index:q,style:{top:-_+Z.top},extra:!0,getHeight:j})});return ve},h=i.useMemo(function(){return{columnsOffset:E}},[E]),O="".concat(s,"-tbody"),T=y(["body","wrapper"]),R={};return g&&(R.position="sticky",R.bottom=0,lt(g)==="object"&&g.offsetScroll&&(R.bottom=g.offsetScroll)),i.createElement(Fo.Provider,{value:h},i.createElement(po,{fullHeight:!1,ref:x,prefixCls:"".concat(O,"-virtual"),styles:{horizontalScrollBar:R},className:O,height:b,itemHeight:m||24,data:w,itemKey:function(k){return l(k.record)},component:T,scrollWidth:u,direction:f,onVirtualScroll:function(k){var $,I=k.x;n({currentTarget:($=x.current)===null||$===void 0?void 0:$.nativeElement,scrollLeft:I})},onScroll:C,extraRender:M},function(K,k,$){var I=l(K.record,k);return i.createElement(Mr,{data:K,rowKey:I,index:k,style:$.style})}))}),is=Tt(ls),ss=function(t,r){var n=r.ref,o=r.onScroll;return i.createElement(is,{ref:n,data:t,onScroll:o})};function cs(e,t){var r=e.data,n=e.columns,o=e.scroll,a=e.sticky,d=e.prefixCls,l=d===void 0?zo:d,c=e.className,s=e.listItemHeight,v=e.components,u=e.onScroll,f=o||{},p=f.x,g=f.y;typeof p!="number"&&(p=1),typeof g!="number"&&(g=500);var b=gt(function(C,x){return Hn(v,C)||x}),m=gt(u),y=i.useMemo(function(){return{sticky:a,scrollY:g,listItemHeight:s,getComponent:b,onScroll:m}},[a,g,s,b,m]);return i.createElement(Un.Provider,{value:y},i.createElement(Dt,he({},e,{className:G(c,"".concat(l,"-virtual")),scroll:F(F({},o),{},{x:p}),components:F(F({},v),{},{body:r!=null&&r.length?ss:void 0}),columns:n,internalHooks:qt,tailor:!0,ref:t})))}var ds=i.forwardRef(cs);function Ao(e){return No(ds,e)}Ao();const us=e=>null,fs=e=>null;var Yn=i.createContext(null),vs=i.createContext({}),ps=function(t){for(var r=t.prefixCls,n=t.level,o=t.isStart,a=t.isEnd,d="".concat(r,"-indent-unit"),l=[],c=0;c<n;c+=1)l.push(i.createElement("span",{key:c,className:G(d,B(B({},"".concat(d,"-start"),o[c]),"".concat(d,"-end"),a[c]))}));return i.createElement("span",{"aria-hidden":"true",className:"".concat(r,"-indent")},l)};const ms=i.memo(ps);var gs=["eventKey","className","style","dragOver","dragOverGapTop","dragOverGapBottom","isLeaf","isStart","isEnd","expanded","selected","checked","halfChecked","loading","domRef","active","data","onMouseMove","selectable"],Br="open",Lr="close",hs="---",jt=function(t){var r,n,o,a=t.eventKey,d=t.className,l=t.style,c=t.dragOver,s=t.dragOverGapTop,v=t.dragOverGapBottom,u=t.isLeaf,f=t.isStart,p=t.isEnd,g=t.expanded,b=t.selected,m=t.checked,y=t.halfChecked,C=t.loading,x=t.domRef,w=t.active,S=t.data,E=t.onMouseMove,N=t.selectable,M=rt(t,gs),h=ge.useContext(Yn),O=ge.useContext(vs),T=ge.useRef(null),R=ge.useState(!1),K=Se(R,2),k=K[0],$=K[1],I=!!(h.disabled||t.disabled||(r=O.nodeDisabled)!==null&&r!==void 0&&r.call(O,S)),P=ge.useMemo(function(){return!h.checkable||t.checkable===!1?!1:h.checkable},[h.checkable,t.checkable]),_=function(z){I||h.onNodeSelect(z,Me(t))},D=function(z){I||!P||t.disableCheckbox||h.onNodeCheck(z,Me(t),!m)},V=ge.useMemo(function(){return typeof N=="boolean"?N:h.selectable},[N,h.selectable]),Q=function(z){h.onNodeClick(z,Me(t)),V?_(z):D(z)},Y=function(z){h.onNodeDoubleClick(z,Me(t))},te=function(z){h.onNodeMouseEnter(z,Me(t))},ye=function(z){h.onNodeMouseLeave(z,Me(t))},Ce=function(z){h.onNodeContextMenu(z,Me(t))},we=ge.useMemo(function(){return!!(h.draggable&&(!h.draggable.nodeDraggable||h.draggable.nodeDraggable(S)))},[h.draggable,S]),J=function(z){z.stopPropagation(),$(!0),h.onNodeDragStart(z,t);try{z.dataTransfer.setData("text/plain","")}catch{}},ee=function(z){z.preventDefault(),z.stopPropagation(),h.onNodeDragEnter(z,t)},Ee=function(z){z.preventDefault(),z.stopPropagation(),h.onNodeDragOver(z,t)},ve=function(z){z.stopPropagation(),h.onNodeDragLeave(z,t)},q=function(z){z.stopPropagation(),$(!1),h.onNodeDragEnd(z,t)},X=function(z){z.preventDefault(),z.stopPropagation(),$(!1),h.onNodeDrop(z,t)},H=function(z){C||h.onNodeExpand(z,Me(t))},j=ge.useMemo(function(){var U=Je(h.keyEntities,a)||{},z=U.children;return!!(z||[]).length},[h.keyEntities,a]),Z=ge.useMemo(function(){return u===!1?!1:u||!h.loadData&&!j||h.loadData&&t.loaded&&!j},[u,h.loadData,j,t.loaded]);ge.useEffect(function(){C||typeof h.loadData=="function"&&g&&!Z&&!t.loaded&&h.onNodeLoad(Me(t))},[C,h.loadData,h.onNodeLoad,g,Z,t]);var le=ge.useMemo(function(){var U;return(U=h.draggable)!==null&&U!==void 0&&U.icon?ge.createElement("span",{className:"".concat(h.prefixCls,"-draggable-icon")},h.draggable.icon):null},[h.draggable]),pe=function(z){var $e=t.switcherIcon||h.switcherIcon;return typeof $e=="function"?$e(F(F({},t),{},{isLeaf:z})):$e},ke=function(){if(Z){var z=pe(!0);return z!==!1?ge.createElement("span",{className:G("".concat(h.prefixCls,"-switcher"),"".concat(h.prefixCls,"-switcher-noop"))},z):null}var $e=pe(!1);return $e!==!1?ge.createElement("span",{onClick:H,className:G("".concat(h.prefixCls,"-switcher"),"".concat(h.prefixCls,"-switcher_").concat(g?Br:Lr))},$e):null},Fe=ge.useMemo(function(){if(!P)return null;var U=typeof P!="boolean"?P:null;return ge.createElement("span",{className:G("".concat(h.prefixCls,"-checkbox"),B(B(B({},"".concat(h.prefixCls,"-checkbox-checked"),m),"".concat(h.prefixCls,"-checkbox-indeterminate"),!m&&y),"".concat(h.prefixCls,"-checkbox-disabled"),I||t.disableCheckbox)),onClick:D,role:"checkbox","aria-checked":y?"mixed":m,"aria-disabled":I||t.disableCheckbox,"aria-label":"Select ".concat(typeof t.title=="string"?t.title:"tree node")},U)},[P,m,y,I,t.disableCheckbox,t.title]),Re=ge.useMemo(function(){return Z?null:g?Br:Lr},[Z,g]),L=ge.useMemo(function(){return ge.createElement("span",{className:G("".concat(h.prefixCls,"-iconEle"),"".concat(h.prefixCls,"-icon__").concat(Re||"docu"),B({},"".concat(h.prefixCls,"-icon_loading"),C))})},[h.prefixCls,Re,C]),A=ge.useMemo(function(){var U=!!h.draggable,z=!t.disabled&&U&&h.dragOverNodeKey===a;return z?h.dropIndicatorRender({dropPosition:h.dropPosition,dropLevelOffset:h.dropLevelOffset,indent:h.indent,prefixCls:h.prefixCls,direction:h.direction}):null},[h.dropPosition,h.dropLevelOffset,h.indent,h.prefixCls,h.direction,h.draggable,h.dragOverNodeKey,h.dropIndicatorRender]),re=ge.useMemo(function(){var U=t.title,z=U===void 0?hs:U,$e="".concat(h.prefixCls,"-node-content-wrapper"),Ie;if(h.showIcon){var ne=t.icon||h.icon;Ie=ne?ge.createElement("span",{className:G("".concat(h.prefixCls,"-iconEle"),"".concat(h.prefixCls,"-icon__customize"))},typeof ne=="function"?ne(t):ne):L}else h.loadData&&C&&(Ie=L);var Pe;return typeof z=="function"?Pe=z(S):h.titleRender?Pe=h.titleRender(S):Pe=z,ge.createElement("span",{ref:T,title:typeof z=="string"?z:"",className:G($e,"".concat($e,"-").concat(Re||"normal"),B({},"".concat(h.prefixCls,"-node-selected"),!I&&(b||k))),onMouseEnter:te,onMouseLeave:ye,onContextMenu:Ce,onClick:Q,onDoubleClick:Y},Ie,ge.createElement("span",{className:"".concat(h.prefixCls,"-title")},Pe),A)},[h.prefixCls,h.showIcon,t,h.icon,L,h.titleRender,S,Re,te,ye,Ce,Q,Y]),be=Ft(M,{aria:!0,data:!0}),ie=Je(h.keyEntities,a)||{},Ne=ie.level,Te=p[p.length-1],Le=!I&&we,me=h.draggingNodeKey===a,ce=N!==void 0?{"aria-selected":!!N}:void 0;return ge.createElement("div",he({ref:x,role:"treeitem","aria-expanded":u?void 0:g,className:G(d,"".concat(h.prefixCls,"-treenode"),(o={},B(B(B(B(B(B(B(B(B(B(o,"".concat(h.prefixCls,"-treenode-disabled"),I),"".concat(h.prefixCls,"-treenode-switcher-").concat(g?"open":"close"),!u),"".concat(h.prefixCls,"-treenode-checkbox-checked"),m),"".concat(h.prefixCls,"-treenode-checkbox-indeterminate"),y),"".concat(h.prefixCls,"-treenode-selected"),b),"".concat(h.prefixCls,"-treenode-loading"),C),"".concat(h.prefixCls,"-treenode-active"),w),"".concat(h.prefixCls,"-treenode-leaf-last"),Te),"".concat(h.prefixCls,"-treenode-draggable"),we),"dragging",me),B(B(B(B(B(B(B(o,"drop-target",h.dropTargetKey===a),"drop-container",h.dropContainerKey===a),"drag-over",!I&&c),"drag-over-gap-top",!I&&s),"drag-over-gap-bottom",!I&&v),"filter-node",(n=h.filterTreeNode)===null||n===void 0?void 0:n.call(h,Me(t))),"".concat(h.prefixCls,"-treenode-leaf"),Z))),style:l,draggable:Le,onDragStart:Le?J:void 0,onDragEnter:we?ee:void 0,onDragOver:we?Ee:void 0,onDragLeave:we?ve:void 0,onDrop:we?X:void 0,onDragEnd:we?q:void 0,onMouseMove:E},ce,be),ge.createElement(ms,{prefixCls:h.prefixCls,level:Ne,isStart:f,isEnd:p}),le,ke(),Fe,re)};jt.isTreeNode=1;function at(e,t){if(!e)return[];var r=e.slice(),n=r.indexOf(t);return n>=0&&r.splice(n,1),r}function st(e,t){var r=(e||[]).slice();return r.indexOf(t)===-1&&r.push(t),r}function Jn(e){return e.split("-")}function ys(e,t){var r=[],n=Je(t,e);function o(){var a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];a.forEach(function(d){var l=d.key,c=d.children;r.push(l),o(c)})}return o(n.children),r}function bs(e){if(e.parent){var t=Jn(e.pos);return Number(t[t.length-1])===e.parent.children.length-1}return!1}function xs(e){var t=Jn(e.pos);return Number(t[t.length-1])===0}function _r(e,t,r,n,o,a,d,l,c,s){var v,u=e.clientX,f=e.clientY,p=e.target.getBoundingClientRect(),g=p.top,b=p.height,m=(s==="rtl"?-1:1)*(((o==null?void 0:o.x)||0)-u),y=(m-12)/n,C=c.filter(function(I){var P;return(P=l[I])===null||P===void 0||(P=P.children)===null||P===void 0?void 0:P.length}),x=Je(l,r.eventKey);if(f<g+b/2){var w=d.findIndex(function(I){return I.key===x.key}),S=w<=0?0:w-1,E=d[S].key;x=Je(l,E)}var N=x.key,M=x,h=x.key,O=0,T=0;if(!C.includes(N))for(var R=0;R<y&&bs(x);R+=1)x=x.parent,T+=1;var K=t.data,k=x.node,$=!0;return xs(x)&&x.level===0&&f<g+b/2&&a({dragNode:K,dropNode:k,dropPosition:-1})&&x.key===r.eventKey?O=-1:(M.children||[]).length&&C.includes(h)?a({dragNode:K,dropNode:k,dropPosition:0})?O=0:$=!1:T===0?y>-1.5?a({dragNode:K,dropNode:k,dropPosition:1})?O=1:$=!1:a({dragNode:K,dropNode:k,dropPosition:0})?O=0:a({dragNode:K,dropNode:k,dropPosition:1})?O=1:$=!1:a({dragNode:K,dropNode:k,dropPosition:1})?O=1:$=!1,{dropPosition:O,dropLevelOffset:T,dropTargetKey:x.key,dropTargetPos:x.pos,dragOverNodeKey:h,dropContainerKey:O===0?null:((v=x.parent)===null||v===void 0?void 0:v.key)||null,dropAllowed:$}}function zr(e,t){if(e){var r=t.multiple;return r?e.slice():e.length?[e[0]]:e}}function bn(e){if(!e)return null;var t;if(Array.isArray(e))t={checkedKeys:e,halfCheckedKeys:void 0};else if(lt(e)==="object")t={checkedKeys:e.checked||void 0,halfCheckedKeys:e.halfChecked||void 0};else return mt(!1,"`checkedKeys` is not an array or an object"),null;return t}function Rn(e,t){var r=new Set;function n(o){if(!r.has(o)){var a=Je(t,o);if(a){r.add(o);var d=a.parent,l=a.node;l.disabled||d&&n(d.key)}}}return(e||[]).forEach(function(o){n(o)}),fe(r)}const vt={},In="SELECT_ALL",Kn="SELECT_INVERT",On="SELECT_NONE",Hr=[],jo=(e,t)=>{let r=[];return(t||[]).forEach(n=>{r.push(n),n&&typeof n=="object"&&e in n&&(r=[].concat(fe(r),fe(jo(e,n[e]))))}),r},Cs=(e,t)=>{const{preserveSelectedRowKeys:r,selectedRowKeys:n,defaultSelectedRowKeys:o,getCheckboxProps:a,onChange:d,onSelect:l,onSelectAll:c,onSelectInvert:s,onSelectNone:v,onSelectMultiple:u,columnWidth:f,type:p,selections:g,fixed:b,renderCell:m,hideSelectAll:y,checkStrictly:C=!0}=t||{},{prefixCls:x,data:w,pageData:S,getRecordByKey:E,getRowKey:N,expandType:M,childrenColumnName:h,locale:O,getPopupContainer:T}=e,R=jn(),[K,k]=Fa(q=>q),[$,I]=ao(n||o||Hr,{value:n}),P=i.useRef(new Map),_=i.useCallback(q=>{if(r){const X=new Map;q.forEach(H=>{let j=E(H);!j&&P.current.has(H)&&(j=P.current.get(H)),X.set(H,j)}),P.current=X}},[E,r]);i.useEffect(()=>{_($)},[$]);const D=i.useMemo(()=>jo(h,S),[h,S]),{keyEntities:V}=i.useMemo(()=>{if(C)return{keyEntities:null};let q=w;if(r){const X=new Set(D.map((j,Z)=>N(j,Z))),H=Array.from(P.current).reduce((j,[Z,le])=>X.has(Z)?j:j.concat(le),[]);q=[].concat(fe(q),fe(H))}return Wn(q,{externalGetKey:N,childrenPropName:h})},[w,N,C,h,r,D]),Q=i.useMemo(()=>{const q=new Map;return D.forEach((X,H)=>{const j=N(X,H),Z=(a?a(X):null)||{};q.set(j,Z)}),q},[D,N,a]),Y=i.useCallback(q=>{const X=N(q);let H;return Q.has(X)?H=Q.get(N(q)):H=a?a(q):void 0,!!(H!=null&&H.disabled)},[Q,N]),[te,ye]=i.useMemo(()=>{if(C)return[$||[],[]];const{checkedKeys:q,halfCheckedKeys:X}=It($,!0,V,Y);return[q||[],X]},[$,C,V,Y]),Ce=i.useMemo(()=>{const q=p==="radio"?te.slice(0,1):te;return new Set(q)},[te,p]),we=i.useMemo(()=>p==="radio"?new Set:new Set(ye),[ye,p]);i.useEffect(()=>{t||I(Hr)},[!!t]);const J=i.useCallback((q,X)=>{let H,j;_(q),r?(H=q,j=q.map(Z=>P.current.get(Z))):(H=[],j=[],q.forEach(Z=>{const le=E(Z);le!==void 0&&(H.push(Z),j.push(le))})),I(H),d==null||d(H,j,{type:X})},[I,E,d,r]),ee=i.useCallback((q,X,H,j)=>{if(l){const Z=H.map(le=>E(le));l(E(q),X,Z,j)}J(H,"single")},[l,E,J]),Ee=i.useMemo(()=>!g||y?null:(g===!0?[In,Kn,On]:g).map(X=>X===In?{key:"all",text:O.selectionAll,onSelect(){J(w.map((H,j)=>N(H,j)).filter(H=>{const j=Q.get(H);return!(j!=null&&j.disabled)||Ce.has(H)}),"all")}}:X===Kn?{key:"invert",text:O.selectInvert,onSelect(){const H=new Set(Ce);S.forEach((Z,le)=>{const pe=N(Z,le),ke=Q.get(pe);ke!=null&&ke.disabled||(H.has(pe)?H.delete(pe):H.add(pe))});const j=Array.from(H);s&&(R.deprecated(!1,"onSelectInvert","onChange"),s(j)),J(j,"invert")}}:X===On?{key:"none",text:O.selectNone,onSelect(){v==null||v(),J(Array.from(Ce).filter(H=>{const j=Q.get(H);return j==null?void 0:j.disabled}),"none")}}:X).map(X=>Object.assign(Object.assign({},X),{onSelect:(...H)=>{var j,Z;(Z=X.onSelect)===null||Z===void 0||(j=Z).call.apply(j,[X].concat(H)),k(null)}})),[g,Ce,S,N,s,J]);return[i.useCallback(q=>{var X;if(!t)return q.filter(ce=>ce!==vt);let H=fe(q);const j=new Set(Ce),Z=D.map(N).filter(ce=>!Q.get(ce).disabled),le=Z.every(ce=>j.has(ce)),pe=Z.some(ce=>j.has(ce)),ke=()=>{const ce=[];le?Z.forEach(z=>{j.delete(z),ce.push(z)}):Z.forEach(z=>{j.has(z)||(j.add(z),ce.push(z))});const U=Array.from(j);c==null||c(!le,U.map(z=>E(z)),ce.map(z=>E(z))),J(U,"all"),k(null)};let Fe,Re;if(p!=="radio"){let ce;if(Ee){const ne={getPopupContainer:T,items:Ee.map((Pe,xe)=>{const{key:De,text:Ue,onSelect:Ve}=Pe;return{key:De??xe,onClick:()=>{Ve==null||Ve(Z)},label:Ue}})};ce=i.createElement("div",{className:`${x}-selection-extra`},i.createElement(uo,{menu:ne,getPopupContainer:T},i.createElement("span",null,i.createElement(La,null))))}const U=D.map((ne,Pe)=>{const xe=N(ne,Pe),De=Q.get(xe)||{};return Object.assign({checked:j.has(xe)},De)}).filter(({disabled:ne})=>ne),z=!!U.length&&U.length===D.length,$e=z&&U.every(({checked:ne})=>ne),Ie=z&&U.some(({checked:ne})=>ne);Re=i.createElement(Zt,{checked:z?$e:!!D.length&&le,indeterminate:z?!$e&&Ie:!le&&pe,onChange:ke,disabled:D.length===0||z,"aria-label":ce?"Custom selection":"Select all",skipGroup:!0}),Fe=!y&&i.createElement("div",{className:`${x}-selection`},Re,ce)}let L;p==="radio"?L=(ce,U,z)=>{const $e=N(U,z),Ie=j.has($e),ne=Q.get($e);return{node:i.createElement(Wt,Object.assign({},ne,{checked:Ie,onClick:Pe=>{var xe;Pe.stopPropagation(),(xe=ne==null?void 0:ne.onClick)===null||xe===void 0||xe.call(ne,Pe)},onChange:Pe=>{var xe;j.has($e)||ee($e,!0,[$e],Pe.nativeEvent),(xe=ne==null?void 0:ne.onChange)===null||xe===void 0||xe.call(ne,Pe)}})),checked:Ie}}:L=(ce,U,z)=>{var $e;const Ie=N(U,z),ne=j.has(Ie),Pe=we.has(Ie),xe=Q.get(Ie);let De;return M==="nest"?De=Pe:De=($e=xe==null?void 0:xe.indeterminate)!==null&&$e!==void 0?$e:Pe,{node:i.createElement(Zt,Object.assign({},xe,{indeterminate:De,checked:ne,skipGroup:!0,onClick:Ue=>{var Ve;Ue.stopPropagation(),(Ve=xe==null?void 0:xe.onClick)===null||Ve===void 0||Ve.call(xe,Ue)},onChange:Ue=>{var Ve;const{nativeEvent:ut}=Ue,{shiftKey:Ke}=ut,$t=Z.findIndex(qe=>qe===Ie),kt=te.some(qe=>Z.includes(qe));if(Ke&&C&&kt){const qe=K($t,Z,j),nt=Array.from(j);u==null||u(!ne,nt.map(Qe=>E(Qe)),qe.map(Qe=>E(Qe))),J(nt,"multiple")}else{const qe=te;if(C){const nt=ne?at(qe,Ie):st(qe,Ie);ee(Ie,!ne,nt,ut)}else{const nt=It([].concat(fe(qe),[Ie]),!0,V,Y),{checkedKeys:Qe,halfCheckedKeys:Xe}=nt;let Ae=Qe;if(ne){const Ye=new Set(Qe);Ye.delete(Ie),Ae=It(Array.from(Ye),{halfCheckedKeys:Xe},V,Y).checkedKeys}ee(Ie,!ne,Ae,ut)}}k(ne?null:$t),(Ve=xe==null?void 0:xe.onChange)===null||Ve===void 0||Ve.call(xe,Ue)}})),checked:ne}};const A=(ce,U,z)=>{const{node:$e,checked:Ie}=L(ce,U,z);return m?m(Ie,U,z,$e):$e};if(!H.includes(vt))if(H.findIndex(ce=>{var U;return((U=ce[Ht])===null||U===void 0?void 0:U.columnType)==="EXPAND_COLUMN"})===0){const[ce,...U]=H;H=[ce,vt].concat(fe(U))}else H=[vt].concat(fe(H));const re=H.indexOf(vt);H=H.filter((ce,U)=>ce!==vt||U===re);const be=H[re-1],ie=H[re+1];let Ne=b;Ne===void 0&&((ie==null?void 0:ie.fixed)!==void 0?Ne=ie.fixed:(be==null?void 0:be.fixed)!==void 0&&(Ne=be.fixed)),Ne&&be&&((X=be[Ht])===null||X===void 0?void 0:X.columnType)==="EXPAND_COLUMN"&&be.fixed===void 0&&(be.fixed=Ne);const Te=G(`${x}-selection-col`,{[`${x}-selection-col-with-dropdown`]:g&&p==="checkbox"}),Le=()=>t!=null&&t.columnTitle?typeof t.columnTitle=="function"?t.columnTitle(Re):t.columnTitle:Fe,me={fixed:Ne,width:f,className:`${x}-selection-column`,title:Le(),render:A,onCell:t.onCell,align:t.align,[Ht]:{className:Te}};return H.map(ce=>ce===vt?me:ce)},[N,D,t,te,Ce,we,f,Ee,M,Q,u,ee,Y]),Ce]};function Ss(e,t){return e._antProxy=e._antProxy||{},Object.keys(t).forEach(r=>{if(!(r in e._antProxy)){const n=e[r];e._antProxy[r]=n,e[r]=t[r]}}),e}function ws(e,t){return i.useImperativeHandle(e,()=>{const r=t(),{nativeElement:n}=r;return typeof Proxy<"u"?new Proxy(n,{get(o,a){return r[a]?r[a]:Reflect.get(o,a)}}):Ss(n,r)})}function Es(e){return t=>{const{prefixCls:r,onExpand:n,record:o,expanded:a,expandable:d}=t,l=`${r}-row-expand-icon`;return i.createElement("button",{type:"button",onClick:c=>{n(o,c),c.stopPropagation()},className:G(l,{[`${l}-spaced`]:!d,[`${l}-expanded`]:d&&a,[`${l}-collapsed`]:d&&!a}),"aria-label":a?e.collapse:e.expand,"aria-expanded":a})}}function $s(e){return(r,n)=>{const o=r.querySelector(`.${e}-container`);let a=n;if(o){const d=getComputedStyle(o),l=parseInt(d.borderLeftWidth,10),c=parseInt(d.borderRightWidth,10);a=n-l-c}return a}}const ht=(e,t)=>"key"in e&&e.key!==void 0&&e.key!==null?e.key:e.dataIndex?Array.isArray(e.dataIndex)?e.dataIndex.join("."):e.dataIndex:t;function Mt(e,t){return t?`${t}-${e}`:`${e}`}const sn=(e,t)=>typeof e=="function"?e(t):e,ks=(e,t)=>{const r=sn(e,t);return Object.prototype.toString.call(r)==="[object Object]"?"":r};function Ns(e){const t=i.useRef(e),r=ba();return[()=>t.current,n=>{t.current=n,r()}]}var Rs=function(t){var r=t.dropPosition,n=t.dropLevelOffset,o=t.indent,a={pointerEvents:"none",position:"absolute",right:0,backgroundColor:"red",height:2};switch(r){case-1:a.top=0,a.left=-n*o;break;case 1:a.bottom=0,a.left=-n*o;break;case 0:a.bottom=0,a.left=o;break}return ge.createElement("div",{style:a})};function Wo(e){if(e==null)throw new TypeError("Cannot destructure "+e)}function Is(e,t){var r=i.useState(!1),n=Se(r,2),o=n[0],a=n[1];dt(function(){if(o)return e(),function(){t()}},[o]),dt(function(){return a(!0),function(){a(!1)}},[])}var Ks=["className","style","motion","motionNodes","motionType","onMotionStart","onMotionEnd","active","treeNodeRequiredProps"],Os=i.forwardRef(function(e,t){var r=e.className,n=e.style,o=e.motion,a=e.motionNodes,d=e.motionType,l=e.onMotionStart,c=e.onMotionEnd,s=e.active,v=e.treeNodeRequiredProps,u=rt(e,Ks),f=i.useState(!0),p=Se(f,2),g=p[0],b=p[1],m=i.useContext(Yn),y=m.prefixCls,C=a&&d!=="hide";dt(function(){a&&C!==g&&b(C)},[a]);var x=function(){a&&l()},w=i.useRef(!1),S=function(){a&&!w.current&&(w.current=!0,c())};Is(x,S);var E=function(M){C===M&&S()};return a?i.createElement(xa,he({ref:t,visible:g},o,{motionAppear:d==="show",onVisibleChanged:E}),function(N,M){var h=N.className,O=N.style;return i.createElement("div",{ref:M,className:G("".concat(y,"-treenode-motion"),h),style:O},a.map(function(T){var R=Object.assign({},(Wo(T.data),T.data)),K=T.title,k=T.key,$=T.isStart,I=T.isEnd;delete R.children;var P=zt(k,v);return i.createElement(jt,he({},R,P,{title:K,active:s,data:T.data,key:k,isStart:$,isEnd:I}))}))}):i.createElement(jt,he({domRef:t,className:r,style:n},u,{active:s}))});function Ts(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],r=e.length,n=t.length;if(Math.abs(r-n)!==1)return{add:!1,key:null};function o(a,d){var l=new Map;a.forEach(function(s){l.set(s,!0)});var c=d.filter(function(s){return!l.has(s)});return c.length===1?c[0]:null}return r<n?{add:!0,key:o(e,t)}:{add:!1,key:o(t,e)}}function Fr(e,t,r){var n=e.findIndex(function(l){return l.key===r}),o=e[n+1],a=t.findIndex(function(l){return l.key===r});if(o){var d=t.findIndex(function(l){return l.key===o.key});return t.slice(a+1,d)}return t.slice(a+1)}var Ps=["prefixCls","data","selectable","checkable","expandedKeys","selectedKeys","checkedKeys","loadedKeys","loadingKeys","halfCheckedKeys","keyEntities","disabled","dragging","dragOverNodeKey","dropPosition","motion","height","itemHeight","virtual","scrollWidth","focusable","activeItem","focused","tabIndex","onKeyDown","onFocus","onBlur","onActiveChange","onListChangeStart","onListChangeEnd"],Ar={width:0,height:0,display:"flex",overflow:"hidden",opacity:0,border:0,padding:0,margin:0},Ds=function(){},wt="RC_TREE_MOTION_".concat(Math.random()),Tn={key:wt},Vo={key:wt,level:0,index:0,pos:"0",node:Tn,nodes:[Tn]},jr={parent:null,children:[],pos:Vo.pos,data:Tn,title:null,key:wt,isStart:[],isEnd:[]};function Wr(e,t,r,n){return t===!1||!r?e:e.slice(0,Math.ceil(r/n)+1)}function Vr(e){var t=e.key,r=e.pos;return Vt(t,r)}function Ms(e){for(var t=String(e.data.key),r=e;r.parent;)r=r.parent,t="".concat(r.data.key," > ").concat(t);return t}var Bs=i.forwardRef(function(e,t){var r=e.prefixCls,n=e.data;e.selectable,e.checkable;var o=e.expandedKeys,a=e.selectedKeys,d=e.checkedKeys,l=e.loadedKeys,c=e.loadingKeys,s=e.halfCheckedKeys,v=e.keyEntities,u=e.disabled,f=e.dragging,p=e.dragOverNodeKey,g=e.dropPosition,b=e.motion,m=e.height,y=e.itemHeight,C=e.virtual,x=e.scrollWidth,w=e.focusable,S=e.activeItem,E=e.focused,N=e.tabIndex,M=e.onKeyDown,h=e.onFocus,O=e.onBlur,T=e.onActiveChange,R=e.onListChangeStart,K=e.onListChangeEnd,k=rt(e,Ps),$=i.useRef(null),I=i.useRef(null);i.useImperativeHandle(t,function(){return{scrollTo:function(A){$.current.scrollTo(A)},getIndentWidth:function(){return I.current.offsetWidth}}});var P=i.useState(o),_=Se(P,2),D=_[0],V=_[1],Q=i.useState(n),Y=Se(Q,2),te=Y[0],ye=Y[1],Ce=i.useState(n),we=Se(Ce,2),J=we[0],ee=we[1],Ee=i.useState([]),ve=Se(Ee,2),q=ve[0],X=ve[1],H=i.useState(null),j=Se(H,2),Z=j[0],le=j[1],pe=i.useRef(n);pe.current=n;function ke(){var L=pe.current;ye(L),ee(L),X([]),le(null),K()}dt(function(){V(o);var L=Ts(D,o);if(L.key!==null)if(L.add){var A=te.findIndex(function(Le){var me=Le.key;return me===L.key}),re=Wr(Fr(te,n,L.key),C,m,y),be=te.slice();be.splice(A+1,0,jr),ee(be),X(re),le("show")}else{var ie=n.findIndex(function(Le){var me=Le.key;return me===L.key}),Ne=Wr(Fr(n,te,L.key),C,m,y),Te=n.slice();Te.splice(ie+1,0,jr),ee(Te),X(Ne),le("hide")}else te!==n&&(ye(n),ee(n))},[o,n]),i.useEffect(function(){f||ke()},[f]);var Fe=b?J:n,Re={expandedKeys:o,selectedKeys:a,loadedKeys:l,loadingKeys:c,checkedKeys:d,halfCheckedKeys:s,dragOverNodeKey:p,dropPosition:g,keyEntities:v};return i.createElement(i.Fragment,null,E&&S&&i.createElement("span",{style:Ar,"aria-live":"assertive"},Ms(S)),i.createElement("div",null,i.createElement("input",{style:Ar,disabled:w===!1||u,tabIndex:w!==!1?N:null,onKeyDown:M,onFocus:h,onBlur:O,value:"",onChange:Ds,"aria-label":"for screen reader"})),i.createElement("div",{className:"".concat(r,"-treenode"),"aria-hidden":!0,style:{position:"absolute",pointerEvents:"none",visibility:"hidden",height:0,overflow:"hidden",border:0,padding:0}},i.createElement("div",{className:"".concat(r,"-indent")},i.createElement("div",{ref:I,className:"".concat(r,"-indent-unit")}))),i.createElement(po,he({},k,{data:Fe,itemKey:Vr,height:m,fullHeight:!1,virtual:C,itemHeight:y,scrollWidth:x,prefixCls:"".concat(r,"-list"),ref:$,role:"tree",onVisibleChange:function(A){A.every(function(re){return Vr(re)!==wt})&&ke()}}),function(L){var A=L.pos,re=Object.assign({},(Wo(L.data),L.data)),be=L.title,ie=L.key,Ne=L.isStart,Te=L.isEnd,Le=Vt(ie,A);delete re.key,delete re.children;var me=zt(Le,Re);return i.createElement(Os,he({},re,me,{title:be,active:!!S&&ie===S.key,pos:A,data:L.data,isStart:Ne,isEnd:Te,motion:b,motionNodes:ie===wt?q:null,motionType:Z,onMotionStart:R,onMotionEnd:ke,treeNodeRequiredProps:Re,onMouseMove:function(){T(null)}}))}))}),Ls=10,Qn=function(e){Ca(r,e);var t=Sa(r);function r(){var n;Ea(this,r);for(var o=arguments.length,a=new Array(o),d=0;d<o;d++)a[d]=arguments[d];return n=t.call.apply(t,[this].concat(a)),B(de(n),"destroyed",!1),B(de(n),"delayedDragEnterLogic",void 0),B(de(n),"loadingRetryTimes",{}),B(de(n),"state",{keyEntities:{},indent:null,selectedKeys:[],checkedKeys:[],halfCheckedKeys:[],loadedKeys:[],loadingKeys:[],expandedKeys:[],draggingNodeKey:null,dragChildrenKeys:[],dropTargetKey:null,dropPosition:null,dropContainerKey:null,dropLevelOffset:null,dropTargetPos:null,dropAllowed:!0,dragOverNodeKey:null,treeData:[],flattenNodes:[],focused:!1,activeKey:null,listChanging:!1,prevProps:null,fieldNames:Ot()}),B(de(n),"dragStartMousePosition",null),B(de(n),"dragNodeProps",null),B(de(n),"currentMouseOverDroppableNodeKey",null),B(de(n),"listRef",i.createRef()),B(de(n),"onNodeDragStart",function(l,c){var s=n.state,v=s.expandedKeys,u=s.keyEntities,f=n.props.onDragStart,p=c.eventKey;n.dragNodeProps=c,n.dragStartMousePosition={x:l.clientX,y:l.clientY};var g=at(v,p);n.setState({draggingNodeKey:p,dragChildrenKeys:ys(p,u),indent:n.listRef.current.getIndentWidth()}),n.setExpandedKeys(g),window.addEventListener("dragend",n.onWindowDragEnd),f==null||f({event:l,node:Me(c)})}),B(de(n),"onNodeDragEnter",function(l,c){var s=n.state,v=s.expandedKeys,u=s.keyEntities,f=s.dragChildrenKeys,p=s.flattenNodes,g=s.indent,b=n.props,m=b.onDragEnter,y=b.onExpand,C=b.allowDrop,x=b.direction,w=c.pos,S=c.eventKey;if(n.currentMouseOverDroppableNodeKey!==S&&(n.currentMouseOverDroppableNodeKey=S),!n.dragNodeProps){n.resetDragState();return}var E=_r(l,n.dragNodeProps,c,g,n.dragStartMousePosition,C,p,u,v,x),N=E.dropPosition,M=E.dropLevelOffset,h=E.dropTargetKey,O=E.dropContainerKey,T=E.dropTargetPos,R=E.dropAllowed,K=E.dragOverNodeKey;if(f.includes(h)||!R){n.resetDragState();return}if(n.delayedDragEnterLogic||(n.delayedDragEnterLogic={}),Object.keys(n.delayedDragEnterLogic).forEach(function(k){clearTimeout(n.delayedDragEnterLogic[k])}),n.dragNodeProps.eventKey!==c.eventKey&&(l.persist(),n.delayedDragEnterLogic[w]=window.setTimeout(function(){if(n.state.draggingNodeKey!==null){var k=fe(v),$=Je(u,c.eventKey);$&&($.children||[]).length&&(k=st(v,c.eventKey)),n.props.hasOwnProperty("expandedKeys")||n.setExpandedKeys(k),y==null||y(k,{node:Me(c),expanded:!0,nativeEvent:l.nativeEvent})}},800)),n.dragNodeProps.eventKey===h&&M===0){n.resetDragState();return}n.setState({dragOverNodeKey:K,dropPosition:N,dropLevelOffset:M,dropTargetKey:h,dropContainerKey:O,dropTargetPos:T,dropAllowed:R}),m==null||m({event:l,node:Me(c),expandedKeys:v})}),B(de(n),"onNodeDragOver",function(l,c){var s=n.state,v=s.dragChildrenKeys,u=s.flattenNodes,f=s.keyEntities,p=s.expandedKeys,g=s.indent,b=n.props,m=b.onDragOver,y=b.allowDrop,C=b.direction;if(n.dragNodeProps){var x=_r(l,n.dragNodeProps,c,g,n.dragStartMousePosition,y,u,f,p,C),w=x.dropPosition,S=x.dropLevelOffset,E=x.dropTargetKey,N=x.dropContainerKey,M=x.dropTargetPos,h=x.dropAllowed,O=x.dragOverNodeKey;v.includes(E)||!h||(n.dragNodeProps.eventKey===E&&S===0?n.state.dropPosition===null&&n.state.dropLevelOffset===null&&n.state.dropTargetKey===null&&n.state.dropContainerKey===null&&n.state.dropTargetPos===null&&n.state.dropAllowed===!1&&n.state.dragOverNodeKey===null||n.resetDragState():w===n.state.dropPosition&&S===n.state.dropLevelOffset&&E===n.state.dropTargetKey&&N===n.state.dropContainerKey&&M===n.state.dropTargetPos&&h===n.state.dropAllowed&&O===n.state.dragOverNodeKey||n.setState({dropPosition:w,dropLevelOffset:S,dropTargetKey:E,dropContainerKey:N,dropTargetPos:M,dropAllowed:h,dragOverNodeKey:O}),m==null||m({event:l,node:Me(c)}))}}),B(de(n),"onNodeDragLeave",function(l,c){n.currentMouseOverDroppableNodeKey===c.eventKey&&!l.currentTarget.contains(l.relatedTarget)&&(n.resetDragState(),n.currentMouseOverDroppableNodeKey=null);var s=n.props.onDragLeave;s==null||s({event:l,node:Me(c)})}),B(de(n),"onWindowDragEnd",function(l){n.onNodeDragEnd(l,null,!0),window.removeEventListener("dragend",n.onWindowDragEnd)}),B(de(n),"onNodeDragEnd",function(l,c){var s=n.props.onDragEnd;n.setState({dragOverNodeKey:null}),n.cleanDragState(),s==null||s({event:l,node:Me(c)}),n.dragNodeProps=null,window.removeEventListener("dragend",n.onWindowDragEnd)}),B(de(n),"onNodeDrop",function(l,c){var s,v=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,u=n.state,f=u.dragChildrenKeys,p=u.dropPosition,g=u.dropTargetKey,b=u.dropTargetPos,m=u.dropAllowed;if(m){var y=n.props.onDrop;if(n.setState({dragOverNodeKey:null}),n.cleanDragState(),g!==null){var C=F(F({},zt(g,n.getTreeNodeRequiredProps())),{},{active:((s=n.getActiveItem())===null||s===void 0?void 0:s.key)===g,data:Je(n.state.keyEntities,g).node}),x=f.includes(g);mt(!x,"Can not drop to dragNode's children node. This is a bug of rc-tree. Please report an issue.");var w=Jn(b),S={event:l,node:Me(C),dragNode:n.dragNodeProps?Me(n.dragNodeProps):null,dragNodesKeys:[n.dragNodeProps.eventKey].concat(f),dropToGap:p!==0,dropPosition:p+Number(w[w.length-1])};v||y==null||y(S),n.dragNodeProps=null}}}),B(de(n),"cleanDragState",function(){var l=n.state.draggingNodeKey;l!==null&&n.setState({draggingNodeKey:null,dropPosition:null,dropContainerKey:null,dropTargetKey:null,dropLevelOffset:null,dropAllowed:!0,dragOverNodeKey:null}),n.dragStartMousePosition=null,n.currentMouseOverDroppableNodeKey=null}),B(de(n),"triggerExpandActionExpand",function(l,c){var s=n.state,v=s.expandedKeys,u=s.flattenNodes,f=c.expanded,p=c.key,g=c.isLeaf;if(!(g||l.shiftKey||l.metaKey||l.ctrlKey)){var b=u.filter(function(y){return y.key===p})[0],m=Me(F(F({},zt(p,n.getTreeNodeRequiredProps())),{},{data:b.data}));n.setExpandedKeys(f?at(v,p):st(v,p)),n.onNodeExpand(l,m)}}),B(de(n),"onNodeClick",function(l,c){var s=n.props,v=s.onClick,u=s.expandAction;u==="click"&&n.triggerExpandActionExpand(l,c),v==null||v(l,c)}),B(de(n),"onNodeDoubleClick",function(l,c){var s=n.props,v=s.onDoubleClick,u=s.expandAction;u==="doubleClick"&&n.triggerExpandActionExpand(l,c),v==null||v(l,c)}),B(de(n),"onNodeSelect",function(l,c){var s=n.state.selectedKeys,v=n.state,u=v.keyEntities,f=v.fieldNames,p=n.props,g=p.onSelect,b=p.multiple,m=c.selected,y=c[f.key],C=!m;C?b?s=st(s,y):s=[y]:s=at(s,y);var x=s.map(function(w){var S=Je(u,w);return S?S.node:null}).filter(Boolean);n.setUncontrolledState({selectedKeys:s}),g==null||g(s,{event:"select",selected:C,node:c,selectedNodes:x,nativeEvent:l.nativeEvent})}),B(de(n),"onNodeCheck",function(l,c,s){var v=n.state,u=v.keyEntities,f=v.checkedKeys,p=v.halfCheckedKeys,g=n.props,b=g.checkStrictly,m=g.onCheck,y=c.key,C,x={event:"check",node:c,checked:s,nativeEvent:l.nativeEvent};if(b){var w=s?st(f,y):at(f,y),S=at(p,y);C={checked:w,halfChecked:S},x.checkedNodes=w.map(function(T){return Je(u,T)}).filter(Boolean).map(function(T){return T.node}),n.setUncontrolledState({checkedKeys:w})}else{var E=It([].concat(fe(f),[y]),!0,u),N=E.checkedKeys,M=E.halfCheckedKeys;if(!s){var h=new Set(N);h.delete(y);var O=It(Array.from(h),{halfCheckedKeys:M},u);N=O.checkedKeys,M=O.halfCheckedKeys}C=N,x.checkedNodes=[],x.checkedNodesPositions=[],x.halfCheckedKeys=M,N.forEach(function(T){var R=Je(u,T);if(R){var K=R.node,k=R.pos;x.checkedNodes.push(K),x.checkedNodesPositions.push({node:K,pos:k})}}),n.setUncontrolledState({checkedKeys:N},!1,{halfCheckedKeys:M})}m==null||m(C,x)}),B(de(n),"onNodeLoad",function(l){var c,s=l.key,v=n.state.keyEntities,u=Je(v,s);if(!(u!=null&&(c=u.children)!==null&&c!==void 0&&c.length)){var f=new Promise(function(p,g){n.setState(function(b){var m=b.loadedKeys,y=m===void 0?[]:m,C=b.loadingKeys,x=C===void 0?[]:C,w=n.props,S=w.loadData,E=w.onLoad;if(!S||y.includes(s)||x.includes(s))return null;var N=S(l);return N.then(function(){var M=n.state.loadedKeys,h=st(M,s);E==null||E(h,{event:"load",node:l}),n.setUncontrolledState({loadedKeys:h}),n.setState(function(O){return{loadingKeys:at(O.loadingKeys,s)}}),p()}).catch(function(M){if(n.setState(function(O){return{loadingKeys:at(O.loadingKeys,s)}}),n.loadingRetryTimes[s]=(n.loadingRetryTimes[s]||0)+1,n.loadingRetryTimes[s]>=Ls){var h=n.state.loadedKeys;mt(!1,"Retry for `loadData` many times but still failed. No more retry."),n.setUncontrolledState({loadedKeys:st(h,s)}),p()}g(M)}),{loadingKeys:st(x,s)}})});return f.catch(function(){}),f}}),B(de(n),"onNodeMouseEnter",function(l,c){var s=n.props.onMouseEnter;s==null||s({event:l,node:c})}),B(de(n),"onNodeMouseLeave",function(l,c){var s=n.props.onMouseLeave;s==null||s({event:l,node:c})}),B(de(n),"onNodeContextMenu",function(l,c){var s=n.props.onRightClick;s&&(l.preventDefault(),s({event:l,node:c}))}),B(de(n),"onFocus",function(){var l=n.props.onFocus;n.setState({focused:!0});for(var c=arguments.length,s=new Array(c),v=0;v<c;v++)s[v]=arguments[v];l==null||l.apply(void 0,s)}),B(de(n),"onBlur",function(){var l=n.props.onBlur;n.setState({focused:!1}),n.onActiveChange(null);for(var c=arguments.length,s=new Array(c),v=0;v<c;v++)s[v]=arguments[v];l==null||l.apply(void 0,s)}),B(de(n),"getTreeNodeRequiredProps",function(){var l=n.state,c=l.expandedKeys,s=l.selectedKeys,v=l.loadedKeys,u=l.loadingKeys,f=l.checkedKeys,p=l.halfCheckedKeys,g=l.dragOverNodeKey,b=l.dropPosition,m=l.keyEntities;return{expandedKeys:c||[],selectedKeys:s||[],loadedKeys:v||[],loadingKeys:u||[],checkedKeys:f||[],halfCheckedKeys:p||[],dragOverNodeKey:g,dropPosition:b,keyEntities:m}}),B(de(n),"setExpandedKeys",function(l){var c=n.state,s=c.treeData,v=c.fieldNames,u=hn(s,l,v);n.setUncontrolledState({expandedKeys:l,flattenNodes:u},!0)}),B(de(n),"onNodeExpand",function(l,c){var s=n.state.expandedKeys,v=n.state,u=v.listChanging,f=v.fieldNames,p=n.props,g=p.onExpand,b=p.loadData,m=c.expanded,y=c[f.key];if(!u){var C=s.includes(y),x=!m;if(mt(m&&C||!m&&!C,"Expand state not sync with index check"),s=x?st(s,y):at(s,y),n.setExpandedKeys(s),g==null||g(s,{node:c,expanded:x,nativeEvent:l.nativeEvent}),x&&b){var w=n.onNodeLoad(c);w&&w.then(function(){var S=hn(n.state.treeData,s,f);n.setUncontrolledState({flattenNodes:S})}).catch(function(){var S=n.state.expandedKeys,E=at(S,y);n.setExpandedKeys(E)})}}}),B(de(n),"onListChangeStart",function(){n.setUncontrolledState({listChanging:!0})}),B(de(n),"onListChangeEnd",function(){setTimeout(function(){n.setUncontrolledState({listChanging:!1})})}),B(de(n),"onActiveChange",function(l){var c=n.state.activeKey,s=n.props,v=s.onActiveChange,u=s.itemScrollOffset,f=u===void 0?0:u;c!==l&&(n.setState({activeKey:l}),l!==null&&n.scrollTo({key:l,offset:f}),v==null||v(l))}),B(de(n),"getActiveItem",function(){var l=n.state,c=l.activeKey,s=l.flattenNodes;return c===null?null:s.find(function(v){var u=v.key;return u===c})||null}),B(de(n),"offsetActiveKey",function(l){var c=n.state,s=c.flattenNodes,v=c.activeKey,u=s.findIndex(function(g){var b=g.key;return b===v});u===-1&&l<0&&(u=s.length),u=(u+l+s.length)%s.length;var f=s[u];if(f){var p=f.key;n.onActiveChange(p)}else n.onActiveChange(null)}),B(de(n),"onKeyDown",function(l){var c=n.state,s=c.activeKey,v=c.expandedKeys,u=c.checkedKeys,f=c.fieldNames,p=n.props,g=p.onKeyDown,b=p.checkable,m=p.selectable;switch(l.which){case pt.UP:{n.offsetActiveKey(-1),l.preventDefault();break}case pt.DOWN:{n.offsetActiveKey(1),l.preventDefault();break}}var y=n.getActiveItem();if(y&&y.data){var C=n.getTreeNodeRequiredProps(),x=y.data.isLeaf===!1||!!(y.data[f.children]||[]).length,w=Me(F(F({},zt(s,C)),{},{data:y.data,active:!0}));switch(l.which){case pt.LEFT:{x&&v.includes(s)?n.onNodeExpand({},w):y.parent&&n.onActiveChange(y.parent.key),l.preventDefault();break}case pt.RIGHT:{x&&!v.includes(s)?n.onNodeExpand({},w):y.children&&y.children.length&&n.onActiveChange(y.children[0].key),l.preventDefault();break}case pt.ENTER:case pt.SPACE:{b&&!w.disabled&&w.checkable!==!1&&!w.disableCheckbox?n.onNodeCheck({},w,!u.includes(s)):!b&&m&&!w.disabled&&w.selectable!==!1&&n.onNodeSelect({},w);break}}}g==null||g(l)}),B(de(n),"setUncontrolledState",function(l){var c=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:null;if(!n.destroyed){var v=!1,u=!0,f={};Object.keys(l).forEach(function(p){if(n.props.hasOwnProperty(p)){u=!1;return}v=!0,f[p]=l[p]}),v&&(!c||u)&&n.setState(F(F({},f),s))}}),B(de(n),"scrollTo",function(l){n.listRef.current.scrollTo(l)}),n}return wa(r,[{key:"componentDidMount",value:function(){this.destroyed=!1,this.onUpdated()}},{key:"componentDidUpdate",value:function(){this.onUpdated()}},{key:"onUpdated",value:function(){var o=this.props,a=o.activeKey,d=o.itemScrollOffset,l=d===void 0?0:d;a!==void 0&&a!==this.state.activeKey&&(this.setState({activeKey:a}),a!==null&&this.scrollTo({key:a,offset:l}))}},{key:"componentWillUnmount",value:function(){window.removeEventListener("dragend",this.onWindowDragEnd),this.destroyed=!0}},{key:"resetDragState",value:function(){this.setState({dragOverNodeKey:null,dropPosition:null,dropLevelOffset:null,dropTargetKey:null,dropContainerKey:null,dropTargetPos:null,dropAllowed:!1})}},{key:"render",value:function(){var o=this.state,a=o.focused,d=o.flattenNodes,l=o.keyEntities,c=o.draggingNodeKey,s=o.activeKey,v=o.dropLevelOffset,u=o.dropContainerKey,f=o.dropTargetKey,p=o.dropPosition,g=o.dragOverNodeKey,b=o.indent,m=this.props,y=m.prefixCls,C=m.className,x=m.style,w=m.showLine,S=m.focusable,E=m.tabIndex,N=E===void 0?0:E,M=m.selectable,h=m.showIcon,O=m.icon,T=m.switcherIcon,R=m.draggable,K=m.checkable,k=m.checkStrictly,$=m.disabled,I=m.motion,P=m.loadData,_=m.filterTreeNode,D=m.height,V=m.itemHeight,Q=m.scrollWidth,Y=m.virtual,te=m.titleRender,ye=m.dropIndicatorRender,Ce=m.onContextMenu,we=m.onScroll,J=m.direction,ee=m.rootClassName,Ee=m.rootStyle,ve=Ft(this.props,{aria:!0,data:!0}),q;R&&(lt(R)==="object"?q=R:typeof R=="function"?q={nodeDraggable:R}:q={});var X={prefixCls:y,selectable:M,showIcon:h,icon:O,switcherIcon:T,draggable:q,draggingNodeKey:c,checkable:K,checkStrictly:k,disabled:$,keyEntities:l,dropLevelOffset:v,dropContainerKey:u,dropTargetKey:f,dropPosition:p,dragOverNodeKey:g,indent:b,direction:J,dropIndicatorRender:ye,loadData:P,filterTreeNode:_,titleRender:te,onNodeClick:this.onNodeClick,onNodeDoubleClick:this.onNodeDoubleClick,onNodeExpand:this.onNodeExpand,onNodeSelect:this.onNodeSelect,onNodeCheck:this.onNodeCheck,onNodeLoad:this.onNodeLoad,onNodeMouseEnter:this.onNodeMouseEnter,onNodeMouseLeave:this.onNodeMouseLeave,onNodeContextMenu:this.onNodeContextMenu,onNodeDragStart:this.onNodeDragStart,onNodeDragEnter:this.onNodeDragEnter,onNodeDragOver:this.onNodeDragOver,onNodeDragLeave:this.onNodeDragLeave,onNodeDragEnd:this.onNodeDragEnd,onNodeDrop:this.onNodeDrop};return i.createElement(Yn.Provider,{value:X},i.createElement("div",{className:G(y,C,ee,B(B(B({},"".concat(y,"-show-line"),w),"".concat(y,"-focused"),a),"".concat(y,"-active-focused"),s!==null)),style:Ee},i.createElement(Bs,he({ref:this.listRef,prefixCls:y,style:x,data:d,disabled:$,selectable:M,checkable:!!K,motion:I,dragging:c!==null,height:D,itemHeight:V,virtual:Y,focusable:S,focused:a,tabIndex:N,activeItem:this.getActiveItem(),onFocus:this.onFocus,onBlur:this.onBlur,onKeyDown:this.onKeyDown,onActiveChange:this.onActiveChange,onListChangeStart:this.onListChangeStart,onListChangeEnd:this.onListChangeEnd,onContextMenu:Ce,onScroll:we,scrollWidth:Q},this.getTreeNodeRequiredProps(),ve))))}}],[{key:"getDerivedStateFromProps",value:function(o,a){var d=a.prevProps,l={prevProps:o};function c(N){return!d&&o.hasOwnProperty(N)||d&&d[N]!==o[N]}var s,v=a.fieldNames;if(c("fieldNames")&&(v=Ot(o.fieldNames),l.fieldNames=v),c("treeData")?s=o.treeData:c("children")&&(mt(!1,"`children` of Tree is deprecated. Please use `treeData` instead."),s=xo(o.children)),s){l.treeData=s;var u=Wn(s,{fieldNames:v});l.keyEntities=F(B({},wt,Vo),u.keyEntities)}var f=l.keyEntities||a.keyEntities;if(c("expandedKeys")||d&&c("autoExpandParent"))l.expandedKeys=o.autoExpandParent||!d&&o.defaultExpandParent?Rn(o.expandedKeys,f):o.expandedKeys;else if(!d&&o.defaultExpandAll){var p=F({},f);delete p[wt];var g=[];Object.keys(p).forEach(function(N){var M=p[N];M.children&&M.children.length&&g.push(M.key)}),l.expandedKeys=g}else!d&&o.defaultExpandedKeys&&(l.expandedKeys=o.autoExpandParent||o.defaultExpandParent?Rn(o.defaultExpandedKeys,f):o.defaultExpandedKeys);if(l.expandedKeys||delete l.expandedKeys,s||l.expandedKeys){var b=hn(s||a.treeData,l.expandedKeys||a.expandedKeys,v);l.flattenNodes=b}if(o.selectable&&(c("selectedKeys")?l.selectedKeys=zr(o.selectedKeys,o):!d&&o.defaultSelectedKeys&&(l.selectedKeys=zr(o.defaultSelectedKeys,o))),o.checkable){var m;if(c("checkedKeys")?m=bn(o.checkedKeys)||{}:!d&&o.defaultCheckedKeys?m=bn(o.defaultCheckedKeys)||{}:s&&(m=bn(o.checkedKeys)||{checkedKeys:a.checkedKeys,halfCheckedKeys:a.halfCheckedKeys}),m){var y=m,C=y.checkedKeys,x=C===void 0?[]:C,w=y.halfCheckedKeys,S=w===void 0?[]:w;if(!o.checkStrictly){var E=It(x,!0,f);x=E.checkedKeys,S=E.halfCheckedKeys}l.checkedKeys=x,l.halfCheckedKeys=S}}return c("loadedKeys")&&(l.loadedKeys=o.loadedKeys),l}}]),r}(i.Component);B(Qn,"defaultProps",{prefixCls:"rc-tree",showLine:!1,showIcon:!0,selectable:!0,multiple:!1,checkable:!1,disabled:!1,checkStrictly:!1,draggable:!1,defaultExpandParent:!0,autoExpandParent:!1,defaultExpandAll:!1,defaultExpandedKeys:[],defaultCheckedKeys:[],defaultSelectedKeys:[],dropIndicatorRender:Rs,allowDrop:function(){return!0},expandAction:!1});B(Qn,"TreeNode",jt);const _s=({treeCls:e,treeNodeCls:t,directoryNodeSelectedBg:r,directoryNodeSelectedColor:n,motionDurationMid:o,borderRadius:a,controlItemBgHover:d})=>({[`${e}${e}-directory ${t}`]:{[`${e}-node-content-wrapper`]:{position:"static",[`> *:not(${e}-drop-indicator)`]:{position:"relative"},"&:hover":{background:"transparent"},"&:before":{position:"absolute",inset:0,transition:`background-color ${o}`,content:'""',borderRadius:a},"&:hover:before":{background:d}},[`${e}-switcher, ${e}-checkbox, ${e}-draggable-icon`]:{zIndex:1},"&-selected":{[`${e}-switcher, ${e}-draggable-icon`]:{color:n},[`${e}-node-content-wrapper`]:{color:n,background:"transparent","&:before, &:hover:before":{background:r}}}}}),zs=new zn("ant-tree-node-fx-do-not-use",{"0%":{opacity:0},"100%":{opacity:1}}),Hs=(e,t)=>({[`.${e}-switcher-icon`]:{display:"inline-block",fontSize:10,verticalAlign:"baseline",svg:{transition:`transform ${t.motionDurationSlow}`}}}),Fs=(e,t)=>({[`.${e}-drop-indicator`]:{position:"absolute",zIndex:1,height:2,backgroundColor:t.colorPrimary,borderRadius:1,pointerEvents:"none","&:after":{position:"absolute",top:-3,insetInlineStart:-6,width:8,height:8,backgroundColor:"transparent",border:`${W(t.lineWidthBold)} solid ${t.colorPrimary}`,borderRadius:"50%",content:'""'}}}),As=(e,t)=>{const{treeCls:r,treeNodeCls:n,treeNodePadding:o,titleHeight:a,indentSize:d,nodeSelectedBg:l,nodeHoverBg:c,colorTextQuaternary:s,controlItemBgActiveDisabled:v}=t;return{[r]:Object.assign(Object.assign({},St(t)),{background:t.colorBgContainer,borderRadius:t.borderRadius,transition:`background-color ${t.motionDurationSlow}`,"&-rtl":{direction:"rtl"},[`&${r}-rtl ${r}-switcher_close ${r}-switcher-icon svg`]:{transform:"rotate(90deg)"},[`&-focused:not(:hover):not(${r}-active-focused)`]:Object.assign({},Ln(t)),[`${r}-list-holder-inner`]:{alignItems:"flex-start"},[`&${r}-block-node`]:{[`${r}-list-holder-inner`]:{alignItems:"stretch",[`${r}-node-content-wrapper`]:{flex:"auto"},[`${n}.dragging:after`]:{position:"absolute",inset:0,border:`1px solid ${t.colorPrimary}`,opacity:0,animationName:zs,animationDuration:t.motionDurationSlow,animationPlayState:"running",animationFillMode:"forwards",content:'""',pointerEvents:"none",borderRadius:t.borderRadius}}},[n]:{display:"flex",alignItems:"flex-start",marginBottom:o,lineHeight:W(a),position:"relative","&:before":{content:'""',position:"absolute",zIndex:1,insetInlineStart:0,width:"100%",top:"100%",height:o},[`&-disabled ${r}-node-content-wrapper`]:{color:t.colorTextDisabled,cursor:"not-allowed","&:hover":{background:"transparent"}},[`${r}-checkbox-disabled + ${r}-node-selected,&${n}-disabled${n}-selected ${r}-node-content-wrapper`]:{backgroundColor:v},[`${r}-checkbox-disabled`]:{pointerEvents:"unset"},[`&:not(${n}-disabled)`]:{[`${r}-node-content-wrapper`]:{"&:hover":{color:t.nodeHoverColor}}},[`&-active ${r}-node-content-wrapper`]:{background:t.controlItemBgHover},[`&:not(${n}-disabled).filter-node ${r}-title`]:{color:t.colorPrimary,fontWeight:500},"&-draggable":{cursor:"grab",[`${r}-draggable-icon`]:{flexShrink:0,width:a,textAlign:"center",visibility:"visible",color:s},[`&${n}-disabled ${r}-draggable-icon`]:{visibility:"hidden"}}},[`${r}-indent`]:{alignSelf:"stretch",whiteSpace:"nowrap",userSelect:"none","&-unit":{display:"inline-block",width:d}},[`${r}-draggable-icon`]:{visibility:"hidden"},[`${r}-switcher, ${r}-checkbox`]:{marginInlineEnd:t.calc(t.calc(a).sub(t.controlInteractiveSize)).div(2).equal()},[`${r}-switcher`]:Object.assign(Object.assign({},Hs(e,t)),{position:"relative",flex:"none",alignSelf:"stretch",width:a,textAlign:"center",cursor:"pointer",userSelect:"none",transition:`all ${t.motionDurationSlow}`,"&-noop":{cursor:"unset"},"&:before":{pointerEvents:"none",content:'""',width:a,height:a,position:"absolute",left:{_skip_check_:!0,value:0},top:0,borderRadius:t.borderRadius,transition:`all ${t.motionDurationSlow}`},[`&:not(${r}-switcher-noop):hover:before`]:{backgroundColor:t.colorBgTextHover},[`&_close ${r}-switcher-icon svg`]:{transform:"rotate(-90deg)"},"&-loading-icon":{color:t.colorPrimary},"&-leaf-line":{position:"relative",zIndex:1,display:"inline-block",width:"100%",height:"100%","&:before":{position:"absolute",top:0,insetInlineEnd:t.calc(a).div(2).equal(),bottom:t.calc(o).mul(-1).equal(),marginInlineStart:-1,borderInlineEnd:`1px solid ${t.colorBorder}`,content:'""'},"&:after":{position:"absolute",width:t.calc(t.calc(a).div(2).equal()).mul(.8).equal(),height:t.calc(a).div(2).equal(),borderBottom:`1px solid ${t.colorBorder}`,content:'""'}}}),[`${r}-node-content-wrapper`]:Object.assign(Object.assign({position:"relative",minHeight:a,paddingBlock:0,paddingInline:t.paddingXS,background:"transparent",borderRadius:t.borderRadius,cursor:"pointer",transition:`all ${t.motionDurationMid}, border 0s, line-height 0s, box-shadow 0s`},Fs(e,t)),{"&:hover":{backgroundColor:c},[`&${r}-node-selected`]:{color:t.nodeSelectedColor,backgroundColor:l},[`${r}-iconEle`]:{display:"inline-block",width:a,height:a,textAlign:"center",verticalAlign:"top","&:empty":{display:"none"}}}),[`${r}-unselectable ${r}-node-content-wrapper:hover`]:{backgroundColor:"transparent"},[`${n}.drop-container > [draggable]`]:{boxShadow:`0 0 0 2px ${t.colorPrimary}`},"&-show-line":{[`${r}-indent-unit`]:{position:"relative",height:"100%","&:before":{position:"absolute",top:0,insetInlineEnd:t.calc(a).div(2).equal(),bottom:t.calc(o).mul(-1).equal(),borderInlineEnd:`1px solid ${t.colorBorder}`,content:'""'},"&-end:before":{display:"none"}},[`${r}-switcher`]:{background:"transparent","&-line-icon":{verticalAlign:"-0.15em"}}},[`${n}-leaf-last ${r}-switcher-leaf-line:before`]:{top:"auto !important",bottom:"auto !important",height:`${W(t.calc(a).div(2).equal())} !important`}})}},js=(e,t,r=!0)=>{const n=`.${e}`,o=`${n}-treenode`,a=t.calc(t.paddingXS).div(2).equal(),d=on(t,{treeCls:n,treeNodeCls:o,treeNodePadding:a});return[As(e,d),r&&_s(d)].filter(Boolean)},Ws=e=>{const{controlHeightSM:t,controlItemBgHover:r,controlItemBgActive:n}=e,o=t;return{titleHeight:o,indentSize:o,nodeHoverBg:r,nodeHoverColor:e.colorText,nodeSelectedBg:n,nodeSelectedColor:e.colorText}},Vs=e=>{const{colorTextLightSolid:t,colorPrimary:r}=e;return Object.assign(Object.assign({},Ws(e)),{directoryNodeSelectedColor:t,directoryNodeSelectedBg:r})},qs=rn("Tree",(e,{prefixCls:t})=>[{[e.componentCls]:Aa(`${t}-checkbox`,e)},js(t,e),$a(e)],Vs),qr=4;function Xs(e){const{dropPosition:t,dropLevelOffset:r,prefixCls:n,indent:o,direction:a="ltr"}=e,d=a==="ltr"?"left":"right",l=a==="ltr"?"right":"left",c={[d]:-r*o+qr,[l]:0};switch(t){case-1:c.top=-3;break;case 1:c.bottom=-3;break;default:c.bottom=-3,c[d]=o+qr;break}return ge.createElement("div",{style:c,className:`${n}-drop-indicator`})}const Gs=e=>{const{prefixCls:t,switcherIcon:r,treeNodeProps:n,showLine:o,switcherLoadingIcon:a}=e,{isLeaf:d,expanded:l,loading:c}=n;if(c)return i.isValidElement(a)?a:i.createElement(ka,{className:`${t}-switcher-loading-icon`});let s;if(o&&typeof o=="object"&&(s=o.showLeafIcon),d){if(!o)return null;if(typeof s!="boolean"&&s){const f=typeof s=="function"?s(n):s,p=`${t}-switcher-line-custom-icon`;return i.isValidElement(f)?En(f,{className:G(f.props.className||"",p)}):f}return s?i.createElement(ko,{className:`${t}-switcher-line-icon`}):i.createElement("span",{className:`${t}-switcher-leaf-line`})}const v=`${t}-switcher-icon`,u=typeof r=="function"?r(n):r;return i.isValidElement(u)?En(u,{className:G(u.props.className||"",v)}):u!==void 0?u:o?l?i.createElement(Zl,{className:`${t}-switcher-line-icon`}):i.createElement(ni,{className:`${t}-switcher-line-icon`}):i.createElement(Kl,{className:v})},qo=ge.forwardRef((e,t)=>{var r;const{getPrefixCls:n,direction:o,virtual:a,tree:d}=ge.useContext(Et),{prefixCls:l,className:c,showIcon:s=!1,showLine:v,switcherIcon:u,switcherLoadingIcon:f,blockNode:p=!1,children:g,checkable:b=!1,selectable:m=!0,draggable:y,motion:C,style:x}=e,w=n("tree",l),S=n(),E=C??Object.assign(Object.assign({},Na(S)),{motionAppear:!1}),N=Object.assign(Object.assign({},e),{checkable:b,selectable:m,showIcon:s,motion:E,blockNode:p,showLine:!!v,dropIndicatorRender:Xs}),[M,h,O]=qs(w),[,T]=fo(),R=T.paddingXS/2+(((r=T.Tree)===null||r===void 0?void 0:r.titleHeight)||T.controlHeightSM),K=ge.useMemo(()=>{if(!y)return!1;let $={};switch(typeof y){case"function":$.nodeDraggable=y;break;case"object":$=Object.assign({},y);break}return $.icon!==!1&&($.icon=$.icon||ge.createElement(Yl,null)),$},[y]),k=$=>ge.createElement(Gs,{prefixCls:w,switcherIcon:u,switcherLoadingIcon:f,treeNodeProps:$,showLine:v});return M(ge.createElement(Qn,Object.assign({itemHeight:R,ref:t,virtual:a},N,{style:Object.assign(Object.assign({},d==null?void 0:d.style),x),prefixCls:w,className:G({[`${w}-icon-hide`]:!s,[`${w}-block-node`]:p,[`${w}-unselectable`]:!m,[`${w}-rtl`]:o==="rtl"},d==null?void 0:d.className,c,h,O),direction:o,checkable:b&&ge.createElement("span",{className:`${w}-checkbox-inner`}),selectable:m,switcherIcon:k,draggable:K}),g))}),Xr=0,xn=1,Gr=2;function Zn(e,t,r){const{key:n,children:o}=r;function a(d){const l=d[n],c=d[o];t(l,d)!==!1&&Zn(c||[],t,r)}e.forEach(a)}function Us({treeData:e,expandedKeys:t,startKey:r,endKey:n,fieldNames:o}){const a=[];let d=Xr;if(r&&r===n)return[r];if(!r||!n)return[];function l(c){return c===r||c===n}return Zn(e,c=>{if(d===Gr)return!1;if(l(c)){if(a.push(c),d===Xr)d=xn;else if(d===xn)return d=Gr,!1}else d===xn&&a.push(c);return t.includes(c)},Ot(o)),a}function Cn(e,t,r){const n=fe(t),o=[];return Zn(e,(a,d)=>{const l=n.indexOf(a);return l!==-1&&(o.push(d),n.splice(l,1)),!!n.length},Ot(r)),o}var Ur=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};function Ys(e){const{isLeaf:t,expanded:r}=e;return t?i.createElement(ko,null):r?i.createElement(Wl,null):i.createElement(Xl,null)}function Yr({treeData:e,children:t}){return e||xo(t)}const Js=(e,t)=>{var{defaultExpandAll:r,defaultExpandParent:n,defaultExpandedKeys:o}=e,a=Ur(e,["defaultExpandAll","defaultExpandParent","defaultExpandedKeys"]);const d=i.useRef(null),l=i.useRef(null),c=()=>{const{keyEntities:M}=Wn(Yr(a));let h;return r?h=Object.keys(M):n?h=Rn(a.expandedKeys||o||[],M):h=a.expandedKeys||o||[],h},[s,v]=i.useState(a.selectedKeys||a.defaultSelectedKeys||[]),[u,f]=i.useState(()=>c());i.useEffect(()=>{"selectedKeys"in a&&v(a.selectedKeys)},[a.selectedKeys]),i.useEffect(()=>{"expandedKeys"in a&&f(a.expandedKeys)},[a.expandedKeys]);const p=(M,h)=>{var O;return"expandedKeys"in a||f(M),(O=a.onExpand)===null||O===void 0?void 0:O.call(a,M,h)},g=(M,h)=>{var O;const{multiple:T,fieldNames:R}=a,{node:K,nativeEvent:k}=h,{key:$=""}=K,I=Yr(a),P=Object.assign(Object.assign({},h),{selected:!0}),_=(k==null?void 0:k.ctrlKey)||(k==null?void 0:k.metaKey),D=k==null?void 0:k.shiftKey;let V;T&&_?(V=M,d.current=$,l.current=V,P.selectedNodes=Cn(I,V,R)):T&&D?(V=Array.from(new Set([].concat(fe(l.current||[]),fe(Us({treeData:I,expandedKeys:u,startKey:$,endKey:d.current,fieldNames:R}))))),P.selectedNodes=Cn(I,V,R)):(V=[$],d.current=$,l.current=V,P.selectedNodes=Cn(I,V,R)),(O=a.onSelect)===null||O===void 0||O.call(a,V,P),"selectedKeys"in a||v(V)},{getPrefixCls:b,direction:m}=i.useContext(Et),{prefixCls:y,className:C,showIcon:x=!0,expandAction:w="click"}=a,S=Ur(a,["prefixCls","className","showIcon","expandAction"]),E=b("tree",y),N=G(`${E}-directory`,{[`${E}-directory-rtl`]:m==="rtl"},C);return i.createElement(qo,Object.assign({icon:Ys,ref:t,blockNode:!0},S,{showIcon:x,expandAction:w,prefixCls:E,className:N,expandedKeys:u,selectedKeys:s,onSelect:g,onExpand:p}))},Qs=i.forwardRef(Js),er=qo;er.DirectoryTree=Qs;er.TreeNode=jt;const Jr=e=>{const{value:t,filterSearch:r,tablePrefixCls:n,locale:o,onChange:a}=e;return r?i.createElement("div",{className:`${n}-filter-dropdown-search`},i.createElement(Ra,{prefix:i.createElement(Ia,null),placeholder:o.filterSearchPlaceholder,onChange:a,value:t,htmlSize:1,className:`${n}-filter-dropdown-search-input`})):null},Zs=e=>{const{keyCode:t}=e;t===pt.ENTER&&e.stopPropagation()},ec=i.forwardRef((e,t)=>i.createElement("div",{className:e.className,onClick:r=>r.stopPropagation(),onKeyDown:Zs,ref:t},e.children));function Kt(e){let t=[];return(e||[]).forEach(({value:r,children:n})=>{t.push(r),n&&(t=[].concat(fe(t),fe(Kt(n))))}),t}function tc(e){return e.some(({children:t})=>t)}function Xo(e,t){return typeof t=="string"||typeof t=="number"?t==null?void 0:t.toString().toLowerCase().includes(e.trim().toLowerCase()):!1}function Go({filters:e,prefixCls:t,filteredKeys:r,filterMultiple:n,searchValue:o,filterSearch:a}){return e.map((d,l)=>{const c=String(d.value);if(d.children)return{key:c||l,label:d.text,popupClassName:`${t}-dropdown-submenu`,children:Go({filters:d.children,prefixCls:t,filteredKeys:r,filterMultiple:n,searchValue:o,filterSearch:a})};const s=n?Zt:Wt,v={key:d.value!==void 0?c:l,label:i.createElement(i.Fragment,null,i.createElement(s,{checked:r.includes(c)}),i.createElement("span",null,d.text))};return o.trim()?typeof a=="function"?a(o,d)?v:null:Xo(o,d.text)?v:null:v})}function Sn(e){return e||[]}const nc=e=>{var t,r,n,o;const{tablePrefixCls:a,prefixCls:d,column:l,dropdownPrefixCls:c,columnKey:s,filterOnClose:v,filterMultiple:u,filterMode:f="menu",filterSearch:p=!1,filterState:g,triggerFilter:b,locale:m,children:y,getPopupContainer:C,rootClassName:x}=e,{filterResetToDefaultFilteredValue:w,defaultFilteredValue:S,filterDropdownProps:E={},filterDropdownOpen:N,filterDropdownVisible:M,onFilterDropdownVisibleChange:h,onFilterDropdownOpenChange:O}=l,[T,R]=i.useState(!1),K=!!(g&&(!((t=g.filteredKeys)===null||t===void 0)&&t.length||g.forceFiltered)),k=L=>{var A;R(L),(A=E.onOpenChange)===null||A===void 0||A.call(E,L),O==null||O(L),h==null||h(L)},$=(o=(n=(r=E.open)!==null&&r!==void 0?r:N)!==null&&n!==void 0?n:M)!==null&&o!==void 0?o:T,I=g==null?void 0:g.filteredKeys,[P,_]=Ns(Sn(I)),D=({selectedKeys:L})=>{_(L)},V=(L,{node:A,checked:re})=>{D(u?{selectedKeys:L}:{selectedKeys:re&&A.key?[A.key]:[]})};i.useEffect(()=>{T&&D({selectedKeys:Sn(I)})},[I]);const[Q,Y]=i.useState([]),te=L=>{Y(L)},[ye,Ce]=i.useState(""),we=L=>{const{value:A}=L.target;Ce(A)};i.useEffect(()=>{T||Ce("")},[T]);const J=L=>{const A=L!=null&&L.length?L:null;if(A===null&&(!g||!g.filteredKeys)||At(A,g==null?void 0:g.filteredKeys,!0))return null;b({column:l,key:s,filteredKeys:A})},ee=()=>{k(!1),J(P())},Ee=({confirm:L,closeDropdown:A}={confirm:!1,closeDropdown:!1})=>{L&&J([]),A&&k(!1),Ce(""),_(w?(S||[]).map(re=>String(re)):[])},ve=({closeDropdown:L}={closeDropdown:!0})=>{L&&k(!1),J(P())},q=(L,A)=>{A.source==="trigger"&&(L&&I!==void 0&&_(Sn(I)),k(L),!L&&!l.filterDropdown&&v&&ee())},X=G({[`${c}-menu-without-submenu`]:!tc(l.filters||[])}),H=L=>{if(L.target.checked){const A=Kt(l==null?void 0:l.filters).map(re=>String(re));_(A)}else _([])},j=({filters:L})=>(L||[]).map((A,re)=>{const be=String(A.value),ie={title:A.text,key:A.value!==void 0?be:String(re)};return A.children&&(ie.children=j({filters:A.children})),ie}),Z=L=>{var A;return Object.assign(Object.assign({},L),{text:L.title,value:L.key,children:((A=L.children)===null||A===void 0?void 0:A.map(re=>Z(re)))||[]})};let le;const{direction:pe,renderEmpty:ke}=i.useContext(Et);if(typeof l.filterDropdown=="function")le=l.filterDropdown({prefixCls:`${c}-custom`,setSelectedKeys:L=>D({selectedKeys:L}),selectedKeys:P(),confirm:ve,clearFilters:Ee,filters:l.filters,visible:$,close:()=>{k(!1)}});else if(l.filterDropdown)le=l.filterDropdown;else{const L=P()||[],A=()=>{var be,ie;const Ne=(be=ke==null?void 0:ke("Table.filter"))!==null&&be!==void 0?be:i.createElement(Er,{image:Er.PRESENTED_IMAGE_SIMPLE,description:m.filterEmptyText,styles:{image:{height:24}},style:{margin:0,padding:"16px 0"}});if((l.filters||[]).length===0)return Ne;if(f==="tree")return i.createElement(i.Fragment,null,i.createElement(Jr,{filterSearch:p,value:ye,onChange:we,tablePrefixCls:a,locale:m}),i.createElement("div",{className:`${a}-filter-dropdown-tree`},u?i.createElement(Zt,{checked:L.length===Kt(l.filters).length,indeterminate:L.length>0&&L.length<Kt(l.filters).length,className:`${a}-filter-dropdown-checkall`,onChange:H},(ie=m==null?void 0:m.filterCheckall)!==null&&ie!==void 0?ie:m==null?void 0:m.filterCheckAll):null,i.createElement(er,{checkable:!0,selectable:!1,blockNode:!0,multiple:u,checkStrictly:!u,className:`${c}-menu`,onCheck:V,checkedKeys:L,selectedKeys:L,showIcon:!1,treeData:j({filters:l.filters}),autoExpandParent:!0,defaultExpandAll:!0,filterTreeNode:ye.trim()?me=>typeof p=="function"?p(ye,Z(me)):Xo(ye,me.title):void 0})));const Te=Go({filters:l.filters||[],filterSearch:p,prefixCls:d,filteredKeys:P(),filterMultiple:u,searchValue:ye}),Le=Te.every(me=>me===null);return i.createElement(i.Fragment,null,i.createElement(Jr,{filterSearch:p,value:ye,onChange:we,tablePrefixCls:a,locale:m}),Le?Ne:i.createElement(Ka,{selectable:!0,multiple:u,prefixCls:`${c}-menu`,className:X,onSelect:D,onDeselect:D,selectedKeys:L,getPopupContainer:C,openKeys:Q,onOpenChange:te,items:Te}))},re=()=>w?At((S||[]).map(be=>String(be)),L,!0):L.length===0;le=i.createElement(i.Fragment,null,A(),i.createElement("div",{className:`${d}-dropdown-btns`},i.createElement(Sr,{type:"link",size:"small",disabled:re(),onClick:()=>Ee()},m.filterReset),i.createElement(Sr,{type:"primary",size:"small",onClick:ee},m.filterConfirm)))}l.filterDropdown&&(le=i.createElement(Oa,{selectable:void 0},le)),le=i.createElement(ec,{className:`${d}-dropdown`},le);const Re=vo({trigger:["click"],placement:pe==="rtl"?"bottomLeft":"bottomRight",children:(()=>{let L;return typeof l.filterIcon=="function"?L=l.filterIcon(K):l.filterIcon?L=l.filterIcon:L=i.createElement(Fl,null),i.createElement("span",{role:"button",tabIndex:-1,className:G(`${d}-trigger`,{active:K}),onClick:A=>{A.stopPropagation()}},L)})(),getPopupContainer:C},Object.assign(Object.assign({},E),{rootClassName:G(x,E.rootClassName),open:$,onOpenChange:q,popupRender:()=>typeof(E==null?void 0:E.dropdownRender)=="function"?E.dropdownRender(le):le}));return i.createElement("div",{className:`${d}-column`},i.createElement("span",{className:`${a}-column-title`},y),i.createElement(uo,Object.assign({},Re)))},Pn=(e,t,r)=>{let n=[];return(e||[]).forEach((o,a)=>{var d;const l=Mt(a,r),c=o.filterDropdown!==void 0;if(o.filters||c||"onFilter"in o)if("filteredValue"in o){let s=o.filteredValue;c||(s=(d=s==null?void 0:s.map(String))!==null&&d!==void 0?d:s),n.push({column:o,key:ht(o,l),filteredKeys:s,forceFiltered:o.filtered})}else n.push({column:o,key:ht(o,l),filteredKeys:t&&o.defaultFilteredValue?o.defaultFilteredValue:void 0,forceFiltered:o.filtered});"children"in o&&(n=[].concat(fe(n),fe(Pn(o.children,t,l))))}),n};function Uo(e,t,r,n,o,a,d,l,c){return r.map((s,v)=>{const u=Mt(v,l),{filterOnClose:f=!0,filterMultiple:p=!0,filterMode:g,filterSearch:b}=s;let m=s;if(m.filters||m.filterDropdown){const y=ht(m,u),C=n.find(({key:x})=>y===x);m=Object.assign(Object.assign({},m),{title:x=>i.createElement(nc,{tablePrefixCls:e,prefixCls:`${e}-filter`,dropdownPrefixCls:t,column:m,columnKey:y,filterState:C,filterOnClose:f,filterMultiple:p,filterMode:g,filterSearch:b,triggerFilter:a,locale:o,getPopupContainer:d,rootClassName:c},sn(s.title,x))})}return"children"in m&&(m=Object.assign(Object.assign({},m),{children:Uo(e,t,m.children,n,o,a,d,u,c)})),m})}const Qr=e=>{const t={};return e.forEach(({key:r,filteredKeys:n,column:o})=>{const a=r,{filters:d,filterDropdown:l}=o;if(l)t[a]=n||null;else if(Array.isArray(n)){const c=Kt(d);t[a]=c.filter(s=>n.includes(String(s)))}else t[a]=null}),t},Dn=(e,t,r)=>t.reduce((o,a)=>{const{column:{onFilter:d,filters:l},filteredKeys:c}=a;return d&&c&&c.length?o.map(s=>Object.assign({},s)).filter(s=>c.some(v=>{const u=Kt(l),f=u.findIndex(g=>String(g)===String(v)),p=f!==-1?u[f]:v;return s[r]&&(s[r]=Dn(s[r],t,r)),d(p,s)})):o},e),Yo=e=>e.flatMap(t=>"children"in t?[t].concat(fe(Yo(t.children||[]))):[t]),rc=e=>{const{prefixCls:t,dropdownPrefixCls:r,mergedColumns:n,onFilterChange:o,getPopupContainer:a,locale:d,rootClassName:l}=e;jn();const c=i.useMemo(()=>Yo(n||[]),[n]),[s,v]=i.useState(()=>Pn(c,!0)),u=i.useMemo(()=>{const b=Pn(c,!1);if(b.length===0)return b;let m=!0;if(b.forEach(({filteredKeys:y})=>{y!==void 0&&(m=!1)}),m){const y=(c||[]).map((C,x)=>ht(C,Mt(x)));return s.filter(({key:C})=>y.includes(C)).map(C=>{const x=c[y.findIndex(w=>w===C.key)];return Object.assign(Object.assign({},C),{column:Object.assign(Object.assign({},C.column),x),forceFiltered:x.filtered})})}return b},[c,s]),f=i.useMemo(()=>Qr(u),[u]),p=b=>{const m=u.filter(({key:y})=>y!==b.key);m.push(b),v(m),o(Qr(m),m)};return[b=>Uo(t,r,b,u,d,p,a,void 0,l),u,f]},oc=(e,t,r)=>{const n=i.useRef({});function o(a){var d;if(!n.current||n.current.data!==e||n.current.childrenColumnName!==t||n.current.getRowKey!==r){let c=function(s){s.forEach((v,u)=>{const f=r(v,u);l.set(f,v),v&&typeof v=="object"&&t in v&&c(v[t]||[])})};const l=new Map;c(e),n.current={data:e,childrenColumnName:t,kvMap:l,getRowKey:r}}return(d=n.current.kvMap)===null||d===void 0?void 0:d.get(a)}return[o]};var ac=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const Jo=10;function lc(e,t){const r={current:e.current,pageSize:e.pageSize};return Object.keys(t&&typeof t=="object"?t:{}).forEach(o=>{const a=e[o];typeof a!="function"&&(r[o]=a)}),r}function ic(e,t,r){const n=r&&typeof r=="object"?r:{},{total:o=0}=n,a=ac(n,["total"]),[d,l]=i.useState(()=>({current:"defaultCurrent"in a?a.defaultCurrent:1,pageSize:"defaultPageSize"in a?a.defaultPageSize:Jo})),c=vo(d,a,{total:o>0?o:e}),s=Math.ceil((o||e)/c.pageSize);c.current>s&&(c.current=s||1);const v=(f,p)=>{l({current:f??1,pageSize:p||c.pageSize})},u=(f,p)=>{var g;r&&((g=r.onChange)===null||g===void 0||g.call(r,f,p)),v(f,p),t(f,p||(c==null?void 0:c.pageSize))};return r===!1?[{},()=>{}]:[Object.assign(Object.assign({},c),{onChange:u}),v]}const Qt="ascend",wn="descend",nn=e=>typeof e.sorter=="object"&&typeof e.sorter.multiple=="number"?e.sorter.multiple:!1,Zr=e=>typeof e=="function"?e:e&&typeof e=="object"&&e.compare?e.compare:!1,sc=(e,t)=>t?e[e.indexOf(t)+1]:e[0],Mn=(e,t,r)=>{let n=[];const o=(a,d)=>{n.push({column:a,key:ht(a,d),multiplePriority:nn(a),sortOrder:a.sortOrder})};return(e||[]).forEach((a,d)=>{const l=Mt(d,r);a.children?("sortOrder"in a&&o(a,l),n=[].concat(fe(n),fe(Mn(a.children,t,l)))):a.sorter&&("sortOrder"in a?o(a,l):t&&a.defaultSortOrder&&n.push({column:a,key:ht(a,l),multiplePriority:nn(a),sortOrder:a.defaultSortOrder}))}),n},Qo=(e,t,r,n,o,a,d,l)=>(t||[]).map((s,v)=>{const u=Mt(v,l);let f=s;if(f.sorter){const p=f.sortDirections||o,g=f.showSorterTooltip===void 0?d:f.showSorterTooltip,b=ht(f,u),m=r.find(({key:h})=>h===b),y=m?m.sortOrder:null,C=sc(p,y);let x;if(s.sortIcon)x=s.sortIcon({sortOrder:y});else{const h=p.includes(Qt)&&i.createElement(Bl,{className:G(`${e}-column-sorter-up`,{active:y===Qt})}),O=p.includes(wn)&&i.createElement(Pl,{className:G(`${e}-column-sorter-down`,{active:y===wn})});x=i.createElement("span",{className:G(`${e}-column-sorter`,{[`${e}-column-sorter-full`]:!!(h&&O)})},i.createElement("span",{className:`${e}-column-sorter-inner`,"aria-hidden":"true"},h,O))}const{cancelSort:w,triggerAsc:S,triggerDesc:E}=a||{};let N=w;C===wn?N=E:C===Qt&&(N=S);const M=typeof g=="object"?Object.assign({title:N},g):{title:N};f=Object.assign(Object.assign({},f),{className:G(f.className,{[`${e}-column-sort`]:y}),title:h=>{const O=`${e}-column-sorters`,T=i.createElement("span",{className:`${e}-column-title`},sn(s.title,h)),R=i.createElement("div",{className:O},T,x);return g?typeof g!="boolean"&&(g==null?void 0:g.target)==="sorter-icon"?i.createElement("div",{className:`${O} ${e}-column-sorters-tooltip-target-sorter`},T,i.createElement(wr,Object.assign({},M),x)):i.createElement(wr,Object.assign({},M),R):R},onHeaderCell:h=>{var O;const T=((O=s.onHeaderCell)===null||O===void 0?void 0:O.call(s,h))||{},R=T.onClick,K=T.onKeyDown;T.onClick=I=>{n({column:s,key:b,sortOrder:C,multiplePriority:nn(s)}),R==null||R(I)},T.onKeyDown=I=>{I.keyCode===pt.ENTER&&(n({column:s,key:b,sortOrder:C,multiplePriority:nn(s)}),K==null||K(I))};const k=ks(s.title,{}),$=k==null?void 0:k.toString();return y&&(T["aria-sort"]=y==="ascend"?"ascending":"descending"),T["aria-label"]=$||"",T.className=G(T.className,`${e}-column-has-sorters`),T.tabIndex=0,s.ellipsis&&(T.title=(k??"").toString()),T}})}return"children"in f&&(f=Object.assign(Object.assign({},f),{children:Qo(e,f.children,r,n,o,a,d,u)})),f}),eo=e=>{const{column:t,sortOrder:r}=e;return{column:t,order:r,field:t.dataIndex,columnKey:t.key}},to=e=>{const t=e.filter(({sortOrder:r})=>r).map(eo);if(t.length===0&&e.length){const r=e.length-1;return Object.assign(Object.assign({},eo(e[r])),{column:void 0,order:void 0,field:void 0,columnKey:void 0})}return t.length<=1?t[0]||{}:t},Bn=(e,t,r)=>{const n=t.slice().sort((d,l)=>l.multiplePriority-d.multiplePriority),o=e.slice(),a=n.filter(({column:{sorter:d},sortOrder:l})=>Zr(d)&&l);return a.length?o.sort((d,l)=>{for(let c=0;c<a.length;c+=1){const s=a[c],{column:{sorter:v},sortOrder:u}=s,f=Zr(v);if(f&&u){const p=f(d,l,u);if(p!==0)return u===Qt?p:-p}}return 0}).map(d=>{const l=d[r];return l?Object.assign(Object.assign({},d),{[r]:Bn(l,t,r)}):d}):o},cc=e=>{const{prefixCls:t,mergedColumns:r,sortDirections:n,tableLocale:o,showSorterTooltip:a,onSorterChange:d}=e,[l,c]=i.useState(()=>Mn(r,!0)),s=(b,m)=>{const y=[];return b.forEach((C,x)=>{const w=Mt(x,m);if(y.push(ht(C,w)),Array.isArray(C.children)){const S=s(C.children,w);y.push.apply(y,fe(S))}}),y},v=i.useMemo(()=>{let b=!0;const m=Mn(r,!1);if(!m.length){const w=s(r);return l.filter(({key:S})=>w.includes(S))}const y=[];function C(w){b?y.push(w):y.push(Object.assign(Object.assign({},w),{sortOrder:null}))}let x=null;return m.forEach(w=>{x===null?(C(w),w.sortOrder&&(w.multiplePriority===!1?b=!1:x=!0)):(x&&w.multiplePriority!==!1||(b=!1),C(w))}),y},[r,l]),u=i.useMemo(()=>{var b,m;const y=v.map(({column:C,sortOrder:x})=>({column:C,order:x}));return{sortColumns:y,sortColumn:(b=y[0])===null||b===void 0?void 0:b.column,sortOrder:(m=y[0])===null||m===void 0?void 0:m.order}},[v]),f=b=>{let m;b.multiplePriority===!1||!v.length||v[0].multiplePriority===!1?m=[b]:m=[].concat(fe(v.filter(({key:y})=>y!==b.key)),[b]),c(m),d(to(m),m)};return[b=>Qo(t,b,v,f,n,o,a),v,u,()=>to(v)]},Zo=(e,t)=>e.map(n=>{const o=Object.assign({},n);return o.title=sn(n.title,t),"children"in o&&(o.children=Zo(o.children,t)),o}),dc=e=>[i.useCallback(r=>Zo(r,e),[e])],uc=Ho((e,t)=>{const{_renderTimes:r}=e,{_renderTimes:n}=t;return r!==n}),fc=Ao((e,t)=>{const{_renderTimes:r}=e,{_renderTimes:n}=t;return r!==n}),vc=e=>{const{componentCls:t,lineWidth:r,lineType:n,tableBorderColor:o,tableHeaderBg:a,tablePaddingVertical:d,tablePaddingHorizontal:l,calc:c}=e,s=`${W(r)} ${n} ${o}`,v=(u,f,p)=>({[`&${t}-${u}`]:{[`> ${t}-container`]:{[`> ${t}-content, > ${t}-body`]:{"\n            > table > tbody > tr > th,\n            > table > tbody > tr > td\n          ":{[`> ${t}-expanded-row-fixed`]:{margin:`${W(c(f).mul(-1).equal())}
              ${W(c(c(p).add(r)).mul(-1).equal())}`}}}}}});return{[`${t}-wrapper`]:{[`${t}${t}-bordered`]:Object.assign(Object.assign(Object.assign({[`> ${t}-title`]:{border:s,borderBottom:0},[`> ${t}-container`]:{borderInlineStart:s,borderTop:s,[`
            > ${t}-content,
            > ${t}-header,
            > ${t}-body,
            > ${t}-summary
          `]:{"> table":{"\n                > thead > tr > th,\n                > thead > tr > td,\n                > tbody > tr > th,\n                > tbody > tr > td,\n                > tfoot > tr > th,\n                > tfoot > tr > td\n              ":{borderInlineEnd:s},"> thead":{"> tr:not(:last-child) > th":{borderBottom:s},"> tr > th::before":{backgroundColor:"transparent !important"}},"\n                > thead > tr,\n                > tbody > tr,\n                > tfoot > tr\n              ":{[`> ${t}-cell-fix-right-first::after`]:{borderInlineEnd:s}},"\n                > tbody > tr > th,\n                > tbody > tr > td\n              ":{[`> ${t}-expanded-row-fixed`]:{margin:`${W(c(d).mul(-1).equal())} ${W(c(c(l).add(r)).mul(-1).equal())}`,"&::after":{position:"absolute",top:0,insetInlineEnd:r,bottom:0,borderInlineEnd:s,content:'""'}}}}}},[`&${t}-scroll-horizontal`]:{[`> ${t}-container > ${t}-body`]:{"> table > tbody":{[`
                > tr${t}-expanded-row,
                > tr${t}-placeholder
              `]:{"> th, > td":{borderInlineEnd:0}}}}}},v("middle",e.tablePaddingVerticalMiddle,e.tablePaddingHorizontalMiddle)),v("small",e.tablePaddingVerticalSmall,e.tablePaddingHorizontalSmall)),{[`> ${t}-footer`]:{border:s,borderTop:0}}),[`${t}-cell`]:{[`${t}-container:first-child`]:{borderTop:0},"&-scrollbar:not([rowspan])":{boxShadow:`0 ${W(r)} 0 ${W(r)} ${a}`}},[`${t}-bordered ${t}-cell-scrollbar`]:{borderInlineEnd:s}}}},pc=e=>{const{componentCls:t}=e;return{[`${t}-wrapper`]:{[`${t}-cell-ellipsis`]:Object.assign(Object.assign({},Ta),{wordBreak:"keep-all",[`
          &${t}-cell-fix-left-last,
          &${t}-cell-fix-right-first
        `]:{overflow:"visible",[`${t}-cell-content`]:{display:"block",overflow:"hidden",textOverflow:"ellipsis"}},[`${t}-column-title`]:{overflow:"hidden",textOverflow:"ellipsis",wordBreak:"keep-all"}})}}},mc=e=>{const{componentCls:t}=e;return{[`${t}-wrapper`]:{[`${t}-tbody > tr${t}-placeholder`]:{textAlign:"center",color:e.colorTextDisabled,"\n          &:hover > th,\n          &:hover > td,\n        ":{background:e.colorBgContainer}}}}},gc=e=>{const{componentCls:t,antCls:r,motionDurationSlow:n,lineWidth:o,paddingXS:a,lineType:d,tableBorderColor:l,tableExpandIconBg:c,tableExpandColumnWidth:s,borderRadius:v,tablePaddingVertical:u,tablePaddingHorizontal:f,tableExpandedRowBg:p,paddingXXS:g,expandIconMarginTop:b,expandIconSize:m,expandIconHalfInner:y,expandIconScale:C,calc:x}=e,w=`${W(o)} ${d} ${l}`,S=x(g).sub(o).equal();return{[`${t}-wrapper`]:{[`${t}-expand-icon-col`]:{width:s},[`${t}-row-expand-icon-cell`]:{textAlign:"center",[`${t}-row-expand-icon`]:{display:"inline-flex",float:"none",verticalAlign:"sub"}},[`${t}-row-indent`]:{height:1,float:"left"},[`${t}-row-expand-icon`]:Object.assign(Object.assign({},Pa(e)),{position:"relative",float:"left",width:m,height:m,color:"inherit",lineHeight:W(m),background:c,border:w,borderRadius:v,transform:`scale(${C})`,"&:focus, &:hover, &:active":{borderColor:"currentcolor"},"&::before, &::after":{position:"absolute",background:"currentcolor",transition:`transform ${n} ease-out`,content:'""'},"&::before":{top:y,insetInlineEnd:S,insetInlineStart:S,height:o},"&::after":{top:S,bottom:S,insetInlineStart:y,width:o,transform:"rotate(90deg)"},"&-collapsed::before":{transform:"rotate(-180deg)"},"&-collapsed::after":{transform:"rotate(0deg)"},"&-spaced":{"&::before, &::after":{display:"none",content:"none"},background:"transparent",border:0,visibility:"hidden"}}),[`${t}-row-indent + ${t}-row-expand-icon`]:{marginTop:b,marginInlineEnd:a},[`tr${t}-expanded-row`]:{"&, &:hover":{"> th, > td":{background:p}},[`${r}-descriptions-view`]:{display:"flex",table:{flex:"auto",width:"100%"}}},[`${t}-expanded-row-fixed`]:{position:"relative",margin:`${W(x(u).mul(-1).equal())} ${W(x(f).mul(-1).equal())}`,padding:`${W(u)} ${W(f)}`}}}},hc=e=>{const{componentCls:t,antCls:r,iconCls:n,tableFilterDropdownWidth:o,tableFilterDropdownSearchWidth:a,paddingXXS:d,paddingXS:l,colorText:c,lineWidth:s,lineType:v,tableBorderColor:u,headerIconColor:f,fontSizeSM:p,tablePaddingHorizontal:g,borderRadius:b,motionDurationSlow:m,colorIcon:y,colorPrimary:C,tableHeaderFilterActiveBg:x,colorTextDisabled:w,tableFilterDropdownBg:S,tableFilterDropdownHeight:E,controlItemBgHover:N,controlItemBgActive:M,boxShadowSecondary:h,filterDropdownMenuBg:O,calc:T}=e,R=`${r}-dropdown`,K=`${t}-filter-dropdown`,k=`${r}-tree`,$=`${W(s)} ${v} ${u}`;return[{[`${t}-wrapper`]:{[`${t}-filter-column`]:{display:"flex",justifyContent:"space-between"},[`${t}-filter-trigger`]:{position:"relative",display:"flex",alignItems:"center",marginBlock:T(d).mul(-1).equal(),marginInline:`${W(d)} ${W(T(g).div(2).mul(-1).equal())}`,padding:`0 ${W(d)}`,color:f,fontSize:p,borderRadius:b,cursor:"pointer",transition:`all ${m}`,"&:hover":{color:y,background:x},"&.active":{color:C}}}},{[`${r}-dropdown`]:{[K]:Object.assign(Object.assign({},St(e)),{minWidth:o,backgroundColor:S,borderRadius:b,boxShadow:h,overflow:"hidden",[`${R}-menu`]:{maxHeight:E,overflowX:"hidden",border:0,boxShadow:"none",borderRadius:"unset",backgroundColor:O,"&:empty::after":{display:"block",padding:`${W(l)} 0`,color:w,fontSize:p,textAlign:"center",content:'"Not Found"'}},[`${K}-tree`]:{paddingBlock:`${W(l)} 0`,paddingInline:l,[k]:{padding:0},[`${k}-treenode ${k}-node-content-wrapper:hover`]:{backgroundColor:N},[`${k}-treenode-checkbox-checked ${k}-node-content-wrapper`]:{"&, &:hover":{backgroundColor:M}}},[`${K}-search`]:{padding:l,borderBottom:$,"&-input":{input:{minWidth:a},[n]:{color:w}}},[`${K}-checkall`]:{width:"100%",marginBottom:d,marginInlineStart:d},[`${K}-btns`]:{display:"flex",justifyContent:"space-between",padding:`${W(T(l).sub(s).equal())} ${W(l)}`,overflow:"hidden",borderTop:$}})}},{[`${r}-dropdown ${K}, ${K}-submenu`]:{[`${r}-checkbox-wrapper + span`]:{paddingInlineStart:l,color:c},"> ul":{maxHeight:"calc(100vh - 130px)",overflowX:"hidden",overflowY:"auto"}}}]},yc=e=>{const{componentCls:t,lineWidth:r,colorSplit:n,motionDurationSlow:o,zIndexTableFixed:a,tableBg:d,zIndexTableSticky:l,calc:c}=e,s=n;return{[`${t}-wrapper`]:{[`
        ${t}-cell-fix-left,
        ${t}-cell-fix-right
      `]:{position:"sticky !important",zIndex:a,background:d},[`
        ${t}-cell-fix-left-first::after,
        ${t}-cell-fix-left-last::after
      `]:{position:"absolute",top:0,right:{_skip_check_:!0,value:0},bottom:c(r).mul(-1).equal(),width:30,transform:"translateX(100%)",transition:`box-shadow ${o}`,content:'""',pointerEvents:"none"},[`${t}-cell-fix-left-all::after`]:{display:"none"},[`
        ${t}-cell-fix-right-first::after,
        ${t}-cell-fix-right-last::after
      `]:{position:"absolute",top:0,bottom:c(r).mul(-1).equal(),left:{_skip_check_:!0,value:0},width:30,transform:"translateX(-100%)",transition:`box-shadow ${o}`,content:'""',pointerEvents:"none"},[`${t}-container`]:{position:"relative","&::before, &::after":{position:"absolute",top:0,bottom:0,zIndex:c(l).add(1).equal({unit:!1}),width:30,transition:`box-shadow ${o}`,content:'""',pointerEvents:"none"},"&::before":{insetInlineStart:0},"&::after":{insetInlineEnd:0}},[`${t}-ping-left`]:{[`&:not(${t}-has-fix-left) ${t}-container::before`]:{boxShadow:`inset 10px 0 8px -8px ${s}`},[`
          ${t}-cell-fix-left-first::after,
          ${t}-cell-fix-left-last::after
        `]:{boxShadow:`inset 10px 0 8px -8px ${s}`},[`${t}-cell-fix-left-last::before`]:{backgroundColor:"transparent !important"}},[`${t}-ping-right`]:{[`&:not(${t}-has-fix-right) ${t}-container::after`]:{boxShadow:`inset -10px 0 8px -8px ${s}`},[`
          ${t}-cell-fix-right-first::after,
          ${t}-cell-fix-right-last::after
        `]:{boxShadow:`inset -10px 0 8px -8px ${s}`}},[`${t}-fixed-column-gapped`]:{[`
        ${t}-cell-fix-left-first::after,
        ${t}-cell-fix-left-last::after,
        ${t}-cell-fix-right-first::after,
        ${t}-cell-fix-right-last::after
      `]:{boxShadow:"none"}}}}},bc=e=>{const{componentCls:t,antCls:r,margin:n}=e;return{[`${t}-wrapper`]:{[`${t}-pagination${r}-pagination`]:{margin:`${W(n)} 0`},[`${t}-pagination`]:{display:"flex",flexWrap:"wrap",rowGap:e.paddingXS,"> *":{flex:"none"},"&-left":{justifyContent:"flex-start"},"&-center":{justifyContent:"center"},"&-right":{justifyContent:"flex-end"}}}}},xc=e=>{const{componentCls:t,tableRadius:r}=e;return{[`${t}-wrapper`]:{[t]:{[`${t}-title, ${t}-header`]:{borderRadius:`${W(r)} ${W(r)} 0 0`},[`${t}-title + ${t}-container`]:{borderStartStartRadius:0,borderStartEndRadius:0,[`${t}-header, table`]:{borderRadius:0},"table > thead > tr:first-child":{"th:first-child, th:last-child, td:first-child, td:last-child":{borderRadius:0}}},"&-container":{borderStartStartRadius:r,borderStartEndRadius:r,"table > thead > tr:first-child":{"> *:first-child":{borderStartStartRadius:r},"> *:last-child":{borderStartEndRadius:r}}},"&-footer":{borderRadius:`0 0 ${W(r)} ${W(r)}`}}}}},Cc=e=>{const{componentCls:t}=e;return{[`${t}-wrapper-rtl`]:{direction:"rtl",table:{direction:"rtl"},[`${t}-pagination-left`]:{justifyContent:"flex-end"},[`${t}-pagination-right`]:{justifyContent:"flex-start"},[`${t}-row-expand-icon`]:{float:"right","&::after":{transform:"rotate(-90deg)"},"&-collapsed::before":{transform:"rotate(180deg)"},"&-collapsed::after":{transform:"rotate(0deg)"}},[`${t}-container`]:{"&::before":{insetInlineStart:"unset",insetInlineEnd:0},"&::after":{insetInlineStart:0,insetInlineEnd:"unset"},[`${t}-row-indent`]:{float:"right"}}}}},Sc=e=>{const{componentCls:t,antCls:r,iconCls:n,fontSizeIcon:o,padding:a,paddingXS:d,headerIconColor:l,headerIconHoverColor:c,tableSelectionColumnWidth:s,tableSelectedRowBg:v,tableSelectedRowHoverBg:u,tableRowHoverBg:f,tablePaddingHorizontal:p,calc:g}=e;return{[`${t}-wrapper`]:{[`${t}-selection-col`]:{width:s,[`&${t}-selection-col-with-dropdown`]:{width:g(s).add(o).add(g(a).div(4)).equal()}},[`${t}-bordered ${t}-selection-col`]:{width:g(s).add(g(d).mul(2)).equal(),[`&${t}-selection-col-with-dropdown`]:{width:g(s).add(o).add(g(a).div(4)).add(g(d).mul(2)).equal()}},[`
        table tr th${t}-selection-column,
        table tr td${t}-selection-column,
        ${t}-selection-column
      `]:{paddingInlineEnd:e.paddingXS,paddingInlineStart:e.paddingXS,textAlign:"center",[`${r}-radio-wrapper`]:{marginInlineEnd:0}},[`table tr th${t}-selection-column${t}-cell-fix-left`]:{zIndex:g(e.zIndexTableFixed).add(1).equal({unit:!1})},[`table tr th${t}-selection-column::after`]:{backgroundColor:"transparent !important"},[`${t}-selection`]:{position:"relative",display:"inline-flex",flexDirection:"column"},[`${t}-selection-extra`]:{position:"absolute",top:0,zIndex:1,cursor:"pointer",transition:`all ${e.motionDurationSlow}`,marginInlineStart:"100%",paddingInlineStart:W(g(p).div(4).equal()),[n]:{color:l,fontSize:o,verticalAlign:"baseline","&:hover":{color:c}}},[`${t}-tbody`]:{[`${t}-row`]:{[`&${t}-row-selected`]:{[`> ${t}-cell`]:{background:v,"&-row-hover":{background:u}}},[`> ${t}-cell-row-hover`]:{background:f}}}}}},wc=e=>{const{componentCls:t,tableExpandColumnWidth:r,calc:n}=e,o=(a,d,l,c)=>({[`${t}${t}-${a}`]:{fontSize:c,[`
        ${t}-title,
        ${t}-footer,
        ${t}-cell,
        ${t}-thead > tr > th,
        ${t}-tbody > tr > th,
        ${t}-tbody > tr > td,
        tfoot > tr > th,
        tfoot > tr > td
      `]:{padding:`${W(d)} ${W(l)}`},[`${t}-filter-trigger`]:{marginInlineEnd:W(n(l).div(2).mul(-1).equal())},[`${t}-expanded-row-fixed`]:{margin:`${W(n(d).mul(-1).equal())} ${W(n(l).mul(-1).equal())}`},[`${t}-tbody`]:{[`${t}-wrapper:only-child ${t}`]:{marginBlock:W(n(d).mul(-1).equal()),marginInline:`${W(n(r).sub(l).equal())} ${W(n(l).mul(-1).equal())}`}},[`${t}-selection-extra`]:{paddingInlineStart:W(n(l).div(4).equal())}}});return{[`${t}-wrapper`]:Object.assign(Object.assign({},o("middle",e.tablePaddingVerticalMiddle,e.tablePaddingHorizontalMiddle,e.tableFontSizeMiddle)),o("small",e.tablePaddingVerticalSmall,e.tablePaddingHorizontalSmall,e.tableFontSizeSmall))}},Ec=e=>{const{componentCls:t,marginXXS:r,fontSizeIcon:n,headerIconColor:o,headerIconHoverColor:a}=e;return{[`${t}-wrapper`]:{[`${t}-thead th${t}-column-has-sorters`]:{outline:"none",cursor:"pointer",transition:`all ${e.motionDurationSlow}, left 0s`,"&:hover":{background:e.tableHeaderSortHoverBg,"&::before":{backgroundColor:"transparent !important"}},"&:focus-visible":{color:e.colorPrimary},[`
          &${t}-cell-fix-left:hover,
          &${t}-cell-fix-right:hover
        `]:{background:e.tableFixedHeaderSortActiveBg}},[`${t}-thead th${t}-column-sort`]:{background:e.tableHeaderSortBg,"&::before":{backgroundColor:"transparent !important"}},[`td${t}-column-sort`]:{background:e.tableBodySortBg},[`${t}-column-title`]:{position:"relative",zIndex:1,flex:1,minWidth:0},[`${t}-column-sorters`]:{display:"flex",flex:"auto",alignItems:"center",justifyContent:"space-between","&::after":{position:"absolute",inset:0,width:"100%",height:"100%",content:'""'}},[`${t}-column-sorters-tooltip-target-sorter`]:{"&::after":{content:"none"}},[`${t}-column-sorter`]:{marginInlineStart:r,color:o,fontSize:0,transition:`color ${e.motionDurationSlow}`,"&-inner":{display:"inline-flex",flexDirection:"column",alignItems:"center"},"&-up, &-down":{fontSize:n,"&.active":{color:e.colorPrimary}},[`${t}-column-sorter-up + ${t}-column-sorter-down`]:{marginTop:"-0.3em"}},[`${t}-column-sorters:hover ${t}-column-sorter`]:{color:a}}}},$c=e=>{const{componentCls:t,opacityLoading:r,tableScrollThumbBg:n,tableScrollThumbBgHover:o,tableScrollThumbSize:a,tableScrollBg:d,zIndexTableSticky:l,stickyScrollBarBorderRadius:c,lineWidth:s,lineType:v,tableBorderColor:u}=e,f=`${W(s)} ${v} ${u}`;return{[`${t}-wrapper`]:{[`${t}-sticky`]:{"&-holder":{position:"sticky",zIndex:l,background:e.colorBgContainer},"&-scroll":{position:"sticky",bottom:0,height:`${W(a)} !important`,zIndex:l,display:"flex",alignItems:"center",background:d,borderTop:f,opacity:r,"&:hover":{transformOrigin:"center bottom"},"&-bar":{height:a,backgroundColor:n,borderRadius:c,transition:`all ${e.motionDurationSlow}, transform 0s`,position:"absolute",bottom:0,"&:hover, &-active":{backgroundColor:o}}}}}}},no=e=>{const{componentCls:t,lineWidth:r,tableBorderColor:n,calc:o}=e,a=`${W(r)} ${e.lineType} ${n}`;return{[`${t}-wrapper`]:{[`${t}-summary`]:{position:"relative",zIndex:e.zIndexTableFixed,background:e.tableBg,"> tr":{"> th, > td":{borderBottom:a}}},[`div${t}-summary`]:{boxShadow:`0 ${W(o(r).mul(-1).equal())} 0 ${n}`}}}},kc=e=>{const{componentCls:t,motionDurationMid:r,lineWidth:n,lineType:o,tableBorderColor:a,calc:d}=e,l=`${W(n)} ${o} ${a}`,c=`${t}-expanded-row-cell`;return{[`${t}-wrapper`]:{[`${t}-tbody-virtual`]:{[`${t}-tbody-virtual-holder-inner`]:{[`
            & > ${t}-row, 
            & > div:not(${t}-row) > ${t}-row
          `]:{display:"flex",boxSizing:"border-box",width:"100%"}},[`${t}-cell`]:{borderBottom:l,transition:`background ${r}`},[`${t}-expanded-row`]:{[`${c}${c}-fixed`]:{position:"sticky",insetInlineStart:0,overflow:"hidden",width:`calc(var(--virtual-width) - ${W(n)})`,borderInlineEnd:"none"}}},[`${t}-bordered`]:{[`${t}-tbody-virtual`]:{"&:after":{content:'""',insetInline:0,bottom:0,borderBottom:l,position:"absolute"},[`${t}-cell`]:{borderInlineEnd:l,[`&${t}-cell-fix-right-first:before`]:{content:'""',position:"absolute",insetBlock:0,insetInlineStart:d(n).mul(-1).equal(),borderInlineStart:l}}},[`&${t}-virtual`]:{[`${t}-placeholder ${t}-cell`]:{borderInlineEnd:l,borderBottom:l}}}}}},Nc=e=>{const{componentCls:t,fontWeightStrong:r,tablePaddingVertical:n,tablePaddingHorizontal:o,tableExpandColumnWidth:a,lineWidth:d,lineType:l,tableBorderColor:c,tableFontSize:s,tableBg:v,tableRadius:u,tableHeaderTextColor:f,motionDurationMid:p,tableHeaderBg:g,tableHeaderCellSplitColor:b,tableFooterTextColor:m,tableFooterBg:y,calc:C}=e,x=`${W(d)} ${l} ${c}`;return{[`${t}-wrapper`]:Object.assign(Object.assign({clear:"both",maxWidth:"100%"},Da()),{[t]:Object.assign(Object.assign({},St(e)),{fontSize:s,background:v,borderRadius:`${W(u)} ${W(u)} 0 0`,scrollbarColor:`${e.tableScrollThumbBg} ${e.tableScrollBg}`}),table:{width:"100%",textAlign:"start",borderRadius:`${W(u)} ${W(u)} 0 0`,borderCollapse:"separate",borderSpacing:0},[`
          ${t}-cell,
          ${t}-thead > tr > th,
          ${t}-tbody > tr > th,
          ${t}-tbody > tr > td,
          tfoot > tr > th,
          tfoot > tr > td
        `]:{position:"relative",padding:`${W(n)} ${W(o)}`,overflowWrap:"break-word"},[`${t}-title`]:{padding:`${W(n)} ${W(o)}`},[`${t}-thead`]:{"\n          > tr > th,\n          > tr > td\n        ":{position:"relative",color:f,fontWeight:r,textAlign:"start",background:g,borderBottom:x,transition:`background ${p} ease`,"&[colspan]:not([colspan='1'])":{textAlign:"center"},[`&:not(:last-child):not(${t}-selection-column):not(${t}-row-expand-icon-cell):not([colspan])::before`]:{position:"absolute",top:"50%",insetInlineEnd:0,width:1,height:"1.6em",backgroundColor:b,transform:"translateY(-50%)",transition:`background-color ${p}`,content:'""'}},"> tr:not(:last-child) > th[colspan]":{borderBottom:0}},[`${t}-tbody`]:{"> tr":{"> th, > td":{transition:`background ${p}, border-color ${p}`,borderBottom:x,[`
              > ${t}-wrapper:only-child,
              > ${t}-expanded-row-fixed > ${t}-wrapper:only-child
            `]:{[t]:{marginBlock:W(C(n).mul(-1).equal()),marginInline:`${W(C(a).sub(o).equal())}
                ${W(C(o).mul(-1).equal())}`,[`${t}-tbody > tr:last-child > td`]:{borderBottomWidth:0,"&:first-child, &:last-child":{borderRadius:0}}}}},"> th":{position:"relative",color:f,fontWeight:r,textAlign:"start",background:g,borderBottom:x,transition:`background ${p} ease`}}},[`${t}-footer`]:{padding:`${W(n)} ${W(o)}`,color:m,background:y}})}},Rc=e=>{const{colorFillAlter:t,colorBgContainer:r,colorTextHeading:n,colorFillSecondary:o,colorFillContent:a,controlItemBgActive:d,controlItemBgActiveHover:l,padding:c,paddingSM:s,paddingXS:v,colorBorderSecondary:u,borderRadiusLG:f,controlHeight:p,colorTextPlaceholder:g,fontSize:b,fontSizeSM:m,lineHeight:y,lineWidth:C,colorIcon:x,colorIconHover:w,opacityLoading:S,controlInteractiveSize:E}=e,N=new Lt(o).onBackground(r).toHexString(),M=new Lt(a).onBackground(r).toHexString(),h=new Lt(t).onBackground(r).toHexString(),O=new Lt(x),T=new Lt(w),R=E/2-C,K=R*2+C*3;return{headerBg:h,headerColor:n,headerSortActiveBg:N,headerSortHoverBg:M,bodySortBg:h,rowHoverBg:h,rowSelectedBg:d,rowSelectedHoverBg:l,rowExpandedBg:t,cellPaddingBlock:c,cellPaddingInline:c,cellPaddingBlockMD:s,cellPaddingInlineMD:v,cellPaddingBlockSM:v,cellPaddingInlineSM:v,borderColor:u,headerBorderRadius:f,footerBg:h,footerColor:n,cellFontSize:b,cellFontSizeMD:b,cellFontSizeSM:b,headerSplitColor:u,fixedHeaderSortActiveBg:N,headerFilterHoverBg:a,filterDropdownMenuBg:r,filterDropdownBg:r,expandIconBg:r,selectionColumnWidth:p,stickyScrollBarBg:g,stickyScrollBarBorderRadius:100,expandIconMarginTop:(b*y-C*3)/2-Math.ceil((m*1.4-C*3)/2),headerIconColor:O.clone().setA(O.a*S).toRgbString(),headerIconHoverColor:T.clone().setA(T.a*S).toRgbString(),expandIconHalfInner:R,expandIconSize:K,expandIconScale:E/K}},ro=2,Ic=rn("Table",e=>{const{colorTextHeading:t,colorSplit:r,colorBgContainer:n,controlInteractiveSize:o,headerBg:a,headerColor:d,headerSortActiveBg:l,headerSortHoverBg:c,bodySortBg:s,rowHoverBg:v,rowSelectedBg:u,rowSelectedHoverBg:f,rowExpandedBg:p,cellPaddingBlock:g,cellPaddingInline:b,cellPaddingBlockMD:m,cellPaddingInlineMD:y,cellPaddingBlockSM:C,cellPaddingInlineSM:x,borderColor:w,footerBg:S,footerColor:E,headerBorderRadius:N,cellFontSize:M,cellFontSizeMD:h,cellFontSizeSM:O,headerSplitColor:T,fixedHeaderSortActiveBg:R,headerFilterHoverBg:K,filterDropdownBg:k,expandIconBg:$,selectionColumnWidth:I,stickyScrollBarBg:P,calc:_}=e,D=on(e,{tableFontSize:M,tableBg:n,tableRadius:N,tablePaddingVertical:g,tablePaddingHorizontal:b,tablePaddingVerticalMiddle:m,tablePaddingHorizontalMiddle:y,tablePaddingVerticalSmall:C,tablePaddingHorizontalSmall:x,tableBorderColor:w,tableHeaderTextColor:d,tableHeaderBg:a,tableFooterTextColor:E,tableFooterBg:S,tableHeaderCellSplitColor:T,tableHeaderSortBg:l,tableHeaderSortHoverBg:c,tableBodySortBg:s,tableFixedHeaderSortActiveBg:R,tableHeaderFilterActiveBg:K,tableFilterDropdownBg:k,tableRowHoverBg:v,tableSelectedRowBg:u,tableSelectedRowHoverBg:f,zIndexTableFixed:ro,zIndexTableSticky:_(ro).add(1).equal({unit:!1}),tableFontSizeMiddle:h,tableFontSizeSmall:O,tableSelectionColumnWidth:I,tableExpandIconBg:$,tableExpandColumnWidth:_(o).add(_(e.padding).mul(2)).equal(),tableExpandedRowBg:p,tableFilterDropdownWidth:120,tableFilterDropdownHeight:264,tableFilterDropdownSearchWidth:140,tableScrollThumbSize:8,tableScrollThumbBg:P,tableScrollThumbBgHover:t,tableScrollBg:r});return[Nc(D),bc(D),no(D),Ec(D),hc(D),vc(D),xc(D),gc(D),no(D),mc(D),Sc(D),yc(D),$c(D),pc(D),wc(D),Cc(D),kc(D)]},Rc,{unitless:{expandIconScale:!0}}),Kc=[],Oc=(e,t)=>{var r,n;const{prefixCls:o,className:a,rootClassName:d,style:l,size:c,bordered:s,dropdownPrefixCls:v,dataSource:u,pagination:f,rowSelection:p,rowKey:g="key",rowClassName:b,columns:m,children:y,childrenColumnName:C,onChange:x,getPopupContainer:w,loading:S,expandIcon:E,expandable:N,expandedRowRender:M,expandIconColumnIndex:h,indentSize:O,scroll:T,sortDirections:R,locale:K,showSorterTooltip:k={target:"full-header"},virtual:$}=e;jn();const I=i.useMemo(()=>m||Gn(y),[m,y]),P=i.useMemo(()=>I.some(oe=>oe.responsive),[I]),_=Ma(P),D=i.useMemo(()=>{const oe=new Set(Object.keys(_).filter(ae=>_[ae]));return I.filter(ae=>!ae.responsive||ae.responsive.some(Oe=>oe.has(Oe)))},[I,_]),V=io(e,["className","style","columns"]),{locale:Q=Ba,direction:Y,table:te,renderEmpty:ye,getPrefixCls:Ce,getPopupContainer:we}=i.useContext(Et),J=lo(c),ee=Object.assign(Object.assign({},Q.Table),K),Ee=u||Kc,ve=Ce("table",o),q=Ce("dropdown",v),[,X]=fo(),H=_n(ve),[j,Z,le]=Ic(ve,H),pe=Object.assign(Object.assign({childrenColumnName:C,expandIconColumnIndex:h},N),{expandIcon:(r=N==null?void 0:N.expandIcon)!==null&&r!==void 0?r:(n=te==null?void 0:te.expandable)===null||n===void 0?void 0:n.expandIcon}),{childrenColumnName:ke="children"}=pe,Fe=i.useMemo(()=>Ee.some(oe=>oe==null?void 0:oe[ke])?"nest":M||N!=null&&N.expandedRowRender?"row":null,[Ee]),Re={body:i.useRef(null)},L=$s(ve),A=i.useRef(null),re=i.useRef(null);ws(t,()=>Object.assign(Object.assign({},re.current),{nativeElement:A.current}));const be=i.useMemo(()=>typeof g=="function"?g:oe=>oe==null?void 0:oe[g],[g]),[ie]=oc(Ee,ke,be),Ne={},Te=(oe,ae,Oe=!1)=>{var Be,je,Ze,et;const We=Object.assign(Object.assign({},Ne),oe);Oe&&((Be=Ne.resetPagination)===null||Be===void 0||Be.call(Ne),!((je=We.pagination)===null||je===void 0)&&je.current&&(We.pagination.current=1),f&&((Ze=f.onChange)===null||Ze===void 0||Ze.call(f,1,(et=We.pagination)===null||et===void 0?void 0:et.pageSize))),T&&T.scrollToFirstRowOnChange!==!1&&Re.body.current&&qa(0,{getContainer:()=>Re.body.current}),x==null||x(We.pagination,We.filters,We.sorter,{currentDataSource:Dn(Bn(Ee,We.sorterStates,ke),We.filterStates,ke),action:ae})},Le=(oe,ae)=>{Te({sorter:oe,sorterStates:ae},"sort",!1)},[me,ce,U,z]=cc({prefixCls:ve,mergedColumns:D,onSorterChange:Le,sortDirections:R||["ascend","descend"],tableLocale:ee,showSorterTooltip:k}),$e=i.useMemo(()=>Bn(Ee,ce,ke),[Ee,ce]);Ne.sorter=z(),Ne.sorterStates=ce;const Ie=(oe,ae)=>{Te({filters:oe,filterStates:ae},"filter",!0)},[ne,Pe,xe]=rc({prefixCls:ve,locale:ee,dropdownPrefixCls:q,mergedColumns:D,onFilterChange:Ie,getPopupContainer:w||we,rootClassName:G(d,H)}),De=Dn($e,Pe,ke);Ne.filters=xe,Ne.filterStates=Pe;const Ue=i.useMemo(()=>{const oe={};return Object.keys(xe).forEach(ae=>{xe[ae]!==null&&(oe[ae]=xe[ae])}),Object.assign(Object.assign({},U),{filters:oe})},[U,xe]),[Ve]=dc(Ue),ut=(oe,ae)=>{Te({pagination:Object.assign(Object.assign({},Ne.pagination),{current:oe,pageSize:ae})},"paginate")},[Ke,$t]=ic(De.length,ut,f);Ne.pagination=f===!1?{}:lc(Ke,f),Ne.resetPagination=$t;const kt=i.useMemo(()=>{if(f===!1||!Ke.pageSize)return De;const{current:oe=1,total:ae,pageSize:Oe=Jo}=Ke;return De.length<ae?De.length>Oe?De.slice((oe-1)*Oe,oe*Oe):De:De.slice((oe-1)*Oe,oe*Oe)},[!!f,De,Ke==null?void 0:Ke.current,Ke==null?void 0:Ke.pageSize,Ke==null?void 0:Ke.total]),[qe,nt]=Cs({prefixCls:ve,data:De,pageData:kt,getRowKey:be,getRecordByKey:ie,expandType:Fe,childrenColumnName:ke,locale:ee,getPopupContainer:w||we},p),Qe=(oe,ae,Oe)=>{let Be;return typeof b=="function"?Be=G(b(oe,ae,Oe)):Be=G(b),G({[`${ve}-row-selected`]:nt.has(be(oe,ae))},Be)};pe.__PARENT_RENDER_ICON__=pe.expandIcon,pe.expandIcon=pe.expandIcon||E||Es(ee),Fe==="nest"&&pe.expandIconColumnIndex===void 0?pe.expandIconColumnIndex=p?1:0:pe.expandIconColumnIndex>0&&p&&(pe.expandIconColumnIndex-=1),typeof pe.indentSize!="number"&&(pe.indentSize=typeof O=="number"?O:15);const Xe=i.useCallback(oe=>Ve(qe(ne(me(oe)))),[me,ne,qe]);let Ae,Ye;if(f!==!1&&(Ke!=null&&Ke.total)){let oe;Ke.size?oe=Ke.size:oe=J==="small"||J==="middle"?"small":void 0;const ae=je=>i.createElement(ja,Object.assign({},Ke,{className:G(`${ve}-pagination ${ve}-pagination-${je}`,Ke.className),size:oe})),Oe=Y==="rtl"?"left":"right",{position:Be}=Ke;if(Be!==null&&Array.isArray(Be)){const je=Be.find(We=>We.includes("top")),Ze=Be.find(We=>We.includes("bottom")),et=Be.every(We=>`${We}`=="none");!je&&!Ze&&!et&&(Ye=ae(Oe)),je&&(Ae=ae(je.toLowerCase().replace("top",""))),Ze&&(Ye=ae(Ze.toLowerCase().replace("bottom","")))}else Ye=ae(Oe)}let bt;typeof S=="boolean"?bt={spinning:S}:typeof S=="object"&&(bt=Object.assign({spinning:!0},S));const ft=G(le,H,`${ve}-wrapper`,te==null?void 0:te.className,{[`${ve}-wrapper-rtl`]:Y==="rtl"},a,d,Z),ot=Object.assign(Object.assign({},te==null?void 0:te.style),l),cn=typeof(K==null?void 0:K.emptyText)<"u"?K.emptyText:(ye==null?void 0:ye("Table"))||i.createElement(_a,{componentName:"Table"}),dn=$?fc:uc,Xt={},un=i.useMemo(()=>{const{fontSize:oe,lineHeight:ae,lineWidth:Oe,padding:Be,paddingXS:je,paddingSM:Ze}=X,et=Math.floor(oe*ae);switch(J){case"middle":return Ze*2+et+Oe;case"small":return je*2+et+Oe;default:return Be*2+et+Oe}},[X,J]);return $&&(Xt.listItemHeight=un),j(i.createElement("div",{ref:A,className:ft,style:ot},i.createElement($o,Object.assign({spinning:!1},bt),Ae,i.createElement(dn,Object.assign({},Xt,V,{ref:re,columns:D,direction:Y,expandable:pe,prefixCls:ve,className:G({[`${ve}-middle`]:J==="middle",[`${ve}-small`]:J==="small",[`${ve}-bordered`]:s,[`${ve}-empty`]:Ee.length===0},le,H,Z),data:kt,rowKey:be,rowClassName:Qe,emptyText:cn,internalHooks:qt,internalRefs:Re,transformColumns:Xe,getContainerWidth:L})),Ye)))},Tc=i.forwardRef(Oc),Pc=(e,t)=>{const r=i.useRef(0);return r.current+=1,i.createElement(Tc,Object.assign({},e,{ref:t,_renderTimes:r.current}))},yt=i.forwardRef(Pc);yt.SELECTION_COLUMN=vt;yt.EXPAND_COLUMN=ct;yt.SELECTION_ALL=In;yt.SELECTION_INVERT=Kn;yt.SELECTION_NONE=On;yt.Column=us;yt.ColumnGroup=fs;yt.Summary=Ko;export{yt as F,Wt as R,$o as S,Qn as T,vs as U,It as a,Ws as b,Wn as c,Gs as d,er as e,js as g,Ga as i};
