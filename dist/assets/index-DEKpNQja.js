import{r as n,F as i,j as t,u as x,d as ie,R as oe,C as u,S as ce,B as p,T as M}from"./index-De_f0oL2.js";import{R as de,o as D,d as me}from"./down-BCLNnN1h.js";import{s as o,i as y}from"./service-D0isGGPv.js";import{R as he}from"./index-BmnYJy3v.js";import{f as r}from"./format-ChnqMkgG.js";import{s as P}from"./index-BcPP1N8I.js";import{D as ue}from"./index-CdSZ9YgQ.js";import{S as g}from"./index-BWJehDyc.js";import{R as fe,a as xe}from"./FullscreenOutlined-DzCTibKW.js";import"./Table-D-iLeFE-.js";import"./useMultipleSelect-B0dEIXT-.js";const Te=()=>{var E;const w=[{title:"单位",key:"cityName",dataIndex:"cityName",align:"center",fixed:"left",width:120,children:[],render:(e,a)=>t.jsx(M,{title:e,children:t.jsx("div",{className:o.over_ellipsis,children:e})})},{title:"部室/营服中心",key:"orgName",dataIndex:"orgName",align:"center",fixed:"left",width:160,children:[],render:(e,a)=>t.jsx(M,{title:e,children:t.jsx("div",{className:o.over_ellipsis,children:e})})},{title:"岗位",key:"position",dataIndex:"position",align:"center",fixed:"left",width:75,children:[]},{title:"当月",key:"level",dataIndex:"level",align:"center",width:115,children:[{title:"薪酬最高",key:"monthSalMax",dataIndex:"monthSalMax",align:"center",children:[],width:70,render:e=>t.jsx("span",{children:r(e)})},{title:"薪酬最低",key:"monthSalMin",dataIndex:"monthSalMin",align:"center",children:[],width:70,render:e=>t.jsx("span",{children:r(e)})},{title:"平均薪酬",key:"monthSalAval",dataIndex:"monthSalAval",align:"center",children:[],width:70,render:e=>t.jsx("span",{children:r(e)})},{title:"平均绩效",key:"monthAchieveAval",dataIndex:"monthAchieveAval",align:"center",children:[],width:70,render:e=>t.jsx("span",{children:r(e)})}]},{title:"全年累计",key:"level",dataIndex:"level",align:"center",width:115,children:[{title:"考评优秀平均绩效",key:"yearLv1AchieveAval",dataIndex:"yearLv1AchieveAval",align:"center",children:[],width:70,render:e=>t.jsx("span",{children:r(e)})},{title:"考评良好平均绩效",key:"yearLv2AchieveAval",dataIndex:"yearLv2AchieveAval",align:"center",children:[],width:70,render:e=>t.jsx("span",{children:r(e)})},{title:"考评称职平均绩效",key:"yearLv3AchieveAval",dataIndex:"yearLv3AchieveAval",align:"center",children:[],width:70,render:e=>t.jsx("span",{children:r(e)})},{title:"最高",key:"yearMax",dataIndex:"yearMax",align:"center",children:[],width:70,render:e=>t.jsx("span",{children:r(e)})},{title:"最低",key:"yearMin",dataIndex:"yearMin",align:"center",children:[],width:70,render:e=>t.jsx("span",{children:r(e)})},{title:"绩优绩平",key:"yearGoodRate",dataIndex:"yearGoodRate",align:"center",children:[],width:70,render:(e,a)=>t.jsx("span",{style:{color:e>1.4?"#ff4d4f":"inherit"},children:e})},{title:"月人均绩效",key:"yearAchieveAval",dataIndex:"yearAchieveAval",align:"center",children:[],width:70,render:e=>t.jsx("span",{children:r(e)})}]}],S=n.useRef(null),j=n.useRef(null),v=n.useRef(null),[c]=i.useForm(),[z,F]=n.useState(0),[A,$]=n.useState([]),[H,I]=n.useState(!1),[Y,O]=n.useState(w),[q,G]=n.useState([]),[f,V]=n.useState(0),[U,B]=n.useState([]),[b,N]=n.useState(!0),[l,T]=n.useState({total:0,pageNum:1,pageSize:50}),{runAsync:C}=x(y.getEnumType,{manual:!0}),{runAsync:J}=x(y.getIdxPosSalAnalysis,{manual:!0}),{runAsync:K}=x(y.exportIdxPosSalAnalysis,{manual:!0});n.useEffect(()=>(O(w),Q(),k(),h(),window.addEventListener("resize",R),()=>{window.removeEventListener("resize",R)}),[]);const R=()=>{d()};n.useEffect(()=>{d()},[(document.querySelector(".compensation_analysis_table .ant-table-header")||{}).offsetHeight]),n.useEffect(()=>{d()},[A]),n.useEffect(()=>{f>0&&h()},[f]),n.useEffect(()=>{(l==null?void 0:l.total)>0&&h()},[l.pageNum,l.pageSize]);const d=()=>{var s;const e=(document.querySelector(".compensation_analysis_table .ant-table-header")||{}).offsetHeight||0,a=(document.querySelector(".compensation_analysis_table .ant-table-pagination")||{}).offsetHeight||26;e&&a&&F(((s=j.current)==null?void 0:s.offsetHeight)-(v.current.offsetHeight+e+a))},Q=async()=>{const[[e,a],[s,m]]=await Promise.all([C({code:"1010",tag:1}),C({code:"IDX_POS_SAL_ANALYSIS_POSITION"})]);e||s||(a.STATUS==="0000"&&B(a.DATA),m.STATUS==="0000"&&G(m.DATA))},h=async()=>{var m,_;const e=c.getFieldsValue();I(!0);const[a,s]=await J({...e,cycleId:(m=e==null?void 0:e.cycleId)==null?void 0:m.format("YYYYMM"),...l});if(I(!1),a){P.error((s==null?void 0:s.DATA)||(s==null?void 0:s.MESSAGE)||"调用失败");return}if(s.STATUS==="0000"){const{DATA:{data:ne}}=s,le=ne.map((L,re)=>({...L,key:L.id||re}));$(le),T({...l,total:(_=s.DATA)==null?void 0:_.totalCount})}else P.error((s==null?void 0:s.MESSAGE)||(s==null?void 0:s.DATA)||"调用失败")},k=()=>{const e=ie().subtract(1,"month");c.setFieldsValue({cycleId:e})},W=e=>{console.log("Success:",e),h()},X=async()=>{var e;try{D("正在导出",0,"loading");const a=c.getFieldsValue(),s=await K({...a,cycleId:(e=a==null?void 0:a.cycleId)==null?void 0:e.format("YYYYMM"),orgaId:a==null?void 0:a.cityId});me(s)}catch(a){D("导出失败",1,"error"),console.error("Download failed:",a)}},Z=()=>{const e=f+1;c.resetFields(),k(),V(e)},ee=e=>{const a={total:e==null?void 0:e.total,pageNum:e==null?void 0:e.current,pageSize:e==null?void 0:e.pageSize};T(a)},te=()=>{},ae=(e,a)=>{var s;return((a==null?void 0:a.enumName)??"").toLowerCase().includes((s=e.toLowerCase())==null?void 0:s.trim())},se=(e,a)=>{var s;return((a==null?void 0:a.value)??"").toLowerCase().includes((s=e.toLowerCase())==null?void 0:s.trim())};return t.jsxs("div",{className:`pt-[0.5rem] px-[0rem] h-full flex flex-col ${o.employment_page}`,children:[t.jsx("div",{ref:S,className:"bg-white pt-2 px-8 mb-2",children:t.jsx(i,{form:c,initialValues:{tag:""},onFinish:W,autoComplete:"off",children:t.jsxs(oe,{gutter:24,children:[t.jsx(u,{span:6,children:t.jsx(i.Item,{label:"月份",name:"cycleId",wrapperCol:{span:20},className:"mb-[0.5rem]",children:t.jsx(ue,{className:"w-full",allowClear:!1,onChange:te,picker:"month"})})}),t.jsx(u,{span:6,children:t.jsx(i.Item,{label:"单位",name:"cityId",wrapperCol:{span:20},className:"mb-[0.5rem]",children:t.jsx(g,{placeholder:"请选择",className:"w-full",allowClear:!0,showSearch:!0,filterOption:ae,options:U,fieldNames:{value:"enumId",label:"enumName"}})})}),t.jsx(u,{span:6,children:t.jsx(i.Item,{label:"岗位",name:"position",wrapperCol:{span:20},className:"mb-[0.5rem]",children:t.jsx(g,{placeholder:"请选择",className:"w-full",allowClear:!0,showSearch:!0,filterOption:se,children:q.map(e=>{const{enumName:a}=e;return t.jsx(g.Option,{value:a,children:a},a)})})})}),t.jsx(u,{span:6,children:t.jsx(i.Item,{labelCol:{span:0},wrapperCol:{span:24},className:"mb-[0.5rem]",children:t.jsxs(ce,{size:"small",children:[t.jsx(p,{type:"primary",htmlType:"submit",children:"查询"}),t.jsx(p,{htmlType:"button",onClick:()=>Z(),children:"重置"})]})})})]})})}),t.jsxs("div",{ref:j,className:"relative bg-white px-5 pt-2.5",style:{height:`calc(100% - ${((E=S.current)==null?void 0:E.offsetHeight)+15}px)`},children:[t.jsxs("div",{ref:v,className:`flex justify-between items-center overflow-hidden mb-2 ${o.animation_box} ${b?"h-[1.6rem]":"h-0"}`,children:[t.jsxs("div",{className:"flex ",children:[b?t.jsx(fe,{className:`${o.shousuo_icon} text-[1rem]`,onClick:()=>{N(!1),setTimeout(()=>{d()},200)}}):t.jsx(xe,{className:`${o.shousuo_icon} absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10`,onClick:()=>{N(!0),setTimeout(()=>{d()},200)}}),t.jsxs("div",{className:"font-bold text-[0.8rem] ml-3",children:["数据列表",t.jsx("span",{className:"text-[0.7rem]",style:{color:"#939393"},children:"（单位：万元）"})]})]}),t.jsx(p,{danger:!0,ghost:!0,icon:t.jsx(de,{}),onClick:()=>X(),children:"导出"})]}),t.jsx(he,{className:"compensation_analysis_table",rowClassName:(e,a)=>a%2===1?"customRow odd":"customRow even",columns:Y,dataSource:A,loading:H,bordered:!0,scroll:{y:`calc(${z}px - 0.625rem - 1.6rem - 0.5rem)`},onChange:ee,pagination:{...l,total:l==null?void 0:l.total,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50"]}})]})]})};export{Te as default};
