import{r as n,I as Ot,_ as Pt,D as S,F as g,j as e,y as E,m as Kt,ce as Ut,B as b,V as Vt,T as qe,R as Gt,C as ye,O as te,N as Bt}from"./index-De_f0oL2.js";import{s as p}from"./index-BcPP1N8I.js";import{S as O}from"./index-BWJehDyc.js";import{e as $t,F as Ke}from"./Table-D-iLeFE-.js";import{d as Qt}from"./utils-BHq6Um7U.js";import{s as _t}from"./Index.module-C_CsuTds.js";import{M as z}from"./index-Dck5cc4J.js";import"./useMultipleSelect-B0dEIXT-.js";var Jt={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M758.2 839.1C851.8 765.9 912 651.9 912 523.9 912 303 733.5 124.3 512.6 124 291.4 123.7 112 302.8 112 523.9c0 125.2 57.5 236.9 147.6 310.2 3.5 2.8 8.6 2.2 11.4-1.3l39.4-50.5c2.7-3.4 2.1-8.3-1.2-11.1-8.1-6.6-15.9-13.7-23.4-21.2a318.64 318.64 0 01-68.6-101.7C200.4 609 192 567.1 192 523.9s8.4-85.1 25.1-124.5c16.1-38.1 39.2-72.3 68.6-101.7 29.4-29.4 63.6-52.5 101.7-68.6C426.9 212.4 468.8 204 512 204s85.1 8.4 124.5 25.1c38.1 16.1 72.3 39.2 101.7 68.6 29.4 29.4 52.5 63.6 68.6 101.7 16.7 39.4 25.1 81.3 25.1 124.5s-8.4 85.1-25.1 124.5a318.64 318.64 0 01-68.6 101.7c-9.3 9.3-19.1 18-29.3 26L668.2 724a8 8 0 00-14.1 3l-39.6 162.2c-1.2 5 2.6 9.9 7.7 9.9l167 .8c6.7 0 10.5-7.7 6.3-12.9l-37.3-47.9z"}}]},name:"redo",theme:"outlined"},Wt=function(d,y){return n.createElement(Ot,Pt({},d,{ref:y,icon:Jt}))},Ht=n.forwardRef(Wt);const x={exportRoleInfo:s=>S.get("/zhyy/manager/extra/role/exportRoleInfo",{params:s,headers:{"Content-Type":"application/json;charset=UTF-8"},responseType:"blob"}),queryList:s=>S.get("/zhyy/blue/sql/queryList?key=station",s),queryRoleType:s=>S.get("/zhyy/blue/sql/queryList?key=dictionary&dictCode=roleType",s),extraUpdateRole:s=>S.post("/zhyy/manager/extra/role/extraUpdateRole",s),deleteRole:s=>S.post("/zhyy/manager/core/role/deleteRole",s),smartSetRoleResource:s=>S.post("/zhyy/manager/core/resource/smartSetRoleResource",s),queryRoleResource:s=>S.post("/zhyy/manager/core/resource/queryRoleResource",s),queryRoleStationAuth:s=>S.post("/zhyy/manager/core/station/queryRoleStationAuth",s),setRoleStationAuth:s=>S.post("/zhyy/manager/core/station/setRoleStationAuth",s),setRoleMenuRecursive2:s=>S.post("/zhyy/manager/core/menu/setRoleMenuRecursive2",s),listSetRoleResource:s=>S.post("/zhyy/manager/extra/role/listSetRoleResource",s),listSetRoleStationAuth:s=>S.post("/zhyy/manager/extra/role/listSetRoleStationAuth",s),listSetRoleMenuRecursive:s=>S.post("/zhyy/manager/extra/role/listSetRoleMenuRecursive",s),queryAllResource:s=>S.post("/zhyy/manager/extra/core/resource/queryAllResource",s),buildRoleMenuSelectTree:s=>S.post("/zhyy/manager/core/menu/buildRoleMenuSelectTree",s),obtainNextRoleCode:s=>S.post("/zhyy/manager/core/role/obtainNextRoleCode",s),extraAddRole:s=>S.post("/zhyy/manager/extra/role/extraAddRole",s),extraQueryRole:s=>S.post("/zhyy/manager/extra/role/extraQueryRole",s)},Xt=s=>{const{type:d,record:y,roleTypeList:r}=s,[T]=g.useForm(),[k,v]=n.useState(!1),[I,M]=n.useState(!1);n.useEffect(()=>{if(d==="add"&&N(),d==="edit"){const{roleCode:w,roleName:C,roleType:h,sortId:f,isTotal:K,remark:U}=y;T.setFieldsValue({remark:U,roleCode:w,roleName:C,roleType:h,sortId:f,isTotal:K===1})}},[]);const N=()=>{v(!0),x.obtainNextRoleCode({}).then(w=>{const C=w[1];C.STATUS==="0000"?T.setFieldsValue({roleCode:C.DATA}):p.error(C.MESSAGE),v(!1)}).catch(()=>{v(!1)})},P=w=>{const{roleCode:C,roleName:h,roleType:f,sortId:K,remark:U,isTotal:se}=w,V={roleCode:C,roleName:h,roleType:f,sortId:K,remark:U,isTotal:se?1:0};d==="edit"&&(V.roleId=y==null?void 0:y.roleId);const F=d==="add"?x.extraAddRole:x.extraUpdateRole;M(!0),F(V).then(q=>{const D=q[1];D.STATUS==="0000"?(p.success(`${d==="add"?"新增":"修改"}角色成功`),s.cancelRoleModal()):p.error(D.MESSAGE),M(!1)}).catch(()=>{M(!1)})};return e.jsxs(g,{form:T,labelCol:{span:6},wrapperCol:{span:14},labelAlign:"right",onFinish:P,children:[e.jsx(g.Item,{label:"角色编码",name:"roleCode",rules:[{required:!0,message:"请输入角色编码！"}],children:e.jsx(E,{placeholder:"请输入角色编码",disabled:d==="edit",suffix:d==="add"?k?e.jsx(Kt,{}):e.jsx(Ht,{onClick:N,style:{color:"#1879ff",cursor:"pointer"}}):null})}),e.jsx(g.Item,{label:"角色名称",name:"roleName",rules:[{required:!0,message:"请输入角色名称！"}],children:e.jsx(E,{placeholder:"请输入角色名称"})}),e.jsx(g.Item,{label:"角色类型",name:"roleType",rules:[{required:!0,message:"请填写角色类型！"}],children:e.jsx(O,{placeholder:"请选择角色类型",allowClear:!0,style:{width:"100%"},children:r.map(w=>{const{valueCode:C,valueName:h}=w;return e.jsx(O.Option,{value:C,option:w,children:h},C)})})}),e.jsx(g.Item,{label:"排序",name:"sortId",rules:[{pattern:/^[+]{0,1}(\d+)$/,message:"只支持数字，请正确输入！"}],children:e.jsx(E,{placeholder:"请输入排序"})}),e.jsx(g.Item,{label:"备注",name:"remark",children:e.jsx(E,{placeholder:"请输入备注"})}),e.jsx(g.Item,{label:"是否统计",name:"isTotal",valuePropName:"checked",children:e.jsx(Ut,{})}),e.jsx(g.Item,{wrapperCol:{offset:6,span:14},children:e.jsx(b,{loading:I,type:"primary",htmlType:"submit",children:"确定"})})]})},ze=({menuTreeCode:s,menuTreeData:d,expandedKeys:y,changeExpandedKeys:r,changeCheckedKeys:T})=>e.jsx("div",{className:"my-[0.75rem]",children:e.jsx($t,{expandedKeys:y,checkedKeys:s,treeData:d,checkable:!0,fieldNames:{key:"menuCode",title:"menuName"},onExpand:r,onCheck:T,style:{border:"1px solid #0000003d",padding:"0.5rem",height:"20rem",overflow:"auto"}})});class Oe extends Vt.Component{render(){const{stationId:d,stationList:y}=this.props;return e.jsx("div",{className:"my-[0.75rem]",children:e.jsx(O,{placeholder:"请选择用户岗位",value:d,mode:"multiple",allowClear:!0,showSearch:!0,getPopupContainer:r=>r.parentElement||document.body,filterOption:(r,T)=>T.children.toLowerCase().includes(r.toLowerCase()),onChange:this.props.changeStationId,style:{width:"15rem"},children:y.map(r=>{const{stationId:T,stationName:k}=r;return e.jsx(O.Option,{value:T,children:k},T)})})})}}const Pe=s=>{const{tableData:d,tableTotal:y,selectedRowKeys:r}=s,[T,k]=n.useState(""),[v,I]=n.useState(""),M=h=>{k(h.target.value)},N=h=>{I(h.target.value)},P=()=>{s.queryTableData(T,v)},w=[{title:"功能编码",key:"resourceCode",dataIndex:"resourceCode",width:120,align:"center"},{title:"功能名称",key:"resourceName",dataIndex:"resourceName",width:200,align:"center",ellipsis:{showTitle:!1},render:h=>e.jsx(qe,{placement:"topLeft",title:h,getPopupContainer:f=>(f==null?void 0:f.closest("div.ant-table-body"))||document.body,children:h})},{title:"功能类型",key:"resourceType",dataIndex:"resourceType",width:120,align:"center",render:h=>h==="100"?"按钮":h},{title:"菜单名称",key:"menuName",width:200,dataIndex:"menuName",align:"center",ellipsis:{showTitle:!1},render:h=>e.jsx(qe,{placement:"topLeft",title:h,getPopupContainer:f=>(f==null?void 0:f.closest("div.ant-table-body"))||document.body,children:h})}],C={onChange:(h,f)=>{s.changeSelectedData(h,f)},selectedRowKeys:r};return e.jsxs("div",{className:"my-[0.75rem]",children:[e.jsxs("div",{style:{display:"flex",alignItems:"center",columnGap:"1%",marginBottom:"1rem"},children:[e.jsx("div",{children:"功能:"}),e.jsx(E,{placeholder:"请输入功能名称或功能编码",allowClear:!0,onChange:M,style:{width:"35%"}}),e.jsx("div",{children:"菜单:"}),e.jsx(E,{placeholder:"请输入菜单名称",allowClear:!0,onChange:N,style:{width:"30%"}}),e.jsx(b,{type:"primary",onClick:P,children:"查询"})]}),e.jsx(Ke,{columns:w,dataSource:d,size:"small",bordered:!0,checkable:!0,rowSelection:C,scroll:{x:"max-content",y:"20rem"},pagination:!1}),e.jsxs("div",{style:{marginTop:"0.25rem",fontWeight:600,textAlign:"right"},children:["共",y,"条"]})]})},Ue=s=>{const d=[];for(let y=0;y<s.length;y++){const r=s[y];r!=null&&r.select&&(r==null?void 0:r.selectType)!=="half"&&d.push(r==null?void 0:r.menuCode),(r==null?void 0:r.children.length)>0&&d.push(...Ue(r==null?void 0:r.children))}return d},ls=()=>{const[s,d]=n.useState(!0),[y,r]=n.useState(""),[T,k]=n.useState(""),[v,I]=n.useState(!1),[M,N]=n.useState(0),[P,w]=n.useState([]),[C,h]=n.useState(1),[f,K]=n.useState(10),[U,se]=n.useState(0),[V,F]=n.useState([]),[q,D]=n.useState([]),[u,j]=n.useState({}),[Ve,ae]=n.useState(!1),[Ge,oe]=n.useState(!0),[Be,ne]=n.useState(!1),[$e,_]=n.useState(!1),[G,fe]=n.useState([]),[Se,Qe]=n.useState([]),[xe,_e]=n.useState([]),[Je,J]=n.useState(!0),[We,W]=n.useState(!1),[He,le]=n.useState(!1),[B,H]=n.useState([]),[ge,Xe]=n.useState([]),[Ye,X]=n.useState(!0),[Ze,$]=n.useState(!1),[et,re]=n.useState(!1),[Ce,tt]=n.useState(0),[Te,je]=n.useState([]),[be,st]=n.useState([]),[we,Y]=n.useState([]),[Q,Z]=n.useState([]),[at,ce]=n.useState(!1),[Re,ie]=n.useState(""),[ot,ue]=n.useState(!1),[nt,de]=n.useState(!0),[lt,he]=n.useState(!1),[rt,me]=n.useState(!1),[ct,ee]=n.useState(!1),[pe,it]=n.useState([]);n.useEffect(()=>{ut(),Ae()},[]);const ut=()=>{x.queryRoleType({}).then(t=>{const a=t[1];if(a.STATUS==="0000"){const o=a.DATA;it(o)}else p.error(a.MESSAGE)})},Ae=()=>{const t={queryName:y,roleType:T,pageNum:C,pageSize:f};s||I(!0),x.extraQueryRole(t).then(a=>{const o=a[1];if(o.STATUS==="0000"){const{data:l,totalCount:c}=o.DATA,i=l==null?void 0:l.map((m,A)=>{const R=(C-1)*f+A+1;return{...m,key:R,num:R}});N(c),w(i)}else p.error(o.MESSAGE);s||I(!1),s&&d(!1)}).catch(()=>{s||I(!1),s&&d(!1)})},dt=()=>{const t={roleName:y,roleType:T};p.warning("文件正在导出，请稍后......"),ae(!0),x.exportRoleInfo(t).then(a=>{var c;const o=a[1];let l=((c=o.headers["content-disposition"])==null?void 0:c.match(/filename=(.*)/)[1])||new Date().valueOf();l=`${decodeURI(l)}`,Qt({fileName:l,res:o.data}),ae(!1)}).catch(()=>{ae(!1)})},Me=t=>new Promise((a,o)=>{const c={roleCode:JSON.stringify(t)=="{}"?"":t==null?void 0:t.roleCode};x.buildRoleMenuSelectTree(c).then(i=>{const m=i[1];if(m.STATUS==="0000"){const A=Ue(m.DATA);fe(A),Qe(m.DATA)}else p.error(m.MESSAGE);a(m)}).catch(i=>o(i))}),ht=t=>new Promise((a,o)=>{const l={roleCode:t==null?void 0:t.roleCode};x.queryRoleStationAuth(l).then(c=>{var m;const i=c[1];if(i.STATUS==="0000"){const A=(m=i.DATA)==null?void 0:m.map(R=>R==null?void 0:R.stationId);H(A)}else p.error(i.MESSAGE);a(i)}).catch(c=>o(c))}),ke=()=>new Promise((t,a)=>{x.queryList({}).then(o=>{const l=o[1];l.STATUS==="0000"?Xe(l.DATA):p.error(l.MESSAGE),t(l)}).catch(o=>a(o))}),mt=t=>new Promise((a,o)=>{const l={roleCode:t==null?void 0:t.roleCode,processorType:"function"};x.queryRoleResource(l).then(c=>{var m;const i=c[1];if(i.STATUS==="0000"){const A=(m=i.DATA)==null?void 0:m.map(R=>R==null?void 0:R.resourceCode);Y(A),Z(i.DATA)}else p.error(i.MESSAGE);a(i)}).catch(c=>o(c))}),ve=()=>new Promise((t,a)=>{x.queryAllResource({}).then(o=>{var c;const l=o[1];if(l.STATUS==="0000"){const i=(c=l.DATA)==null?void 0:c.map(m=>({...m,key:m==null?void 0:m.resourceCode}));tt((i==null?void 0:i.length)||0),je(i),st(i)}else p.error(l.MESSAGE);$(!1),t(l)}).catch(o=>a(o))}),pt=t=>{r(t.target.value)},yt=t=>{k(t)},ft=()=>{if(V.length===0&&q.length===0){p.warning("没有勾选角色，请先勾选要操作的角色！");return}setTimeout(()=>{he(!0),Promise.all([Me(u),ke(),ve()]).then(()=>{de(!1)})})},St=()=>{he(!1),de(!0),F([]),D([])},xt=()=>new Promise((t,a)=>{const o={roleCode:q,list:G};x.listSetRoleMenuRecursive(o).then(l=>{const c=l[1];t(c)}).catch(l=>a(l))}),gt=()=>new Promise((t,a)=>{const o={roleCode:q,list:B};x.listSetRoleStationAuth(o).then(l=>{const c=l[1];t(c)}).catch(l=>a(l))}),Ct=()=>new Promise((t,a)=>{const o=Q.map(c=>{const{resourceType:i,subResourceType:m,resourceCode:A,resourceName:R}=c;return{resourceType:i,subResourceType:m,resourceCode:A,resourceName:R}}),l={roleCode:q,list:o};x.listSetRoleResource(l).then(c=>{const i=c[1];t(i)}).catch(c=>a(c))}),Tt=()=>{setTimeout(()=>{me(!0),Promise.all([G.length>0?xt():Promise.resolve(),B.length>0?gt():Promise.resolve(),Q.length>0?Ct():Promise.resolve()]).then(()=>{p.success("批量修改成功！"),me(!1),he(!1),de(!0),F([]),D([]),L()}).catch(()=>{me(!1)})})},jt=async t=>{ne(!0),j(t),await Me(t),oe(!1)},bt=()=>{ne(!1),j({}),oe(!0)},Ie=t=>{_e(t)},De=t=>{fe(t)},wt=()=>{const t={roleCode:u==null?void 0:u.roleCode,menuCodes:G};_(!0),x.setRoleMenuRecursive2(t).then(a=>{const o=a[1];o.STATUS==="0000"?(p.success("修改菜单成功！"),_(!1),ne(!1),j({}),oe(!0),L()):(_(!1),p.error(o.MESSAGE))}).catch(()=>{_(!1)})},Rt=t=>{le(!0),j(t),Promise.all([ke(),ht(t)]).then(()=>{J(!1)}).catch(()=>{J(!1)})},At=()=>{le(!1),j({}),H([]),J(!0)},Le=t=>{H(t)},Mt=()=>{const t={roleCode:u==null?void 0:u.roleCode,stationCodes:B};W(!0),x.setRoleStationAuth(t).then(a=>{const o=a[1];o.STATUS==="0000"?(p.success("修改用户岗位成功"),J(!0),W(!1),le(!1),H([]),j({}),L()):(p.error(o.MESSAGE),W(!1))}).catch(()=>{W(!1)})},kt=t=>{re(!0),j(t),Promise.all([mt(t),ve()]).then(()=>{X(!1)}).catch(()=>{X(!1)})},vt=()=>{re(!1),X(!0),j({}),Y([]),Z([])},Ee=(t,a)=>{Y(t),Z(a)},Ne=(t,a)=>{let o=[];!t&&!a&&(o=be),(t||a)&&(o=be.filter(l=>{const{resourceCode:c,resourceName:i,menuName:m}=l;return(c.search(t)>=0||i.search(t)>=0)&&m.search(a)>=0})),je(o)},It=()=>{const t={roleCode:u==null?void 0:u.roleCode,clearAllResource:!1,resourceList:Q};$(!0),x.smartSetRoleResource(t).then(a=>{const o=a[1];o.STATUS==="0000"?(p.success("功能授权成功！"),re(!1),$(!1),X(!0),j({}),Y([]),Z([]),L()):(p.error(o.MESSAGE),$(!1))}).catch(()=>{$(!1)})},Fe=(t,a)=>{ce(!0),ie(t),j(a)},Dt=()=>{ce(!1),ie(""),j({}),L()},Lt=t=>{ue(!0),j(t)},Et=()=>{ue(!1),j({})},Nt=()=>{const t={roleId:u==null?void 0:u.roleId};ee(!0),x.deleteRole(t).then(a=>{const o=a[1];o.STATUS==="0000"?(p.success("删除成功！"),ue(!1),j({}),ee(!1),L()):(p.error(o.MESSAGE),ee(!1))}).catch(()=>{ee(!1)})};n.useEffect(()=>{Ae()},[C,f,U]);const L=()=>{F([]),D([]),se(t=>t+1)},Ft=(t,a)=>{h(t),K(a)},qt={onChange:(t,a)=>{F(t),D(a.map(o=>o==null?void 0:o.roleCode))},selectedRowKeys:V},zt=[{title:"序号",key:"num",dataIndex:"num",width:50,align:"center"},{title:"角色编码",key:"roleCode",dataIndex:"roleCode",width:140,align:"center"},{title:"角色名称",key:"roleName",dataIndex:"roleName",width:140,align:"center"},{title:"角色类型",key:"roleType",dataIndex:"roleType",width:140,align:"center",render:t=>{const a=pe.find(o=>o.valueCode===t);return a?a.valueName:""}},{title:"操作",width:175,align:"center",render:(t,a)=>e.jsxs("div",{children:[e.jsx(b,{size:"small",type:"link",onClick:()=>jt(a),children:"菜单"}),e.jsx(b,{style:{display:"none"},size:"small",type:"link",onClick:()=>Rt(a),children:"岗位"}),e.jsx(b,{size:"small",type:"link",onClick:()=>kt(a),children:"功能"}),e.jsx(b,{size:"small",type:"link",onClick:()=>Fe("edit",a),children:"修改"}),e.jsx(b,{size:"small",type:"link",onClick:()=>Lt(a),children:"删除"})]})}];return e.jsxs("div",{className:`pt-[0.5rem] px-[0rem] h-full ${_t.rbac_page}`,children:[e.jsx("div",{className:"bg-white pt-[0.5rem] px-[1.75rem] mb-[0.5rem]",children:e.jsx(g,{labelCol:{span:6},children:e.jsxs(Gt,{gutter:24,children:[e.jsx(ye,{span:6,children:e.jsx(g.Item,{name:"code1",label:"角色",children:e.jsx(E,{placeholder:"请输入角色名称",allowClear:!0,onChange:pt})})}),e.jsx(ye,{span:6,children:e.jsx(g.Item,{name:"code2",label:"角色类型",children:e.jsx(O,{placeholder:"请选择角色类型",allowClear:!0,onChange:yt,children:pe.map(t=>{const{valueCode:a,valueName:o}=t;return e.jsx(O.Option,{value:a,option:t,children:o},a)})})})}),e.jsx(ye,{span:12,children:e.jsxs("div",{className:"text-right",children:[e.jsx(b,{type:"primary",onClick:L,children:"查询"}),e.jsx(b,{type:"primary",className:"ml-[0.4rem]",onClick:ft,children:"批量操作"}),e.jsx(b,{type:"primary",className:"ml-[0.4rem]",onClick:()=>Fe("add",{}),children:"新增角色"}),e.jsx(b,{type:"primary",className:"ml-[0.4rem]",onClick:dt,loading:Ve,children:"导出"})]})})]})})}),e.jsx("div",{className:"bg-white pt-[0.5rem] px-[0.5rem] h-[calc(100%-4.8rem)] overflow-y-auto",children:e.jsx(Ke,{loading:v,columns:zt,dataSource:P,rowSelection:qt,size:"small",bordered:!0,scroll:{x:765},pagination:{total:M,showTotal:()=>`共 ${M} 条`,defaultCurrent:1,defaultPageSize:10,current:C,pageSize:f,showSizeChanger:!0,showQuickJumper:!0,onChange:Ft}})}),e.jsx(z,{title:"批量操作",open:lt,bodyStyle:{paddingTop:"1rem"},width:"50%",destroyOnClose:!0,okText:"提交",confirmLoading:rt,onOk:Tt,onCancel:St,children:nt?e.jsx(te,{paragraph:{rows:8},title:!1,active:!0}):e.jsx(Bt,{defaultActiveKey:"1",items:[{key:"1",label:"修改菜单",children:e.jsx(ze,{menuTreeCode:G,menuTreeData:Se,expandedKeys:xe,changeExpandedKeys:Ie,changeCheckedKeys:De}),forceRender:!0},{key:"2",label:"修改用户岗位",children:e.jsx(Oe,{stationId:B,stationList:ge,changeStationId:Le}),forceRender:!0},{key:"3",label:"功能授权",children:e.jsx(Pe,{tableData:Te,tableTotal:Ce,selectedRowKeys:we,selectedRows:Q,changeSelectedData:Ee,queryTableData:Ne}),forceRender:!0}]})}),e.jsx(z,{title:Re==="add"?"添加角色信息":"修改角色信息",open:at,destroyOnClose:!0,onCancel:()=>{ce(!1),ie(""),j({})},footer:null,children:e.jsx(Xt,{type:Re,record:u,roleTypeList:pe,cancelRoleModal:Dt})}),e.jsx(z,{title:e.jsxs("div",{children:["修改菜单",e.jsxs("span",{style:{color:"#ff0000"},children:[" （",u==null?void 0:u.roleName,"）"]})]}),open:Be,destroyOnClose:!0,onCancel:bt,footer:null,children:Ge?e.jsx(te,{paragraph:{rows:8},title:!1,active:!0}):e.jsxs(g,{labelCol:{span:4},wrapperCol:{span:19},labelAlign:"right",children:[e.jsx(g.Item,{label:"菜单",children:e.jsx(ze,{menuTreeCode:G,menuTreeData:Se,expandedKeys:xe,changeExpandedKeys:Ie,changeCheckedKeys:De})}),e.jsx(g.Item,{wrapperCol:{offset:4,span:19},children:e.jsx(b,{type:"primary",loading:$e,onClick:wt,children:"确定"})})]})}),e.jsx(z,{title:e.jsxs("div",{children:["修改用户岗位",e.jsxs("span",{style:{color:"#ff0000"},children:[" （",u==null?void 0:u.roleName,"）"]})]}),open:He,destroyOnClose:!0,onCancel:At,footer:null,children:Je?e.jsx(te,{paragraph:{rows:8},title:!1,active:!0}):e.jsxs(g,{labelCol:{span:4},wrapperCol:{span:19},labelAlign:"right",children:[e.jsx(g.Item,{label:"岗位",children:e.jsx(Oe,{stationId:B,stationList:ge,changeStationId:Le})}),e.jsx(g.Item,{wrapperCol:{offset:4,span:19},children:e.jsx(b,{type:"primary",loading:We,onClick:Mt,children:"确定"})})]})}),e.jsx(z,{title:e.jsxs("div",{children:["功能授权",e.jsxs("span",{style:{color:"#ff0000"},children:[" （",u==null?void 0:u.roleName,"）"]})]}),open:et,width:"60%",destroyOnClose:!0,onOk:It,confirmLoading:Ze,onCancel:vt,children:Ye?e.jsx(te,{paragraph:{rows:8},title:!1,active:!0}):e.jsx(Pe,{tableData:Te,tableTotal:Ce,selectedRowKeys:we,selectedRows:Q,changeSelectedData:Ee,queryTableData:Ne})}),e.jsxs(z,{open:ot,width:"20%",closable:!1,destroyOnClose:!0,onOk:Nt,confirmLoading:ct,onCancel:Et,children:["是否删除",e.jsx("span",{style:{color:"#ff0000"},children:u==null?void 0:u.roleName}),"?"]})]})};export{ls as default};
