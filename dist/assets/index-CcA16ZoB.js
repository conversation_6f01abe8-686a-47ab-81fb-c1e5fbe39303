import{F as T,r,A as ye,u as y,d as c,j as e,R as q,C as w,S as Se,B as h,a4 as Ie}from"./index-De_f0oL2.js";import{p as S}from"./service-B_wWoC3F.js";import{R as je,o as J,d as Te}from"./down-BCLNnN1h.js";import{R as we}from"./index-BmnYJy3v.js";import{s as f}from"./index-BcPP1N8I.js";import{D as Pe}from"./index-CdSZ9YgQ.js";import{C as be}from"./index-DZyVV6rP.js";import{R as Ne,a as Ye}from"./FullscreenOutlined-DzCTibKW.js";import{R as Ae,U as _e,a as ve}from"./index-BH5uxjwl.js";import{M as Re}from"./index-Dck5cc4J.js";import{P as Ce}from"./index-TkzW9Zmk.js";import"./Table-D-iLeFE-.js";import"./index-BWJehDyc.js";import"./useMultipleSelect-B0dEIXT-.js";import"./DeleteOutlined-D-FcgX8f.js";const De="_detail_modal_11oal_2",Ee="_pointSalaryImport_page_11oal_6",Me="_animation_box_11oal_6",I={detail_modal:De,pointSalaryImport_page:Ee,animation_box:Me},{Dragger:ke}=_e,Ze=()=>{var $,L,O,z,H;const[m]=T.useForm(),b=r.useRef(null),N=r.useRef(null),Y=r.useRef(null),[B,K]=r.useState(0),[A,_]=r.useState(!0),[Q,v]=r.useState(!1),[W,P]=r.useState(!1),[u,Z]=r.useState([]),[X,ee]=r.useState([]),[te,ae]=r.useState([]),{currentUser:R}=ye(),[d,C]=r.useState({total:0,pageNum:1,pageSize:50}),{runAsync:D}=y(S.getEnumType,{manual:!0}),{runAsync:ne}=y(S.getPointSalaryImport,{manual:!0}),{runAsync:oe}=y(S.downloadTemplate,{manual:!0}),{runAsync:se}=y(S.exportPointSalaryImport,{manual:!0}),{runAsync:le}=y(S.importPointSalaryImport,{manual:!0}),re=[{title:"业务账期",dataIndex:"cycleId",key:"cycleId",align:"center",width:100,fixed:"left"},{title:"单位",dataIndex:"cityName",key:"cityName",align:"center",width:160},{title:"岗位",dataIndex:"position",key:"position",align:"center",width:130},{title:"积分",key:"level",dataIndex:"level",align:"center",children:[{title:"服务积分单价",dataIndex:"servicePointsPrice",key:"servicePointsPrice",align:"center",width:130},{title:"动作积分单价",dataIndex:"actionPointsPrice",key:"actionPointsPrice",align:"center",width:130},{title:"价值积分单价-营销",dataIndex:"valuePointsPrice",key:"valuePointsPrice",align:"center",width:130},{title:"价值积分单价-交付",dataIndex:"valuePointsPriceDeliver",key:"valuePointsPriceDeliver",align:"center",width:130},{title:"价值积分单价-奖惩",dataIndex:"valuePointsPriceRap",key:"valuePointsPriceRap",align:"center",width:130},{title:"自定义积分单价",dataIndex:"customPointsPrice",key:"customPointsPrice",align:"center",width:130}]}];r.useEffect(()=>(ce(),F(),window.addEventListener("resize",E),()=>{window.removeEventListener("resize",E)}),[]);const E=()=>{x()};r.useEffect(()=>{x()},[(document.querySelector(".pointSalaryImport_table .ant-table-header")||{}).offsetHeight]);const x=()=>{var a;const t=(document.querySelector(".pointSalaryImport_table .ant-table-header")||{}).offsetHeight||0,n=(document.querySelector(".pointSalaryImport_table .ant-table-pagination")||{}).offsetHeight||26;t&&n&&K(((a=N.current)==null?void 0:a.offsetHeight)-(Y.current.offsetHeight+t+n))},M=async t=>{var n;try{J("正在导出",0,"loading");let a=null;if(t===1)a=await oe({templateId:"SALARY_POINTS_AND_UNIT_PRICE"});else if(t===2){const{loginDate:o,cityId:l,category:i,cycleId:p,empId:g}=m.getFieldsValue(),s={beginTime:(o==null?void 0:o.length)>0?c(o[0]).format("YYYY-MM-DD"):null,endTime:(o==null?void 0:o.length)>0?c(o[1]).format("YYYY-MM-DD"):null,cycleId:p?(n=c(p))==null?void 0:n.format("YYYYMM"):"",cityId:l?l[(l==null?void 0:l.length)-1]:null,category:i,empId:g};a=await se(s)}Te(a)}catch(a){J("导出失败",1,"error"),console.error("Download failed:",a)}},ie=t=>{j({...t})},ce=async()=>{var l;const[[t,n],[a]]=await Promise.all([D({code:"1010",tag:1}),D({code:"EMPLOYEE_TRANSFER_CATEGORY"})]);if(t||a)return;n.STATUS==="0000"&&ee((l=n.DATA)==null?void 0:l.filter(i=>(i==null?void 0:i.orgId)!=="49757"));const o={...m.getFieldsValue()};j(o)},j=async t=>{var U,V;v(!0);const{loginDate:n,cityId:a,category:o,cycleId:l,empId:i}=t,p={...d,beginTime:(n==null?void 0:n.length)>0?c(n[0]).format("YYYY-MM-DD"):null,endTime:(n==null?void 0:n.length)>0?c(n[1]).format("YYYY-MM-DD"):null,cycleId:l?(U=c(l))==null?void 0:U.format("YYYYMM"):"",cityId:a?a[(a==null?void 0:a.length)-1]:null,category:o,empId:i},[g,s]=await ne(p);if(v(!1),g){f.error((s==null?void 0:s.DATA)||(s==null?void 0:s.MESSAGE)||"调用失败");return}if(s.STATUS==="0000"){const{DATA:{data:fe}}=s,xe=fe.map((G,ge)=>({...G,key:G.id||ge}));ae(xe),C({...d,total:(V=s.DATA)==null?void 0:V.totalCount})}else f.error((s==null?void 0:s.MESSAGE)||(s==null?void 0:s.DATA)||"调用失败")},de=async()=>{const t=new FormData;u.map(o=>o==null?void 0:o.originFileObj).forEach(o=>{t.append("file",o)});const[n,a]=await le(t);if(n){f.error((a==null?void 0:a.DATA)||(a==null?void 0:a.MESSAGE)||"调用失败");return}if(a.STATUS==="0000"){const{loginDate:o,yearVal:l,cityId:i,category:p}=m.getFieldsValue(),g={beginTime:o.length>0?c(o[0]).format("YYYY-MM-DD"):null,endTime:o.length>0?c(o[1]).format("YYYY-MM-DD"):null,yearVal:l?c(l).format("YYYY"):"",cityId:i[(i==null?void 0:i.length)-1],category:p};x(),j(g),P(!1),f.success(a==null?void 0:a.DATA)}else f.error((a==null?void 0:a.MESSAGE)||(a==null?void 0:a.DATA)||"调用失败")},me=()=>{(u==null?void 0:u.length)>0?de():f.error("请先选择文件上传")},k=()=>{P(!1)},ue=()=>{m.resetFields(),F()},pe=t=>{const n={total:t==null?void 0:t.total,pageNum:t==null?void 0:t.current,pageSize:t==null?void 0:t.pageSize};C(n)};r.useEffect(()=>{if((d==null?void 0:d.total)>0){const t={...m.getFieldsValue()};j(t)}},[d.pageNum,d.pageSize]);const F=()=>{const t=c().subtract(1,"month");m.setFieldsValue({cycleId:t})},he=(t,n)=>{var a,o;return(((a=n[0])==null?void 0:a.enumName)??"").toLowerCase().includes((o=t.toLowerCase())==null?void 0:o.trim())};return e.jsxs("div",{className:`pt-[0.5rem] px-[0rem] h-full flex flex-col ${I.pointSalaryImport_page}`,children:[e.jsxs(e.Fragment,{children:[e.jsx("div",{ref:b,className:"bg-white pt-[0.5rem] px-[1.75rem] mb-[0.5rem]",children:e.jsx(T,{form:m,labelCol:{span:6},onFinish:ie,initialValues:{loginDate:"",category:"KOU_JING_TIAO_ZHENG",cityId:"",empId:""},children:e.jsxs(q,{gutter:24,children:[e.jsx(w,{span:6,children:e.jsx(T.Item,{name:"cycleId",label:"账期",className:"mb-[0.5rem]",children:e.jsx(Pe,{className:"w-full",picker:"month"})})}),e.jsx(w,{span:5,children:e.jsx(T.Item,{name:"cityId",label:"单位",className:"mb-[0.5rem]",children:e.jsx(be,{allowClear:!0,changeOnSelect:!0,expandTrigger:"hover",displayRender:t=>t[t.length-1],options:X,fieldNames:{value:"enumId",label:"enumName",children:"children"},placeholder:"请选择单位",showSearch:{filter:he},onSearch:t=>console.log(t)})})}),e.jsx(w,{span:4,children:e.jsx("div",{className:"text-right",children:e.jsxs(Se,{children:[e.jsx(h,{type:"primary",htmlType:"submit",children:"查询"}),e.jsx(h,{onClick:()=>ue(),children:"重置"})]})})})]})})}),e.jsxs("div",{ref:N,className:"relative bg-white px-5 pt-2.5",style:{height:`calc(100% - ${(($=b.current)==null?void 0:$.offsetHeight)+15}px)`},children:[e.jsxs("div",{ref:Y,className:`flex justify-between items-center overflow-hidden mb-[0.1rem] ${I.animation_box} ${A?"h-[1.8rem]":"h-0"}`,children:[e.jsxs("div",{className:"flex ",children:[A?e.jsx(Ne,{className:`${I.shousuo_icon} text-[1rem]`,onClick:()=>{_(!1),setTimeout(()=>{x()},200)}}):e.jsx(Ye,{className:`${I.shousuo_icon} absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10`,onClick:()=>{_(!0),setTimeout(()=>{x()},200)}}),e.jsx("div",{className:"font-bold text-[0.8rem] ml-3",children:"数据列表"})]}),e.jsx("div",{className:"flex gap-x-[2.5rem]",children:e.jsxs("div",{className:"flex gap-x-[0.75rem]",children:[!((O=(L=R.roleInfo)==null?void 0:L.roleCode)!=null&&O.includes("ATJ0001"))&&e.jsxs("div",{className:"flex items-center gap-x-[0.25rem]",children:[e.jsx(Ie,{name:"excel",width:20,height:20}),e.jsx("span",{className:"text-[#E60027] cursor-pointer",onClick:()=>M(1),children:"下载导入模版"})]}),!((H=(z=R.roleInfo)==null?void 0:z.roleCode)!=null&&H.includes("ATJ0001"))&&e.jsx(h,{danger:!0,ghost:!0,icon:e.jsx(Ae,{}),onClick:()=>P(!0),children:"导入"}),e.jsx(h,{danger:!0,ghost:!0,icon:e.jsx(je,{}),onClick:()=>M(2),children:"导出"})]})})]}),e.jsx(we,{className:"pointSalaryImport_table",rowClassName:(t,n)=>n%2===1?"customRow odd":"customRow even",columns:re,dataSource:te,bordered:!0,scroll:{y:`calc(${B}px - 0.625rem - 1.6rem - 0.5rem)`},loading:Q,onChange:pe,pagination:{...d,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50"]}})]})]}),e.jsx(Re,{title:"文件上传",destroyOnClose:!0,open:W,centered:!0,className:I.detail_modal,footer:null,onCancel:k,children:e.jsxs("div",{className:"mt-4 mb-8",style:{marginBottom:"0px"},children:[e.jsx(q,{children:e.jsx(w,{span:22,offset:1,className:"h-[10rem]",children:e.jsxs(ke,{action:"",maxCount:1,multiple:!1,fileList:u,accept:".xls,.xlsx",beforeUpload(t,n){return console.log(t,n),!1},onChange(t){const{status:n}=t.file;n!=="uploading"&&(console.log(t.file,t.fileList),Z(t.fileList))},children:[e.jsx("p",{className:"ant-upload-drag-icon",children:e.jsx(ve,{style:{color:"#F14846"}})}),e.jsx("p",{className:"ant-upload-text",children:"点击或将文件拖拽到这里上传"}),e.jsx("p",{className:"ant-upload-hint",children:"支持excel格式的文件。"})]})})}),e.jsxs("div",{style:{display:"flex",justifyContent:"center",marginTop:"2rem"},children:[e.jsx(h,{danger:!0,onClick:k,children:"取消"}),e.jsx(Ce,{title:"",description:"导入文件如有重复数据，将会被覆盖，请确认是否上传。",onConfirm:me,okText:"确认",cancelText:"取消",children:e.jsx(h,{danger:!0,type:"primary",style:{marginLeft:"1rem"},disabled:u.length<1,children:"上传"})})]})]})})]})};export{Ze as default};
