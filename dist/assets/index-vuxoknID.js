import{F as i,u as _,j as e,S as ee,y as F,B as y,z as Se,r as d,d as r,R as Q,C as E,a4 as Ae,T as V}from"./index-De_f0oL2.js";import{p as C}from"./service-CUJHac7r.js";import{R as be,o as Z,d as Ye}from"./down-BCLNnN1h.js";import{p as Ee}from"./service-CEuf3VDV.js";import{S as O}from"./index-BWJehDyc.js";import{D as $}from"./index-CdSZ9YgQ.js";import{R as _e}from"./index-BmnYJy3v.js";import{s as N}from"./index-BcPP1N8I.js";import{C as ve}from"./index-DZyVV6rP.js";import{R as De,a as Ce}from"./FullscreenOutlined-DzCTibKW.js";import{R as Me,U as Re,a as ke}from"./index-BH5uxjwl.js";import{M as X}from"./index-Dck5cc4J.js";import{P as Fe}from"./index-TkzW9Zmk.js";import"./Table-D-iLeFE-.js";import"./useMultipleSelect-B0dEIXT-.js";import"./DeleteOutlined-D-FcgX8f.js";const Oe="_detail_modal_8aqgx_2",Le="_peopleAdjustment_page_8aqgx_17",Ve="_animation_box_8aqgx_17",$e="_over_ellipsis_8aqgx_20",qe="_addData_8aqgx_26",g={detail_modal:Oe,peopleAdjustment_page:Le,animation_box:Ve,over_ellipsis:$e,addData:qe},ze=[{title:"分类",dataIndex:"category"},{title:"账期",dataIndex:"cycleId"},{title:"员工编号",dataIndex:"empId"},{title:"姓名",dataIndex:"empName"},{title:"备注",dataIndex:"remark"}],He=(p,S)=>{let A;return function(...v){clearTimeout(A),A=setTimeout(()=>{p(...v)},S)}},Ue=p=>{const[S]=i.useForm(),{runAsync:A}=_(Ee.getEmployeeByEmpId,{manual:!0}),v=x=>{p.addData(x.users),console.log("Received values of form:",x)},L=He((x,b,j)=>{A({empId:x}).then(f=>{const m=f[1];if(m.STATUS==="0000"){const{DATA:I}=m;if(I!=null&&I.employeeName){const u=[...j.getFieldsValue().users];u[b].empName=I.employeeName,j.setFieldsValue({users:u})}else{const u=[...j.getFieldsValue().users];u[b].empName="",j.setFieldsValue({users:u})}}})},1e3);return e.jsxs("div",{className:g.addData,children:[e.jsx("div",{children:e.jsxs("div",{style:{background:"#fafafa",display:"flex",textAlign:"center",height:"3rem",border:"1px solid #f0f0f0",width:"100%"},children:[ze.map(x=>e.jsx("p",{style:{width:"25%",fontWeight:500,fontSize:14,margin:"auto 3px"},children:x.title},x.dataIndex)),e.jsx("p",{style:{width:"25%",fontWeight:500,fontSize:14,margin:"auto 1rem"},children:"操作"})]})}),e.jsxs(i,{name:"dynamic_form_nest_item",form:S,onFinish:v,style:{width:"100%",marginTop:16,maxHeight:"500px"},autoComplete:"off",children:[e.jsx(i.List,{name:"users",children:(x,{add:b,remove:j})=>e.jsxs("div",{children:[e.jsx("div",{style:{width:"100%",maxHeight:"390px",overflowY:"auto"},children:x.map(({key:f,name:m},I)=>{var T;return e.jsxs(ee,{style:{marginBottom:8,width:"100%"},align:"baseline",children:[e.jsx(i.Item,{name:[m,"category"],rules:[{required:!0,message:"请输入分类"}],children:e.jsx(O,{placeholder:"请选择分类",allowClear:!0,children:(T=p.typeList)==null?void 0:T.map(u=>e.jsx(O.Option,{value:u.enumId,children:u.enumName},u.enumId))})},f+"category"),e.jsx(i.Item,{name:[m,"cycleId"],rules:[{required:!0,message:"请输入账期"}],children:e.jsx($,{className:"w-full",picker:"month"})},f+"cycleId"),e.jsx(i.Item,{name:[m,"empId"],rules:[{required:!0,message:"请输入员工编号"}],children:e.jsx(F,{placeholder:"请输入员工编号",onChange:u=>{const M=u.target.value;L(M,I,S)}})},f+"empId"),e.jsx(i.Item,{name:[m,"empName"],rules:[{required:!0,message:"请输入正确的员工编号"}],children:e.jsx(F,{placeholder:"姓名",disabled:!0})},f+"empName"),e.jsx(i.Item,{name:[m,"remark"],children:e.jsx(F,{placeholder:"请输入备注"})},f+"remark"),e.jsx(y,{type:"dashed",onClick:()=>j(m),style:{margin:"auto",width:"50%",marginLeft:"25%"},children:"删除"})]},f)})}),e.jsx(i.Item,{style:{width:"100%",display:"flex",justifyContent:"right",marginTop:12},children:e.jsx(y,{onClick:()=>b(),danger:!0,block:!0,icon:e.jsx(Se,{}),style:{width:150},children:"新增一条数据"})})]})}),e.jsx(i.Item,{style:{textAlign:"center",width:"100%",background:"#fff"},children:e.jsx(y,{type:"primary",htmlType:"submit",children:"提交"})})]})]})},{Dragger:Pe}=Re,{RangePicker:Ge}=$,ct=()=>{var B;const[p]=i.useForm(),S=d.useRef(null),A=d.useRef(null),v=d.useRef(null),[L,x]=d.useState(0),[b,j]=d.useState(!0),[f,m]=d.useState(!1),[I,T]=d.useState(!1),[u,M]=d.useState(!1),[D,te]=d.useState([]),[ae,se]=d.useState([]),[q,le]=d.useState([]),[ne,re]=d.useState([]),[w,z]=d.useState({total:0,pageNum:1,pageSize:50}),{runAsync:H}=_(C.getEnumType,{manual:!0}),{runAsync:oe}=_(C.updateEmployeeTransfer,{manual:!0}),{runAsync:ie}=_(C.downloadEmployeeTemplate,{manual:!0}),{runAsync:ce}=_(C.emportEmployeeTransfer,{manual:!0}),{runAsync:de}=_(C.importEmployeeTransfer,{manual:!0}),{runAsync:me}=_(C.addEmployeeSaveBatch,{manual:!0}),ue=[{title:"单位",width:130,dataIndex:"cityName",align:"center",key:"cityName",fixed:"left",render:(t,s)=>e.jsx(V,{title:t,children:e.jsx("div",{className:g.over_ellipsis,children:t})})},{title:"分类",width:100,dataIndex:"category",align:"center",key:"category",fixed:"left"},{title:"账期",dataIndex:"cycleId",align:"center",key:"cycleId",width:80},{title:"姓名",dataIndex:"empName",align:"center",key:"empName",width:80},{title:"员工编号",dataIndex:"empId",align:"center",key:"empId",width:80},{title:"导入人名称",dataIndex:"createName",align:"center",key:"createName",width:80},{title:"导入人编号",dataIndex:"createEmpId",align:"center",key:"createEmpId",width:80},{title:"导入人组织",dataIndex:"createOrgaName",align:"center",key:"createOrgaName",width:130,render:(t,s)=>e.jsx(V,{title:t,children:e.jsx("div",{className:g.over_ellipsis,children:t})})},{title:"导入时间",dataIndex:"createTime",align:"center",key:"createTime",width:100},{title:"备注",dataIndex:"remark",key:"remark",align:"center",width:100,render:(t,s)=>e.jsx(V,{title:t,children:e.jsx("div",{className:g.over_ellipsis,children:t})})}];d.useEffect(()=>(he(),window.addEventListener("resize",U),()=>{window.removeEventListener("resize",U)}),[]);const U=()=>{R()};d.useEffect(()=>{R()},[(document.querySelector(".peopleAdjustment_table .ant-table-header")||{}).offsetHeight]);const R=()=>{var a;const t=(document.querySelector(".peopleAdjustment_table .ant-table-header")||{}).offsetHeight||0,s=(document.querySelector(".peopleAdjustment_table .ant-table-pagination")||{}).offsetHeight||26;t&&s&&x(((a=A.current)==null?void 0:a.offsetHeight)-(v.current.offsetHeight+t+s))},P=async t=>{var s;try{Z("正在导出",0,"loading");let a=null;if(t===1)a=await ie({templateId:"EMPLOYEE_TRANSFER"});else if(t===2){const{loginDate:l,cityId:n,category:c,cycleId:h,empId:Y}=p.getFieldsValue(),o={beginTime:(l==null?void 0:l.length)>0?r(l[0]).format("YYYY-MM-DD"):null,endTime:(l==null?void 0:l.length)>0?r(l[1]).format("YYYY-MM-DD"):null,cycleId:h?(s=r(h))==null?void 0:s.format("YYYYMM"):"",cityId:n?n[(n==null?void 0:n.length)-1]:null,category:c,empId:Y};a=await ce(o)}Ye(a)}catch(a){Z("导出失败",1,"error"),console.error("Download failed:",a)}},pe=t=>{k({...t})},he=async()=>{var c;const[[t,s],[a,l]]=await Promise.all([H({code:"1010",tag:1}),H({code:"EMPLOYEE_TRANSFER_CATEGORY"})]);if(t||a)return;s.STATUS==="0000"&&se((c=s.DATA)==null?void 0:c.filter(h=>(h==null?void 0:h.orgId)!=="49757")),l.STATUS==="0000"&&le(l.DATA);const n={...p.getFieldsValue()};k(n)},k=async t=>{var W,J;m(!0);const{loginDate:s,cityId:a,category:l,cycleId:n,empId:c}=t,h={...w,beginTime:(s==null?void 0:s.length)>0?r(s[0]).format("YYYY-MM-DD"):null,endTime:(s==null?void 0:s.length)>0?r(s[1]).format("YYYY-MM-DD"):null,cycleId:n?(W=r(n))==null?void 0:W.format("YYYYMM"):"",cityId:a?a[(a==null?void 0:a.length)-1]:null,category:l,empId:c};console.log(123);const[Y,o]=await oe(h);if(m(!1),Y){N.error((o==null?void 0:o.DATA)||(o==null?void 0:o.MESSAGE)||"调用失败");return}if(o.STATUS==="0000"){const{DATA:{data:Te}}=o,we=Te.map((K,Ne)=>({...K,key:K.id||Ne}));re(we),z({...w,total:(J=o.DATA)==null?void 0:J.totalCount})}else N.error((o==null?void 0:o.MESSAGE)||(o==null?void 0:o.DATA)||"调用失败")},xe=async()=>{const t=new FormData;D.map(l=>l==null?void 0:l.originFileObj).forEach(l=>{t.append("file",l)});const[s,a]=await de(t);if(s){N.error((a==null?void 0:a.DATA)||(a==null?void 0:a.MESSAGE)||"调用失败");return}if(a.STATUS==="0000"){const{loginDate:l,yearVal:n,cityId:c,category:h}=p.getFieldsValue(),Y={beginTime:l.length>0?r(l[0]).format("YYYY-MM-DD"):null,endTime:l.length>0?r(l[1]).format("YYYY-MM-DD"):null,yearVal:n?r(n).format("YYYY"):"",cityId:c[(c==null?void 0:c.length)-1],category:h};R(),k(Y),M(!1),N.success(a==null?void 0:a.DATA)}else N.error((a==null?void 0:a.MESSAGE)||(a==null?void 0:a.DATA)||"调用失败")},fe=()=>{(D==null?void 0:D.length)>0?xe():N.error("请先选择文件上传")},G=()=>{M(!1)},ge=()=>{p.resetFields()},ye=t=>{const s={total:t==null?void 0:t.total,pageNum:t==null?void 0:t.current,pageSize:t==null?void 0:t.pageSize};z(s)};d.useEffect(()=>{if((w==null?void 0:w.total)>0){const t={...p.getFieldsValue()};k(t)}},[w.pageNum,w.pageSize]);const je=async t=>{t.forEach(l=>{var n;l.cycleId=(n=r(l.cycleId))==null?void 0:n.format("YYYYMM")}),console.log("record",t);const[s,a]=await me(t);if(m(!1),s){N.error((a==null?void 0:a.DATA)||(a==null?void 0:a.MESSAGE)||"调用失败");return}if(a.STATUS==="0000"){const{loginDate:l,yearVal:n,cityId:c,category:h}=p.getFieldsValue(),Y={beginTime:l.length>0?r(l[0]).format("YYYY-MM-DD"):null,endTime:l.length>0?r(l[1]).format("YYYY-MM-DD"):null,yearVal:n?r(n).format("YYYY"):"",cityId:c[(c==null?void 0:c.length)-1],category:h};k(Y),T(!1)}else N.error((a==null?void 0:a.MESSAGE)||(a==null?void 0:a.DATA)||"调用失败")},Ie=(t,s)=>{var a,l;return(((a=s[0])==null?void 0:a.enumName)??"").toLowerCase().includes((l=t.toLowerCase())==null?void 0:l.trim())};return e.jsxs("div",{className:`pt-[0.5rem] px-[0rem] h-full flex flex-col ${g.peopleAdjustment_page}`,children:[e.jsxs(e.Fragment,{children:[e.jsx("div",{ref:S,className:"bg-white pt-[0.5rem] px-[1.75rem] mb-[0.5rem]",children:e.jsx(i,{form:p,labelCol:{span:6},onFinish:pe,initialValues:{loginDate:"",category:"KOU_JING_TIAO_ZHENG",cityId:"",empId:""},children:e.jsxs(Q,{gutter:24,children:[e.jsx(E,{span:7,children:e.jsx(i.Item,{label:"导入时间",name:"loginDate",wrapperCol:{span:24},className:"mb-[0.5rem]",children:e.jsx(Ge,{style:{width:"100%"},allowClear:!0,ranges:{近一天:[r().subtract(1,"day"),r()],近三天:[r().subtract(3,"day"),r()],近七天:[r().subtract(7,"day"),r()]}})})}),e.jsx(E,{span:5,children:e.jsx(i.Item,{name:"category",label:"分类",className:"mb-[0.5rem]",children:e.jsx(O,{placeholder:"请选择类型",allowClear:!0,children:q.map(t=>{const{enumId:s,enumName:a}=t;return e.jsx(O.Option,{value:s,children:a},s)})})})}),e.jsx(E,{span:6,children:e.jsx(i.Item,{name:"cycleId",label:"账期",className:"mb-[0.5rem]",children:e.jsx($,{className:"w-full",picker:"month"})})}),e.jsx(E,{span:6,children:e.jsx(i.Item,{name:"cityId",label:"单位",className:"mb-[0.5rem]",children:e.jsx(ve,{allowClear:!0,changeOnSelect:!0,expandTrigger:"hover",displayRender:t=>t[t.length-1],options:ae,fieldNames:{value:"enumId",label:"enumName",children:"children"},placeholder:"请选择",showSearch:{filter:Ie},onSearch:t=>console.log(t)})})}),e.jsx(E,{span:7,children:e.jsx(i.Item,{name:"empId",label:"员工编号",wrapperCol:{span:24},className:"mb-[0.5rem]",children:e.jsx(F,{placeholder:"请输入",allowClear:!0})})}),e.jsx(E,{span:4,children:e.jsx("div",{className:"text-right",children:e.jsxs(ee,{children:[e.jsx(y,{type:"primary",htmlType:"submit",children:"查询"}),e.jsx(y,{onClick:()=>ge(),children:"重置"})]})})})]})})}),e.jsxs("div",{ref:A,className:"relative bg-white px-5 pt-2.5",style:{height:`calc(100% - ${((B=S.current)==null?void 0:B.offsetHeight)+15}px)`},children:[e.jsxs("div",{ref:v,className:`flex justify-between items-center overflow-hidden mb-[0.1rem] ${g.animation_box} ${b?"h-[1.8rem]":"h-0"}`,children:[e.jsxs("div",{className:"flex ",children:[b?e.jsx(De,{className:`${g.shousuo_icon} text-[1rem]`,onClick:()=>{j(!1),setTimeout(()=>{R()},200)}}):e.jsx(Ce,{className:`${g.shousuo_icon} absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10`,onClick:()=>{j(!0),setTimeout(()=>{R()},200)}}),e.jsx("div",{className:"font-bold text-[0.8rem] ml-3",children:"数据列表"})]}),e.jsx("div",{className:"flex gap-x-[2.5rem]",children:e.jsxs("div",{className:"flex gap-x-[0.75rem]",children:[e.jsxs("div",{className:"flex items-center gap-x-[0.25rem]",children:[e.jsx(Ae,{name:"excel",width:20,height:20}),e.jsx("span",{className:"text-[#E60027] cursor-pointer",onClick:()=>P(1),children:"下载导入模版"})]}),e.jsx(y,{danger:!0,ghost:!0,icon:e.jsx(Me,{}),onClick:()=>M(!0),children:"导入"}),e.jsx(y,{danger:!0,ghost:!0,icon:e.jsx(be,{}),onClick:()=>P(2),children:"导出"}),e.jsx(y,{danger:!0,type:"primary",onClick:()=>T(!0),children:"新增"})]})})]}),e.jsx(_e,{className:"peopleAdjustment_table",rowClassName:(t,s)=>s%2===1?"customRow odd":"customRow even",columns:ue,dataSource:ne,scroll:{y:`calc(${L}px - 0.625rem - 1.6rem - 0.5rem)`},loading:f,onChange:ye,pagination:{...w,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50"]}})]})]}),e.jsx(X,{title:"新增数据",destroyOnClose:!0,open:I,centered:!0,className:g.detail_modal,footer:null,onCancel:()=>T(!1),width:"60%",children:e.jsx(Ue,{typeList:q,addData:je})}),e.jsx(X,{title:"文件上传",destroyOnClose:!0,open:u,centered:!0,className:g.detail_modal,footer:null,onCancel:G,children:e.jsxs("div",{className:"mt-4 mb-8",style:{marginBottom:"0px"},children:[e.jsx(Q,{children:e.jsx(E,{span:22,offset:1,className:"h-[10rem]",children:e.jsxs(Pe,{action:"",maxCount:1,multiple:!1,fileList:D,beforeUpload(t,s){return console.log(t,s),!1},onChange(t){const{status:s}=t.file;s!=="uploading"&&(console.log(t.file,t.fileList),te(t.fileList))},children:[e.jsx("p",{className:"ant-upload-drag-icon",children:e.jsx(ke,{style:{color:"#F14846"}})}),e.jsx("p",{className:"ant-upload-text",children:"点击或将文件拖拽到这里上传"}),e.jsx("p",{className:"ant-upload-hint",children:"支持excel格式的文件。"})]})})}),e.jsxs("div",{style:{display:"flex",justifyContent:"center",marginTop:"2rem"},children:[e.jsx(y,{danger:!0,onClick:G,children:"取消"}),e.jsx(Fe,{title:"",description:"导入文件如有重复数据，将会被覆盖，请确认是否上传。",onConfirm:fe,okText:"确认",cancelText:"取消",children:e.jsx(y,{danger:!0,type:"primary",style:{marginLeft:"1rem"},disabled:D.length<1,children:"上传"})})]})]})})]})};export{ct as default};
