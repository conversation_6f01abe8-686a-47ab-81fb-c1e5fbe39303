import{F as g,r,u as y,d as o,j as e,R as H,C as j,S as Ne,B as p,a4 as Ie,T as b}from"./index-De_f0oL2.js";import{p as T}from"./service-CUJHac7r.js";import{R as _e,o as P,d as Ae}from"./down-BCLNnN1h.js";import be from"./index-B0EitxS2.js";import{R as Ye}from"./index-BmnYJy3v.js";import{f as De}from"./format-ChnqMkgG.js";import{s as h}from"./index-BcPP1N8I.js";import{D as V}from"./index-CdSZ9YgQ.js";import{S as B}from"./index-BWJehDyc.js";import{C as Ee}from"./index-DZyVV6rP.js";import{R as Ce,a as Me}from"./FullscreenOutlined-DzCTibKW.js";import{R as ve,U as ke,a as Re}from"./index-BH5uxjwl.js";import{M as q}from"./index-Dck5cc4J.js";import{P as Oe}from"./index-TkzW9Zmk.js";import"./Table-D-iLeFE-.js";import"./useMultipleSelect-B0dEIXT-.js";import"./debounce-D4mn-XUD.js";import"./service-CEuf3VDV.js";import"./DeleteOutlined-D-FcgX8f.js";const Fe="_detail_modal_1u61p_2",Le="_other_page_1u61p_13",Ue="_animation_box_1u61p_13",$e="_over_ellipsis_1u61p_16",m={detail_modal:Fe,other_page:Le,animation_box:Ue,over_ellipsis:$e},{Dragger:ze}=ke,{RangePicker:Ge}=V,it=()=>{var U;const[f]=g.useForm(),Y=r.useRef(null),D=r.useRef(null),E=r.useRef(null),[J,K]=r.useState(0),[C,M]=r.useState(!0),[Q,N]=r.useState(!1),[W,I]=r.useState(!1),[X,_]=r.useState(!1),[x,Z]=r.useState([]),[A,ee]=r.useState([]),[v,te]=r.useState([]),[ae,ne]=r.useState([]),[u,k]=r.useState({total:0,pageNum:1,pageSize:50}),{runAsync:R}=y(T.getEnumType,{manual:!0}),{runAsync:le}=y(T.updateOther,{manual:!0}),{runAsync:se}=y(T.downloadEmployeeTemplate,{manual:!0}),{runAsync:re}=y(T.emportOther,{manual:!0}),{runAsync:oe}=y(T.importOther,{manual:!0}),{runAsync:ie}=y(T.additionalBudget,{manual:!0}),ce=[{title:"单位",width:170,dataIndex:"cityName",align:"center",key:"cityName",fixed:"left",render:(t,n)=>e.jsx(b,{title:t,children:e.jsx("div",{className:m.over_ellipsis,children:t})})},{title:"分类",width:120,dataIndex:"category",align:"center",key:"category",fixed:"left"},{title:"账期",dataIndex:"cycleId",align:"center",key:"cycleId",width:150},{title:"金额",dataIndex:"amount",align:"center",key:"amount",width:80,render:t=>e.jsx("span",{children:De(t)})},{title:"导入人名称",dataIndex:"createName",align:"center",key:"createName",width:150},{title:"导入人编号",dataIndex:"createEmpId",align:"center",key:"createEmpId",width:150},{title:"导入人组织",dataIndex:"createOrgaName",align:"center",key:"createOrgaName",width:150,render:(t,n)=>e.jsx(b,{title:t,children:e.jsx("div",{className:m.over_ellipsis,children:t})})},{title:"导入时间",dataIndex:"createTime",align:"center",key:"createTime",width:150},{title:"备注",dataIndex:"remark",key:"remark",align:"center",width:100,render:(t,n)=>e.jsx(b,{title:t,children:e.jsx("div",{className:m.over_ellipsis,children:t})})}],de=[{title:"分类",width:"30%",dataIndex:"category",key:"category",select:!0,children:v,fixed:"left"},{title:"账期",width:"30%",dataIndex:"cycleId",key:"cycleId",fixed:"left",date:"month"},{title:"金额",dataIndex:"amount",key:"amount",fixed:"right",width:"30%"}];r.useEffect(()=>(ue(),window.addEventListener("resize",O),()=>{window.removeEventListener("resize",O)}),[]);const O=()=>{S()};r.useEffect(()=>{S()},[(document.querySelector(".other_table .ant-table-header")||{}).offsetHeight]);const S=()=>{var a;const t=(document.querySelector(".other_table .ant-table-header")||{}).offsetHeight||0,n=(document.querySelector(".other_table .ant-table-pagination")||{}).offsetHeight||26;t&&n&&K(((a=D.current)==null?void 0:a.offsetHeight)-(E.current.offsetHeight+t+n))},F=async t=>{var n;try{P("正在导出",0,"loading");let a=null;if(t===1)a=await se({templateId:"ADDITIONAL_BUDGET"});else if(t===2){const{loginDate:l,cityId:i,category:c,cycleId:d}=f.getFieldsValue(),s={beginTime:(l==null?void 0:l.length)>0?o(l[0]).format("YYYY-MM-DD"):null,endTime:(l==null?void 0:l.length)>0?o(l[1]).format("YYYY-MM-DD"):null,cycleId:d?(n=o(d))==null?void 0:n.format("YYYYMM"):"",cityId:i?i[(i==null?void 0:i.length)-1]:null,category:c};a=await re(s)}Ae(a)}catch(a){P("导出失败",1,"error"),console.error("Download failed:",a)}},me=t=>{w({...t})},ue=async()=>{var c;const[[t,n],[a,l]]=await Promise.all([R({code:"1010",tag:1}),R({code:"ADDITIONAL_BUDGET_CATEGORY"})]);if(t||a)return;n.STATUS==="0000"&&ee((c=n.DATA)==null?void 0:c.filter(d=>(d==null?void 0:d.orgId)!=="49757")),l.STATUS==="0000"&&te(l.DATA);const i={...f.getFieldsValue()};w(i)},w=async t=>{var $,z;N(!0);const{loginDate:n,cityId:a,category:l,cycleId:i}=t,c={...u,beginTime:(n==null?void 0:n.length)>0?o(n[0]).format("YYYY-MM-DD"):null,endTime:(n==null?void 0:n.length)>0?o(n[1]).format("YYYY-MM-DD"):null,cycleId:i?($=o(i))==null?void 0:$.format("YYYYMM"):"",cityId:a?a[(a==null?void 0:a.length)-1]:null,category:l};console.log(123);const[d,s]=await le(c);if(N(!1),d){h.error((s==null?void 0:s.DATA)||(s==null?void 0:s.MESSAGE)||"调用失败");return}if(s.STATUS==="0000"){const{DATA:{data:Te}}=s,Se=Te.map((G,we)=>({...G,key:G.id||we}));ne(Se),k({...u,total:(z=s.DATA)==null?void 0:z.totalCount})}else h.error((s==null?void 0:s.MESSAGE)||(s==null?void 0:s.DATA)||"调用失败")},he=async()=>{const t=new FormData;x.map(l=>l==null?void 0:l.originFileObj).forEach(l=>{t.append("file",l)});const[n,a]=await oe(t);if(n){h.error((a==null?void 0:a.DATA)||(a==null?void 0:a.MESSAGE)||"调用失败");return}a.STATUS==="0000"?(_(!1),h.success(a==null?void 0:a.DATA)):h.error((a==null?void 0:a.MESSAGE)||(a==null?void 0:a.DATA)||"调用失败")},pe=()=>{(x==null?void 0:x.length)>0?he():h.error("请先选择文件上传")},L=()=>{_(!1)},fe=()=>{f.resetFields()},xe=t=>{const n={total:t==null?void 0:t.total,pageNum:t==null?void 0:t.current,pageSize:t==null?void 0:t.pageSize};k(n)};r.useEffect(()=>{if((u==null?void 0:u.total)>0){const t={...f.getFieldsValue()};w(t)}},[u.pageNum,u.pageSize]);const ge=async t=>{t.forEach(l=>{var c;const i=A.filter(d=>d.enumId===l.cityId)[0].enumName;l.cityName=i,l.cycleId=(c=o(l.cycleId))==null?void 0:c.format("YYYYMM")}),console.log("record",t);const[n,a]=await ie(t);if(N(!1),n){h.error((a==null?void 0:a.DATA)||(a==null?void 0:a.MESSAGE)||"调用失败");return}if(a.STATUS==="0000"){const{loginDate:l,cycleId:i,cityId:c,category:d}=f.getFieldsValue(),s={beginTime:l.length>0?o(l[0]).format("YYYY-MM-DD"):null,endTime:l.length>0?o(l[1]).format("YYYY-MM-DD"):null,cycleId:i?o(i).format("YYYY-MM"):"",cityId:c[(c==null?void 0:c.length)-1],category:d};w(s),I(!1)}else h.error((a==null?void 0:a.MESSAGE)||(a==null?void 0:a.DATA)||"调用失败")},ye=(t,n)=>{var a,l;return(((a=n[0])==null?void 0:a.enumName)??"").toLowerCase().includes((l=t.toLowerCase())==null?void 0:l.trim())},je=t=>{console.log(t)};return e.jsxs("div",{className:`pt-[0.5rem] px-[0rem] h-full flex flex-col ${m.other_page}`,children:[e.jsxs(e.Fragment,{children:[e.jsx("div",{ref:Y,className:"bg-white pt-[0.5rem] px-[1.75rem] mb-[0.5rem]",children:e.jsx(g,{form:f,labelCol:{span:6},onFinish:me,initialValues:{loginDate:"",category:"DA_XUE_SHENG_ZU_FANG_BU_TIE",cityId:""},children:e.jsxs(H,{gutter:24,children:[e.jsx(j,{span:7,children:e.jsx(g.Item,{label:"导入时间",name:"loginDate",wrapperCol:{span:24},className:"mb-[0.5rem]",children:e.jsx(Ge,{style:{width:"100%"},allowClear:!0,ranges:{近一天:[o().subtract(1,"day"),o()],近三天:[o().subtract(3,"day"),o()],近七天:[o().subtract(7,"day"),o()]}})})}),e.jsx(j,{span:5,children:e.jsx(g.Item,{name:"category",label:"分类",className:"mb-[0.5rem]",children:e.jsx(B,{placeholder:"请选择类型",allowClear:!0,children:v.map(t=>{const{enumId:n,enumName:a}=t;return e.jsx(B.Option,{value:n,children:a},n)})})})}),e.jsx(j,{span:4,children:e.jsx(g.Item,{name:"cycleId",label:"账期",className:"mb-[0.5rem]",children:e.jsx(V,{className:"w-full",picker:"month"})})}),e.jsx(j,{span:4,children:e.jsx(g.Item,{name:"cityId",label:"单位",className:"mb-[0.5rem]",children:e.jsx(Ee,{allowClear:!0,changeOnSelect:!0,expandTrigger:"hover",displayRender:t=>t[t.length-1],options:A,fieldNames:{value:"enumId",label:"enumName",children:"children"},placeholder:"请选择",showSearch:{filter:ye},onSearch:t=>console.log(t)})})}),e.jsx(j,{span:4,children:e.jsx("div",{className:"text-right",children:e.jsxs(Ne,{children:[e.jsx(p,{type:"primary",htmlType:"submit",children:"查询"}),e.jsx(p,{onClick:()=>fe(),children:"重置"})]})})})]})})}),e.jsxs("div",{ref:D,className:"relative bg-white px-5 pt-2.5",style:{height:`calc(100% - ${((U=Y.current)==null?void 0:U.offsetHeight)+17}px)`},children:[e.jsxs("div",{ref:E,className:`flex justify-between items-center overflow-hidden mb-2 ${m.animation_box} ${C?"h-[1.8rem]":"h-0"}`,children:[e.jsxs("div",{className:"flex ",children:[C?e.jsx(Ce,{className:`${m.shousuo_icon} text-[1rem]`,onClick:()=>{M(!1),setTimeout(()=>{S()},200)}}):e.jsx(Me,{className:`${m.shousuo_icon} absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10`,onClick:()=>{M(!0),setTimeout(()=>{S()},200)}}),e.jsx("div",{className:"font-bold text-[0.8rem] ml-3",children:"数据列表"})]}),e.jsx("div",{className:"flex gap-x-[2.5rem]",children:e.jsxs("div",{className:"flex gap-x-[0.75rem]",children:[e.jsxs("div",{className:"flex items-center gap-x-[0.25rem]",children:[e.jsx(Ie,{name:"excel",width:20,height:20}),e.jsx("span",{className:"text-[#E60027] cursor-pointer",onClick:()=>F(1),children:"下载导入模版"})]}),e.jsx(p,{danger:!0,ghost:!0,icon:e.jsx(ve,{}),onClick:()=>_(!0),children:"导入"}),e.jsx(p,{danger:!0,ghost:!0,icon:e.jsx(_e,{}),onClick:()=>F(2),children:"导出"}),e.jsx(p,{danger:!0,type:"primary",onClick:()=>I(!0),children:"新增"})]})})]}),e.jsx(Ye,{className:"other_table",rowClassName:(t,n)=>n%2===1?"customRow odd":"customRow even",columns:ce,dataSource:ae,scroll:{y:`calc(${J}px - 0.625rem - 1.6rem - 0.5rem)`},loading:Q,onChange:xe,pagination:{...u,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50"]}})]})]}),e.jsx(q,{title:"新增数据",destroyOnClose:!0,open:W,centered:!0,className:m.detail_modal,footer:null,onCancel:()=>I(!1),width:"50%",children:e.jsx(be,{columns:de,addData:ge,unitList:A})}),e.jsx(q,{title:"文件上传",destroyOnClose:!0,open:X,centered:!0,className:m.detail_modal,footer:null,onCancel:L,children:e.jsxs("div",{className:"mt-4 mb-8",style:{marginBottom:"0px"},children:[e.jsx(H,{children:e.jsx(j,{span:22,offset:1,className:"h-[10rem]",children:e.jsxs(ze,{action:"",maxCount:1,multiple:!1,fileList:x,beforeUpload(t,n){return console.log(t,n),!1},onChange(t){const{status:n}=t.file;n!=="uploading"&&(console.log(t.file,t.fileList),Z(t.fileList))},children:[e.jsx("p",{className:"ant-upload-drag-icon",children:e.jsx(Re,{style:{color:"#F14846"}})}),e.jsx("p",{className:"ant-upload-text",children:"点击或将文件拖拽到这里上传"}),e.jsx("p",{className:"ant-upload-hint",children:"支持excel格式的文件。"})]})})}),e.jsxs("div",{style:{display:"flex",justifyContent:"center",marginTop:"2rem"},children:[e.jsx(p,{danger:!0,onClick:L,children:"取消"}),e.jsx(Oe,{title:"",description:"导入文件如有重复数据，将会被覆盖，请确认是否上传。",onConfirm:pe,onCancel:je,okText:"确认",cancelText:"取消",children:e.jsx(p,{danger:!0,type:"primary",style:{marginLeft:"1rem"},disabled:x.length<1,children:"上传"})})]})]})})]})};export{it as default};
