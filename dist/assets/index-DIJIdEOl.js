import{F as s,r as a,u as h,d as ee,j as l,R as te,C as w,S as le,B as f}from"./index-De_f0oL2.js";import{s as d,c as p}from"./Index.module-CMg3LkdG.js";import{R as ne,o as C,d as ae}from"./down-BCLNnN1h.js";import{R as ie}from"./index-BmnYJy3v.js";import{s as m}from"./index-BcPP1N8I.js";import{D as re}from"./index-CdSZ9YgQ.js";import{S as ce}from"./index-BWJehDyc.js";import{R as se,a as oe}from"./FullscreenOutlined-DzCTibKW.js";import"./Table-D-iLeFE-.js";import"./useMultipleSelect-B0dEIXT-.js";const be=()=>{var R;const[r]=s.useForm(),x=a.useRef(null),g=a.useRef(null),y=a.useRef(null),[v,E]=a.useState(0),[b,V]=a.useState([]),[z,S]=a.useState(!1),[F,$]=a.useState([]),[u,L]=a.useState(0),[H,M]=a.useState([]),[A,N]=a.useState(!0),[i,D]=a.useState({total:0,pageNum:1,pageSize:50}),_=[{title:"单位(当月）",key:"unit",dataIndex:"unit",align:"center",fixed:"left",width:120,children:[]},{title:"基层责任单元经理合计",key:"um",dataIndex:"um",align:"center",children:[{title:"人数",key:"umNumber",dataIndex:"umNumber",align:"center",children:[],width:120},{title:"平均年龄",key:"umAvg",dataIndex:"umAvg",align:"center",children:[],width:120},{title:"其中：37 岁及以下",key:"um37DownNumber",dataIndex:"um37DownNumber",align:"center",children:[],width:140},{title:"其中：87后人数",key:"um87AfterDownNumber",dataIndex:"um87AfterDownNumber",align:"center",children:[],width:140},{title:"87后占比",key:"um87AfterDownScale",dataIndex:"um87AfterDownScale",align:"center",children:[],width:100},{title:"其中：32 岁及以下",key:"um32DownNumber",dataIndex:"um32DownNumber",align:"center",children:[],width:140},{title:"32及以下占比",key:"um32DownScale",dataIndex:"um32DownScale",align:"center",children:[],width:120}]},{title:"三级经理合计",key:"ml",dataIndex:"ml",align:"center",children:[{title:"人数",key:"mlNumber",dataIndex:"mlNumber",align:"center",children:[],width:120},{title:"平均年龄",key:"mlAvg",dataIndex:"mlAvg",align:"center",children:[],width:120},{title:"87后占比",key:"ml87AfterDownScale",dataIndex:"ml87AfterDownScale",align:"center",children:[],width:120},{title:"其中: 90后",key:"ml90AfterDownNumber",dataIndex:"ml90AfterDownNumber",align:"center",children:[],width:120},{title:" 90后占比",key:"ml90AfterDownScale",dataIndex:"ml90AfterDownScale",align:"center",children:[],width:120}]},{title:"三级正",key:"l3Principal",dataIndex:"l3Principal",align:"center",children:[{title:"人数",key:"l3PrincipalNumber",dataIndex:"l3PrincipalNumber",align:"center",children:[],width:120},{title:"平均年龄",key:"l3PrincipalAvg",dataIndex:"l3PrincipalAvg",align:"center",children:[],width:120},{title:"其中：37岁及以下",key:"l3Principal37DownNumber",dataIndex:"l3Principal37DownNumber",align:"center",children:[],width:140},{title:"37岁及以下占比",key:"l3Principal37DownScale",dataIndex:"l3Principal37DownScale",align:"center",children:[],width:140},{title:"其中：32岁及以下",key:"l3Principal32DownNumber",dataIndex:"l3Principal32DownNumber",align:"center",children:[],width:140},{title:"32岁及以下占比",key:"l3Principal32DownScale",dataIndex:"l3Principal32DownScale",align:"center",children:[],width:140}]},{title:"三级副",key:"l3Vice",dataIndex:"l3Vice",align:"center",children:[{title:"人数",key:"l3ViceNumber",dataIndex:"l3ViceNumber",align:"center",children:[],width:120},{title:"平均年龄",key:"l3ViceAvg",dataIndex:"l3ViceAvg",align:"center",children:[],width:120},{title:"其中：37岁及以下",key:"l3Vice37DownNumber",dataIndex:"l3Vice37DownNumber",align:"center",children:[],width:140},{title:"37岁及以下占比",key:"l3Vice37DownScale",dataIndex:"l3Vice37DownScale",align:"center",children:[],width:140},{title:"其中：32岁及以下",key:"l3Vice32DownNumber",dataIndex:"l3Vice32DownNumber",align:"center",children:[],width:140},{title:"32岁及以下占比",key:"l3Vice32DownScale",dataIndex:"l3Vice32DownScale",align:"center",children:[],width:140}]}],{runAsync:Y}=h(p.getEnumType,{manual:!0}),{runAsync:q}=h(p.getPeopleAnalysisList,{manual:!0}),{runAsync:G}=h(p.exportPeopleAnalysis,{manual:!0});a.useEffect(()=>($(_),O(),k(),o(),window.addEventListener("resize",I),()=>{window.removeEventListener("resize",I)}),[]);const I=()=>{c()};a.useEffect(()=>{c()},[(document.querySelector(".peopleAnalysisReport_table .ant-table-header")||{}).offsetHeight]),a.useEffect(()=>{c()},[b]),a.useEffect(()=>{u>0&&o()},[u]),a.useEffect(()=>{(i==null?void 0:i.total)>0&&o()},[i.pageNum,i.pageSize]);const c=()=>{var n;const t=(document.querySelector(".peopleAnalysisReport_table .ant-table-header")||{}).offsetHeight||0,e=(document.querySelector(".peopleAnalysisReport_table .ant-table-pagination")||{}).offsetHeight||26;t&&e&&E(((n=g.current)==null?void 0:n.offsetHeight)-(y.current.offsetHeight+t+e))},O=async()=>{const[t,e]=await Y({code:"1010",tag:1});if(t){m.error((e==null?void 0:e.DATA)||(e==null?void 0:e.MESSAGE)||"调用失败");return}e.STATUS==="0000"?M(e.DATA):m.error((e==null?void 0:e.MESSAGE)||(e==null?void 0:e.DATA)||"调用失败")},o=async()=>{var j,T;const t=r.getFieldsValue();S(!0);const[e,n]=await q({map:{...t,cycleId:(j=t==null?void 0:t.cycleId)==null?void 0:j.format("YYYYMM")},pagination:{...i}});if(S(!1),e){m.error((n==null?void 0:n.DATA)||(n==null?void 0:n.MESSAGE)||"调用失败");return}if(n.STATUS==="0000"){const{DATA:{data:W}}=n,X=W.map((P,Z)=>({...P,key:P.id||Z}));V(X),D({...i,total:(T=n.DATA)==null?void 0:T.totalCount})}else m.error((n==null?void 0:n.MESSAGE)||(n==null?void 0:n.DATA)||"调用失败")},k=()=>{const t=ee().subtract(1,"month");r.setFieldsValue({cycleId:t})},B=t=>{console.log("Success:",t),o()},U=async()=>{var t;try{C("正在导出",0,"loading");const e=r.getFieldsValue(),n=await G({map:{...e,cycleId:(t=e==null?void 0:e.cycleId)==null?void 0:t.format("YYYYMM")}});ae(n)}catch(e){C("导出失败",1,"error"),console.error("Download failed:",e)}},J=()=>{const t=u+1;r.resetFields(),k(),L(t)},K=t=>{const e={total:t==null?void 0:t.total,pageNum:t==null?void 0:t.current,pageSize:t==null?void 0:t.pageSize};D(e)},Q=(t,e)=>{var n;return((e==null?void 0:e.enumName)??"").toLowerCase().includes((n=t.toLowerCase())==null?void 0:n.trim())};return l.jsxs("div",{className:`pt-[0.5rem] px-[0rem] h-full flex flex-col ${d.employment_page}`,children:[l.jsx("div",{ref:x,className:"bg-white pt-2 px-8 mb-2",children:l.jsx(s,{form:r,initialValues:{tag:""},onFinish:B,autoComplete:"off",children:l.jsxs(te,{gutter:24,children:[l.jsx(w,{span:6,children:l.jsx(s.Item,{label:"月份",name:"cycleId",wrapperCol:{span:20},className:"mb-[0.5rem]",children:l.jsx(re,{className:"w-full",allowClear:!1,picker:"month"})})}),l.jsx(w,{span:6,children:l.jsx(s.Item,{label:"单位",name:"unit",wrapperCol:{span:20},className:"mb-[0.5rem]",children:l.jsx(ce,{placeholder:"请选择",className:"w-full",allowClear:!0,showSearch:!0,filterOption:Q,options:H,fieldNames:{value:"enumName",label:"enumName"}})})}),l.jsx(w,{span:6,children:l.jsx(s.Item,{labelCol:{span:0},wrapperCol:{span:24},className:"mb-[0.5rem]",children:l.jsxs(le,{size:"small",children:[l.jsx(f,{type:"primary",htmlType:"submit",children:"查询"}),l.jsx(f,{htmlType:"button",onClick:()=>J(),children:"重置"})]})})})]})})}),l.jsxs("div",{ref:g,className:"relative bg-white px-5 pt-2.5",style:{height:`calc(100% - ${((R=x.current)==null?void 0:R.offsetHeight)+15}px)`},children:[l.jsxs("div",{ref:y,className:`flex justify-between items-center overflow-hidden mb-2 ${d.animation_box} ${A?"h-[1.8rem]":"h-0"}`,children:[l.jsxs("div",{className:"flex ",children:[A?l.jsx(se,{className:`${d.shousuo_icon} text-[1rem]`,onClick:()=>{N(!1),setTimeout(()=>{c()},200)}}):l.jsx(oe,{className:`${d.shousuo_icon} absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10`,onClick:()=>{N(!0),setTimeout(()=>{c()},200)}}),l.jsxs("div",{className:"font-bold text-[0.8rem] ml-3",children:["数据列表",l.jsx("span",{className:"text-[0.7rem]",style:{color:"#939393"},children:"（单位：万元）"})]})]}),l.jsx(f,{danger:!0,ghost:!0,icon:l.jsx(ne,{}),onClick:()=>U(),children:"导出"})]}),l.jsx(ie,{className:"peopleAnalysisReport_table",rowClassName:(t,e)=>e%2===1?"customRow odd":"customRow even",columns:F,dataSource:b,loading:z,bordered:!0,scroll:{y:`calc(${v}px - 0.625rem - 1.6rem - 0.5rem)`},onChange:K,pagination:{...i,total:i==null?void 0:i.total,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50"]}})]})]})};export{be as default};
