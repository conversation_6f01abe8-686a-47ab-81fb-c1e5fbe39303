import{r as l,F as m,j as t,u as j,d as v,R as Se,C as u,y as C,S as ve,B as I,T as g}from"./index-De_f0oL2.js";import{p as Ce}from"./service-ClCV2GnI.js";import{e as N}from"./service-DcPXuTuP.js";import{R as Ie,o as q,d as Ne}from"./down-BCLNnN1h.js";import{s as d}from"./Index.module-CQ0G-enU.js";import{R as Te}from"./index-BmnYJy3v.js";import{s as $e}from"./index-BcPP1N8I.js";import{D as Re}from"./index-CdSZ9YgQ.js";import{C as _e}from"./index-DZyVV6rP.js";import{S as U}from"./index-BWJehDyc.js";import{R as Ee,a as Fe}from"./FullscreenOutlined-DzCTibKW.js";import"./Table-D-iLeFE-.js";import"./useMultipleSelect-B0dEIXT-.js";const We=()=>{var z;const T=[{title:"单位",key:"unit",dataIndex:"unit",align:"center",fixed:"left",width:150,children:[],render:(e,s)=>t.jsx(g,{title:e,children:t.jsx("div",{className:d.over_ellipsis,children:e})})},{title:"部门",key:"business",dataIndex:"business",align:"center",fixed:"left",width:200,children:[],render:(e,s)=>t.jsx(g,{title:e,children:t.jsx("div",{className:d.over_ellipsis,children:e&&e.startsWith("天津市分公司")?e.slice(6):e})})},{title:"员工编号",key:"code",dataIndex:"code",align:"center",fixed:"left",width:75,children:[]},{title:"员工姓名",key:"name",dataIndex:"name",align:"center",fixed:"left",width:75,children:[]},{title:"当前岗位",key:"post",dataIndex:"post",align:"center",width:110,children:[],render:(e,s)=>t.jsx(g,{title:e,children:t.jsx("div",{className:d.over_ellipsis,children:e})})},{title:"岗级",key:"level",dataIndex:"level",align:"center",children:[],width:50}],$=l.useRef(null),R=l.useRef(null),_=l.useRef(null),[r]=m.useForm(),[B,W]=l.useState(0),[E,G]=l.useState([]),[J,F]=l.useState(!1),[K,Q]=l.useState(T),[X,Z]=l.useState([]),[Y,O]=l.useState(r.getFieldValue([])),[h,b]=l.useState([]),[y,ee]=l.useState(0),[te,se]=l.useState([]),[k,A]=l.useState(!0),[i,L]=l.useState({total:0,pageNum:1,pageSize:50}),{runAsync:oe}=j(Ce.getEnumType,{manual:!0}),{runAsync:ne}=j(N.build4LevelOrgTree,{manual:!0}),{runAsync:le}=j(N.getEmployeePerPag,{manual:!0}),{runAsync:ae}=j(N.exportEmployeePerExcel,{manual:!0});l.useEffect(()=>(re(),P(),x(),window.addEventListener("resize",V),()=>{window.removeEventListener("resize",V)}),[]);const V=()=>{p()};l.useEffect(()=>{p()},[(document.querySelector(".report_personal_table .ant-table-header")||{}).offsetHeight]),l.useEffect(()=>{p()},[E]),l.useEffect(()=>{y>0&&x()},[y]),l.useEffect(()=>{(i==null?void 0:i.total)>0&&x()},[i.pageNum,i.pageSize]);const p=()=>{var o;const e=(document.querySelector(".report_personal_table .ant-table-header")||{}).offsetHeight,s=(document.querySelector(".report_personal_table .ant-table-pagination")||{}).offsetHeight||26;e&&s&&W(((o=R.current)==null?void 0:o.offsetHeight)-(_.current.offsetHeight+e+s))},re=async()=>{const[e,s]=await oe({code:"1014",region:""});e||s.STATUS==="0000"&&Z(s.DATA)},M=async()=>{var o;const[e,s]=await ne({monthId:(o=r.getFieldValue("month"))==null?void 0:o.format("YYYYMM"),tag:"2"});e||s.STATUS==="0000"&&(se(s.DATA),b([]),r.setFieldValue("org",""))},x=async e=>{var w,D,H;F(!0),ie();const s=r.getFieldsValue(),o=i,n=h?h[h.length-1]:{},[c,a]=await le({...s,month:(w=s==null?void 0:s.month)==null?void 0:w.format("YYYYMM"),orgId4:Number(n==null?void 0:n.level)===4?n==null?void 0:n.orgId:"",orgId5:Number(n==null?void 0:n.level)===5?n==null?void 0:n.orgId:"",orgId6:Number(n==null?void 0:n.level)===6?n==null?void 0:n.orgId:"",post:(D=s==null?void 0:s.post)==null?void 0:D.split(",")[0],...o});if(F(!1),!c)if(a.STATUS==="0000"){const{DATA:{data:je}}=a,S=v(r.getFieldValue("month")).format("YYYYMM"),be=je.map((f,ye)=>({...f,key:f.id||ye,[`unit${S}`]:f.unit,[`post${S}`]:f.post,[`level${S}`]:f.level}));G(be),L({...i,total:(H=a.DATA)==null?void 0:H.totalCount})}else $e.error(a==null?void 0:a.MESSAGE)},P=()=>{const e=v();r.setFieldsValue({month:e,org:[]}),M()},ie=()=>{const e=v(r.getFieldValue("month")),s=e.month(),o=[...T];for(let n=s+1;n>=0;n--){const c=`${e.subtract(n,"month").format("YYYYMM")}`;o.push({title:c,key:`month${c}`,dataIndex:`month${c}`,align:"center",width:500,children:[{title:"单位",key:`unit${c}`,dataIndex:`unit${c}`,align:"center",width:150,render:(a,w)=>t.jsx(g,{title:a,children:t.jsx("div",{className:d.over_ellipsis,children:a})})},{title:"岗位",key:`post${c}`,dataIndex:`post${c}`,align:"center",width:110,render:(a,w)=>t.jsx(g,{title:a,children:t.jsx("div",{className:d.over_ellipsis,children:a})})},{title:"岗级",key:`level${c}`,dataIndex:`level${c}`,align:"center",width:50}]})}Q(o)},ce=()=>{O(r.getFieldValue("org"))},de=(e,s)=>{b(s)},me=e=>{console.log("Success:",e),x()},he=async()=>{var e;try{q("正在导出",0,"loading");const s=r.getFieldsValue(),o=h?h[h.length-1]:{},n=await ae({...s,orgId4:Number(o==null?void 0:o.level)===4?o==null?void 0:o.orgId:"",orgId5:Number(o==null?void 0:o.level)===5?o==null?void 0:o.orgId:"",orgId6:Number(o==null?void 0:o.level)===6?o==null?void 0:o.orgId:"",month:(e=s==null?void 0:s.month)==null?void 0:e.format("YYYYMM")});Ne(n)}catch(s){q("导出失败",1,"error"),console.error("Download failed:",s)}},ue=()=>{const e=y+1;r.resetFields(),b([]),P(),ee(e)},pe=(e,s)=>s.some(o=>o.orgName.toLowerCase().indexOf(e.trim().toLowerCase())>-1),fe=(e,s)=>{console.log(e,s)},ge=e=>{const s={total:e==null?void 0:e.total,pageNum:e==null?void 0:e.current,pageSize:e==null?void 0:e.pageSize};L(s)},xe=(e,s)=>{const o=e.trim();return s.children.toLowerCase().includes(o.toLowerCase())},we=e=>{e.month&&M()};return t.jsxs("div",{className:`h-full pt-[0.5rem] flex flex-col ${d.employment_page}`,children:[t.jsx("div",{ref:$,className:"bg-white pt-[0.5rem] px-8 mb-[0.5rem]",children:t.jsx(m,{form:r,initialValues:{tag:""},wrapperCol:{span:20},onFieldsChange:ce,onFinish:me,onValuesChange:we,autoComplete:"off",children:t.jsxs(Se,{gutter:24,children:[t.jsx(u,{span:6,children:t.jsx(m.Item,{label:"月份",name:"month",children:t.jsx(Re,{className:"w-full",allowClear:!1,onChange:fe,picker:"month"})})}),t.jsx(u,{span:6,children:t.jsx(m.Item,{label:"组织",name:"org",children:t.jsx(_e,{allowClear:!0,changeOnSelect:!0,expandTrigger:"hover",displayRender:e=>e[e.length-1],options:te,onChange:de,fieldNames:{value:"orgId",label:"orgName",children:"children"},placeholder:"请选择",showSearch:{filter:pe},onSearch:e=>console.log(e)})})}),t.jsx(u,{span:6,children:t.jsx(m.Item,{label:"员工姓名",name:"name",children:t.jsx(C,{placeholder:"请输入员工姓名",allowClear:!0})})}),t.jsx(u,{span:6,children:t.jsx(m.Item,{label:"员工编号",name:"code",children:t.jsx(C,{placeholder:"请输入员工编号",allowClear:!0})})}),t.jsx(u,{span:6,children:t.jsx(m.Item,{label:"岗位",name:"post",children:Y&&Y[0]==="49757"?t.jsx(U,{placeholder:"请选择",className:"w-full",allowClear:!0,showSearch:!0,filterOption:xe,children:X.map(e=>{const{region:s,enumName:o}=e;return t.jsx(U.Option,{value:`${o},${s}`,children:o},`${o},${s}`)})}):t.jsx(C,{placeholder:"请输入",allowClear:!0})})}),t.jsx(u,{span:6,children:t.jsx(m.Item,{labelCol:{span:0},wrapperCol:{span:24},children:t.jsxs(ve,{size:"small",children:[t.jsx(I,{type:"primary",htmlType:"submit",children:"查询"}),t.jsx(I,{htmlType:"button",onClick:()=>ue(),children:"重置"})]})})})]})})}),t.jsxs("div",{ref:R,className:"relative bg-white px-5 pt-2.5",style:{height:`calc(100% - ${((z=$.current)==null?void 0:z.offsetHeight)+15}px)`},children:[t.jsxs("div",{ref:_,className:`flex justify-between items-center mb-2 overflow-hidden ${d.animation_box} ${k?"h-[2rem]":"h-0"}`,children:[t.jsxs("div",{className:"flex ",children:[k?t.jsx(Ee,{className:`${d.shousuo_icon} text-[1rem]`,onClick:()=>{A(!1),setTimeout(()=>{p()},200)}}):t.jsx(Fe,{className:`${d.shousuo_icon} absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10`,onClick:()=>{A(!0),setTimeout(()=>{p()},200)}}),t.jsx("div",{className:"font-bold text-[0.8rem] ml-3",children:"数据列表"})]}),t.jsx(I,{danger:!0,ghost:!0,icon:t.jsx(Ie,{}),onClick:()=>he(),children:"导出"})]}),t.jsx(Te,{className:"report_personal_table",rowClassName:(e,s)=>s%2===1?"customRow odd":"customRow even",columns:K,dataSource:E,loading:J,bordered:!0,scroll:{y:`calc(${B}px - 0.625rem - 1.6rem - 0.5rem)`},onChange:ge,pagination:{...i,total:i==null?void 0:i.total,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50"]}})]})]})};export{We as default};
