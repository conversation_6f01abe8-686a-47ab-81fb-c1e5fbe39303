import{F as u,r as i,u as w,d as h,j as t,R as de,C as p,y as H,S as me,B as P,T as ue}from"./index-De_f0oL2.js";import{p as I}from"./service-B_wWoC3F.js";import{R as he,o as L,d as pe}from"./down-BCLNnN1h.js";import{R as ge}from"./index-BmnYJy3v.js";import{s as O}from"./index-BcPP1N8I.js";import{D as ye}from"./index-CdSZ9YgQ.js";import{C as V}from"./index-DZyVV6rP.js";import{R as fe,a as xe}from"./FullscreenOutlined-DzCTibKW.js";import"./Table-D-iLeFE-.js";import"./index-BWJehDyc.js";import"./useMultipleSelect-B0dEIXT-.js";const ve="_pointSalaryCalc_page_1uv4x_6",we="_animation_box_1uv4x_6",Ie="_over_ellipsis_1uv4x_9",y={pointSalaryCalc_page:ve,animation_box:we,over_ellipsis:Ie},Ye=()=>{var E;const[m]=u.useForm(),b=i.useRef(null),k=i.useRef(null),j=i.useRef(null),[q,G]=i.useState(0),[C,R]=i.useState(!0),[U,T]=i.useState(!1),[B,J]=i.useState([]),[K,_]=i.useState([]),[Q,W]=i.useState([]),[s,A]=i.useState({total:0,pageNum:1,pageSize:50}),{runAsync:S}=w(I.getEnumType,{manual:!0}),{runAsync:Z}=w(I.getPointSalaryCalc,{manual:!0}),{runAsync:Se}=w(I.downloadTemplate,{manual:!0}),{runAsync:X}=w(I.exportPointSalaryCalc,{manual:!0}),ee=[{title:"单位",dataIndex:"cityName",key:"cityName",align:"center",width:160,fixed:"left",render:(e,a)=>t.jsx(ue,{title:e,children:t.jsx("div",{className:y.over_ellipsis,children:e})})},{title:"营服中心",dataIndex:"departmentRoom",key:"departmentRoom",align:"center",width:130,render:(e,a)=>a.departmentRoom===a.employeeNumber?{children:e,props:{colSpan:4,rowSpan:1}}:e},{title:"员工编号",dataIndex:"employeeNumber",key:"employeeNumber",align:"center",width:100,render:(e,a)=>a.departmentRoom===a.employeeNumber?{children:e,props:{colSpan:0}}:e},{title:"员工姓名",dataIndex:"employeeName",key:"employeeName",align:"center",width:100,render:(e,a)=>a.departmentRoom===a.employeeNumber?{children:e,props:{colSpan:0}}:e},{title:"角色名称",dataIndex:"roleName",key:"roleName",align:"center",width:100,render:(e,a)=>a.departmentRoom===a.employeeNumber?{children:e,props:{colSpan:0}}:e},{title:"积分绩效",key:"level",dataIndex:"level",align:"center",children:[{title:"绩效合计",dataIndex:"performanceAll",key:"performanceAll",align:"center",width:100},{title:"服务（建维）积分绩效",key:"level",dataIndex:"level",align:"center",children:[{title:"积分",dataIndex:"serviceMaintenancePoints",key:"serviceMaintenancePoints",align:"center",width:80},{title:"单价",dataIndex:"serviceMaintenancePrice",key:"serviceMaintenancePrice",align:"center",width:80},{title:"绩效",dataIndex:"serviceMaintenancePerformance",key:"serviceMaintenancePerformance",align:"center",width:80}]},{title:"动作积分绩效",key:"level",dataIndex:"level",align:"center",children:[{title:"积分",dataIndex:"actionPoints",key:"actionPoints",align:"center",width:80},{title:"单价",dataIndex:"actionPrice",key:"actionPrice",align:"center",width:80},{title:"绩效",dataIndex:"actionPerformance",key:"actionPerformance",align:"center",width:80}]},{title:"价值积分绩效",key:"level",dataIndex:"level",align:"center",children:[{title:"价值积分-营销",key:"level",dataIndex:"level",align:"center",children:[{title:"积分",dataIndex:"valuePointsMarketing",key:"valuePointsMarketing",align:"center",width:80},{title:"单价",dataIndex:"valuePriceMarketing",key:"valuePriceMarketing",align:"center",width:80},{title:"绩效",dataIndex:"valuePerformanceMarketing",key:"valuePerformanceMarketing",align:"center",width:80}]},{title:"价值积分-交付",key:"level",dataIndex:"level",align:"center",children:[{title:"积分",dataIndex:"valuePointsDelivery",key:"valuePointsDelivery",align:"center",width:80},{title:"单价",dataIndex:"valuePriceDelivery",key:"valuePriceDelivery",align:"center",width:80},{title:"绩效",dataIndex:"valuePerformanceDelivery",key:"valuePerformanceDelivery",align:"center",width:80}]},{title:"价值积分-奖惩",key:"level",dataIndex:"level",align:"center",children:[{title:"积分",dataIndex:"valuePointsRap",key:"valuePointsRap",align:"center",width:80},{title:"单价",dataIndex:"valuePriceRap",key:"valuePriceRap",align:"center",width:80},{title:"绩效",dataIndex:"valuePerformanceRap",key:"valuePerformanceRap",align:"center",width:80}]},{title:"绩效合计",key:"valuePerformanceAll",dataIndex:"valuePerformanceAll",align:"center",width:100}]}]},{title:"自定义积分",key:"level",dataIndex:"level",align:"center",children:[{title:"积分",dataIndex:"customPoints",key:"customPoints",align:"center",width:80},{title:"单价",dataIndex:"customPrice",key:"customPrice",align:"center",width:80},{title:"绩效",dataIndex:"customPerformance",key:"customPerformance",align:"center",width:80}]}];i.useEffect(()=>(ne(),Y(),window.addEventListener("resize",M),()=>{window.removeEventListener("resize",M)}),[]);const M=()=>{f()};i.useEffect(()=>{f()},[(document.querySelector(".pointSalaryCalc_table .ant-table-header")||{}).offsetHeight]);const f=()=>{var n;const e=(document.querySelector(".pointSalaryCalc_table .ant-table-header")||{}).offsetHeight||0,a=(document.querySelector(".pointSalaryCalc_table .ant-table-pagination")||{}).offsetHeight||26;e&&a&&G(((n=k.current)==null?void 0:n.offsetHeight)-(j.current.offsetHeight+e+a))},te=async e=>{var a;try{L("正在导出",0,"loading");let n=null;if(e!==1){if(e===2){const{loginDate:l,cityId:o,category:c,cycleId:g,deptId:d,empId:x,empName:v}=m.getFieldsValue(),r={beginTime:(l==null?void 0:l.length)>0?h(l[0]).format("YYYY-MM-DD"):null,endTime:(l==null?void 0:l.length)>0?h(l[1]).format("YYYY-MM-DD"):null,cycleId:g?(a=h(g))==null?void 0:a.format("YYYYMM"):"",cityId:o?o[(o==null?void 0:o.length)-1]:null,deptId:d?d[(d==null?void 0:d.length)-1]:null,category:c,empId:x||null,empName:v||null};n=await X(r)}}pe(n)}catch(n){L("导出失败",1,"error"),console.error("Download failed:",n)}},ae=e=>{N({...e})},ne=async()=>{var o;const[[e,a],[n]]=await Promise.all([S({code:"1010",tag:1}),S({code:"EMPLOYEE_TRANSFER_CATEGORY"})]);if(e||n)return;a.STATUS==="0000"&&J((o=a.DATA)==null?void 0:o.filter(c=>(c==null?void 0:c.orgId)!=="49757"));const l={...m.getFieldsValue()};N(l)},le=async e=>{m.setFieldValue("deptId",""),_([]);const[a,n]=await S({code:"1011",region:e[e.length-1]});a||n.STATUS==="0000"&&_(n.DATA)},N=async e=>{var F,$;T(!0);const{loginDate:a,cityId:n,deptId:l,category:o,cycleId:c,empId:g,empName:d}=e,x={...s,beginTime:(a==null?void 0:a.length)>0?h(a[0]).format("YYYY-MM-DD"):null,endTime:(a==null?void 0:a.length)>0?h(a[1]).format("YYYY-MM-DD"):null,cycleId:c?(F=h(c))==null?void 0:F.format("YYYYMM"):"",cityId:n?n[(n==null?void 0:n.length)-1]:null,deptId:l?l[(l==null?void 0:l.length)-1]:null,category:o,empId:g||null,empName:d||null},[v,r]=await Z(x);if(T(!1),v){O.error((r==null?void 0:r.DATA)||(r==null?void 0:r.MESSAGE)||"调用失败");return}if(r.STATUS==="0000"){const{DATA:{data:oe}}=r,se=oe.map((z,ce)=>({...z,key:z.id||ce}));W(se),A({...s,total:($=r.DATA)==null?void 0:$.totalCount})}else O.error((r==null?void 0:r.MESSAGE)||(r==null?void 0:r.DATA)||"调用失败")},re=()=>{m.resetFields(),Y()},ie=e=>{const a={total:e==null?void 0:e.total,pageNum:e==null?void 0:e.current,pageSize:e==null?void 0:e.pageSize};A(a)};i.useEffect(()=>{if((s==null?void 0:s.total)>0){const e={...m.getFieldsValue()};N(e)}},[s.pageNum,s.pageSize]);const Y=()=>{const e=h().subtract(1,"month");m.setFieldsValue({cycleId:e})},D=(e,a)=>{var n,l;return(((n=a[0])==null?void 0:n.enumName)??"").toLowerCase().includes((l=e.toLowerCase())==null?void 0:l.trim())};return t.jsx("div",{className:`pt-[0.5rem] px-[0rem] h-full flex flex-col ${y.pointSalaryCalc_page}`,children:t.jsxs(t.Fragment,{children:[t.jsx("div",{ref:b,className:"bg-white pt-[0.5rem] px-[1.75rem] mb-[0.5rem]",children:t.jsx(u,{form:m,labelCol:{span:6},onFinish:ae,initialValues:{loginDate:"",category:"KOU_JING_TIAO_ZHENG",cityId:"",empId:"",empName:""},children:t.jsxs(de,{gutter:24,children:[t.jsx(p,{span:5,children:t.jsx(u.Item,{name:"cycleId",label:"账期",className:"mb-[0.5rem]",children:t.jsx(ye,{className:"w-full",picker:"month"})})}),t.jsx(p,{span:5,children:t.jsx(u.Item,{name:"cityId",label:"单位",className:"mb-[0.5rem]",children:t.jsx(V,{allowClear:!0,changeOnSelect:!0,expandTrigger:"hover",displayRender:e=>e[e.length-1],options:B,fieldNames:{value:"enumId",label:"enumName",children:"children"},placeholder:"请选择单位",showSearch:{filter:D},onChange:le,onSearch:e=>console.log(e)})})}),t.jsx(p,{span:5,children:t.jsx(u.Item,{name:"deptId",label:"部室",className:"mb-[0.5rem]",children:t.jsx(V,{allowClear:!0,changeOnSelect:!0,expandTrigger:"hover",displayRender:e=>e[e.length-1],options:K,fieldNames:{value:"enumId",label:"enumName",children:"children"},placeholder:"请选择部室",showSearch:{filter:D},onSearch:e=>console.log(e)})})}),t.jsx(p,{span:5,children:t.jsx(u.Item,{name:"empId",label:"员工编号",wrapperCol:{span:24},className:"mb-[0.5rem]",children:t.jsx(H,{placeholder:"请输入员工编号",allowClear:!0})})}),t.jsx(p,{span:5,children:t.jsx(u.Item,{name:"empName",label:"员工姓名",wrapperCol:{span:24},className:"mb-[0.5rem]",children:t.jsx(H,{placeholder:"请输入员工姓名",allowClear:!0})})}),t.jsx(p,{span:4,children:t.jsx("div",{className:"text-right",children:t.jsxs(me,{children:[t.jsx(P,{type:"primary",htmlType:"submit",children:"查询"}),t.jsx(P,{onClick:()=>re(),children:"重置"})]})})})]})})}),t.jsxs("div",{ref:k,className:"relative bg-white px-5 pt-2.5",style:{height:`calc(100% - ${((E=b.current)==null?void 0:E.offsetHeight)+15}px)`},children:[t.jsxs("div",{ref:j,className:`flex justify-between items-center overflow-hidden mb-[0.1rem] ${y.animation_box} ${C?"h-[1.8rem]":"h-0"}`,children:[t.jsxs("div",{className:"flex ",children:[C?t.jsx(fe,{className:`${y.shousuo_icon} text-[1rem]`,onClick:()=>{R(!1),setTimeout(()=>{f()},200)}}):t.jsx(xe,{className:`${y.shousuo_icon} absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10`,onClick:()=>{R(!0),setTimeout(()=>{f()},200)}}),t.jsx("div",{className:"font-bold text-[0.8rem] ml-3",children:"数据列表"})]}),t.jsx("div",{className:"flex gap-x-[2.5rem]",children:t.jsx("div",{className:"flex gap-x-[0.75rem]",children:t.jsx(P,{danger:!0,ghost:!0,icon:t.jsx(he,{}),onClick:()=>te(2),children:"导出"})})})]}),t.jsx(ge,{className:"pointSalaryCalc_table",rowClassName:(e,a)=>a%2===1?"customRow odd":"customRow even",columns:ee,dataSource:Q,bordered:!0,scroll:{y:`calc(${q}px - 0.625rem - 1.6rem - 0.5rem)`},loading:U,onChange:ie,pagination:{...s,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50"]}})]})]})})};export{Ye as default};
