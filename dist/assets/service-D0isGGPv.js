import{D as t}from"./index-De_f0oL2.js";const r="_employment_page_1ggn6_2",a="_animation_box_1ggn6_2",o="_over_ellipsis_1ggn6_5",n={employment_page:r,animation_box:a,over_ellipsis:o},l={getEnumType:e=>t.post("/zhyy/employee/getEnumType",e),getPerIncomeProfit:e=>t.get("/zhyy/salary/perIncomeProfit/list",{params:e}),exportPerIncomeProfit:e=>t.get("/zhyy/salary/perIncomeProfit/exportExcel",{params:e,headers:{"Content-Type":"application/json;charset=UTF-8"},responseType:"blob"}),getMarketExitRate:e=>t.get("/zhyy/salary/marketExitRate/list",{params:e}),exportMarketExitRate:e=>t.get("/zhyy/salary/marketExitRate/exportExcel",{params:e,headers:{"Content-Type":"application/json;charset=UTF-8"},responseType:"blob"}),getIdxPosSalAnalysis:e=>t.get("/zhyy/salary/idxPosSalAnalysis/list",{params:e}),exportIdxPosSalAnalysis:e=>t.get("/zhyy/salary/idxPosSalAnalysis/exportExcel",{params:e,headers:{"Content-Type":"application/json;charset=UTF-8"},responseType:"blob"}),getStatEvalYearMonth:e=>t.get("/zhyy/salary/statEvalYearMonth/list",{params:e}),exportStatEvalYearMonth:e=>t.get("/zhyy/salary/statEvalYearMonth/exportExcel",{params:e,headers:{"Content-Type":"application/json;charset=UTF-8"},responseType:"blob"}),build4LevelOrgTree2:e=>t.post("/zhyy/employee/org/build4LevelOrgTree2",e)};export{l as i,n as s};
