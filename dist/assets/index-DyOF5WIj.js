import{F as f,r as g,u as O,j as l,B as p,y as C,z as $}from"./index-De_f0oL2.js";import{u as B}from"./debounce-D4mn-XUD.js";import{p as M}from"./service-CEuf3VDV.js";import{C as q}from"./index-DZyVV6rP.js";import{S as b}from"./index-BWJehDyc.js";import{D as L}from"./index-CdSZ9YgQ.js";import{F as P}from"./Table-D-iLeFE-.js";import{s as z}from"./index-BcPP1N8I.js";import"./useMultipleSelect-B0dEIXT-.js";const G="_add_yohve_1",H={add:G},i=u=>{const[x]=f.useForm(),r=g.useRef([]),[k,I]=g.useState([]),[N,T]=g.useState(u.columns),{runAsync:v}=O(M.getEmployeeByEmpId,{manual:!0});g.useEffect(()=>{S()},[]);const S=()=>{T([...R(u.columns),{title:"操作",dataIndex:"action",width:80,align:"center",fixed:"right",render:(s,d)=>l.jsx("div",{className:"action",children:l.jsx(p,{type:"link",onClick:()=>D(d),children:"删除"})})}])},E=async(s,d,a,t)=>{var e;const[n,o]=await v({empId:d});if(!n)if(o.STATUS==="0000"){const{DATA:h}=o,w=h==null?void 0:h.employeeName;r.current=(e=r.current)==null?void 0:e.map(y=>{const j={...y};return(y==null?void 0:y.key)===(s==null?void 0:s.key)&&(j.empName=w),j}),x.setFieldValue(t,w),console.log("修改：",r.current)}else z.error(o==null?void 0:o.MESSAGE)},R=s=>{var d;return(d=s==null?void 0:s.filter(a=>(a==null?void 0:a.dataIndex)!=="action"))==null?void 0:d.map(a=>({...a,width:a!=null&&a.width&&a.width>120?a==null?void 0:a.width:120,render:(t,n,o)=>l.jsx(f.Item,{name:a.dataIndex+(n==null?void 0:n.key),rules:[{required:["forwardAmount","forwardContent","empId","empName"].includes(a==null?void 0:a.dataIndex),message:`请输入${a.title}`}],children:(a==null?void 0:a.actionType)==="cascader"?l.jsx(q,{allowClear:!1,changeOnSelect:!0,expandTrigger:"hover",disabled:["rewardsOrgName"].includes(a==null?void 0:a.dataIndex)&&(n==null?void 0:n.rewardsType)==="staff",displayRender:e=>e[e.length-1],options:(u==null?void 0:u.cascaderOption)||[],fieldNames:{value:"orgId",label:"orgName",children:"children"},placeholder:`请选择${a==null?void 0:a.title}`,showSearch:{filter:F},onSearch:e=>console.log(e),value:t==null?void 0:t.map(e=>e==null?void 0:e.orgId),onChange:(e,h)=>c(n,h,a)}):(a==null?void 0:a.actionType)==="select"?l.jsx(l.Fragment,{children:l.jsx(b,{placeholder:`请选择${a==null?void 0:a.title}`,allowClear:!0,value:t,onChange:e=>c(n,e,a),children:(u[a==null?void 0:a.actionOptionName]||[]).map(e=>l.jsx(b.Option,{value:e==null?void 0:e.enumId,children:e==null?void 0:e.enumName},e==null?void 0:e.enumId))})}):(a==null?void 0:a.actionType)==="datePicker"?l.jsx(L,{picker:"month",value:t,placeholder:`请选择${a.title}`,onChange:e=>c(n,e,a),style:{width:"100%"}}):(a==null?void 0:a.dataIndex)==="empId"?l.jsx(C,{placeholder:`请输入${a.title}`,value:t,disabled:["rewardsOrgAmount"].includes(a==null?void 0:a.dataIndex)&&(n==null?void 0:n.rewardsType)==="staff",onChange:e=>c(n,e.target.value,a,`empName${n==null?void 0:n.key}`)}):l.jsx(C,{placeholder:(a==null?void 0:a.dataIndex)==="empName"?"":`请输入${a.title}`,disabled:(a==null?void 0:a.dataIndex)==="empName",value:t,onChange:e=>c(n,e.target.value,a)})},t+o)}))},c=(s,d,a,t)=>{var n;r.current=(n=r.current)==null?void 0:n.map(o=>{const e={...o};return(o==null?void 0:o.key)===(s==null?void 0:s.key)&&(e[a==null?void 0:a.dataIndex]=d),e}),(a==null?void 0:a.dataIndex)!=="empId"||B(E(s,d,a,t),500),console.log("修改：",r.current)},D=s=>{var d;r.current=(d=r.current)==null?void 0:d.filter(a=>(a==null?void 0:a.key)!==(s==null?void 0:s.key)),I(r.current)},_=()=>{u==null||u.submitData(r.current)},A=()=>{r.current=[...r.current,{key:new Date().getTime()}],I(r.current)},F=(s,d)=>d.some(a=>a.orgName.toLowerCase().indexOf(s.trim().toLowerCase())>-1);return l.jsx("div",{className:H.add,children:l.jsxs(f,{name:"dynamic_form_nest_item",form:x,onFinish:_,style:{width:"100%",marginTop:16,maxHeight:"500px"},autoComplete:"off",children:[l.jsx(P,{style:{marginBottom:20},className:"edit-table",columns:N,dataSource:k,bordered:!0,scroll:{y:"20rem"},pagination:!1}),l.jsx(f.Item,{style:{width:"100%",display:"flex",justifyContent:"right"},children:l.jsx(p,{onClick:()=>A(),danger:!0,block:!0,icon:l.jsx($,{}),style:{width:150},children:"新增一条数据"})}),l.jsx(f.Item,{style:{textAlign:"center",width:"100%",background:"#fff"},children:l.jsx(p,{type:"primary",htmlType:"submit",style:{},children:"提交"})})]})})};export{i as default};
