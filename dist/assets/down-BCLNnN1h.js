import{aK as R,aL as j,aM as P,V as l,aN as E,aO as k,r as p,q as N,aP as I,aQ as L,I as D,_ as H,j as v}from"./index-De_f0oL2.js";import{S as M}from"./Table-D-iLeFE-.js";let r=null,u=e=>e(),m=[],f={};function x(){const{getContainer:e,rtl:o,maxCount:n,top:s,bottom:c,showProgress:t,pauseOnHover:a}=f,i=(e==null?void 0:e())||document.body;return{getContainer:()=>i,rtl:o,maxCount:n,top:s,bottom:c,showProgress:t,pauseOnHover:a}}const U=l.forwardRef((e,o)=>{const{notificationConfig:n,sync:s}=e,{getPrefixCls:c}=p.useContext(N),t=f.prefixCls||c("notification"),a=p.useContext(I),[i,h]=L(Object.assign(Object.assign(Object.assign({},n),{prefixCls:t}),a.notification));return l.useEffect(s,[]),l.useImperativeHandle(o,()=>{const d=Object.assign({},i);return Object.keys(d).forEach(b=>{d[b]=(...O)=>(s(),i[b].apply(i,O))}),{instance:d,sync:s}}),h}),G=l.forwardRef((e,o)=>{const[n,s]=l.useState(x),c=()=>{s(x)};l.useEffect(c,[]);const t=k(),a=t.getRootPrefixCls(),i=t.getIconPrefixCls(),h=t.getTheme(),d=l.createElement(U,{ref:o,sync:c,notificationConfig:n});return l.createElement(E,{prefixCls:a,iconPrefixCls:i,theme:h},t.holderRender?t.holderRender(d):d)});function C(){if(!r){const e=document.createDocumentFragment(),o={fragment:e};r=o,u(()=>{P()(l.createElement(G,{ref:s=>{const{instance:c,sync:t}=s||{};Promise.resolve().then(()=>{!o.instance&&c&&(o.instance=c,o.sync=t,C())})}}),e)});return}r.instance&&(m.forEach(e=>{switch(e.type){case"open":{u(()=>{r.instance.open(Object.assign(Object.assign({},f),e.config))});break}case"destroy":u(()=>{r==null||r.instance.destroy(e.key)});break}}),m=[])}function S(e){f=Object.assign(Object.assign({},f),e),u(()=>{var o;(o=r==null?void 0:r.sync)===null||o===void 0||o.call(r)})}function w(e){m.push({type:"open",config:e}),C()}const _=e=>{m.push({type:"destroy",key:e}),C()},F=["success","info","warning","error"],V={open:w,destroy:_,config:S,useNotification:j,_InternalPanelDoNotUseOrYouWillBeFired:R},g=V;F.forEach(e=>{g[e]=o=>w(Object.assign(Object.assign({},o),{type:e}))});var z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M505.7 661a8 8 0 0012.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V168c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.8zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"}}]},name:"download",theme:"outlined"},B=function(o,n){return p.createElement(D,H({},o,{ref:n,icon:z}))},q=p.forwardRef(B);const A=e=>{try{const o=e[1]||{},n=e[2]||{},s=n==null?void 0:n.headers["content-disposition"];let c="downloaded-file";if(s){const i=s.match(/filename="?([^"]+)"?/);i&&i.length>1&&(c=decodeURIComponent(i[1]))}const t=window.URL.createObjectURL(o),a=document.createElement("a");a.href=t,a.download=c,document.body.appendChild(a),a.click(),a.remove(),window.URL.revokeObjectURL(t),y("导出成功",1,"success")}catch(o){y("导出失败",1,"error"),console.error("Download failed:",o)}},y=(e,o,n)=>{n==="success"?g.success({key:"upLoad",message:e,duration:o,onClick:()=>{}}):n==="error"?g.error({key:"upLoad",message:e,duration:o,onClick:()=>{}}):g.open({key:"upLoad",message:v.jsxs("div",{children:[v.jsx(M,{style:{marginRight:5}}),e]}),duration:o,onClick:()=>{}})};export{q as R,A as d,y as o};
