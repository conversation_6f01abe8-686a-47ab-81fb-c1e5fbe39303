import{r,F as c,j as t,u as f,d as ne,R as ae,C as p,S as re,B as x,T as _}from"./index-De_f0oL2.js";import{s as d,i as g}from"./service-D0isGGPv.js";import{R as se,o as D,d as oe}from"./down-BCLNnN1h.js";import{R as le}from"./index-BmnYJy3v.js";import{f as o}from"./format-ChnqMkgG.js";import{s as h}from"./index-BcPP1N8I.js";import{D as ie}from"./index-CdSZ9YgQ.js";import{S as ce}from"./index-BWJehDyc.js";import{R as de,a as me}from"./FullscreenOutlined-DzCTibKW.js";import{R as he}from"./InfoCircleOutlined-DUD7mNgc.js";import"./Table-D-iLeFE-.js";import"./useMultipleSelect-B0dEIXT-.js";const Pe=()=>{var N;const y=[{title:"单位",key:"cityName",dataIndex:"cityName",align:"center",fixed:"left",width:120,children:[],render:(e,n)=>t.jsx(_,{title:e,children:t.jsx("div",{className:d.over_ellipsis,children:e})})},{title:"主营业务收入",key:"incomeMainBusi",dataIndex:"incomeMainBusi",align:"center",width:70,children:[],render:e=>t.jsx("span",{children:o(e)})},{title:"经营利润",key:"profitBusi",dataIndex:"profitBusi",align:"center",width:70,children:[],render:e=>t.jsx("span",{children:o(e)})},{title:"全量人均创收",key:"incomeTotalPer",dataIndex:"incomeTotalPer",align:"center",width:70,children:[],render:e=>t.jsx("span",{children:o(e)})},{title:"全口径人均创收",key:"incomeTotalCaliber",dataIndex:"incomeTotalCaliber",align:"center",width:70,children:[],render:e=>t.jsx("span",{children:o(e)})},{title:"人均创收（从业人员）",key:"incomePer",dataIndex:"incomePer",align:"center",children:[],width:70,render:e=>t.jsx("span",{children:o(e)})},{title:"全量人均创利",key:"profitTotalPer",dataIndex:"profitTotalPer",align:"center",children:[],width:70,render:e=>t.jsx("span",{children:o(e)})},{title:"全口径人均创利",key:"profitTotalCaliber",dataIndex:"profitTotalCaliber",align:"center",children:[],width:70,render:e=>t.jsx("span",{children:o(e)})},{title:"从业人员人均创利",key:"level",dataIndex:"level",align:"center",width:70,children:[{title:"人均创利",key:"profitPer",dataIndex:"profitPer",align:"center",children:[],width:70,render:e=>t.jsx("span",{children:o(e)})},{title:"预算值",key:"budget",dataIndex:"budget",align:"center",children:[],width:70,render:e=>t.jsx("span",{children:o(e)})},{title:"完成率",key:"completeRate",dataIndex:"completeRate",align:"center",children:[],width:70}]},{title:t.jsxs(t.Fragment,{children:[t.jsx("span",{children:"人员统计"}),t.jsx(_,{title:"不含跨单位",children:t.jsx(he,{})})]}),key:"level",dataIndex:"level",align:"center",width:50,children:[{title:"人数合计",key:"totalPerson",dataIndex:"totalPerson",align:"center",children:[],width:70},{title:"合同制",key:"totalPersonContract",dataIndex:"totalPersonContract",align:"center",children:[],width:70},{title:"劳务派遣",key:"totalPersonLabor",dataIndex:"totalPersonLabor",align:"center",children:[],width:70},{title:"紧密型外包",key:"totalPersonTos",dataIndex:"totalPersonTos",align:"center",width:70},{title:"其他外包",key:"totalPersonOos",dataIndex:"totalPersonOos",align:"center",width:70},{title:"调整人数",key:"totalPersonAdjust",dataIndex:"totalPersonAdjust",align:"center",width:70}]}],w=r.useRef(null),j=r.useRef(null),I=r.useRef(null),[l]=c.useForm(),[F,$]=r.useState(0),[b,z]=r.useState([]),[M,S]=r.useState(!1),[L,H]=r.useState(y),[u,Y]=r.useState(0),[B,O]=r.useState([]),[T,P]=r.useState(!0),[s,R]=r.useState({total:0,pageNum:1,pageSize:50}),{runAsync:q}=f(g.getEnumType,{manual:!0}),{runAsync:G}=f(g.getPerIncomeProfit,{manual:!0}),{runAsync:V}=f(g.exportPerIncomeProfit,{manual:!0});r.useEffect(()=>(H(y),U(),k(),m(),window.addEventListener("resize",C),()=>{window.removeEventListener("resize",C)}),[]);const C=()=>{i()};r.useEffect(()=>{i()},[(document.querySelector(".percapita_Income_table .ant-table-header")||{}).offsetHeight]),r.useEffect(()=>{i()},[b]),r.useEffect(()=>{u>0&&m()},[u]),r.useEffect(()=>{(s==null?void 0:s.total)>0&&m()},[s.pageNum,s.pageSize]);const i=()=>{var a;const e=(document.querySelector(".percapita_Income_table .ant-table-header")||{}).offsetHeight||0,n=(document.querySelector(".percapita_Income_table .ant-table-pagination")||{}).offsetHeight||26;e&&n&&$(((a=j.current)==null?void 0:a.offsetHeight)-(I.current.offsetHeight+e+n))},U=async()=>{const[e,n]=await q({code:"1010",tag:1});if(e){h.error((n==null?void 0:n.DATA)||(n==null?void 0:n.MESSAGE)||"调用失败");return}n.STATUS==="0000"?O(n.DATA):h.error((n==null?void 0:n.MESSAGE)||(n==null?void 0:n.DATA)||"调用失败")},m=async()=>{var A,E;const e=l.getFieldsValue();S(!0);const[n,a]=await G({...e,cycleId:(A=e==null?void 0:e.cycleId)==null?void 0:A.format("YYYYMM"),...s});if(S(!1),n){h.error((a==null?void 0:a.DATA)||(a==null?void 0:a.MESSAGE)||"调用失败");return}if(a.STATUS==="0000"){const{DATA:{data:Z}}=a,ee=Z.map((v,te)=>({...v,key:v.id||te}));z(ee),R({...s,total:(E=a.DATA)==null?void 0:E.totalCount})}else h.error((a==null?void 0:a.MESSAGE)||(a==null?void 0:a.DATA)||"调用失败")},k=()=>{const e=ne().subtract(1,"month");l.setFieldsValue({cycleId:e})},J=e=>{console.log("Success:",e),m()},K=async()=>{var e;try{D("正在导出",0,"loading");const n=l.getFieldsValue(),a=await V({...n,cycleId:(e=n==null?void 0:n.cycleId)==null?void 0:e.format("YYYYMM"),orgaId:n==null?void 0:n.cityId});oe(a)}catch(n){D("导出失败",1,"error"),console.error("Download failed:",n)}},Q=()=>{const e=u+1;l.resetFields(),k(),Y(e)},W=e=>{const n={total:e==null?void 0:e.total,pageNum:e==null?void 0:e.current,pageSize:e==null?void 0:e.pageSize};R(n)},X=(e,n)=>{var a;return((n==null?void 0:n.enumName)??"").toLowerCase().includes((a=e.toLowerCase())==null?void 0:a.trim())};return t.jsxs("div",{className:`pt-[0.5rem] px-[0rem] h-full flex flex-col ${d.employment_page}`,children:[t.jsx("div",{ref:w,className:"bg-white pt-2 px-8 mb-2",children:t.jsx(c,{form:l,initialValues:{tag:""},onFinish:J,autoComplete:"off",children:t.jsxs(ae,{gutter:24,children:[t.jsx(p,{span:6,children:t.jsx(c.Item,{label:"月份",name:"cycleId",wrapperCol:{span:20},className:"mb-[0.5rem]",children:t.jsx(ie,{className:"w-full",allowClear:!1,picker:"month"})})}),t.jsx(p,{span:6,children:t.jsx(c.Item,{label:"单位",name:"cityId",wrapperCol:{span:20},className:"mb-[0.5rem]",children:t.jsx(ce,{placeholder:"请选择",className:"w-full",allowClear:!0,showSearch:!0,filterOption:X,options:B,fieldNames:{value:"enumId",label:"enumName"}})})}),t.jsx(p,{span:6,children:t.jsx(c.Item,{labelCol:{span:0},wrapperCol:{span:24},className:"mb-[0.5rem]",children:t.jsxs(re,{size:"small",children:[t.jsx(x,{type:"primary",htmlType:"submit",children:"查询"}),t.jsx(x,{htmlType:"button",onClick:()=>Q(),children:"重置"})]})})})]})})}),t.jsxs("div",{ref:j,className:"relative bg-white px-5 pt-2.5",style:{height:`calc(100% - ${((N=w.current)==null?void 0:N.offsetHeight)+15}px)`},children:[t.jsxs("div",{ref:I,className:`flex justify-between items-center overflow-hidden mb-2 ${d.animation_box} ${T?"h-[1.8rem]":"h-0"}`,children:[t.jsxs("div",{className:"flex ",children:[T?t.jsx(de,{className:`${d.shousuo_icon} text-[1rem]`,onClick:()=>{P(!1),setTimeout(()=>{i()},200)}}):t.jsx(me,{className:`${d.shousuo_icon} absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10`,onClick:()=>{P(!0),setTimeout(()=>{i()},200)}}),t.jsxs("div",{className:"font-bold text-[0.8rem] ml-3",children:["数据列表",t.jsx("span",{className:"text-[0.7rem]",style:{color:"#939393"},children:"（单位：万元）"})]})]}),t.jsx(x,{danger:!0,ghost:!0,icon:t.jsx(se,{}),onClick:()=>K(),children:"导出"})]}),t.jsx(le,{className:"percapita_Income_table",rowClassName:(e,n)=>n%2===1?"customRow odd":"customRow even",columns:L,dataSource:b,loading:M,bordered:!0,scroll:{y:`calc(${F}px - 0.625rem - 1.6rem - 0.5rem)`},onChange:W,pagination:{...s,total:s==null?void 0:s.total,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50"]}})]})]})};export{Pe as default};
