import{F as r,u as w,j as e,S as v,y as S,B as u,z as k,V as C}from"./index-De_f0oL2.js";import{u as b}from"./debounce-D4mn-XUD.js";import{p as A}from"./service-CEuf3VDV.js";import{S as x}from"./index-BWJehDyc.js";import{D as E}from"./index-CdSZ9YgQ.js";const T="_add_hdvhk_1",_={add:T};C.createContext(null);const z=c=>{const[h]=r.useForm(),p=c.columns;c.unitList;const{runAsync:y}=w(A.getEmployeeByEmpId,{manual:!0}),g=t=>{c.addData(t.users),console.log("Received values of form:",t)},j=(t,d)=>{if(console.log(t.users,d),t.users){const i=t.users.filter(n=>n)[0];console.log(t.users.filter(n=>n));const a=[...d.users];a.forEach((n,m)=>{i.empId&&n.empId&&b(y({empId:i.empId}).then(s=>{const l=s[1];if(l.STATUS==="0000"){const{DATA:o}=l;n.empName=o==null?void 0:o.employeeName,a[m]=n}}),500)}),h.setFieldsValue({users:a})}};return e.jsxs("div",{className:_.add,children:[e.jsx("div",{children:e.jsxs("div",{style:{background:"#fafafa",display:"flex",textAlign:"center",height:"3rem",border:"1px solid #f0f0f0",width:"100%"},children:[p.map(t=>e.jsxs("p",{style:{width:"25%",fontWeight:500,fontSize:14,margin:"auto 3px"},children:[" ",t.title," "]},t.dataIndex)),e.jsx("p",{style:{width:"25%",fontWeight:500,fontSize:14,margin:"auto 1rem"},children:"操作"})]})}),e.jsxs(r,{name:"dynamic_form_nest_item",form:h,onFinish:g,onValuesChange:j,style:{width:"100%",marginTop:16,maxHeight:"500px"},autoComplete:"off",children:[e.jsx(r.List,{name:"users",children:(t,{add:d,remove:i})=>e.jsxs("div",{children:[e.jsx("div",{style:{width:"100%",maxHeight:"390px",overflowY:"auto"},children:t.map(({key:a,name:n,...m})=>e.jsxs(v,{style:{marginBottom:8,width:"100%"},align:"baseline",children:[p.map(s=>{var l;return e.jsx(r.Item,{...m,name:[n,s.dataIndex],rules:[{required:!0,message:`请输入${s.title}`}],children:s.select?e.jsx(x,{placeholder:`请选择${s.title}`,allowClear:!0,children:(l=s==null?void 0:s.children)==null?void 0:l.map(o=>{const{enumId:f,enumName:I}=o;return e.jsx(x.Option,{value:f,children:I},f)})}):s.date?e.jsx(E,{className:"w-full",picker:s.date}):e.jsx(S,{placeholder:`请输入${s.title}`})},s.dataIndex)}),e.jsx(u,{type:"dashed",onClick:()=>i(n),style:{margin:"auto",width:"50%",marginLeft:"25%"},children:"删除"})]},a))}),e.jsx(r.Item,{style:{width:"100%",display:"flex",justifyContent:"right",marginTop:12},children:e.jsx(u,{onClick:()=>d(),danger:!0,block:!0,icon:e.jsx(k,{}),style:{width:150},children:"新增一条数据"})})]})}),e.jsx(r.Item,{style:{textAlign:"center",width:"100%",background:"#fff"},children:e.jsx(u,{type:"primary",htmlType:"submit",style:{},children:"提交"})})]})]})};export{z as default};
