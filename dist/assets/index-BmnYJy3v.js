import{aR as Ke,r as U,ay as Te,V as Ge,j as k}from"./index-De_f0oL2.js";import{F as Je}from"./Table-D-iLeFE-.js";var fe={exports:{}},B={},G={exports:{}},ze={},Me={exports:{}},Qe="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED",Ze=Qe,et=Ze;function Ee(){}function Ne(){}Ne.resetWarningCache=Ee;var tt=function(){function e(n,a,o,i,s,l){if(l!==et){var h=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw h.name="Invariant Violation",h}}e.isRequired=e;function t(){return e}var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:Ne,resetWarningCache:Ee};return r.PropTypes=r,r};Me.exports=tt();var J=Me.exports;function We(e){var t,r,n="";if(typeof e=="string"||typeof e=="number")n+=e;else if(typeof e=="object")if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(r=We(e[t]))&&(n&&(n+=" "),n+=r);else for(t in e)e[t]&&(n&&(n+=" "),n+=t);return n}function De(){for(var e,t,r=0,n="";r<arguments.length;)(e=arguments[r++])&&(t=We(e))&&(n&&(n+=" "),n+=t);return n}const rt=Object.freeze(Object.defineProperty({__proto__:null,clsx:De,default:De},Symbol.toStringTag,{value:"Module"})),nt=Ke(rt);var y={},E={};Object.defineProperty(E,"__esModule",{value:!0});E.dontSetMe=lt;E.findInArray=at;E.int=st;E.isFunction=ot;E.isNum=it;function at(e,t){for(let r=0,n=e.length;r<n;r++)if(t.apply(t,[e[r],r,e]))return e[r]}function ot(e){return typeof e=="function"||Object.prototype.toString.call(e)==="[object Function]"}function it(e){return typeof e=="number"&&!isNaN(e)}function st(e){return parseInt(e,10)}function lt(e,t,r){if(e[t])return new Error("Invalid prop ".concat(t," passed to ").concat(r," - do not set this, set it on the child."))}var X={};Object.defineProperty(X,"__esModule",{value:!0});X.browserPrefixToKey=He;X.browserPrefixToStyle=ut;X.default=void 0;X.getPrefix=$e;const re=["Moz","Webkit","O","ms"];function $e(){var e;let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"transform";if(typeof window>"u")return"";const r=(e=window.document)===null||e===void 0||(e=e.documentElement)===null||e===void 0?void 0:e.style;if(!r||t in r)return"";for(let n=0;n<re.length;n++)if(He(t,re[n])in r)return re[n];return""}function He(e,t){return t?"".concat(t).concat(ct(e)):e}function ut(e,t){return t?"-".concat(t.toLowerCase(),"-").concat(e):e}function ct(e){let t="",r=!0;for(let n=0;n<e.length;n++)r?(t+=e[n].toUpperCase(),r=!1):e[n]==="-"?r=!0:t+=e[n];return t}X.default=$e();Object.defineProperty(y,"__esModule",{value:!0});y.addClassName=je;y.addEvent=pt;y.addUserSelectStyles=Pt;y.createCSSTransform=St;y.createSVGTransform=Ot;y.getTouch=wt;y.getTouchIdentifier=Dt;y.getTranslation=de;y.innerHeight=vt;y.innerWidth=yt;y.matchesSelector=Ye;y.matchesSelectorAndParentsTo=dt;y.offsetXYFromParent=bt;y.outerHeight=gt;y.outerWidth=mt;y.removeClassName=Ue;y.removeEvent=ht;y.removeUserSelectStyles=_t;var x=E,Pe=ft(X);function Xe(e){if(typeof WeakMap!="function")return null;var t=new WeakMap,r=new WeakMap;return(Xe=function(n){return n?r:t})(e)}function ft(e,t){if(e&&e.__esModule)return e;if(e===null||typeof e!="object"&&typeof e!="function")return{default:e};var r=Xe(t);if(r&&r.has(e))return r.get(e);var n={},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if(o!=="default"&&Object.prototype.hasOwnProperty.call(e,o)){var i=a?Object.getOwnPropertyDescriptor(e,o):null;i&&(i.get||i.set)?Object.defineProperty(n,o,i):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}let V="";function Ye(e,t){return V||(V=(0,x.findInArray)(["matches","webkitMatchesSelector","mozMatchesSelector","msMatchesSelector","oMatchesSelector"],function(r){return(0,x.isFunction)(e[r])})),(0,x.isFunction)(e[V])?e[V](t):!1}function dt(e,t,r){let n=e;do{if(Ye(n,t))return!0;if(n===r)return!1;n=n.parentNode}while(n);return!1}function pt(e,t,r,n){if(!e)return;const a={capture:!0,...n};e.addEventListener?e.addEventListener(t,r,a):e.attachEvent?e.attachEvent("on"+t,r):e["on"+t]=r}function ht(e,t,r,n){if(!e)return;const a={capture:!0,...n};e.removeEventListener?e.removeEventListener(t,r,a):e.detachEvent?e.detachEvent("on"+t,r):e["on"+t]=null}function gt(e){let t=e.clientHeight;const r=e.ownerDocument.defaultView.getComputedStyle(e);return t+=(0,x.int)(r.borderTopWidth),t+=(0,x.int)(r.borderBottomWidth),t}function mt(e){let t=e.clientWidth;const r=e.ownerDocument.defaultView.getComputedStyle(e);return t+=(0,x.int)(r.borderLeftWidth),t+=(0,x.int)(r.borderRightWidth),t}function vt(e){let t=e.clientHeight;const r=e.ownerDocument.defaultView.getComputedStyle(e);return t-=(0,x.int)(r.paddingTop),t-=(0,x.int)(r.paddingBottom),t}function yt(e){let t=e.clientWidth;const r=e.ownerDocument.defaultView.getComputedStyle(e);return t-=(0,x.int)(r.paddingLeft),t-=(0,x.int)(r.paddingRight),t}function bt(e,t,r){const a=t===t.ownerDocument.body?{left:0,top:0}:t.getBoundingClientRect(),o=(e.clientX+t.scrollLeft-a.left)/r,i=(e.clientY+t.scrollTop-a.top)/r;return{x:o,y:i}}function St(e,t){const r=de(e,t,"px");return{[(0,Pe.browserPrefixToKey)("transform",Pe.default)]:r}}function Ot(e,t){return de(e,t,"")}function de(e,t,r){let{x:n,y:a}=e,o="translate(".concat(n).concat(r,",").concat(a).concat(r,")");if(t){const i="".concat(typeof t.x=="string"?t.x:t.x+r),s="".concat(typeof t.y=="string"?t.y:t.y+r);o="translate(".concat(i,", ").concat(s,")")+o}return o}function wt(e,t){return e.targetTouches&&(0,x.findInArray)(e.targetTouches,r=>t===r.identifier)||e.changedTouches&&(0,x.findInArray)(e.changedTouches,r=>t===r.identifier)}function Dt(e){if(e.targetTouches&&e.targetTouches[0])return e.targetTouches[0].identifier;if(e.changedTouches&&e.changedTouches[0])return e.changedTouches[0].identifier}function Pt(e){if(!e)return;let t=e.getElementById("react-draggable-style-el");t||(t=e.createElement("style"),t.type="text/css",t.id="react-draggable-style-el",t.innerHTML=`.react-draggable-transparent-selection *::-moz-selection {all: inherit;}
`,t.innerHTML+=`.react-draggable-transparent-selection *::selection {all: inherit;}
`,e.getElementsByTagName("head")[0].appendChild(t)),e.body&&je(e.body,"react-draggable-transparent-selection")}function _t(e){if(e)try{if(e.body&&Ue(e.body,"react-draggable-transparent-selection"),e.selection)e.selection.empty();else{const t=(e.defaultView||window).getSelection();t&&t.type!=="Caret"&&t.removeAllRanges()}}catch{}}function je(e,t){e.classList?e.classList.add(t):e.className.match(new RegExp("(?:^|\\s)".concat(t,"(?!\\S)")))||(e.className+=" ".concat(t))}function Ue(e,t){e.classList?e.classList.remove(t):e.className=e.className.replace(new RegExp("(?:^|\\s)".concat(t,"(?!\\S)"),"g"),"")}var N={};Object.defineProperty(N,"__esModule",{value:!0});N.canDragX=Ct;N.canDragY=Tt;N.createCoreData=Mt;N.createDraggableData=Et;N.getBoundPosition=Rt;N.getControlPosition=zt;N.snapToGrid=xt;var R=E,j=y;function Rt(e,t,r){if(!e.props.bounds)return[t,r];let{bounds:n}=e.props;n=typeof n=="string"?n:Nt(n);const a=pe(e);if(typeof n=="string"){const{ownerDocument:o}=a,i=o.defaultView;let s;if(n==="parent"?s=a.parentNode:s=o.querySelector(n),!(s instanceof i.HTMLElement))throw new Error('Bounds selector "'+n+'" could not find an element.');const l=s,h=i.getComputedStyle(a),m=i.getComputedStyle(l);n={left:-a.offsetLeft+(0,R.int)(m.paddingLeft)+(0,R.int)(h.marginLeft),top:-a.offsetTop+(0,R.int)(m.paddingTop)+(0,R.int)(h.marginTop),right:(0,j.innerWidth)(l)-(0,j.outerWidth)(a)-a.offsetLeft+(0,R.int)(m.paddingRight)-(0,R.int)(h.marginRight),bottom:(0,j.innerHeight)(l)-(0,j.outerHeight)(a)-a.offsetTop+(0,R.int)(m.paddingBottom)-(0,R.int)(h.marginBottom)}}return(0,R.isNum)(n.right)&&(t=Math.min(t,n.right)),(0,R.isNum)(n.bottom)&&(r=Math.min(r,n.bottom)),(0,R.isNum)(n.left)&&(t=Math.max(t,n.left)),(0,R.isNum)(n.top)&&(r=Math.max(r,n.top)),[t,r]}function xt(e,t,r){const n=Math.round(t/e[0])*e[0],a=Math.round(r/e[1])*e[1];return[n,a]}function Ct(e){return e.props.axis==="both"||e.props.axis==="x"}function Tt(e){return e.props.axis==="both"||e.props.axis==="y"}function zt(e,t,r){const n=typeof t=="number"?(0,j.getTouch)(e,t):null;if(typeof t=="number"&&!n)return null;const a=pe(r),o=r.props.offsetParent||a.offsetParent||a.ownerDocument.body;return(0,j.offsetXYFromParent)(n||e,o,r.props.scale)}function Mt(e,t,r){const n=!(0,R.isNum)(e.lastX),a=pe(e);return n?{node:a,deltaX:0,deltaY:0,lastX:t,lastY:r,x:t,y:r}:{node:a,deltaX:t-e.lastX,deltaY:r-e.lastY,lastX:e.lastX,lastY:e.lastY,x:t,y:r}}function Et(e,t){const r=e.props.scale;return{node:t.node,x:e.state.x+t.deltaX/r,y:e.state.y+t.deltaY/r,deltaX:t.deltaX/r,deltaY:t.deltaY/r,lastX:e.state.x,lastY:e.state.y}}function Nt(e){return{left:e.left,top:e.top,right:e.right,bottom:e.bottom}}function pe(e){const t=e.findDOMNode();if(!t)throw new Error("<DraggableCore>: Unmounted during event!");return t}var Q={},Z={};Object.defineProperty(Z,"__esModule",{value:!0});Z.default=Wt;function Wt(){}Object.defineProperty(Q,"__esModule",{value:!0});Q.default=void 0;var ne=Ht(U),_=he(J),$t=he(Te),O=y,$=N,ae=E,I=he(Z);function he(e){return e&&e.__esModule?e:{default:e}}function Ae(e){if(typeof WeakMap!="function")return null;var t=new WeakMap,r=new WeakMap;return(Ae=function(n){return n?r:t})(e)}function Ht(e,t){if(e&&e.__esModule)return e;if(e===null||typeof e!="object"&&typeof e!="function")return{default:e};var r=Ae(t);if(r&&r.has(e))return r.get(e);var n={},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if(o!=="default"&&Object.prototype.hasOwnProperty.call(e,o)){var i=a?Object.getOwnPropertyDescriptor(e,o):null;i&&(i.get||i.set)?Object.defineProperty(n,o,i):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}function D(e,t,r){return t=Xt(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Xt(e){var t=Yt(e,"string");return typeof t=="symbol"?t:String(t)}function Yt(e,t){if(typeof e!="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}const T={touch:{start:"touchstart",move:"touchmove",stop:"touchend"},mouse:{start:"mousedown",move:"mousemove",stop:"mouseup"}};let H=T.mouse,ee=class extends ne.Component{constructor(){super(...arguments),D(this,"dragging",!1),D(this,"lastX",NaN),D(this,"lastY",NaN),D(this,"touchIdentifier",null),D(this,"mounted",!1),D(this,"handleDragStart",t=>{if(this.props.onMouseDown(t),!this.props.allowAnyClick&&typeof t.button=="number"&&t.button!==0)return!1;const r=this.findDOMNode();if(!r||!r.ownerDocument||!r.ownerDocument.body)throw new Error("<DraggableCore> not mounted on DragStart!");const{ownerDocument:n}=r;if(this.props.disabled||!(t.target instanceof n.defaultView.Node)||this.props.handle&&!(0,O.matchesSelectorAndParentsTo)(t.target,this.props.handle,r)||this.props.cancel&&(0,O.matchesSelectorAndParentsTo)(t.target,this.props.cancel,r))return;t.type==="touchstart"&&t.preventDefault();const a=(0,O.getTouchIdentifier)(t);this.touchIdentifier=a;const o=(0,$.getControlPosition)(t,a,this);if(o==null)return;const{x:i,y:s}=o,l=(0,$.createCoreData)(this,i,s);(0,I.default)("DraggableCore: handleDragStart: %j",l),(0,I.default)("calling",this.props.onStart),!(this.props.onStart(t,l)===!1||this.mounted===!1)&&(this.props.enableUserSelectHack&&(0,O.addUserSelectStyles)(n),this.dragging=!0,this.lastX=i,this.lastY=s,(0,O.addEvent)(n,H.move,this.handleDrag),(0,O.addEvent)(n,H.stop,this.handleDragStop))}),D(this,"handleDrag",t=>{const r=(0,$.getControlPosition)(t,this.touchIdentifier,this);if(r==null)return;let{x:n,y:a}=r;if(Array.isArray(this.props.grid)){let s=n-this.lastX,l=a-this.lastY;if([s,l]=(0,$.snapToGrid)(this.props.grid,s,l),!s&&!l)return;n=this.lastX+s,a=this.lastY+l}const o=(0,$.createCoreData)(this,n,a);if((0,I.default)("DraggableCore: handleDrag: %j",o),this.props.onDrag(t,o)===!1||this.mounted===!1){try{this.handleDragStop(new MouseEvent("mouseup"))}catch{const l=document.createEvent("MouseEvents");l.initMouseEvent("mouseup",!0,!0,window,0,0,0,0,0,!1,!1,!1,!1,0,null),this.handleDragStop(l)}return}this.lastX=n,this.lastY=a}),D(this,"handleDragStop",t=>{if(!this.dragging)return;const r=(0,$.getControlPosition)(t,this.touchIdentifier,this);if(r==null)return;let{x:n,y:a}=r;if(Array.isArray(this.props.grid)){let l=n-this.lastX||0,h=a-this.lastY||0;[l,h]=(0,$.snapToGrid)(this.props.grid,l,h),n=this.lastX+l,a=this.lastY+h}const o=(0,$.createCoreData)(this,n,a);if(this.props.onStop(t,o)===!1||this.mounted===!1)return!1;const s=this.findDOMNode();s&&this.props.enableUserSelectHack&&(0,O.removeUserSelectStyles)(s.ownerDocument),(0,I.default)("DraggableCore: handleDragStop: %j",o),this.dragging=!1,this.lastX=NaN,this.lastY=NaN,s&&((0,I.default)("DraggableCore: Removing handlers"),(0,O.removeEvent)(s.ownerDocument,H.move,this.handleDrag),(0,O.removeEvent)(s.ownerDocument,H.stop,this.handleDragStop))}),D(this,"onMouseDown",t=>(H=T.mouse,this.handleDragStart(t))),D(this,"onMouseUp",t=>(H=T.mouse,this.handleDragStop(t))),D(this,"onTouchStart",t=>(H=T.touch,this.handleDragStart(t))),D(this,"onTouchEnd",t=>(H=T.touch,this.handleDragStop(t)))}componentDidMount(){this.mounted=!0;const t=this.findDOMNode();t&&(0,O.addEvent)(t,T.touch.start,this.onTouchStart,{passive:!1})}componentWillUnmount(){this.mounted=!1;const t=this.findDOMNode();if(t){const{ownerDocument:r}=t;(0,O.removeEvent)(r,T.mouse.move,this.handleDrag),(0,O.removeEvent)(r,T.touch.move,this.handleDrag),(0,O.removeEvent)(r,T.mouse.stop,this.handleDragStop),(0,O.removeEvent)(r,T.touch.stop,this.handleDragStop),(0,O.removeEvent)(t,T.touch.start,this.onTouchStart,{passive:!1}),this.props.enableUserSelectHack&&(0,O.removeUserSelectStyles)(r)}}findDOMNode(){var t,r;return(t=this.props)!==null&&t!==void 0&&t.nodeRef?(r=this.props)===null||r===void 0||(r=r.nodeRef)===null||r===void 0?void 0:r.current:$t.default.findDOMNode(this)}render(){return ne.cloneElement(ne.Children.only(this.props.children),{onMouseDown:this.onMouseDown,onMouseUp:this.onMouseUp,onTouchEnd:this.onTouchEnd})}};Q.default=ee;D(ee,"displayName","DraggableCore");D(ee,"propTypes",{allowAnyClick:_.default.bool,children:_.default.node.isRequired,disabled:_.default.bool,enableUserSelectHack:_.default.bool,offsetParent:function(e,t){if(e[t]&&e[t].nodeType!==1)throw new Error("Draggable's offsetParent must be a DOM Node.")},grid:_.default.arrayOf(_.default.number),handle:_.default.string,cancel:_.default.string,nodeRef:_.default.object,onStart:_.default.func,onDrag:_.default.func,onStop:_.default.func,onMouseDown:_.default.func,scale:_.default.number,className:ae.dontSetMe,style:ae.dontSetMe,transform:ae.dontSetMe});D(ee,"defaultProps",{allowAnyClick:!1,disabled:!1,enableUserSelectHack:!0,onStart:function(){},onDrag:function(){},onStop:function(){},onMouseDown:function(){},scale:1});(function(e){Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"DraggableCore",{enumerable:!0,get:function(){return l.default}}),e.default=void 0;var t=P(U),r=m(J),n=m(Te),a=m(nt),o=y,i=N,s=E,l=m(Q),h=m(Z);function m(c){return c&&c.__esModule?c:{default:c}}function v(c){if(typeof WeakMap!="function")return null;var u=new WeakMap,d=new WeakMap;return(v=function(p){return p?d:u})(c)}function P(c,u){if(c&&c.__esModule)return c;if(c===null||typeof c!="object"&&typeof c!="function")return{default:c};var d=v(u);if(d&&d.has(c))return d.get(c);var p={},S=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var g in c)if(g!=="default"&&Object.prototype.hasOwnProperty.call(c,g)){var M=S?Object.getOwnPropertyDescriptor(c,g):null;M&&(M.get||M.set)?Object.defineProperty(p,g,M):p[g]=c[g]}return p.default=c,d&&d.set(c,p),p}function w(){return w=Object.assign?Object.assign.bind():function(c){for(var u=1;u<arguments.length;u++){var d=arguments[u];for(var p in d)Object.prototype.hasOwnProperty.call(d,p)&&(c[p]=d[p])}return c},w.apply(this,arguments)}function b(c,u,d){return u=W(u),u in c?Object.defineProperty(c,u,{value:d,enumerable:!0,configurable:!0,writable:!0}):c[u]=d,c}function W(c){var u=z(c,"string");return typeof u=="symbol"?u:String(u)}function z(c,u){if(typeof c!="object"||c===null)return c;var d=c[Symbol.toPrimitive];if(d!==void 0){var p=d.call(c,u);if(typeof p!="object")return p;throw new TypeError("@@toPrimitive must return a primitive value.")}return(u==="string"?String:Number)(c)}class C extends t.Component{static getDerivedStateFromProps(u,d){let{position:p}=u,{prevPropsPosition:S}=d;return p&&(!S||p.x!==S.x||p.y!==S.y)?((0,h.default)("Draggable: getDerivedStateFromProps %j",{position:p,prevPropsPosition:S}),{x:p.x,y:p.y,prevPropsPosition:{...p}}):null}constructor(u){super(u),b(this,"onDragStart",(d,p)=>{if((0,h.default)("Draggable: onDragStart: %j",p),this.props.onStart(d,(0,i.createDraggableData)(this,p))===!1)return!1;this.setState({dragging:!0,dragged:!0})}),b(this,"onDrag",(d,p)=>{if(!this.state.dragging)return!1;(0,h.default)("Draggable: onDrag: %j",p);const S=(0,i.createDraggableData)(this,p),g={x:S.x,y:S.y,slackX:0,slackY:0};if(this.props.bounds){const{x:A,y:Y}=g;g.x+=this.state.slackX,g.y+=this.state.slackY;const[L,ve]=(0,i.getBoundPosition)(this,g.x,g.y);g.x=L,g.y=ve,g.slackX=this.state.slackX+(A-g.x),g.slackY=this.state.slackY+(Y-g.y),S.x=g.x,S.y=g.y,S.deltaX=g.x-this.state.x,S.deltaY=g.y-this.state.y}if(this.props.onDrag(d,S)===!1)return!1;this.setState(g)}),b(this,"onDragStop",(d,p)=>{if(!this.state.dragging||this.props.onStop(d,(0,i.createDraggableData)(this,p))===!1)return!1;(0,h.default)("Draggable: onDragStop: %j",p);const g={dragging:!1,slackX:0,slackY:0};if(!!this.props.position){const{x:A,y:Y}=this.props.position;g.x=A,g.y=Y}this.setState(g)}),this.state={dragging:!1,dragged:!1,x:u.position?u.position.x:u.defaultPosition.x,y:u.position?u.position.y:u.defaultPosition.y,prevPropsPosition:{...u.position},slackX:0,slackY:0,isElementSVG:!1},u.position&&!(u.onDrag||u.onStop)&&console.warn("A `position` was applied to this <Draggable>, without drag handlers. This will make this component effectively undraggable. Please attach `onDrag` or `onStop` handlers so you can adjust the `position` of this element.")}componentDidMount(){typeof window.SVGElement<"u"&&this.findDOMNode()instanceof window.SVGElement&&this.setState({isElementSVG:!0})}componentWillUnmount(){this.setState({dragging:!1})}findDOMNode(){var u,d;return(u=(d=this.props)===null||d===void 0||(d=d.nodeRef)===null||d===void 0?void 0:d.current)!==null&&u!==void 0?u:n.default.findDOMNode(this)}render(){const{axis:u,bounds:d,children:p,defaultPosition:S,defaultClassName:g,defaultClassNameDragging:M,defaultClassNameDragged:A,position:Y,positionOffset:L,scale:ve,...Ve}=this.props;let ye={},be=null;const Se=!!!Y||this.state.dragging,Oe=Y||S,we={x:(0,i.canDragX)(this)&&Se?this.state.x:Oe.x,y:(0,i.canDragY)(this)&&Se?this.state.y:Oe.y};this.state.isElementSVG?be=(0,o.createSVGTransform)(we,L):ye=(0,o.createCSSTransform)(we,L);const ke=(0,a.default)(p.props.className||"",g,{[M]:this.state.dragging,[A]:this.state.dragged});return t.createElement(l.default,w({},Ve,{onStart:this.onDragStart,onDrag:this.onDrag,onStop:this.onDragStop}),t.cloneElement(t.Children.only(p),{className:ke,style:{...p.props.style,...ye},transform:be}))}}e.default=C,b(C,"displayName","Draggable"),b(C,"propTypes",{...l.default.propTypes,axis:r.default.oneOf(["both","x","y","none"]),bounds:r.default.oneOfType([r.default.shape({left:r.default.number,right:r.default.number,top:r.default.number,bottom:r.default.number}),r.default.string,r.default.oneOf([!1])]),defaultClassName:r.default.string,defaultClassNameDragging:r.default.string,defaultClassNameDragged:r.default.string,defaultPosition:r.default.shape({x:r.default.number,y:r.default.number}),positionOffset:r.default.shape({x:r.default.oneOfType([r.default.number,r.default.string]),y:r.default.oneOfType([r.default.number,r.default.string])}),position:r.default.shape({x:r.default.number,y:r.default.number}),className:s.dontSetMe,style:s.dontSetMe,transform:s.dontSetMe}),b(C,"defaultProps",{...l.default.defaultProps,axis:"both",bounds:!1,defaultClassName:"react-draggable",defaultClassNameDragging:"react-draggable-dragging",defaultClassNameDragged:"react-draggable-dragged",defaultPosition:{x:0,y:0},scale:1})})(ze);const{default:Ie,DraggableCore:jt}=ze;G.exports=Ie;G.exports.default=Ie;G.exports.DraggableCore=jt;var Ut=G.exports,ge={};ge.__esModule=!0;ge.cloneElement=Lt;var At=It(U);function It(e){return e&&e.__esModule?e:{default:e}}function _e(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function Re(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?_e(Object(r),!0).forEach(function(n){Ft(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):_e(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Ft(e,t,r){return t=Bt(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Bt(e){var t=qt(e,"string");return typeof t=="symbol"?t:String(t)}function qt(e,t){if(typeof e!="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Lt(e,t){return t.style&&e.props.style&&(t.style=Re(Re({},e.props.style),t.style)),t.className&&e.props.className&&(t.className=e.props.className+" "+t.className),At.default.cloneElement(e,t)}var q={};q.__esModule=!0;q.resizableProps=void 0;var f=Vt(J);function Vt(e){return e&&e.__esModule?e:{default:e}}var kt={axis:f.default.oneOf(["both","x","y","none"]),className:f.default.string,children:f.default.element.isRequired,draggableOpts:f.default.shape({allowAnyClick:f.default.bool,cancel:f.default.string,children:f.default.node,disabled:f.default.bool,enableUserSelectHack:f.default.bool,offsetParent:f.default.node,grid:f.default.arrayOf(f.default.number),handle:f.default.string,nodeRef:f.default.object,onStart:f.default.func,onDrag:f.default.func,onStop:f.default.func,onMouseDown:f.default.func,scale:f.default.number}),height:function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];var a=r[0];if(a.axis==="both"||a.axis==="y"){var o;return(o=f.default.number).isRequired.apply(o,r)}return f.default.number.apply(f.default,r)},handle:f.default.oneOfType([f.default.node,f.default.func]),handleSize:f.default.arrayOf(f.default.number),lockAspectRatio:f.default.bool,maxConstraints:f.default.arrayOf(f.default.number),minConstraints:f.default.arrayOf(f.default.number),onResizeStop:f.default.func,onResizeStart:f.default.func,onResize:f.default.func,resizeHandles:f.default.arrayOf(f.default.oneOf(["s","w","e","n","sw","nw","se","ne"])),transformScale:f.default.number,width:function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];var a=r[0];if(a.axis==="both"||a.axis==="x"){var o;return(o=f.default.number).isRequired.apply(o,r)}return f.default.number.apply(f.default,r)}};q.resizableProps=kt;B.__esModule=!0;B.default=void 0;var F=Zt(U),Kt=Ut,Gt=ge,Jt=q,Qt=["children","className","draggableOpts","width","height","handle","handleSize","lockAspectRatio","axis","minConstraints","maxConstraints","onResize","onResizeStop","onResizeStart","resizeHandles","transformScale"];function Fe(e){if(typeof WeakMap!="function")return null;var t=new WeakMap,r=new WeakMap;return(Fe=function(a){return a?r:t})(e)}function Zt(e,t){if(e&&e.__esModule)return e;if(e===null||typeof e!="object"&&typeof e!="function")return{default:e};var r=Fe(t);if(r&&r.has(e))return r.get(e);var n={},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if(o!=="default"&&Object.prototype.hasOwnProperty.call(e,o)){var i=a?Object.getOwnPropertyDescriptor(e,o):null;i&&(i.get||i.set)?Object.defineProperty(n,o,i):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}function se(){return se=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},se.apply(this,arguments)}function er(e,t){if(e==null)return{};var r={},n=Object.keys(e),a,o;for(o=0;o<n.length;o++)a=n[o],!(t.indexOf(a)>=0)&&(r[a]=e[a]);return r}function xe(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function oe(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?xe(Object(r),!0).forEach(function(n){tr(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):xe(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function tr(e,t,r){return t=rr(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function rr(e){var t=nr(e,"string");return typeof t=="symbol"?t:String(t)}function nr(e,t){if(typeof e!="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function ar(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,le(e,t)}function le(e,t){return le=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,a){return n.__proto__=a,n},le(e,t)}var me=function(e){ar(t,e);function t(){for(var n,a=arguments.length,o=new Array(a),i=0;i<a;i++)o[i]=arguments[i];return n=e.call.apply(e,[this].concat(o))||this,n.handleRefs={},n.lastHandleRect=null,n.slack=null,n}var r=t.prototype;return r.componentWillUnmount=function(){this.resetData()},r.resetData=function(){this.lastHandleRect=this.slack=null},r.runConstraints=function(a,o){var i=this.props,s=i.minConstraints,l=i.maxConstraints,h=i.lockAspectRatio;if(!s&&!l&&!h)return[a,o];if(h){var m=this.props.width/this.props.height,v=a-this.props.width,P=o-this.props.height;Math.abs(v)>Math.abs(P*m)?o=a/m:a=o*m}var w=a,b=o,W=this.slack||[0,0],z=W[0],C=W[1];return a+=z,o+=C,s&&(a=Math.max(s[0],a),o=Math.max(s[1],o)),l&&(a=Math.min(l[0],a),o=Math.min(l[1],o)),this.slack=[z+(w-a),C+(b-o)],[a,o]},r.resizeHandler=function(a,o){var i=this;return function(s,l){var h=l.node,m=l.deltaX,v=l.deltaY;a==="onResizeStart"&&i.resetData();var P=(i.props.axis==="both"||i.props.axis==="x")&&o!=="n"&&o!=="s",w=(i.props.axis==="both"||i.props.axis==="y")&&o!=="e"&&o!=="w";if(!(!P&&!w)){var b=o[0],W=o[o.length-1],z=h.getBoundingClientRect();if(i.lastHandleRect!=null){if(W==="w"){var C=z.left-i.lastHandleRect.left;m+=C}if(b==="n"){var c=z.top-i.lastHandleRect.top;v+=c}}i.lastHandleRect=z,W==="w"&&(m=-m),b==="n"&&(v=-v);var u=i.props.width+(P?m/i.props.transformScale:0),d=i.props.height+(w?v/i.props.transformScale:0),p=i.runConstraints(u,d);u=p[0],d=p[1];var S=u!==i.props.width||d!==i.props.height,g=typeof i.props[a]=="function"?i.props[a]:null,M=a==="onResize"&&!S;g&&!M&&(s.persist==null||s.persist(),g(s,{node:h,size:{width:u,height:d},handle:o})),a==="onResizeStop"&&i.resetData()}}},r.renderResizeHandle=function(a,o){var i=this.props.handle;if(!i)return F.createElement("span",{className:"react-resizable-handle react-resizable-handle-"+a,ref:o});if(typeof i=="function")return i(a,o);var s=typeof i.type=="string",l=oe({ref:o},s?{}:{handleAxis:a});return F.cloneElement(i,l)},r.render=function(){var a=this,o=this.props,i=o.children,s=o.className,l=o.draggableOpts;o.width,o.height,o.handle,o.handleSize,o.lockAspectRatio,o.axis,o.minConstraints,o.maxConstraints,o.onResize,o.onResizeStop,o.onResizeStart;var h=o.resizeHandles;o.transformScale;var m=er(o,Qt);return(0,Gt.cloneElement)(i,oe(oe({},m),{},{className:(s?s+" ":"")+"react-resizable",children:[].concat(i.props.children,h.map(function(v){var P,w=(P=a.handleRefs[v])!=null?P:a.handleRefs[v]=F.createRef();return F.createElement(Kt.DraggableCore,se({},l,{nodeRef:w,key:"resizableHandle-"+v,onStop:a.resizeHandler("onResizeStop",v),onStart:a.resizeHandler("onResizeStart",v),onDrag:a.resizeHandler("onResize",v)}),a.renderResizeHandle(v,w))}))}))},t}(F.Component);B.default=me;me.propTypes=Jt.resizableProps;me.defaultProps={axis:"both",handleSize:[20,20],lockAspectRatio:!1,minConstraints:[20,20],maxConstraints:[1/0,1/0],resizeHandles:["se"],transformScale:1};var te={};te.__esModule=!0;te.default=void 0;var ie=ur(U),or=Be(J),ir=Be(B),sr=q,lr=["handle","handleSize","onResize","onResizeStart","onResizeStop","draggableOpts","minConstraints","maxConstraints","lockAspectRatio","axis","width","height","resizeHandles","style","transformScale"];function Be(e){return e&&e.__esModule?e:{default:e}}function qe(e){if(typeof WeakMap!="function")return null;var t=new WeakMap,r=new WeakMap;return(qe=function(a){return a?r:t})(e)}function ur(e,t){if(e&&e.__esModule)return e;if(e===null||typeof e!="object"&&typeof e!="function")return{default:e};var r=qe(t);if(r&&r.has(e))return r.get(e);var n={},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if(o!=="default"&&Object.prototype.hasOwnProperty.call(e,o)){var i=a?Object.getOwnPropertyDescriptor(e,o):null;i&&(i.get||i.set)?Object.defineProperty(n,o,i):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}function ue(){return ue=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ue.apply(this,arguments)}function Ce(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function K(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ce(Object(r),!0).forEach(function(n){cr(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ce(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function cr(e,t,r){return t=fr(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function fr(e){var t=dr(e,"string");return typeof t=="symbol"?t:String(t)}function dr(e,t){if(typeof e!="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function pr(e,t){if(e==null)return{};var r={},n=Object.keys(e),a,o;for(o=0;o<n.length;o++)a=n[o],!(t.indexOf(a)>=0)&&(r[a]=e[a]);return r}function hr(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,ce(e,t)}function ce(e,t){return ce=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,a){return n.__proto__=a,n},ce(e,t)}var Le=function(e){hr(t,e);function t(){for(var n,a=arguments.length,o=new Array(a),i=0;i<a;i++)o[i]=arguments[i];return n=e.call.apply(e,[this].concat(o))||this,n.state={width:n.props.width,height:n.props.height,propsWidth:n.props.width,propsHeight:n.props.height},n.onResize=function(s,l){var h=l.size;n.props.onResize?(s.persist==null||s.persist(),n.setState(h,function(){return n.props.onResize&&n.props.onResize(s,l)})):n.setState(h)},n}t.getDerivedStateFromProps=function(a,o){return o.propsWidth!==a.width||o.propsHeight!==a.height?{width:a.width,height:a.height,propsWidth:a.width,propsHeight:a.height}:null};var r=t.prototype;return r.render=function(){var a=this.props,o=a.handle,i=a.handleSize;a.onResize;var s=a.onResizeStart,l=a.onResizeStop,h=a.draggableOpts,m=a.minConstraints,v=a.maxConstraints,P=a.lockAspectRatio,w=a.axis;a.width,a.height;var b=a.resizeHandles,W=a.style,z=a.transformScale,C=pr(a,lr);return ie.createElement(ir.default,{axis:w,draggableOpts:h,handle:o,handleSize:i,height:this.state.height,lockAspectRatio:P,maxConstraints:v,minConstraints:m,onResizeStart:s,onResize:this.onResize,onResizeStop:l,resizeHandles:b,transformScale:z,width:this.state.width},ie.createElement("div",ue({},C,{style:K(K({},W),{},{width:this.state.width+"px",height:this.state.height+"px"})})))},t}(ie.Component);te.default=Le;Le.propTypes=K(K({},sr.resizableProps),{},{children:or.default.element});fe.exports=function(){throw new Error("Don't instantiate Resizable directly! Use require('react-resizable').Resizable")};var gr=fe.exports.Resizable=B.default;fe.exports.ResizableBox=te.default;const mr=e=>{const{onResize:t,width:r,...n}=e;return r?k.jsx(gr,{className:"resize-cell",width:r,height:0,onResize:t,draggableOpts:{enableUserSelectHack:!1},children:k.jsx("th",{...n})}):k.jsx("th",{...n})},Or=e=>{const{columns:t=[],...r}=e,[n,a]=Ge.useState(t),o=l=>(h,{size:m})=>{a(v=>{const P=w=>w.map(b=>"key"in b&&b.key===l?{...b,width:m.width}:"children"in b?{...b,children:P(b.children)}:b);return P(v)})},i=l=>l.map(h=>{const m={...h,onHeaderCell:v=>({width:v.width,onResize:o(v.key)})};return h.children&&h.children.length?{...m,children:i(h.children)}:m}),s=i(n);return U.useEffect(()=>{a(t)},[t]),k.jsx(Je,{...r,components:{header:{cell:mr}},columns:s})};export{Or as R};
