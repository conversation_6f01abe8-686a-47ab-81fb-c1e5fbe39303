import{b as F,E as ee,bN as te,bO as oe,G as ne,bP as re,b1 as ae,bQ as le,r as a,q as H,c as h,bR as se,k as K,w as X,T as ie,br as ce,f as de,K as pe,bS as G,s as me,P as ue,B as ge,bT as fe,bU as ve,v as ye}from"./index-De_f0oL2.js";const N=e=>e?typeof e=="function"?e():e:null,be=e=>{const{componentCls:o,popoverColor:n,titleMinWidth:t,fontWeightStrong:r,innerPadding:l,boxShadowSecondary:d,colorTextHeading:i,borderRadiusLG:c,zIndexPopup:m,titleMarginBottom:p,colorBgElevated:u,popoverBg:b,titleBorderBottom:C,innerContentPadding:f,titlePadding:s}=e;return[{[o]:Object.assign(Object.assign({},ne(e)),{position:"absolute",top:0,left:{_skip_check_:!0,value:0},zIndex:m,fontWeight:"normal",whiteSpace:"normal",textAlign:"start",cursor:"auto",userSelect:"text","--valid-offset-x":"var(--arrow-offset-horizontal, var(--arrow-x))",transformOrigin:["var(--valid-offset-x, 50%)","var(--arrow-y, 50%)"].join(" "),"--antd-arrow-background-color":u,width:"max-content",maxWidth:"100vw","&-rtl":{direction:"rtl"},"&-hidden":{display:"none"},[`${o}-content`]:{position:"relative"},[`${o}-inner`]:{backgroundColor:b,backgroundClip:"padding-box",borderRadius:c,boxShadow:d,padding:l},[`${o}-title`]:{minWidth:t,marginBottom:p,color:i,fontWeight:r,borderBottom:C,padding:s},[`${o}-inner-content`]:{color:n,padding:f}})},oe(e,"var(--antd-arrow-background-color)"),{[`${o}-pure`]:{position:"relative",maxWidth:"none",margin:e.sizePopupArrow,display:"inline-block",[`${o}-content`]:{display:"inline-block"}}}]},Ce=e=>{const{componentCls:o}=e;return{[o]:re.map(n=>{const t=e[`${n}6`];return{[`&${o}-${n}`]:{"--antd-arrow-background-color":t,[`${o}-inner`]:{backgroundColor:t},[`${o}-arrow`]:{background:"transparent"}}}})}},xe=e=>{const{lineWidth:o,controlHeight:n,fontHeight:t,padding:r,wireframe:l,zIndexPopupBase:d,borderRadiusLG:i,marginXS:c,lineType:m,colorSplit:p,paddingSM:u}=e,b=n-t,C=b/2,f=b/2-o,s=r;return Object.assign(Object.assign(Object.assign({titleMinWidth:177,zIndexPopup:d+30},ae(e)),le({contentRadius:i,limitVerticalRadius:!0})),{innerPadding:l?0:12,titleMarginBottom:l?0:c,titlePadding:l?`${C}px ${s}px ${f}px`:0,titleBorderBottom:l?`${o}px ${m} ${p}`:"none",innerContentPadding:l?`${u}px ${s}px`:0})},U=F("Popover",e=>{const{colorBgElevated:o,colorText:n}=e,t=ee(e,{popoverBg:o,popoverColor:n});return[be(t),Ce(t),te(t,"zoom-big")]},xe,{resetStyle:!1,deprecatedTokens:[["width","titleMinWidth"],["minWidth","titleMinWidth"]]});var Oe=function(e,o){var n={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&o.indexOf(t)<0&&(n[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)o.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(n[t[r]]=e[t[r]]);return n};const q=({title:e,content:o,prefixCls:n})=>!e&&!o?null:a.createElement(a.Fragment,null,e&&a.createElement("div",{className:`${n}-title`},e),o&&a.createElement("div",{className:`${n}-inner-content`},o)),Pe=e=>{const{hashId:o,prefixCls:n,className:t,style:r,placement:l="top",title:d,content:i,children:c}=e,m=N(d),p=N(i),u=h(o,n,`${n}-pure`,`${n}-placement-${l}`,t);return a.createElement("div",{className:u,style:r},a.createElement("div",{className:`${n}-arrow`}),a.createElement(se,Object.assign({},e,{className:o,prefixCls:n}),c||a.createElement(q,{prefixCls:n,title:m,content:p})))},Y=e=>{const{prefixCls:o,className:n}=e,t=Oe(e,["prefixCls","className"]),{getPrefixCls:r}=a.useContext(H),l=r("popover",o),[d,i,c]=U(l);return d(a.createElement(Pe,Object.assign({},t,{prefixCls:l,hashId:i,className:h(n,c)})))};var he=function(e,o){var n={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&o.indexOf(t)<0&&(n[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)o.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(n[t[r]]=e[t[r]]);return n};const Ne=a.forwardRef((e,o)=>{var n,t;const{prefixCls:r,title:l,content:d,overlayClassName:i,placement:c="top",trigger:m="hover",children:p,mouseEnterDelay:u=.1,mouseLeaveDelay:b=.1,onOpenChange:C,overlayStyle:f={},styles:s,classNames:g}=e,O=he(e,["prefixCls","title","content","overlayClassName","placement","trigger","children","mouseEnterDelay","mouseLeaveDelay","onOpenChange","overlayStyle","styles","classNames"]),{getPrefixCls:P,className:_,style:E,classNames:S,styles:B}=K("popover"),$=P("popover",r),[w,z,W]=U($),V=P(),R=h(i,z,W,_,S.root,g==null?void 0:g.root),j=h(S.body,g==null?void 0:g.body),[D,M]=X(!1,{value:(n=e.open)!==null&&n!==void 0?n:e.visible,defaultValue:(t=e.defaultOpen)!==null&&t!==void 0?t:e.defaultVisible}),k=(x,T)=>{M(x,!0),C==null||C(x,T)},v=x=>{x.keyCode===pe.ESC&&k(!1,x)},y=x=>{k(x)},I=N(l),L=N(d);return w(a.createElement(ie,Object.assign({placement:c,trigger:m,mouseEnterDelay:u,mouseLeaveDelay:b},O,{prefixCls:$,classNames:{root:R,body:j},styles:{root:Object.assign(Object.assign(Object.assign(Object.assign({},B.root),E),f),s==null?void 0:s.root),body:Object.assign(Object.assign({},B.body),s==null?void 0:s.body)},ref:o,open:D,onOpenChange:y,overlay:I||L?a.createElement(q,{prefixCls:$,title:I,content:L}):null,transitionName:ce(V,"zoom-big",O.transitionName),"data-popover-inject":!0}),de(p,{onKeyDown:x=>{var T,A;a.isValidElement(p)&&((A=p==null?void 0:(T=p.props).onKeyDown)===null||A===void 0||A.call(T,x)),v(x)}})))}),Q=Ne;Q._InternalPanelDoNotUseOrYouWillBeFired=Y;const Se=e=>{const{componentCls:o,iconCls:n,antCls:t,zIndexPopup:r,colorText:l,colorWarning:d,marginXXS:i,marginXS:c,fontSize:m,fontWeightStrong:p,colorTextHeading:u}=e;return{[o]:{zIndex:r,[`&${t}-popover`]:{fontSize:m},[`${o}-message`]:{marginBottom:c,display:"flex",flexWrap:"nowrap",alignItems:"start",[`> ${o}-message-icon ${n}`]:{color:d,fontSize:m,lineHeight:1,marginInlineEnd:c},[`${o}-title`]:{fontWeight:p,color:u,"&:only-child":{fontWeight:"normal"}},[`${o}-description`]:{marginTop:i,color:l}},[`${o}-buttons`]:{textAlign:"end",whiteSpace:"nowrap",button:{marginInlineStart:c}}}}},$e=e=>{const{zIndexPopupBase:o}=e;return{zIndexPopup:o+60}},Z=F("Popconfirm",e=>Se(e),$e,{resetStyle:!1});var we=function(e,o){var n={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&o.indexOf(t)<0&&(n[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)o.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(n[t[r]]=e[t[r]]);return n};const J=e=>{const{prefixCls:o,okButtonProps:n,cancelButtonProps:t,title:r,description:l,cancelText:d,okText:i,okType:c="primary",icon:m=a.createElement(G,null),showCancel:p=!0,close:u,onConfirm:b,onCancel:C,onPopupClick:f}=e,{getPrefixCls:s}=a.useContext(H),[g]=me("Popconfirm",ue.Popconfirm),O=N(r),P=N(l);return a.createElement("div",{className:`${o}-inner-content`,onClick:f},a.createElement("div",{className:`${o}-message`},m&&a.createElement("span",{className:`${o}-message-icon`},m),a.createElement("div",{className:`${o}-message-text`},O&&a.createElement("div",{className:`${o}-title`},O),P&&a.createElement("div",{className:`${o}-description`},P))),a.createElement("div",{className:`${o}-buttons`},p&&a.createElement(ge,Object.assign({onClick:C,size:"small"},t),d||(g==null?void 0:g.cancelText)),a.createElement(fe,{buttonProps:Object.assign(Object.assign({size:"small"},ve(c)),n),actionFn:b,close:u,prefixCls:s("btn"),quitOnNullishReturnValue:!0,emitEvent:!0},i||(g==null?void 0:g.okText))))},je=e=>{const{prefixCls:o,placement:n,className:t,style:r}=e,l=we(e,["prefixCls","placement","className","style"]),{getPrefixCls:d}=a.useContext(H),i=d("popconfirm",o),[c]=Z(i);return c(a.createElement(Y,{placement:n,className:h(i,t),style:r,content:a.createElement(J,Object.assign({prefixCls:i},l))}))};var Ee=function(e,o){var n={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&o.indexOf(t)<0&&(n[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)o.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(n[t[r]]=e[t[r]]);return n};const Be=a.forwardRef((e,o)=>{var n,t;const{prefixCls:r,placement:l="top",trigger:d="click",okType:i="primary",icon:c=a.createElement(G,null),children:m,overlayClassName:p,onOpenChange:u,onVisibleChange:b,overlayStyle:C,styles:f,classNames:s}=e,g=Ee(e,["prefixCls","placement","trigger","okType","icon","children","overlayClassName","onOpenChange","onVisibleChange","overlayStyle","styles","classNames"]),{getPrefixCls:O,className:P,style:_,classNames:E,styles:S}=K("popconfirm"),[B,$]=X(!1,{value:(n=e.open)!==null&&n!==void 0?n:e.visible,defaultValue:(t=e.defaultOpen)!==null&&t!==void 0?t:e.defaultVisible}),w=(v,y)=>{$(v,!0),b==null||b(v),u==null||u(v,y)},z=v=>{w(!1,v)},W=v=>{var y;return(y=e.onConfirm)===null||y===void 0?void 0:y.call(void 0,v)},V=v=>{var y;w(!1,v),(y=e.onCancel)===null||y===void 0||y.call(void 0,v)},R=(v,y)=>{const{disabled:I=!1}=e;I||w(v,y)},j=O("popconfirm",r),D=h(j,P,p,E.root,s==null?void 0:s.root),M=h(E.body,s==null?void 0:s.body),[k]=Z(j);return k(a.createElement(Q,Object.assign({},ye(g,["title"]),{trigger:d,placement:l,onOpenChange:R,open:B,ref:o,classNames:{root:D,body:M},styles:{root:Object.assign(Object.assign(Object.assign(Object.assign({},S.root),_),C),f==null?void 0:f.root),body:Object.assign(Object.assign({},S.body),f==null?void 0:f.body)},content:a.createElement(J,Object.assign({okType:i,icon:c},e,{prefixCls:j,close:z,onConfirm:W,onCancel:V})),"data-popover-inject":!0}),m))}),ke=Be;ke._InternalPanelDoNotUseOrYouWillBeFired=je;export{ke as P};
