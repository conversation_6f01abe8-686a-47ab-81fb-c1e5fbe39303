import{b as Q,ac as U,E as K,a as F,G as Y,r as l,q as _,c as T,aC as M,aD as Z,v as ee,aE as oe,aF as re,aG as te,aH as ae,aI as H,aJ as le}from"./index-De_f0oL2.js";const ne=e=>{const{paddingXXS:t,lineWidth:a,tagPaddingHorizontal:o,componentCls:r,calc:s}=e,n=s(o).sub(a).equal(),u=s(t).sub(a).equal();return{[r]:Object.assign(Object.assign({},Y(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:n,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${F(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${r}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${r}-close-icon`]:{marginInlineStart:u,fontSize:e.tagIconSize,color:e.colorIcon,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${r}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${r}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:n}}),[`${r}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}},I=e=>{const{lineWidth:t,fontSizeIcon:a,calc:o}=e,r=e.fontSizeSM;return K(e,{tagFontSize:r,tagLineHeight:F(o(e.lineHeightSM).mul(r).equal()),tagIconSize:o(a).sub(o(t).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},P=e=>({defaultBg:new U(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText}),V=Q("Tag",e=>{const t=I(e);return ne(t)},P);var se=function(e,t){var a={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(a[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(a[o[r]]=e[o[r]]);return a};const ce=l.forwardRef((e,t)=>{const{prefixCls:a,style:o,className:r,checked:s,onChange:n,onClick:u}=e,d=se(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:m,tag:g}=l.useContext(_),f=h=>{n==null||n(!s),u==null||u(h)},C=m("tag",a),[y,S,i]=V(C),$=T(C,`${C}-checkable`,{[`${C}-checkable-checked`]:s},g==null?void 0:g.className,r,S,i);return y(l.createElement("span",Object.assign({},d,{ref:t,style:Object.assign(Object.assign({},o),g==null?void 0:g.style),className:$,onClick:f})))}),ie=e=>Z(e,(t,{textColor:a,lightBorderColor:o,lightColor:r,darkColor:s})=>({[`${e.componentCls}${e.componentCls}-${t}`]:{color:a,background:r,borderColor:o,"&-inverse":{color:e.colorTextLightSolid,background:s,borderColor:s},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}})),de=M(["Tag","preset"],e=>{const t=I(e);return ie(t)},P);function ge(e){return typeof e!="string"?e:e.charAt(0).toUpperCase()+e.slice(1)}const v=(e,t,a)=>{const o=ge(a);return{[`${e.componentCls}${e.componentCls}-${t}`]:{color:e[`color${a}`],background:e[`color${o}Bg`],borderColor:e[`color${o}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}},ue=M(["Tag","status"],e=>{const t=I(e);return[v(t,"success","Success"),v(t,"processing","Info"),v(t,"error","Error"),v(t,"warning","Warning")]},P);var Ce=function(e,t){var a={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(a[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(a[o[r]]=e[o[r]]);return a};const pe=l.forwardRef((e,t)=>{const{prefixCls:a,className:o,rootClassName:r,style:s,children:n,icon:u,color:d,onClose:m,bordered:g=!0,visible:f}=e,C=Ce(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:y,direction:S,tag:i}=l.useContext(_),[$,h]=l.useState(!0),W=ee(C,["closeIcon","closable"]);l.useEffect(()=>{f!==void 0&&h(f)},[f]);const E=oe(d),N=re(d),x=E||N,q=Object.assign(Object.assign({backgroundColor:d&&!x?d:void 0},i==null?void 0:i.style),s),c=y("tag",a),[L,R,k]=V(c),A=T(c,i==null?void 0:i.className,{[`${c}-${d}`]:x,[`${c}-has-color`]:d&&!x,[`${c}-hidden`]:!$,[`${c}-rtl`]:S==="rtl",[`${c}-borderless`]:!g},o,r,R,k),j=b=>{b.stopPropagation(),m==null||m(b),!b.defaultPrevented&&h(!1)},[,D]=te(H(e),H(i),{closable:!1,closeIconRender:b=>{const J=l.createElement("span",{className:`${c}-close-icon`,onClick:j},b);return ae(b,J,p=>({onClick:B=>{var O;(O=p==null?void 0:p.onClick)===null||O===void 0||O.call(p,B),j(B)},className:T(p==null?void 0:p.className,`${c}-close-icon`)}))}}),X=typeof C.onClick=="function"||n&&n.type==="a",w=u||null,G=w?l.createElement(l.Fragment,null,w,n&&l.createElement("span",null,n)):n,z=l.createElement("span",Object.assign({},W,{ref:t,className:A,style:q}),G,D,E&&l.createElement(de,{key:"preset",prefixCls:c}),N&&l.createElement(ue,{key:"status",prefixCls:c}));return L(X?l.createElement(le,{component:"Tag"},z):z)}),be=pe;be.CheckableTag=ce;export{be as T};
