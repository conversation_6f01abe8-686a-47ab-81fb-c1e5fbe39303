import{F as S,r as l,A as ye,u as y,d as i,j as e,R as q,C as w,S as Ie,B as p,a4 as je}from"./index-De_f0oL2.js";import{p as I}from"./service-B_wWoC3F.js";import{R as Te,o as J,d as Se}from"./down-BCLNnN1h.js";import{R as we}from"./index-BmnYJy3v.js";import{s as f}from"./index-BcPP1N8I.js";import{D as be}from"./index-CdSZ9YgQ.js";import{C as Ye}from"./index-DZyVV6rP.js";import{R as Ne,a as Ae}from"./FullscreenOutlined-DzCTibKW.js";import{R as _e,U as Re,a as Ce}from"./index-BH5uxjwl.js";import{M as De}from"./index-Dck5cc4J.js";import{P as Ee}from"./index-TkzW9Zmk.js";import"./Table-D-iLeFE-.js";import"./index-BWJehDyc.js";import"./useMultipleSelect-B0dEIXT-.js";import"./DeleteOutlined-D-FcgX8f.js";const ve="_detail_modal_1dhsg_2",Me="_incrementIncome_page_1dhsg_6",ke="_animation_box_1dhsg_6",j={detail_modal:ve,incrementIncome_page:Me,animation_box:ke},{Dragger:Fe}=Re,Ze=()=>{var H,L,O,z,P;const[d]=S.useForm(),Y=l.useRef(null),N=l.useRef(null),A=l.useRef(null),[B,K]=l.useState(0),[_,R]=l.useState(!0),[Q,C]=l.useState(!1),[W,b]=l.useState(!1),[u,Z]=l.useState([]),[X,ee]=l.useState([]),[te,ne]=l.useState([]),{currentUser:D}=ye(),[m,E]=l.useState({total:0,pageNum:1,pageSize:50}),{runAsync:v}=y(I.getEnumType,{manual:!0}),{runAsync:ae}=y(I.getIncrementIncome,{manual:!0}),{runAsync:se}=y(I.downloadTemplate,{manual:!0}),{runAsync:oe}=y(I.exportIncrementIncome,{manual:!0}),{runAsync:re}=y(I.importIncrementIncome,{manual:!0}),le=[{title:"单位",dataIndex:"cityName",key:"cityName",align:"center",width:160,fixed:"left"},{title:"薪酬发放时间",dataIndex:"payTime",key:"payTime",align:"center",width:130},{title:"分类",dataIndex:"typeDesc",key:"typeDesc",align:"center",width:130},{title:"增量收益分享核定规则（核定到组织）",key:"level",dataIndex:"level",align:"center",children:[{title:"核定规则",dataIndex:"approvedRules",key:"approvedRules",align:"center",width:130},{title:"分享门槛",dataIndex:"shareThreshold",key:"shareThreshold",align:"center",width:130},{title:"分享比例",dataIndex:"shareRatio",key:"shareRatio",align:"center",width:130}]},{title:"营销服务中心及各部室下设中心",dataIndex:"marketingServiceCenter",key:"marketingServiceCenter",align:"center",width:200},{title:"兑现金额",dataIndex:"currentPayment",key:"currentPayment",align:"center",width:150}];l.useEffect(()=>(ie(),$(),window.addEventListener("resize",M),()=>{window.removeEventListener("resize",M)}),[]);const M=()=>{x()};l.useEffect(()=>{x()},[(document.querySelector(".incrementIncome_table .ant-table-header")||{}).offsetHeight]);const x=()=>{var n;const t=(document.querySelector(".incrementIncome_table .ant-table-header")||{}).offsetHeight||0,a=(document.querySelector(".incrementIncome_table .ant-table-pagination")||{}).offsetHeight||26;t&&a&&K(((n=N.current)==null?void 0:n.offsetHeight)-(A.current.offsetHeight+t+a))},k=async t=>{var a;try{J("正在导出",0,"loading");let n=null;if(t===1)n=await se({templateId:"INCRE_REV_SHARE_MONTHLY_DATA"});else if(t===2){const{loginDate:s,cityId:r,category:c,cycleId:h,empId:g}=d.getFieldsValue(),o={beginTime:(s==null?void 0:s.length)>0?i(s[0]).format("YYYY-MM-DD"):null,endTime:(s==null?void 0:s.length)>0?i(s[1]).format("YYYY-MM-DD"):null,cycleId:h?(a=i(h))==null?void 0:a.format("YYYYMM"):"",cityId:r?r[(r==null?void 0:r.length)-1]:null,category:c,empId:g};n=await oe(o)}Se(n)}catch(n){J("导出失败",1,"error"),console.error("Download failed:",n)}},ce=t=>{T({...t})},ie=async()=>{var r;const[[t,a],[n]]=await Promise.all([v({code:"1010",tag:1}),v({code:"EMPLOYEE_TRANSFER_CATEGORY"})]);if(t||n)return;a.STATUS==="0000"&&ee((r=a.DATA)==null?void 0:r.filter(c=>(c==null?void 0:c.orgId)!=="49757"));const s={...d.getFieldsValue()};T(s)},T=async t=>{var U,V;C(!0);const{loginDate:a,cityId:n,category:s,cycleId:r,empId:c}=t,h={...m,beginTime:(a==null?void 0:a.length)>0?i(a[0]).format("YYYY-MM-DD"):null,endTime:(a==null?void 0:a.length)>0?i(a[1]).format("YYYY-MM-DD"):null,cycleId:r?(U=i(r))==null?void 0:U.format("YYYYMM"):"",cityId:n?n[(n==null?void 0:n.length)-1]:null,category:s,empId:c},[g,o]=await ae(h);if(C(!1),g){f.error((o==null?void 0:o.DATA)||(o==null?void 0:o.MESSAGE)||"调用失败");return}if(o.STATUS==="0000"){const{DATA:{data:fe}}=o,xe=fe.map((G,ge)=>({...G,key:G.id||ge}));ne(xe),E({...m,total:(V=o.DATA)==null?void 0:V.totalCount})}else f.error((o==null?void 0:o.MESSAGE)||(o==null?void 0:o.DATA)||"调用失败")},me=async()=>{const t=new FormData;u.map(s=>s==null?void 0:s.originFileObj).forEach(s=>{t.append("file",s)});const[a,n]=await re(t);if(a){f.error((n==null?void 0:n.DATA)||(n==null?void 0:n.MESSAGE)||"调用失败");return}if(n.STATUS==="0000"){const{loginDate:s,yearVal:r,cityId:c,category:h}=d.getFieldsValue(),g={beginTime:s.length>0?i(s[0]).format("YYYY-MM-DD"):null,endTime:s.length>0?i(s[1]).format("YYYY-MM-DD"):null,yearVal:r?i(r).format("YYYY"):"",cityId:c[(c==null?void 0:c.length)-1],category:h};x(),T(g),b(!1),f.success(n==null?void 0:n.DATA)}else f.error((n==null?void 0:n.MESSAGE)||(n==null?void 0:n.DATA)||"调用失败")},de=()=>{(u==null?void 0:u.length)>0?me():f.error("请先选择文件上传")},F=()=>{b(!1)},ue=()=>{d.resetFields(),$()},he=t=>{const a={total:t==null?void 0:t.total,pageNum:t==null?void 0:t.current,pageSize:t==null?void 0:t.pageSize};E(a)};l.useEffect(()=>{if((m==null?void 0:m.total)>0){const t={...d.getFieldsValue()};T(t)}},[m.pageNum,m.pageSize]);const $=()=>{const t=i().subtract(1,"month");d.setFieldsValue({cycleId:t})},pe=(t,a)=>{var n,s;return(((n=a[0])==null?void 0:n.enumName)??"").toLowerCase().includes((s=t.toLowerCase())==null?void 0:s.trim())};return e.jsxs("div",{className:`pt-[0.5rem] px-[0rem] h-full flex flex-col ${j.incrementIncome_page}`,children:[e.jsxs(e.Fragment,{children:[e.jsx("div",{ref:Y,className:"bg-white pt-[0.5rem] px-[1.75rem] mb-[0.5rem]",children:e.jsx(S,{form:d,labelCol:{span:6},onFinish:ce,initialValues:{loginDate:"",category:"KOU_JING_TIAO_ZHENG",cityId:"",empId:""},children:e.jsxs(q,{gutter:24,children:[e.jsx(w,{span:6,children:e.jsx(S.Item,{name:"cycleId",label:"账期",className:"mb-[0.5rem]",children:e.jsx(be,{className:"w-full",picker:"month"})})}),e.jsx(w,{span:5,children:e.jsx(S.Item,{name:"cityId",label:"单位",className:"mb-[0.5rem]",children:e.jsx(Ye,{allowClear:!0,changeOnSelect:!0,expandTrigger:"hover",displayRender:t=>t[t.length-1],options:X,fieldNames:{value:"enumId",label:"enumName",children:"children"},placeholder:"请选择单位",showSearch:{filter:pe},onSearch:t=>console.log(t)})})}),e.jsx(w,{span:4,children:e.jsx("div",{className:"text-right",children:e.jsxs(Ie,{children:[e.jsx(p,{type:"primary",htmlType:"submit",children:"查询"}),e.jsx(p,{onClick:()=>ue(),children:"重置"})]})})})]})})}),e.jsxs("div",{ref:N,className:"relative bg-white px-5 pt-2.5",style:{height:`calc(100% - ${((H=Y.current)==null?void 0:H.offsetHeight)+15}px)`},children:[e.jsxs("div",{ref:A,className:`flex justify-between items-center overflow-hidden mb-[0.1rem] ${j.animation_box} ${_?"h-[1.8rem]":"h-0"}`,children:[e.jsxs("div",{className:"flex ",children:[_?e.jsx(Ne,{className:`${j.shousuo_icon} text-[1rem]`,onClick:()=>{R(!1),setTimeout(()=>{x()},200)}}):e.jsx(Ae,{className:`${j.shousuo_icon} absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10`,onClick:()=>{R(!0),setTimeout(()=>{x()},200)}}),e.jsx("div",{className:"font-bold text-[0.8rem] ml-3",children:"数据列表"})]}),e.jsx("div",{className:"flex gap-x-[2.5rem]",children:e.jsxs("div",{className:"flex gap-x-[0.75rem]",children:[!((O=(L=D.roleInfo)==null?void 0:L.roleCode)!=null&&O.includes("ATJ0001"))&&e.jsxs("div",{className:"flex items-center gap-x-[0.25rem]",children:[e.jsx(je,{name:"excel",width:20,height:20}),e.jsx("span",{className:"text-[#E60027] cursor-pointer",onClick:()=>k(1),children:"下载导入模版"})]}),!((P=(z=D.roleInfo)==null?void 0:z.roleCode)!=null&&P.includes("ATJ0001"))&&e.jsx(p,{danger:!0,ghost:!0,icon:e.jsx(_e,{}),onClick:()=>b(!0),children:"导入"}),e.jsx(p,{danger:!0,ghost:!0,icon:e.jsx(Te,{}),onClick:()=>k(2),children:"导出"})]})})]}),e.jsx(we,{className:"incrementIncome_table",rowClassName:(t,a)=>a%2===1?"customRow odd":"customRow even",columns:le,dataSource:te,bordered:!0,scroll:{y:`calc(${B}px - 0.625rem - 1.6rem - 0.5rem)`},loading:Q,onChange:he,pagination:{...m,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50"]}})]})]}),e.jsx(De,{title:"文件上传",destroyOnClose:!0,open:W,centered:!0,className:j.detail_modal,footer:null,onCancel:F,children:e.jsxs("div",{className:"mt-4 mb-8",style:{marginBottom:"0px"},children:[e.jsx(q,{children:e.jsx(w,{span:22,offset:1,className:"h-[10rem]",children:e.jsxs(Fe,{action:"",maxCount:1,multiple:!1,fileList:u,accept:".xls,.xlsx",beforeUpload(t,a){return console.log(t,a),!1},onChange(t){const{status:a}=t.file;a!=="uploading"&&(console.log(t.file,t.fileList),Z(t.fileList))},children:[e.jsx("p",{className:"ant-upload-drag-icon",children:e.jsx(Ce,{style:{color:"#F14846"}})}),e.jsx("p",{className:"ant-upload-text",children:"点击或将文件拖拽到这里上传"}),e.jsx("p",{className:"ant-upload-hint",children:"支持excel格式的文件。"})]})})}),e.jsxs("div",{style:{display:"flex",justifyContent:"center",marginTop:"2rem"},children:[e.jsx(p,{danger:!0,onClick:F,children:"取消"}),e.jsx(Ee,{title:"",description:"导入文件如有重复数据，将会被覆盖，请确认是否上传。",onConfirm:de,okText:"确认",cancelText:"取消",children:e.jsx(p,{danger:!0,type:"primary",style:{marginLeft:"1rem"},disabled:u.length<1,children:"上传"})})]})]})})]})};export{Ze as default};
