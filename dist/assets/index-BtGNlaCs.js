import{r as a,I as at,_ as rt,g as kt,o as Et,a as Ot,b as Nt,c as Ye,e as Lt,f as $t,K as ct,h as Pt,i as Yt,k as _t,l as wt,T as it,m as Ft,t as St,n as ve,p as Ct,q as Ut,s as zt,v as jt,w as dt,x as Ht,F as A,u as se,j as t,B as ee,y as K,z as It,d as fe,R as ne,C as O,A as Bt,S as ut}from"./index-De_f0oL2.js";import{c as ce,s as je}from"./Index.module-CMg3LkdG.js";import{u as Tt}from"./debounce-D4mn-XUD.js";import{R as Vt,o as pt,d as Wt}from"./down-BCLNnN1h.js";import{R as Gt}from"./index-BmnYJy3v.js";import{s as _}from"./index-BcPP1N8I.js";import{C as qt}from"./index-DZyVV6rP.js";import{R as Kt,S as le}from"./index-BWJehDyc.js";import{D as pe}from"./index-CdSZ9YgQ.js";import{i as ht,F as Jt}from"./Table-D-iLeFE-.js";import{M as Dt}from"./index-Dck5cc4J.js";import{P as mt}from"./index-TkzW9Zmk.js";import{R as Xt,a as Qt}from"./FullscreenOutlined-DzCTibKW.js";import{R as Zt}from"./DeleteOutlined-D-FcgX8f.js";import"./useMultipleSelect-B0dEIXT-.js";const en=(e,n=!1)=>n&&e==null?[]:Array.isArray(e)?e:[e];var tn={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32zM704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.2c3.5 1.3 7.2 2 11 2H704c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM664 888H414V746c0-22.1-17.9-40-40-40H232V264h432v624z"}}]},name:"copy",theme:"outlined"},nn=function(n,r){return a.createElement(at,rt({},n,{ref:r,icon:tn}))},ln=a.forwardRef(nn),an={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M257.7 752c2 0 4-.2 6-.5L431.9 722c2-.4 3.9-1.3 5.3-2.8l423.9-423.9a9.96 9.96 0 000-14.1L694.9 114.9c-1.9-1.9-4.4-2.9-7.1-2.9s-5.2 1-7.1 2.9L256.8 538.8c-1.5 1.5-2.4 3.3-2.8 5.3l-29.5 168.2a33.5 33.5 0 009.4 29.8c6.6 6.4 14.9 9.9 23.8 9.9zm67.4-174.4L687.8 215l73.3 73.3-362.7 362.6-88.9 15.7 15.6-89zM880 836H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32z"}}]},name:"edit",theme:"outlined"},rn=function(n,r){return a.createElement(at,rt({},n,{ref:r,icon:an}))},on=a.forwardRef(rn),sn={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M864 170h-60c-4.4 0-8 3.6-8 8v518H310v-73c0-6.7-7.8-10.5-13-6.3l-141.9 112a8 8 0 000 12.6l141.9 112c5.3 4.2 13 .4 13-6.3v-75h498c35.3 0 64-28.7 64-64V178c0-4.4-3.6-8-8-8z"}}]},name:"enter",theme:"outlined"},cn=function(n,r){return a.createElement(at,rt({},n,{ref:r,icon:sn}))},dn=a.forwardRef(cn);const un=(e,n,r,l)=>{const{titleMarginBottom:c,fontWeightStrong:x}=l;return{marginBottom:c,color:r,fontWeight:x,fontSize:e,lineHeight:n}},pn=e=>{const n=[1,2,3,4,5],r={};return n.forEach(l=>{r[`
      h${l}&,
      div&-h${l},
      div&-h${l} > textarea,
      h${l}
    `]=un(e[`fontSizeHeading${l}`],e[`lineHeightHeading${l}`],e.colorTextHeading,e)}),r},hn=e=>{const{componentCls:n}=e;return{"a&, a":Object.assign(Object.assign({},Et(e)),{userSelect:"text",[`&[disabled], &${n}-disabled`]:{color:e.colorTextDisabled,cursor:"not-allowed","&:active, &:hover":{color:e.colorTextDisabled},"&:active":{pointerEvents:"none"}}})}},mn=e=>({code:{margin:"0 0.2em",paddingInline:"0.4em",paddingBlock:"0.2em 0.1em",fontSize:"85%",fontFamily:e.fontFamilyCode,background:"rgba(150, 150, 150, 0.1)",border:"1px solid rgba(100, 100, 100, 0.2)",borderRadius:3},kbd:{margin:"0 0.2em",paddingInline:"0.4em",paddingBlock:"0.15em 0.1em",fontSize:"90%",fontFamily:e.fontFamilyCode,background:"rgba(150, 150, 150, 0.06)",border:"1px solid rgba(100, 100, 100, 0.2)",borderBottomWidth:2,borderRadius:3},mark:{padding:0,backgroundColor:kt[2]},"u, ins":{textDecoration:"underline",textDecorationSkipInk:"auto"},"s, del":{textDecoration:"line-through"},strong:{fontWeight:600},"ul, ol":{marginInline:0,marginBlock:"0 1em",padding:0,li:{marginInline:"20px 0",marginBlock:0,paddingInline:"4px 0",paddingBlock:0}},ul:{listStyleType:"circle",ul:{listStyleType:"disc"}},ol:{listStyleType:"decimal"},"pre, blockquote":{margin:"1em 0"},pre:{padding:"0.4em 0.6em",whiteSpace:"pre-wrap",wordWrap:"break-word",background:"rgba(150, 150, 150, 0.1)",border:"1px solid rgba(100, 100, 100, 0.2)",borderRadius:3,fontFamily:e.fontFamilyCode,code:{display:"inline",margin:0,padding:0,fontSize:"inherit",fontFamily:"inherit",background:"transparent",border:0}},blockquote:{paddingInline:"0.6em 0",paddingBlock:0,borderInlineStart:"4px solid rgba(100, 100, 100, 0.2)",opacity:.85}}),gn=e=>{const{componentCls:n,paddingSM:r}=e,l=r;return{"&-edit-content":{position:"relative","div&":{insetInlineStart:e.calc(e.paddingSM).mul(-1).equal(),marginTop:e.calc(l).mul(-1).equal(),marginBottom:`calc(1em - ${Ot(l)})`},[`${n}-edit-content-confirm`]:{position:"absolute",insetInlineEnd:e.calc(e.marginXS).add(2).equal(),insetBlockEnd:e.marginXS,color:e.colorIcon,fontWeight:"normal",fontSize:e.fontSize,fontStyle:"normal",pointerEvents:"none"},textarea:{margin:"0!important",MozTransition:"none",height:"1em"}}}},fn=e=>({[`${e.componentCls}-copy-success`]:{"\n    &,\n    &:hover,\n    &:focus":{color:e.colorSuccess}},[`${e.componentCls}-copy-icon-only`]:{marginInlineStart:0}}),yn=()=>({"\n  a&-ellipsis,\n  span&-ellipsis\n  ":{display:"inline-block",maxWidth:"100%"},"&-ellipsis-single-line":{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis","a&, span&":{verticalAlign:"bottom"},"> code":{paddingBlock:0,maxWidth:"calc(100% - 1.2em)",display:"inline-block",overflow:"hidden",textOverflow:"ellipsis",verticalAlign:"bottom",boxSizing:"content-box"}},"&-ellipsis-multiple-line":{display:"-webkit-box",overflow:"hidden",WebkitLineClamp:3,WebkitBoxOrient:"vertical"}}),xn=e=>{const{componentCls:n,titleMarginTop:r}=e;return{[n]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({color:e.colorText,wordBreak:"break-word",lineHeight:e.lineHeight,[`&${n}-secondary`]:{color:e.colorTextDescription},[`&${n}-success`]:{color:e.colorSuccessText},[`&${n}-warning`]:{color:e.colorWarningText},[`&${n}-danger`]:{color:e.colorErrorText,"a&:active, a&:focus":{color:e.colorErrorTextActive},"a&:hover":{color:e.colorErrorTextHover}},[`&${n}-disabled`]:{color:e.colorTextDisabled,cursor:"not-allowed",userSelect:"none"},"\n        div&,\n        p\n      ":{marginBottom:"1em"}},pn(e)),{[`
      & + h1${n},
      & + h2${n},
      & + h3${n},
      & + h4${n},
      & + h5${n}
      `]:{marginTop:r},"\n      div,\n      ul,\n      li,\n      p,\n      h1,\n      h2,\n      h3,\n      h4,\n      h5":{"\n        + h1,\n        + h2,\n        + h3,\n        + h4,\n        + h5\n        ":{marginTop:r}}}),mn(e)),hn(e)),{[`
        ${n}-expand,
        ${n}-collapse,
        ${n}-edit,
        ${n}-copy
      `]:Object.assign(Object.assign({},Et(e)),{marginInlineStart:e.marginXXS})}),gn(e)),fn(e)),yn()),{"&-rtl":{direction:"rtl"}})}},bn=()=>({titleMarginTop:"1.2em",titleMarginBottom:"0.5em"}),Rt=Nt("Typography",e=>[xn(e)],bn),vn=e=>{const{prefixCls:n,"aria-label":r,className:l,style:c,direction:x,maxLength:j,autoSize:E=!0,value:w,onSave:C,onCancel:h,onEnd:I,component:R,enterIcon:P=a.createElement(dn,null)}=e,N=a.useRef(null),F=a.useRef(!1),L=a.useRef(null),[y,p]=a.useState(w);a.useEffect(()=>{p(w)},[w]),a.useEffect(()=>{var k;if(!((k=N.current)===null||k===void 0)&&k.resizableTextArea){const{textArea:$}=N.current.resizableTextArea;$.focus();const{length:T}=$.value;$.setSelectionRange(T,T)}},[]);const o=({target:k})=>{p(k.value.replace(/[\n\r]/g,""))},b=()=>{F.current=!0},f=()=>{F.current=!1},i=({keyCode:k})=>{F.current||(L.current=k)},d=()=>{C(y.trim())},m=({keyCode:k,ctrlKey:$,altKey:T,metaKey:X,shiftKey:B})=>{L.current!==k||F.current||$||T||X||B||(k===ct.ENTER?(d(),I==null||I()):k===ct.ESC&&h())},z=()=>{d()},[M,Y,J]=Rt(n),W=Ye(n,`${n}-edit-content`,{[`${n}-rtl`]:x==="rtl",[`${n}-${R}`]:!!R},l,Y,J);return M(a.createElement("div",{className:W,style:c},a.createElement(Lt,{ref:N,maxLength:j,value:y,onChange:o,onKeyDown:i,onKeyUp:m,onCompositionStart:b,onCompositionEnd:f,onBlur:z,"aria-label":r,rows:1,autoSize:E}),P!==null?$t(P,{className:`${n}-edit-content-confirm`}):null))};var En=function(){var e=document.getSelection();if(!e.rangeCount)return function(){};for(var n=document.activeElement,r=[],l=0;l<e.rangeCount;l++)r.push(e.getRangeAt(l));switch(n.tagName.toUpperCase()){case"INPUT":case"TEXTAREA":n.blur();break;default:n=null;break}return e.removeAllRanges(),function(){e.type==="Caret"&&e.removeAllRanges(),e.rangeCount||r.forEach(function(c){e.addRange(c)}),n&&n.focus()}},wn=En,gt={"text/plain":"Text","text/html":"Url",default:"Text"},Sn="Copy to clipboard: #{key}, Enter";function Cn(e){var n=(/mac os x/i.test(navigator.userAgent)?"⌘":"Ctrl")+"+C";return e.replace(/#{\s*key\s*}/g,n)}function jn(e,n){var r,l,c,x,j,E,w=!1;n||(n={}),r=n.debug||!1;try{c=wn(),x=document.createRange(),j=document.getSelection(),E=document.createElement("span"),E.textContent=e,E.ariaHidden="true",E.style.all="unset",E.style.position="fixed",E.style.top=0,E.style.clip="rect(0, 0, 0, 0)",E.style.whiteSpace="pre",E.style.webkitUserSelect="text",E.style.MozUserSelect="text",E.style.msUserSelect="text",E.style.userSelect="text",E.addEventListener("copy",function(h){if(h.stopPropagation(),n.format)if(h.preventDefault(),typeof h.clipboardData>"u"){r&&console.warn("unable to use e.clipboardData"),r&&console.warn("trying IE specific stuff"),window.clipboardData.clearData();var I=gt[n.format]||gt.default;window.clipboardData.setData(I,e)}else h.clipboardData.clearData(),h.clipboardData.setData(n.format,e);n.onCopy&&(h.preventDefault(),n.onCopy(h.clipboardData))}),document.body.appendChild(E),x.selectNodeContents(E),j.addRange(x);var C=document.execCommand("copy");if(!C)throw new Error("copy command was unsuccessful");w=!0}catch(h){r&&console.error("unable to copy using execCommand: ",h),r&&console.warn("trying IE specific stuff");try{window.clipboardData.setData(n.format||"text",e),n.onCopy&&n.onCopy(window.clipboardData),w=!0}catch(I){r&&console.error("unable to copy using clipboardData: ",I),r&&console.error("falling back to prompt"),l=Cn("message"in n?n.message:Sn),window.prompt(l,e)}}finally{j&&(typeof j.removeRange=="function"?j.removeRange(x):j.removeAllRanges()),E&&document.body.removeChild(E),c()}return w}var In=jn;const Tn=Pt(In);var Dn=function(e,n,r,l){function c(x){return x instanceof r?x:new r(function(j){j(x)})}return new(r||(r=Promise))(function(x,j){function E(h){try{C(l.next(h))}catch(I){j(I)}}function w(h){try{C(l.throw(h))}catch(I){j(I)}}function C(h){h.done?x(h.value):c(h.value).then(E,w)}C((l=l.apply(e,n||[])).next())})};const Rn=({copyConfig:e,children:n})=>{const[r,l]=a.useState(!1),[c,x]=a.useState(!1),j=a.useRef(null),E=()=>{j.current&&clearTimeout(j.current)},w={};e.format&&(w.format=e.format),a.useEffect(()=>E,[]);const C=Yt(h=>Dn(void 0,void 0,void 0,function*(){var I;h==null||h.preventDefault(),h==null||h.stopPropagation(),x(!0);try{const R=typeof e.text=="function"?yield e.text():e.text;Tn(R||en(n,!0).join("")||"",w),x(!1),l(!0),E(),j.current=setTimeout(()=>{l(!1)},3e3),(I=e.onCopy)===null||I===void 0||I.call(e,h)}catch(R){throw x(!1),R}}));return{copied:r,copyLoading:c,onClick:C}};function Qe(e,n){return a.useMemo(()=>{const r=!!e;return[r,Object.assign(Object.assign({},n),r&&typeof e=="object"?e:null)]},[e])}const An=e=>{const n=a.useRef(void 0);return a.useEffect(()=>{n.current=e}),n.current},Mn=(e,n,r)=>a.useMemo(()=>e===!0?{title:n??r}:a.isValidElement(e)?{title:e}:typeof e=="object"?Object.assign({title:n??r},e):{title:e},[e,n,r]);var kn=function(e,n){var r={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&n.indexOf(l)<0&&(r[l]=e[l]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var c=0,l=Object.getOwnPropertySymbols(e);c<l.length;c++)n.indexOf(l[c])<0&&Object.prototype.propertyIsEnumerable.call(e,l[c])&&(r[l[c]]=e[l[c]]);return r};const At=a.forwardRef((e,n)=>{const{prefixCls:r,component:l="article",className:c,rootClassName:x,setContentRef:j,children:E,direction:w,style:C}=e,h=kn(e,["prefixCls","component","className","rootClassName","setContentRef","children","direction","style"]),{getPrefixCls:I,direction:R,className:P,style:N}=_t("typography"),F=w??R,L=j?wt(n,j):n,y=I("typography",r),[p,o,b]=Rt(y),f=Ye(y,P,{[`${y}-rtl`]:F==="rtl"},c,x,o,b),i=Object.assign(Object.assign({},N),C);return p(a.createElement(l,Object.assign({className:f,style:i,ref:L},h),E))});function ft(e){return e===!1?[!1,!1]:Array.isArray(e)?e:[e]}function Ze(e,n,r){return e===!0||e===void 0?n:e||r&&n}function On(e){const n=document.createElement("em");e.appendChild(n);const r=e.getBoundingClientRect(),l=n.getBoundingClientRect();return e.removeChild(n),r.left>l.left||l.right>r.right||r.top>l.top||l.bottom>r.bottom}const ot=e=>["string","number"].includes(typeof e),Nn=({prefixCls:e,copied:n,locale:r,iconOnly:l,tooltips:c,icon:x,tabIndex:j,onCopy:E,loading:w})=>{const C=ft(c),h=ft(x),{copied:I,copy:R}=r??{},P=n?I:R,N=Ze(C[n?1:0],P),F=typeof N=="string"?N:P;return a.createElement(it,{title:N},a.createElement("button",{type:"button",className:Ye(`${e}-copy`,{[`${e}-copy-success`]:n,[`${e}-copy-icon-only`]:l}),onClick:E,"aria-label":F,tabIndex:j},n?Ze(h[1],a.createElement(Kt,null),!0):Ze(h[0],w?a.createElement(Ft,null):a.createElement(ln,null),!0)))},Le=a.forwardRef(({style:e,children:n},r)=>{const l=a.useRef(null);return a.useImperativeHandle(r,()=>({isExceed:()=>{const c=l.current;return c.scrollHeight>c.clientHeight},getHeight:()=>l.current.clientHeight})),a.createElement("span",{"aria-hidden":!0,ref:l,style:Object.assign({position:"fixed",display:"block",left:0,top:0,pointerEvents:"none",backgroundColor:"rgba(255, 0, 0, 0.65)"},e)},n)}),Ln=e=>e.reduce((n,r)=>n+(ot(r)?String(r).length:1),0);function yt(e,n){let r=0;const l=[];for(let c=0;c<e.length;c+=1){if(r===n)return l;const x=e[c],E=ot(x)?String(x).length:1,w=r+E;if(w>n){const C=n-r;return l.push(String(x).slice(0,C)),l}l.push(x),r=w}return e}const et=0,tt=1,nt=2,lt=3,xt=4,$e={display:"-webkit-box",overflow:"hidden",WebkitBoxOrient:"vertical"};function $n(e){const{enableMeasure:n,width:r,text:l,children:c,rows:x,expanded:j,miscDeps:E,onEllipsis:w}=e,C=a.useMemo(()=>St(l),[l]),h=a.useMemo(()=>Ln(C),[l]),I=a.useMemo(()=>c(C,!1),[l]),[R,P]=a.useState(null),N=a.useRef(null),F=a.useRef(null),L=a.useRef(null),y=a.useRef(null),p=a.useRef(null),[o,b]=a.useState(!1),[f,i]=a.useState(et),[d,m]=a.useState(0),[z,M]=a.useState(null);ve(()=>{i(n&&r&&h?tt:et)},[r,l,x,n,C]),ve(()=>{var k,$,T,X;if(f===tt){i(nt);const B=F.current&&getComputedStyle(F.current).whiteSpace;M(B)}else if(f===nt){const B=!!(!((k=L.current)===null||k===void 0)&&k.isExceed());i(B?lt:xt),P(B?[0,h]:null),b(B);const Q=(($=L.current)===null||$===void 0?void 0:$.getHeight())||0,Ee=x===1?0:((T=y.current)===null||T===void 0?void 0:T.getHeight())||0,he=((X=p.current)===null||X===void 0?void 0:X.getHeight())||0,me=Math.max(Q,Ee+he);m(me+1),w(B)}},[f]);const Y=R?Math.ceil((R[0]+R[1])/2):0;ve(()=>{var k;const[$,T]=R||[0,0];if($!==T){const B=(((k=N.current)===null||k===void 0?void 0:k.getHeight())||0)>d;let Q=Y;T-$===1&&(Q=B?$:T),P(B?[$,Q]:[Q,T])}},[R,Y]);const J=a.useMemo(()=>{if(!n)return c(C,!1);if(f!==lt||!R||R[0]!==R[1]){const k=c(C,!1);return[xt,et].includes(f)?k:a.createElement("span",{style:Object.assign(Object.assign({},$e),{WebkitLineClamp:x})},k)}return c(j?C:yt(C,R[0]),o)},[j,f,R,C].concat(Ct(E))),W={width:r,margin:0,padding:0,whiteSpace:z==="nowrap"?"normal":"inherit"};return a.createElement(a.Fragment,null,J,f===nt&&a.createElement(a.Fragment,null,a.createElement(Le,{style:Object.assign(Object.assign(Object.assign({},W),$e),{WebkitLineClamp:x}),ref:L},I),a.createElement(Le,{style:Object.assign(Object.assign(Object.assign({},W),$e),{WebkitLineClamp:x-1}),ref:y},I),a.createElement(Le,{style:Object.assign(Object.assign(Object.assign({},W),$e),{WebkitLineClamp:1}),ref:p},c([],!0))),f===lt&&R&&R[0]!==R[1]&&a.createElement(Le,{style:Object.assign(Object.assign({},W),{top:400}),ref:N},c(yt(C,Y),!0)),f===tt&&a.createElement("span",{style:{whiteSpace:"inherit"},ref:F}))}const Pn=({enableEllipsis:e,isEllipsis:n,children:r,tooltipProps:l})=>!(l!=null&&l.title)||!e?r:a.createElement(it,Object.assign({open:n?void 0:!1},l),r);var Yn=function(e,n){var r={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&n.indexOf(l)<0&&(r[l]=e[l]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var c=0,l=Object.getOwnPropertySymbols(e);c<l.length;c++)n.indexOf(l[c])<0&&Object.prototype.propertyIsEnumerable.call(e,l[c])&&(r[l[c]]=e[l[c]]);return r};function _n({mark:e,code:n,underline:r,delete:l,strong:c,keyboard:x,italic:j},E){let w=E;function C(h,I){I&&(w=a.createElement(h,{},w))}return C("strong",c),C("u",r),C("del",l),C("code",n),C("mark",e),C("kbd",x),C("i",j),w}const Fn="...",bt=["delete","mark","code","underline","strong","keyboard","italic"],_e=a.forwardRef((e,n)=>{var r;const{prefixCls:l,className:c,style:x,type:j,disabled:E,children:w,ellipsis:C,editable:h,copyable:I,component:R,title:P}=e,N=Yn(e,["prefixCls","className","style","type","disabled","children","ellipsis","editable","copyable","component","title"]),{getPrefixCls:F,direction:L}=a.useContext(Ut),[y]=zt("Text"),p=a.useRef(null),o=a.useRef(null),b=F("typography",l),f=jt(N,bt),[i,d]=Qe(h),[m,z]=dt(!1,{value:d.editing}),{triggerType:M=["icon"]}=d,Y=v=>{var D;v&&((D=d.onStart)===null||D===void 0||D.call(d)),z(v)},J=An(m);ve(()=>{var v;!m&&J&&((v=o.current)===null||v===void 0||v.focus())},[m]);const W=v=>{v==null||v.preventDefault(),Y(!0)},k=v=>{var D;(D=d.onChange)===null||D===void 0||D.call(d,v),Y(!1)},$=()=>{var v;(v=d.onCancel)===null||v===void 0||v.call(d),Y(!1)},[T,X]=Qe(I),{copied:B,copyLoading:Q,onClick:Ee}=Rn({copyConfig:X,children:w}),[he,me]=a.useState(!1),[ie,Fe]=a.useState(!1),[Te,Ue]=a.useState(!1),[we,ze]=a.useState(!1),[He,de]=a.useState(!0),[ae,H]=Qe(C,{expandable:!1,symbol:v=>v?y==null?void 0:y.collapse:y==null?void 0:y.expand}),[te,Be]=dt(H.defaultExpanded||!1,{value:H.expanded}),V=ae&&(!te||H.expandable==="collapsible"),{rows:oe=1}=H,ye=a.useMemo(()=>V&&(H.suffix!==void 0||H.onEllipsis||H.expandable||i||T),[V,H,i,T]);ve(()=>{ae&&!ye&&(me(ht("webkitLineClamp")),Fe(ht("textOverflow")))},[ye,ae]);const[q,ge]=a.useState(V),De=a.useMemo(()=>ye?!1:oe===1?ie:he,[ye,ie,he]);ve(()=>{ge(De&&V)},[De,V]);const Re=V&&(q?we:Te),re=V&&oe===1&&q,Se=V&&oe>1&&q,Ve=(v,D)=>{var u;Be(D.expanded),(u=H.onExpand)===null||u===void 0||u.call(H,v,D)},[Ae,Me]=a.useState(0),We=({offsetWidth:v})=>{Me(v)},Ge=v=>{var D;Ue(v),Te!==v&&((D=H.onEllipsis)===null||D===void 0||D.call(H,v))};a.useEffect(()=>{const v=p.current;if(ae&&q&&v){const D=On(v);we!==D&&ze(D)}},[ae,q,w,Se,He,Ae]),a.useEffect(()=>{const v=p.current;if(typeof IntersectionObserver>"u"||!v||!q||!V)return;const D=new IntersectionObserver(()=>{de(!!v.offsetParent)});return D.observe(v),()=>{D.disconnect()}},[q,V]);const Ce=Mn(H.tooltip,d.text,w),ue=a.useMemo(()=>{if(!(!ae||q))return[d.text,w,P,Ce.title].find(ot)},[ae,q,P,Ce.title,Re]);if(m)return a.createElement(vn,{value:(r=d.text)!==null&&r!==void 0?r:typeof w=="string"?w:"",onSave:k,onCancel:$,onEnd:d.onEnd,prefixCls:b,className:c,style:x,direction:L,component:R,maxLength:d.maxLength,autoSize:d.autoSize,enterIcon:d.enterIcon});const ke=()=>{const{expandable:v,symbol:D}=H;return v?a.createElement("button",{type:"button",key:"expand",className:`${b}-${te?"collapse":"expand"}`,onClick:u=>Ve(u,{expanded:!te}),"aria-label":te?y.collapse:y==null?void 0:y.expand},typeof D=="function"?D(te):D):null},qe=()=>{if(!i)return;const{icon:v,tooltip:D,tabIndex:u}=d,S=St(D)[0]||(y==null?void 0:y.edit),s=typeof S=="string"?S:"";return M.includes("icon")?a.createElement(it,{key:"edit",title:D===!1?"":S},a.createElement("button",{type:"button",ref:o,className:`${b}-edit`,onClick:W,"aria-label":s,tabIndex:u},v||a.createElement(on,{role:"button"}))):null},Ke=()=>T?a.createElement(Nn,Object.assign({key:"copy"},X,{prefixCls:b,copied:B,locale:y,onCopy:Ee,loading:Q,iconOnly:w==null})):null,Je=v=>[v&&ke(),qe(),Ke()],Xe=v=>[v&&!te&&a.createElement("span",{"aria-hidden":!0,key:"ellipsis"},Fn),H.suffix,Je(v)];return a.createElement(Ht,{onResize:We,disabled:!V},v=>a.createElement(Pn,{tooltipProps:Ce,enableEllipsis:V,isEllipsis:Re},a.createElement(At,Object.assign({className:Ye({[`${b}-${j}`]:j,[`${b}-disabled`]:E,[`${b}-ellipsis`]:ae,[`${b}-ellipsis-single-line`]:re,[`${b}-ellipsis-multiple-line`]:Se},c),prefixCls:l,style:Object.assign(Object.assign({},x),{WebkitLineClamp:Se?oe:void 0}),component:R,ref:wt(v,p,n),direction:L,onClick:M.includes("text")?W:void 0,"aria-label":ue==null?void 0:ue.toString(),title:P},f),a.createElement($n,{enableMeasure:V&&!q,text:w,rows:oe,width:Ae,onEllipsis:Ge,expanded:te,miscDeps:[B,te,Q,i,T,y].concat(Ct(bt.map(D=>e[D])))},(D,u)=>_n(e,a.createElement(a.Fragment,null,D.length>0&&u&&!te&&ue?a.createElement("span",{key:"show-content","aria-hidden":!0},D):D,Xe(u)))))))});var Un=function(e,n){var r={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&n.indexOf(l)<0&&(r[l]=e[l]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var c=0,l=Object.getOwnPropertySymbols(e);c<l.length;c++)n.indexOf(l[c])<0&&Object.prototype.propertyIsEnumerable.call(e,l[c])&&(r[l[c]]=e[l[c]]);return r};const zn=a.forwardRef((e,n)=>{var{ellipsis:r,rel:l}=e,c=Un(e,["ellipsis","rel"]);const x=Object.assign(Object.assign({},c),{rel:l===void 0&&c.target==="_blank"?"noopener noreferrer":l});return delete x.navigate,a.createElement(_e,Object.assign({},x,{ref:n,ellipsis:!!r,component:"a"}))}),Hn=a.forwardRef((e,n)=>a.createElement(_e,Object.assign({ref:n},e,{component:"div"})));var Bn=function(e,n){var r={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&n.indexOf(l)<0&&(r[l]=e[l]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var c=0,l=Object.getOwnPropertySymbols(e);c<l.length;c++)n.indexOf(l[c])<0&&Object.prototype.propertyIsEnumerable.call(e,l[c])&&(r[l[c]]=e[l[c]]);return r};const Vn=(e,n)=>{var{ellipsis:r}=e,l=Bn(e,["ellipsis"]);const c=a.useMemo(()=>r&&typeof r=="object"?jt(r,["expandable","rows"]):r,[r]);return a.createElement(_e,Object.assign({ref:n},l,{ellipsis:c,component:"span"}))},Wn=a.forwardRef(Vn);var Gn=function(e,n){var r={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&n.indexOf(l)<0&&(r[l]=e[l]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var c=0,l=Object.getOwnPropertySymbols(e);c<l.length;c++)n.indexOf(l[c])<0&&Object.prototype.propertyIsEnumerable.call(e,l[c])&&(r[l[c]]=e[l[c]]);return r};const qn=[1,2,3,4,5],Kn=a.forwardRef((e,n)=>{const{level:r=1}=e,l=Gn(e,["level"]),c=qn.includes(r)?`h${r}`:"h1";return a.createElement(_e,Object.assign({ref:n},l,{component:c}))}),Ie=At;Ie.Text=Wn;Ie.Link=zn;Ie.Title=Kn;Ie.Paragraph=Hn;const Jn="_add_yohve_1",Xn={add:Jn},Qn=e=>{const[n]=A.useForm(),r=a.useRef([]),[l,c]=a.useState([]),[x,j]=a.useState(e.columns),{runAsync:E}=se(ce.getEmployeeByEmpId,{manual:!0}),w=Tt(async y=>{var i;const{record:p,value:o}=y;console.log(p,o,"record-xxx");const[b,f]=await E({empId:o});if(!b)if(f.STATUS==="0000"){const{DATA:d}=f;console.log(d,"userInfo");const m=d==null?void 0:d.empName,z=d==null?void 0:d.gender,M=d==null?void 0:d.nation,Y=d==null?void 0:d.birthDate,J=d==null?void 0:d.ageYear,W=d==null?void 0:d.ageMonth,k=d==null?void 0:d.department;r.current=(i=r.current)==null?void 0:i.map($=>{const T={...$};return($==null?void 0:$.key)===(p==null?void 0:p.key)&&(T.employeeName=m,T.gender=z,T.ethnicity=M,T.birthDate=Y,T.fulltimeEducationYear=J,T.fulltimeEducationMonth=W,T.department=k),T}),c([...r.current]),n.setFieldsValue({[`employeeName${p==null?void 0:p.key}`]:m,[`gender${p==null?void 0:p.key}`]:z,[`birthDate${p==null?void 0:p.key}`]:Y,[`ethnicity${p==null?void 0:p.key}`]:M,[`fulltimeEducationYear${p==null?void 0:p.key}`]:J,[`fulltimeEducationMonth${p==null?void 0:p.key}`]:W,[`department${p==null?void 0:p.key}`]:k})}else _.error(f==null?void 0:f.MESSAGE)},500);a.useEffect(()=>{C()},[]);const C=()=>{j([...h(e.columns),{title:"操作",dataIndex:"action",width:80,align:"center",fixed:"right",render:(y,p)=>t.jsx("div",{className:"action",children:t.jsx(ee,{type:"link",onClick:()=>R(p),children:"删除"})})}])},h=y=>{var p;return console.log(y,"columns"),(p=y==null?void 0:y.filter(o=>(o==null?void 0:o.dataIndex)!=="action"&&(o==null?void 0:o.dataIndex)!=="exitSituation"))==null?void 0:p.map(o=>{var b;return((b=o==null?void 0:o.children)==null?void 0:b.length)>0?{...o,width:o!=null&&o.width&&o.width>100?o==null?void 0:o.width:100,children:h(o==null?void 0:o.children)}:{...o,width:o!=null&&o.width&&o.width>120?o==null?void 0:o.width:120,render:(f,i,d)=>t.jsx(A.Item,{name:o.dataIndex+(i==null?void 0:i.key),rules:[{required:(o==null?void 0:o.dataIndex)==="employeeId"&&(i==null?void 0:i.employeeId),message:`请输入${o.title}`}],children:(o==null?void 0:o.actionType)==="cascader"?t.jsx(qt,{allowClear:!0,changeOnSelect:!0,expandTrigger:"hover",displayRender:m=>m[m.length-1],options:(e==null?void 0:e.cascaderOption)||[],fieldNames:{value:"orgId",label:"orgName",children:"children"},placeholder:["rewardsOrgName"].includes(o==null?void 0:o.dataIndex)&&(i==null?void 0:i.rewardsType)==="staff"?"":`请选择${o==null?void 0:o.title}`,showSearch:{filter:L},onSearch:m=>console.log(m),value:f==null?void 0:f.map(m=>m==null?void 0:m.orgId),onChange:(m,z)=>I(i,z,o)}):(o==null?void 0:o.actionType)==="select"?t.jsx(t.Fragment,{children:t.jsx(le,{placeholder:`请选择${o==null?void 0:o.title}`,allowClear:!0,value:f,onChange:m=>I(i,m,o),children:(e[o==null?void 0:o.actionOptionName]||[]).map(m=>t.jsx(le.Option,{value:m==null?void 0:m.enumName,children:m==null?void 0:m.enumName},m==null?void 0:m.enumId))})}):(o==null?void 0:o.actionType)==="datePicker"?t.jsx(pe,{picker:"date",value:f,allowClear:!0,format:"YYYY-MM-DD",placeholder:`请选择${o.title}`,onChange:m=>I(i,m,o),style:{width:"100%"}}):(o==null?void 0:o.dataIndex)==="employeeId"?t.jsx(K,{placeholder:`请输入${o.title}`,value:f,allowClear:!0,onChange:m=>I(i,m.target.value,o,`employeeName${i==null?void 0:i.key}`)}):t.jsx(K,{placeholder:["employeeName","birthDate","fulltimeEducationYear","fulltimeEducationMonth","ethnicity","department","gender","personnelChange"].includes(o==null?void 0:o.dataIndex)?"":`请输入${o.title}`,value:(o==null?void 0:o.dataIndex)==="personnelChange"?"新增":f,allowClear:!0,disabled:["employeeName","birthDate","fulltimeEducationYear","fulltimeEducationMonth","ethnicity","department","gender","personnelChange"].includes(o==null?void 0:o.dataIndex),onChange:m=>I(i,m.target.value,o)})},f+d)}})},I=(y,p,o,b)=>{var f;r.current=(f=r.current)==null?void 0:f.map(i=>{const d={...i};return(i==null?void 0:i.key)===(y==null?void 0:y.key)&&(d[o==null?void 0:o.dataIndex]=p),d}),(o==null?void 0:o.dataIndex)==="employeeId"&&w({record:y,value:p,column:o,formName:b})},R=y=>{var p;r.current=(p=r.current)==null?void 0:p.filter(o=>(o==null?void 0:o.key)!==(y==null?void 0:y.key)),c(r.current)},P=["managerLevel3StartDate","currentLevelDate","currentPositionDate","unitManagerStartDate","unitManagerPositionDate"],N=()=>{if(r.current.some(b=>{const f=!!b.employeeId,i=!!b.employeeName;return!(f&&i)})){_.error("请完整填写所有员工信息（员工编号和姓名）");return}if(r.current.length===0){_.error("请至少添加一条员工信息");return}const p=x.filter(b=>b.children&&b.children.length>0?!0:!x.some(i=>i.children&&i.children.some(d=>d.dataIndex===b.dataIndex))).reduce((b,f)=>f.children&&f.children.length>0?[...b,...f.children.map(i=>i.dataIndex)]:[...b,f.dataIndex],[]).filter(b=>b&&b!=="action"),o=r.current.map(b=>{const f={...b};return p.forEach(i=>{i in f||(f[i]="")}),P.forEach(i=>{if(f[i]){const d=fe(f[i]);d.isValid()&&(f[i]=d.format("YYYY-MM-DD"))}}),f});console.log("processedData",o),e==null||e.submitData(o)},F=()=>{console.log(r.current,"tableDataRef.current1");const y=new Date().getTime(),p={key:y,personnelChange:"新增"};r.current=[...r.current,p],console.log(r.current,"tableDataRef.current2"),c(r.current),n.setFieldsValue({[`personnelChange${y}`]:"新增"})},L=(y,p)=>p.some(o=>o.orgName.toLowerCase().indexOf(y.trim().toLowerCase())>-1);return t.jsx("div",{className:Xn.add,children:t.jsxs(A,{name:"dynamic_form_nest_item",form:n,onFinish:N,style:{width:"100%",marginTop:16,maxHeight:"500px"},autoComplete:"off",children:[t.jsx(Jt,{style:{marginBottom:20},className:"edit-table",columns:x,dataSource:l,bordered:!0,scroll:{y:"20rem"},pagination:!1}),t.jsx(A.Item,{style:{width:"100%",display:"flex",justifyContent:"right"},children:t.jsx(ee,{onClick:()=>F(),danger:!0,block:!0,icon:t.jsx(It,{}),style:{width:150},children:"新增一条数据"})}),t.jsx(A.Item,{style:{textAlign:"center",width:"100%",background:"#fff"},children:t.jsx(ee,{type:"primary",htmlType:"submit",style:{},children:"提交"})})]})})},{Option:be}=le,{Title:Pe}=Ie,vt=["managerLevel3StartDate","currentLevelDate","currentPositionDate","unitManagerStartDate","unitManagerPositionDate","exitDate"],Zn=({open:e,onCancel:n,onOk:r,initialValues:l,unitList:c,personnelChangeList:x,managerLevelList:j,unitManagerList:E,talentList:w,exitReasonList:C})=>{const[h]=A.useForm(),[I,R]=a.useState(!1),[P,N]=a.useState(""),[F,L]=a.useState(""),{runAsync:y}=se(ce.getEmployeeByEmpId,{manual:!0});a.useEffect(()=>{if(e){const i={...l};console.log(i,"formattedValues"),vt.forEach(m=>{i[m]&&(i[m]=fe(i[m]))}),["isManagerLevel3","isUnitManager","talentLevel"].forEach(m=>{i[m]===""&&(i[m]=void 0)}),i.backList==="其他"?(N("其他"),L(i.reason||""),i.exitReason="其他"):(N(i.backList||""),L(""),i.exitReason=i.backList),h.setFieldsValue(i),R(i.personnelChange==="退出")}},[e,l]);const p=async i=>{const d=i.target.value;if(!d)return;const[m,z]=await y({empId:d});if(!m&&z.STATUS==="0000"){const M=z.DATA;h.setFieldsValue({employeeName:M==null?void 0:M.empName,department:M==null?void 0:M.department,gender:M==null?void 0:M.gender,ethnicity:M==null?void 0:M.nation,birthDate:M==null?void 0:M.birthDate})}},o=i=>{if(R(i==="退出"),i!=="退出")h.setFieldsValue({exitReason:void 0,exitDate:void 0,postExitPosition:void 0,customExitReason:void 0}),N(""),L("");else{const{exitDate:d,postExitPosition:m,reason:z,backList:M}=l;console.log(d,m,z,M),M==="其他"?(N("其他"),L(z||""),h.setFieldsValue({exitReason:"其他",customExitReason:z,exitDate:d?fe(d):"",postExitPosition:m})):(N(M||""),L(""),h.setFieldsValue({exitReason:M,exitDate:d?fe(d):"",postExitPosition:m}))}},b=i=>{N(i),i!=="其他"&&(L(""),h.setFieldsValue({reason:void 0}))},f=()=>{h.validateFields().then(i=>{if(vt.forEach(d=>{i[d]&&(i[d]=fe(i[d]).format("YYYY-MM-DD"))}),I&&P==="其他"&&!F){_.error("请填写自定义退出原因");return}r({...i,reason:P==="其他"?F:"",backList:P})})};return t.jsx(Dt,{open:e,title:t.jsx("div",{style:{textAlign:"center",color:"#E60027"},children:"-修改数据-"}),centered:!0,onCancel:n,onOk:f,width:"70%",destroyOnClose:!0,maskClosable:!1,footer:null,children:t.jsxs(A,{form:h,layout:"horizontal",initialValues:l,colon:!1,children:[t.jsxs(ne,{gutter:24,children:[t.jsx(O,{span:8,children:t.jsx(A.Item,{label:"单位：",name:"unit",rules:[{required:!0,message:"请选择单位"}],labelCol:{flex:"120px"},wrapperCol:{flex:1},labelAlign:"right",children:t.jsx(le,{placeholder:"请选择单位",allowClear:!0,children:c==null?void 0:c.map(i=>t.jsx(be,{value:i.enumName,children:i.enumName},i.enumId))})})}),t.jsx(O,{span:8,children:t.jsx(A.Item,{label:"人员变化：",name:"personnelChange",rules:[{required:!0,message:"请选择人员变化"}],labelCol:{flex:"120px"},wrapperCol:{flex:1},labelAlign:"right",children:t.jsx(le,{placeholder:"请选择",onChange:o,allowClear:!0,children:x.map(i=>t.jsx(be,{value:i.enumName,children:i.enumName},i.enumId))})})}),t.jsx(O,{span:8,children:t.jsx(A.Item,{label:"基层责任单元：",name:"basicResponsibilityUnit",labelCol:{flex:"120px"},wrapperCol:{flex:1},labelAlign:"right",children:t.jsx(K,{placeholder:"请填写基层责任单元"})})})]}),t.jsxs(ne,{gutter:24,children:[t.jsx(O,{span:8,children:t.jsx(A.Item,{label:"员工编号：",name:"employeeId",rules:[{required:!0,message:"请输入员工编号"}],labelCol:{flex:"120px"},wrapperCol:{flex:1},labelAlign:"right",children:t.jsx(K,{placeholder:"请输入员工编号",onBlur:p})})}),t.jsx(O,{span:8,children:t.jsx(A.Item,{label:"员工姓名：",name:"employeeName",labelCol:{flex:"120px"},wrapperCol:{flex:1},labelAlign:"right",children:t.jsx(K,{disabled:!0})})}),t.jsx(O,{span:8,children:t.jsx(A.Item,{label:"部门：",name:"department",labelCol:{flex:"120px"},wrapperCol:{flex:1},labelAlign:"right",children:t.jsx(K,{disabled:!0})})})]}),t.jsxs(ne,{gutter:24,children:[t.jsx(O,{span:8,children:t.jsx(A.Item,{label:"党内职务：",name:"partyPosition",labelCol:{flex:"120px"},wrapperCol:{flex:1},labelAlign:"right",children:t.jsx(K,{placeholder:"请输入党内职务"})})}),t.jsx(O,{span:8,children:t.jsx(A.Item,{label:"岗位名称：",name:"position",labelCol:{flex:"120px"},wrapperCol:{flex:1},labelAlign:"right",children:t.jsx(K,{placeholder:"请输入岗位名称"})})}),t.jsx(O,{span:8,children:t.jsx(A.Item,{label:"岗位级别：",name:"partyPosition",labelCol:{flex:"120px"},wrapperCol:{flex:1},labelAlign:"right",children:t.jsx(K,{placeholder:"请输入岗位级别"})})})]}),I&&t.jsxs("div",{children:[t.jsx(Pe,{level:5,style:{fontWeight:"bold",margin:"0 0 8px 10px"},children:"退出相关"}),t.jsxs(ne,{gutter:24,children:[t.jsx(O,{span:12,children:t.jsx(A.Item,{label:"退出时间：",name:"exitDate",rules:[{required:!0,message:"请选择退出时间"}],labelCol:{flex:"120px"},wrapperCol:{flex:1},labelAlign:"right",children:t.jsx(pe,{style:{width:"100%"},format:"YYYY-MM-DD",placeholder:"请选择退出时间"})})}),t.jsx(O,{span:12,children:t.jsx(A.Item,{label:"退出后岗位：",name:"postExitPosition",rules:[{required:!0,message:"请填写退出后岗位"}],labelCol:{flex:"120px"},wrapperCol:{flex:1},labelAlign:"right",children:t.jsx(K,{placeholder:"请填写退出后岗位"})})})]}),t.jsx(ne,{gutter:24,children:t.jsx(O,{span:24,children:t.jsx(A.Item,{label:"退出原因：",name:"exitReason",rules:[{required:!0,message:"请选择退出原因"}],labelCol:{flex:"120px"},wrapperCol:{flex:1},labelAlign:"right",children:t.jsx(le,{placeholder:"请选择退出原因",onChange:b,allowClear:!0,children:C.map(i=>t.jsx(be,{value:i.enumName,children:i.enumName},i.enumId))})})})}),P==="其他"&&t.jsx(ne,{gutter:24,children:t.jsx(O,{span:24,children:t.jsx(A.Item,{label:"自定义退出原因：",name:"reason",rules:[{required:!0,message:"请填写自定义退出原因"}],labelCol:{flex:"120px"},wrapperCol:{flex:1},labelAlign:"right",children:t.jsx(K,{placeholder:"请输入自定义退出原因",value:F,onChange:i=>L(i.target.value)})})})})]}),t.jsx(O,{span:5,children:t.jsx(Pe,{level:5,style:{fontWeight:"bold",margin:"0 0 8px 10px"},children:"人员类别"})}),t.jsxs(ne,{gutter:24,children:[t.jsx(O,{span:8,children:t.jsx(A.Item,{label:"三级经理：",name:"isManagerLevel3",labelCol:{flex:"120px"},wrapperCol:{flex:1},labelAlign:"right",children:t.jsx(le,{allowClear:!0,placeholder:"请选择三级经理",value:h.getFieldValue("isManagerLevel3")||void 0,children:j.map(i=>t.jsx(be,{value:i.enumName,children:i.enumName},i.enumId))})})}),t.jsx(O,{span:8,children:t.jsx(A.Item,{label:"基层责任单元经理：",name:"isUnitManager",labelCol:{flex:"120px"},wrapperCol:{flex:1},labelAlign:"right",children:t.jsx(le,{allowClear:!0,placeholder:"请选择基层责任单元经理",value:h.getFieldValue("isUnitManager")||void 0,children:E.map(i=>t.jsx(be,{value:i.enumName,children:i.enumName},i.enumId))})})}),t.jsx(O,{span:8,children:t.jsx(A.Item,{label:"人才：",name:"talentLevel",labelCol:{flex:"120px"},wrapperCol:{flex:1},labelAlign:"right",children:t.jsx(le,{allowClear:!0,placeholder:"请选择人才",value:h.getFieldValue("talentLevel")||void 0,children:w.map(i=>t.jsx(be,{value:i.enumName,children:i.enumName},i.enumId))})})})]}),t.jsx(O,{span:5,children:t.jsx(Pe,{level:5,style:{fontWeight:"bold",margin:"0 0 8px 10px"},children:"三级经理任职信息"})}),t.jsxs(ne,{gutter:24,children:[t.jsx(O,{span:8,children:t.jsx(A.Item,{label:"任期起始时间：",name:"managerLevel3StartDate",labelCol:{flex:"120px"},wrapperCol:{flex:1},labelAlign:"right",children:t.jsx(pe,{style:{width:"100%"},format:"YYYY-MM-DD",placeholder:"请选择任期起始时间"})})}),t.jsx(O,{span:8,children:t.jsx(A.Item,{label:"任现级别时间：",name:"currentLevelDate",labelCol:{flex:"120px"},wrapperCol:{flex:1},labelAlign:"right",children:t.jsx(pe,{style:{width:"100%"},format:"YYYY-MM-DD",placeholder:"请选择任现级别时间"})})}),t.jsx(O,{span:8,children:t.jsx(A.Item,{label:"任现岗时间：",name:"currentPositionDate",labelCol:{flex:"120px"},wrapperCol:{flex:1},labelAlign:"right",children:t.jsx(pe,{style:{width:"100%"},format:"YYYY-MM-DD",placeholder:"请选择任现岗时间"})})})]}),t.jsx(O,{span:5,children:t.jsx(Pe,{level:5,style:{fontWeight:"bold",margin:"0 0 8px 10px"},children:"基层责任单元经理任职信息"})}),t.jsxs(ne,{gutter:24,children:[t.jsx(O,{span:8,children:t.jsx(A.Item,{label:"任期起始时间：",name:"unitManagerStartDate",labelCol:{flex:"120px"},wrapperCol:{flex:1},labelAlign:"right",children:t.jsx(pe,{style:{width:"100%"},format:"YYYY-MM-DD",placeholder:"请选择任期起始时间"})})}),t.jsx(O,{span:8,children:t.jsx(A.Item,{label:"任现岗时间：",name:"unitManagerPositionDate",labelCol:{flex:"120px"},wrapperCol:{flex:1},labelAlign:"right",children:t.jsx(pe,{style:{width:"100%"},format:"YYYY-MM-DD",placeholder:"请选择任现岗时间"})})})]}),t.jsx(ne,{justify:"center",style:{marginTop:32},children:t.jsx(O,{children:t.jsx(A.Item,{children:t.jsxs("div",{style:{display:"flex",gap:24,justifyContent:"center"},children:[t.jsx(ee,{type:"primary",onClick:f,style:{width:100},children:"确定"}),t.jsx(ee,{onClick:n,style:{width:100},children:"取消"})]})})})})]})})},gl=()=>{var D;const e=a.useRef(null),n=a.useRef(null),r=a.useRef(null),[l]=A.useForm(),[c,x]=a.useState(0),[j,E]=a.useState(!1),[w,C]=a.useState([]),[h,I]=a.useState([]),[R,P]=a.useState([]),[N,F]=a.useState([]),[L,y]=a.useState([]),[p,o]=a.useState([]),[b,f]=a.useState([]),[i,d]=a.useState([]),[m,z]=a.useState([]),[M,Y]=a.useState(!1),[J,W]=a.useState(0),[k,$]=a.useState(!0),[T,X]=a.useState({total:0,pageNum:1,pageSize:50}),[B,Q]=a.useState(!1),[Ee,he]=a.useState(null),{currentUser:me}=Bt(),{runAsync:ie}=se(ce.getEnumType,{manual:!0}),{runAsync:Fe}=se(ce.getEmployeeByEmpId,{manual:!0}),{runAsync:Te}=se(ce.getEmployeeMonthlyList,{manual:!0}),{runAsync:Ue}=se(ce.exportPeopleMonthReport,{manual:!0}),{runAsync:we}=se(ce.deleteEmployeeMonthlyList,{manual:!0}),{runAsync:ze}=se(ce.addEmployeeMonthlyReport,{manual:!0}),{runAsync:He}=se(ce.updateEmployeeMonthlyReport,{manual:!0}),[de,ae]=a.useState([{title:"单位",key:"unit",dataIndex:"unit",actionType:"select",actionOptionName:"unitList",align:"center",fixed:"left",width:180,children:[]},{title:"人员变化",key:"personnelChange",dataIndex:"personnelChange",align:"center",width:130,children:[]},{title:"人员类别",key:"personnelType",dataIndex:"personnelType",align:"center",width:130,children:[{title:"三级经理",key:"isManagerLevel3",dataIndex:"isManagerLevel3",actionType:"select",actionOptionName:"managerLevelList",align:"center",width:130,children:[]},{title:"基层责任单元经理",key:"isUnitManager",dataIndex:"isUnitManager",actionType:"select",actionOptionName:"unitManagerList",align:"center",width:130,children:[]},{title:"人才每月自动获取更新人员名单",key:"talentLevel",dataIndex:"talentLevel",actionType:"select",actionOptionName:"talentList",align:"center",width:130,children:[]}]},{title:"员工编号",key:"employeeId",dataIndex:"employeeId",align:"center",width:130,children:[]},{title:"姓名",key:"employeeName",dataIndex:"employeeName",align:"center",width:130,children:[]},{title:"部门",key:"department",dataIndex:"department",align:"center",width:130,children:[]},{title:"基层责任单元",key:"basicResponsibilityUnit",dataIndex:"basicResponsibilityUnit",align:"center",width:130,children:[]},{title:"岗位名称",key:"position",dataIndex:"position",align:"center",width:130,children:[]},{title:"岗级",key:"positionLevel",dataIndex:"positionLevel",align:"center",width:130,children:[]},{title:"性别",key:"gender",dataIndex:"gender",align:"center",width:130,children:[]},{title:"民族",key:"ethnicity",dataIndex:"ethnicity",align:"center",width:130,children:[]},{title:"出生年月",key:"birthDate",dataIndex:"birthDate",align:"center",width:130,children:[]},{title:"年龄",key:"age",dataIndex:"age",align:"center",children:[{title:"年",key:"fulltimeEducationYear",dataIndex:"fulltimeEducationYear",align:"center",width:100,children:[]},{title:"月",key:"fulltimeEducationMonth",dataIndex:"fulltimeEducationMonth",align:"center",width:100,children:[]}]},{title:"全日制教育",key:"fulltimeEducation",dataIndex:"fulltimeEducation",align:"center",width:130,children:[{title:"学历学位",key:"fulltimeEducationDegree",dataIndex:"fulltimeEducationDegree",align:"center",width:130,children:[]},{title:"毕业院校系及专业",key:"fulltimeEducationSchool",dataIndex:"fulltimeEducationSchool",align:"center",width:130,children:[]}]},{title:"最高学历",key:"highestEducation",dataIndex:"highestEducation",align:"center",width:130,children:[{title:"学历学位",key:"highestEducationDegree",dataIndex:"highestEducationDegree",align:"center",width:130,children:[]},{title:"毕业院校系及专业",key:"highestEducationSchool",dataIndex:"highestEducationSchool",align:"center",width:130,children:[]}]},{title:"入党时间",key:"partyJoinDate",dataIndex:"partyJoinDate",align:"center",width:130,children:[]},{title:"参加工作时间",key:"workStartDate",dataIndex:"workStartDate",align:"center",width:130,children:[]},{title:"党内职务",key:"partyPosition",dataIndex:"partyPosition",align:"center",width:130,children:[]},{title:"职级薪档",key:"salaryGrade",dataIndex:"salaryGrade",align:"center",width:130,children:[]},{title:"电话",key:"phone",dataIndex:"phone",align:"center",width:150,children:[]},{title:"综合考评",key:"comprehensiveAssessment",dataIndex:"comprehensiveAssessment",align:"center",width:130,children:[]},{title:"三级经理任职信息",key:"threeLevelManager",dataIndex:"threeLevelManager",align:"center",width:130,children:[{title:"任三级经理起始时间（含正副职）",key:"managerLevel3StartDate",dataIndex:"managerLevel3StartDate",actionType:"datePicker",align:"center",width:150,children:[]},{title:"任现级别时间",key:"currentLevelDate",dataIndex:"currentLevelDate",actionType:"datePicker",align:"center",width:150,children:[]},{title:"任现岗时间",key:"currentPositionDate",actionType:"datePicker",dataIndex:"currentPositionDate",align:"center",width:150,children:[]}]},{title:"基层责任单元经理任职信息",key:"baseResponsibilityUnitManager",dataIndex:"baseResponsibilityUnitManager",align:"center",width:130,children:[{title:"任基层责任单元经理起始时间",key:"unitManagerStartDate",dataIndex:"unitManagerStartDate",actionType:"datePicker",align:"center",width:150,children:[]},{title:"任现岗时间",key:"unitManagerPositionDate",dataIndex:"unitManagerPositionDate",actionType:"datePicker",align:"center",width:150,children:[]}]},{title:"人才信息",key:"talentInfo",dataIndex:"talentInfo",align:"center",width:130,children:[{title:"专业类别",key:"professionalCategory",dataIndex:"professionalCategory",actionType:"select",actionOptionName:"professionalKindList",align:"center",width:150,children:[]},{title:"人才等级",key:"currentTalentLevel",dataIndex:"currentTalentLevel",actionType:"select",actionOptionName:"talentLevelList",align:"center",width:130,children:[]},{title:"现等级人才时间",key:"currentTalentLevelDate",dataIndex:"currentTalentLevelDate",align:"center",width:150,children:[]}]},{title:"退出情况说明",key:"exitSituation",dataIndex:"exitSituation",align:"center",width:130,children:[{title:"退出时间",key:"exitDate",dataIndex:"exitDate",actionType:"datePicker",align:"center",width:150,children:[]},{title:"退出原因",key:"exitReason",dataIndex:"exitReason",actionType:"select",actionOptionName:"exitReasonList",align:"center",width:150,children:[]},{title:"退出后岗位",key:"postExitPosition",dataIndex:"postExitPosition",align:"center",width:150,children:[]}]},{title:"操作",key:"action",dataIndex:"action",align:"center",width:200,fixed:"right",render:(u,S)=>t.jsxs(ut,{size:"middle",children:[t.jsx(ee,{type:"link",onClick:()=>te(S),children:"编辑"}),t.jsx(mt,{title:"删除",description:"确定删除此条数据吗？",placement:"topLeft",onConfirm:()=>Ke(S),okText:"确认",cancelText:"取消",children:t.jsx(ee,{type:"link",danger:!0,children:"删除"})})]})}]),H=()=>{const u=Number(fe(l.getFieldsValue().cycleId).format("YYYY")),S=[...de],s=S.findIndex(g=>g.key==="comprehensiveAssessment");s!==-1&&(S[s].children=[{title:u-4,key:"evaluationFirst",dataIndex:"evaluationFirst",align:"center",width:100,children:[]},{title:u-3,key:"evaluationSecond",dataIndex:"evaluationSecond",align:"center",width:100,children:[]},{title:u-2,key:"evaluationThird",dataIndex:"evaluationThird",align:"center",width:100,children:[]},{title:u-1,key:"evaluationFourth",dataIndex:"evaluationFourth",align:"center",width:100,children:[]},{title:u,key:"evaluationFifth",dataIndex:"evaluationFifth",align:"center",width:100,children:[]}],ae(S),oe.some(g=>g.key==="comprehensiveAssessment")&&Me(i))},te=u=>{he(u),Q(!0)},Be=["fulltimeEducation","highestEducation","partyJoinDate","workStartDate","salaryGrade","phone","comprehensiveAssessment","talentInfo"],V=["unit","personnelChange","personnelType","employeeId","employeeName","department","basicResponsibilityUnit","partyPosition","threeLevelManager","baseResponsibilityUnitManager","talentInfo","action"],[oe,ye]=a.useState(de.filter(u=>V.includes(u.key)));a.useEffect(()=>(console.log("当前菜单编码:",me==null?void 0:me.currentMenuCode),Y(!1),e.current&&x(e.current.offsetHeight),De(),re(),window.addEventListener("resize",q),()=>{window.removeEventListener("resize",q)}),[]);const q=()=>{ge()};a.useEffect(()=>{J>0&&re()},[J]),a.useEffect(()=>{setTimeout(()=>{ge()},100)},[oe]),a.useEffect(()=>{(b==null?void 0:b.length)>0&&ge()},[b]),a.useEffect(()=>{(T==null?void 0:T.total)>0&&re()},[T.pageNum,T.pageSize]);const ge=()=>{var s;const u=(document.querySelector(".peopleMonthReport_table .ant-table-header")||{}).offsetHeight||0,S=(document.querySelector(".peopleMonthReport_table .ant-table-pagination")||{}).offsetHeight||26;u&&S&&x(((s=n.current)==null?void 0:s.offsetHeight)-(r.current.offsetHeight+u+S))},De=async()=>{const[[u,S],[s,g],[U,Z],[G,xe],[Oe,Ne],[Mt,st]]=await Promise.all([ie({code:"1010",tag:1}),ie({code:"reportPersonChange"}),ie({code:"reportManagerLevel"}),ie({code:"reportUnitManager"}),ie({code:"reportTalent"}),ie({code:"reportExitReason"})]);u||s||U||G||Oe||Mt||(S.STATUS==="0000"&&C(S.DATA),g.STATUS==="0000"&&I(g.DATA),Z.STATUS==="0000"&&P(Z.DATA),xe.STATUS==="0000"&&F(xe.DATA),Ne.STATUS==="0000"&&y(Ne.DATA),st.STATUS==="0000"&&o(st.DATA),z([{enumId:"position",level:null,sort:1,enumName:"岗位名称",region:null},{enumId:"positionLevel",level:null,sort:2,enumName:"岗位级别",region:null},{enumId:"gender",level:null,sort:3,enumName:"性别",region:null},{enumId:"ethnicity",level:null,sort:4,enumName:"民族",region:null},{enumId:"birthDate",level:null,sort:5,enumName:"出生日期",region:null},{enumId:"age",level:null,sort:6,enumName:"年龄",region:null},{enumId:"fulltimeEducation",level:null,sort:7,enumName:"全日制学历",region:null},{enumId:"highestEducation",level:null,sort:8,enumName:"最高学历",region:null},{enumId:"partyJoinDate",level:null,sort:9,enumName:"入党时间",region:null},{enumId:"workStartDate",level:null,sort:10,enumName:"参加工作时间",region:null},{enumId:"salaryGrade",level:null,sort:11,enumName:"职级薪档",region:null},{enumId:"phone",level:null,sort:12,enumName:"电话",region:null},{enumId:"comprehensiveAssessment",level:null,sort:13,enumName:"综合考评",region:null},{enumId:"exitSituation",level:null,sort:14,enumName:"退出情况说明",region:null}]))},Re=async u=>{const[S,s]=await Fe({empId:u.target.value});if(S){_.error((s==null?void 0:s.DATA)||(s==null?void 0:s.MESSAGE)||"调用失败");return}if(s.STATUS==="0000"){const{DATA:g}=s;l.setFieldsValue({empName:g==null?void 0:g.empName,gender:g==null?void 0:g.gender,department:g==null?void 0:g.department,birthDate:g==null?void 0:g.birthDate,ethnicity:g==null?void 0:g.ethnicity})}else _.error((s==null?void 0:s.MESSAGE)||(s==null?void 0:s.DATA)||"调用失败")},re=async u=>{var Z;H(),Y(!0);const S=T,s=l.getFieldsValue(),[g,U]=await Te({map:{cycleId:s==null?void 0:s.cycleId.format("YYYYMM"),employeeId:(s==null?void 0:s.empId)??"",employeeName:(s==null?void 0:s.empName)??""},pagination:{pageNum:S.pageNum,pageSize:S.pageSize}});if(Y(!1),g){_.error((U==null?void 0:U.DATA)||(U==null?void 0:U.MESSAGE)||"调用失败");return}if(U.STATUS==="0000"){const{DATA:{data:G}}=U,xe=G.map((Oe,Ne)=>({...Oe,key:Oe.id||Ne}));f(xe),X({...T,total:(Z=U.DATA)==null?void 0:Z.totalCount})}else _.error((U==null?void 0:U.MESSAGE)||(U==null?void 0:U.DATA)||"调用失败")},Se=Tt(Re,500),Ve=()=>{re()},Ae=async()=>{try{pt("正在导出",0,"loading");const{cycleId:u,empId:S,empName:s}=l.getFieldsValue(),g=await Ue({map:{cycleId:u==null?void 0:u.format("YYYYMM"),employeeId:S??"",employeeName:s??""}});Wt(g)}catch(u){pt("导出失败",1,"error"),console.error("Download failed:",u)}},Me=u=>{const S=de.find(G=>G.key==="action"),s=de.filter(G=>V.includes(G.key)&&G.key!=="action"),g=de.filter(G=>u.some(xe=>xe===G.key)&&G.key!=="action"),U=[...s,...g],Z=Array.from(new Map(U.map(G=>[G.key,G])).values());S&&Z.push(S),ye(Z),d(u)},We=()=>{const u=J+1;l.resetFields(),W(u)},Ge=u=>{const S={total:u==null?void 0:u.total,pageNum:u==null?void 0:u.current,pageSize:u==null?void 0:u.pageSize};X(S)},Ce=(u,S)=>{console.log("selectedRowKeys changed: ",u),console.log(S,"selectedRows"),ke(u)},[ue,ke]=a.useState([]),qe={selectedRowKeys:ue,onChange:Ce},Ke=async u=>{var g;if(!u.employeeId){_.error("该记录不包含有效员工编号");return}const[S,s]=await we({map:{cycleId:(g=l.getFieldsValue().cycleId)==null?void 0:g.format("YYYYMM"),employeeId:[u.employeeId]}});if(S){_.error((s==null?void 0:s.DATA)||(s==null?void 0:s.MESSAGE)||"调用失败");return}s.STATUS==="0000"?(_.success((s==null?void 0:s.MESSAGE)||(s==null?void 0:s.DATA)||"删除成功"),re()):_.error((s==null?void 0:s.MESSAGE)||(s==null?void 0:s.DATA)||"删除失败")},Je=async()=>{var U;if(ue.length===0){_.warning("请选择要删除的记录");return}const S=b.filter(Z=>ue.includes(Z.key)).map(Z=>Z.employeeId).filter(Boolean);if(S.length===0){_.error("所选记录不包含有效员工编号");return}const[s,g]=await we({map:{cycleId:(U=l.getFieldsValue().cycleId)==null?void 0:U.format("YYYYMM"),employeeId:S}});if(s){_.error((g==null?void 0:g.DATA)||(g==null?void 0:g.MESSAGE)||"调用失败");return}g.STATUS==="0000"?(_.success((g==null?void 0:g.MESSAGE)||(g==null?void 0:g.DATA)||"删除成功"),ke([]),re()):_.error((g==null?void 0:g.MESSAGE)||(g==null?void 0:g.DATA)||"删除失败")},Xe=async u=>{Y(!0),console.log(u,"data");const[S,s]=await ze(u);if(S){_.error((s==null?void 0:s.DATA)||(s==null?void 0:s.MESSAGE)||"调用失败"),Y(!1);return}s.STATUS==="0000"?(_.success((s==null?void 0:s.MESSAGE)||(s==null?void 0:s.DATA)||"新增成功"),E(!1),Y(!1),re()):(_.error((s==null?void 0:s.MESSAGE)||(s==null?void 0:s.DATA)||"新增失败"),Y(!1))},v=async u=>{var g;const[S,s]=await He({...u,cycleId:(g=l.getFieldsValue().cycleId)==null?void 0:g.format("YYYYMM")});if(S){_.error((s==null?void 0:s.DATA)||(s==null?void 0:s.MESSAGE)||"调用失败"),Y(!1);return}s.STATUS==="0000"?(_.success((s==null?void 0:s.MESSAGE)||(s==null?void 0:s.DATA)||"修改成功"),Q(!1),Y(!1),re()):(_.error((s==null?void 0:s.MESSAGE)||(s==null?void 0:s.DATA)||"修改失败"),Y(!1))};return t.jsxs("div",{className:`pt-[0.5rem] px-[0rem] h-full flex flex-col ${je.employment_page}`,children:[t.jsx("div",{ref:e,className:"bg-white pt-2 px-8 mb-2 h-[2.6rem]",children:t.jsx(A,{form:l,initialValues:{tag:"",cycleId:fe()},onFinish:Ve,autoComplete:"off",children:t.jsxs(ne,{gutter:24,children:[t.jsx(O,{span:6,children:t.jsx(A.Item,{label:"月份",name:"cycleId",wrapperCol:{span:20},children:t.jsx(pe,{className:"w-full",picker:"month",format:"YYYYMM",allowClear:!1})})}),t.jsx(O,{span:6,children:t.jsx(A.Item,{name:"empId",label:"员工编号",className:"mb-[0.5rem]",children:t.jsx(K,{placeholder:"请输入员工编号",allowClear:!0,style:{width:"100%"},onChange:Se})})}),t.jsx(O,{span:6,children:t.jsx(A.Item,{name:"empName",label:"员工姓名",className:"mb-[0.5rem]",children:t.jsx(K,{placeholder:"请输入员工姓名",allowClear:!0,style:{width:"100%"}})})}),t.jsx(O,{span:6,children:t.jsx(A.Item,{labelCol:{span:0},wrapperCol:{span:24},children:t.jsxs(ut,{size:"small",children:[t.jsx(ee,{type:"primary",htmlType:"submit",children:"查询"}),t.jsx(ee,{htmlType:"button",onClick:()=>We(),children:"重置"}),t.jsx(ee,{danger:!0,ghost:!0,icon:t.jsx(Vt,{}),onClick:()=>Ae(),children:"导出"})]})})})]})})}),t.jsxs("div",{ref:n,className:"relative bg-white px-5 pt-2.5",style:{height:`calc(100% - ${((D=e.current)==null?void 0:D.offsetHeight)+15}px)`},children:[t.jsxs("div",{ref:r,className:`flex justify-between items-center overflow-hidden mb-2 ${je.animation_box} ${k?"h-[1.6rem]":"h-0"}`,children:[t.jsxs("div",{className:"flex ",children:[k?t.jsx(Xt,{className:`${je.shousuo_icon} text-[1rem]`,onClick:()=>{$(!1),setTimeout(()=>{ge()},200)}}):t.jsx(Qt,{className:`${je.shousuo_icon} absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10`,onClick:()=>{$(!0),setTimeout(()=>{ge()},200)}}),t.jsxs("div",{className:"font-bold text-[0.8rem] ml-3",children:["数据列表",t.jsx("span",{className:"text-[0.7rem]",style:{color:"#939393"},children:"（单位：万元）"})]})]}),t.jsxs("div",{style:{display:"flex",justifyContent:"space-between"},children:[t.jsx(le,{placeholder:"请选择",className:"w-full",allowClear:!1,showSearch:!0,value:i,mode:"multiple",style:{marginRight:"0.4rem",width:"15rem"},maxTagCount:1,onChange:Me,children:m.map(u=>{const{enumId:S,enumName:s}=u;return t.jsx(le.Option,{value:S,children:s},S)})}),t.jsxs(ee,{type:"primary",className:"ml-[0.4rem]",onClick:()=>{E(!0)},children:[t.jsx(It,{}),"新增"]}),t.jsx(mt,{title:"删除",description:"确定删除所选中的数据吗？",onConfirm:Je,okText:"确认",cancelText:"取消",children:t.jsxs(ee,{danger:!0,ghost:!0,className:"ml-[0.4rem]",onClick:()=>{},children:[t.jsx(Zt,{}),"删除"]})})]})]}),t.jsx(Gt,{className:"peopleMonthReport_table",rowClassName:(u,S)=>S%2===1?"customRow odd":"customRow even",columns:oe,dataSource:b,loading:M,rowSelection:qe,bordered:!0,scroll:{y:`calc(${c}px - 0.625rem - 1.6rem - 0.5rem)`},onChange:Ge,pagination:{...T,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50"]}})]}),t.jsx(Dt,{title:"- 新增数据 -",destroyOnClose:!0,open:j,wrapClassName:je.add_table_modal,centered:!0,footer:null,onCancel:()=>E(!1),width:"80%",children:t.jsx(Qn,{columns:de.filter(u=>!Be.includes(u.dataIndex)),submitData:u=>Xe(u),unitList:w,managerLevelList:R,unitManagerList:N,talentList:L})}),t.jsx(Zn,{open:B,onCancel:()=>{Q(!1),he(null)},onOk:v,initialValues:Ee,unitList:w,personnelChangeList:h,managerLevelList:R,unitManagerList:N,talentList:L,exitReasonList:p})]})};export{gl as default};
