import{r as s,F as a,j as t,u,d as c,R as W,C as d,y as S,S as X,B as g,T}from"./index-De_f0oL2.js";import{R as Z,o as I,d as ee}from"./down-BCLNnN1h.js";import{e as x}from"./service-Cudu09YY.js";import{e as te}from"./service-DcPXuTuP.js";import{s as le}from"./Index.module-C_CsuTds.js";import{s as re}from"./index-BcPP1N8I.js";import{D as ne}from"./index-CdSZ9YgQ.js";import{C as oe}from"./index-DZyVV6rP.js";import{S as i}from"./index-BWJehDyc.js";import{F as se}from"./Table-D-iLeFE-.js";import"./useMultipleSelect-B0dEIXT-.js";const{RangePicker:ae}=ne,we=()=>{const j=[{title:"序号",dataIndex:"num",align:"center",width:50,render:(e,l,n)=>(r==null?void 0:r.pageSize)*((r==null?void 0:r.pageNum)-1)+n+1},{title:"账号",dataIndex:"loginName",align:"center",width:100},{title:"姓名",dataIndex:"fullName",align:"center",width:100},{title:"地市",dataIndex:"areaName",align:"center",width:100},{title:"部门",dataIndex:"orgName",align:"center",width:150,ellipsis:{showTitle:!1},render:e=>t.jsx(T,{title:e,placement:"topLeft",getPopupContainer:l=>(l==null?void 0:l.closest("div.ant-table-body"))||document.body,children:e})},{title:"角色",dataIndex:"roleNames",align:"center",width:200,ellipsis:{showTitle:!1},render:e=>t.jsx(T,{title:e,placement:"topLeft",getPopupContainer:l=>(l==null?void 0:l.closest("div.ant-table-body"))||document.body,children:e})},{title:"登录时间",dataIndex:"loginDate",align:"center",width:200},{title:"退出时间",dataIndex:"logoutDate",align:"logoutDate",width:200},{title:"事件类型",dataIndex:"loginEventType",key:"loginEventType",align:"center",width:100,render:e=>{switch(e){case"100":return t.jsx("div",{children:"登录成功"});case"200":return t.jsx("div",{style:{color:"#ff0000"},children:"登录失败"});case"201":return t.jsx("div",{style:{color:"#ff0000"},children:"验证码错误"});case"202":return t.jsx("div",{style:{color:"#ff0000"},children:"用户禁用"});case"203":return t.jsx("div",{style:{color:"#ff0000"},children:"连续登录失败"});case"204":return t.jsx("div",{style:{color:"#ff0000"},children:"用户信息异常"})}}},{title:"IP",dataIndex:"hostAddress",align:"center",width:120}],p=s.useRef(null),[m]=a.useForm(),[v,R]=s.useState(0),[r,w]=s.useState({total:0,pageNum:1,pageSize:50}),[A,L]=s.useState(j),[N,Y]=s.useState([]),[F,y]=s.useState(!1),[o,D]=s.useState([]),[O,E]=s.useState([]),[M,P]=s.useState([]),{runAsync:k}=u(x.loginlogDownload,{manual:!0}),{runAsync:z}=u(x.queryList,{manual:!0}),{runAsync:q}=u(x.loginLog,{manual:!0}),{runAsync:U}=u(te.build4LevelOrgTree,{manual:!0});s.useEffect(()=>{p.current&&R(p.current.offsetHeight),V(),_()},[]),s.useEffect(()=>{(r==null?void 0:r.total)>0&&(f(),L(j))},[JSON.stringify(r)]);const V=async()=>{const[e,l]=await U({tag:"1"});e||l.STATUS==="0000"&&(P(l.DATA),f())},_=async()=>{const[e,l]=await z({key:"getRoleList"});e||l.STATUS==="0000"&&E(l.DATA)},f=async e=>{var C;const l=m.getFieldsValue(),n=o[(o==null?void 0:o.length)-1]||{};y(!0);const[Q,h]=await q({loginDateStart:l!=null&&l.loginDete?l==null?void 0:l.loginDete[0].format("YYYYMMDD"):"",loginDateEnd:l!=null&&l.loginDete?l==null?void 0:l.loginDete[1].format("YYYYMMDD"):"",adCode:n==null?void 0:n.orgId,level:(n==null?void 0:n.level)||"",...l,pageNum:r==null?void 0:r.pageNum,pageSize:r==null?void 0:r.pageSize,loginDete:void 0,areaRecord:void 0});if(y(!1),!Q)if(h.STATUS==="0000"){const{DATA:{list:K}}=h;Y(K),w({...r,total:(C=h.DATA)==null?void 0:C.total})}else re.error(h==null?void 0:h.MESSAGE)},$=e=>{console.log("Success:",e),f()},B=async()=>{try{I("正在导出",0,"loading");const e=m.getFieldsValue(),l=o[(o==null?void 0:o.length)-1]||{},n=await k({loginDateStart:e!=null&&e.loginDete?e==null?void 0:e.loginDete[0].format("YYYYMMDD"):"",loginDateEnd:e!=null&&e.loginDete?e==null?void 0:e.loginDete[1].format("YYYYMMDD"):"",adCode:l==null?void 0:l.orgId,level:(o==null?void 0:o.length)||"",...e,...r,loginDete:void 0,areaRecord:void 0});ee(n)}catch(e){I("导出失败",1,"error"),console.error("Download failed:",e)}},H=()=>{m.resetFields(),D([])},J=(e,l)=>l.some(n=>n.orgName.toLowerCase().indexOf(e.trim().toLowerCase())>-1),G=e=>{w({...e,pageNum:e==null?void 0:e.current})},b=(e,l)=>{const n=e.trim();return l.children.toLowerCase().includes(n.toLowerCase())};return t.jsxs("div",{className:`h-full pt-4 ${le.rbac_page}`,children:[t.jsx("div",{ref:p,className:"bg-white pt-4 px-8 mb-4",children:t.jsx(a,{form:m,initialValues:{loginDete:[c().subtract(1,"day"),c()]},onFinish:$,autoComplete:"off",children:t.jsxs(W,{gutter:24,children:[t.jsx(d,{span:6,children:t.jsx(a.Item,{label:"用户",name:"queryName",wrapperCol:{span:20},children:t.jsx(S,{placeholder:"请输入用户"})})}),t.jsx(d,{span:6,children:t.jsx(a.Item,{label:"登录时间",name:"loginDete",wrapperCol:{span:20},children:t.jsx(ae,{style:{width:"100%"},allowClear:!1,ranges:{近一天:[c().subtract(1,"day"),c()],近三天:[c().subtract(3,"day"),c()],近七天:[c().subtract(7,"day"),c()]}})})}),t.jsx(d,{span:6,children:t.jsx(a.Item,{label:"组织机构",name:"areaRecord",wrapperCol:{span:20},children:t.jsx(oe,{allowClear:!1,changeOnSelect:!0,expandTrigger:"hover",displayRender:e=>e[e.length-1],options:M,fieldNames:{value:"orgId",label:"orgName",children:"children"},onChange:(e,l)=>{console.log(e,l),D(l)},placeholder:"请选择组织机构",showSearch:{filter:J},onSearch:e=>console.log(e)})})}),t.jsx(d,{span:6,children:t.jsx(a.Item,{label:"部门",name:"orgName",wrapperCol:{span:20},children:t.jsx(S,{placeholder:"请输入部门"})})}),t.jsx(d,{span:6,children:t.jsx(a.Item,{label:"角色",name:"roleCodes",wrapperCol:{span:20},children:t.jsx(i,{placeholder:"请选择角色",className:"w-full",allowClear:!0,showSearch:!0,filterOption:b,children:O.map(e=>{const{roleCode:l,roleName:n}=e;return t.jsx(i.Option,{value:l,children:n},l)})})})}),t.jsx(d,{span:6,children:t.jsx(a.Item,{label:"事件类型",name:"loginEventType",wrapperCol:{span:20},children:t.jsxs(i,{placeholder:"请选择",className:"w-full",allowClear:!0,showSearch:!0,filterOption:b,children:[t.jsx(i.Option,{value:"100",children:"登录成功"},"100"),t.jsx(i.Option,{value:"200",children:"登录失败"},"200"),t.jsx(i.Option,{value:"201",children:"验证码错误"},"201"),t.jsx(i.Option,{value:"202",children:"用户禁用"},"202"),t.jsx(i.Option,{value:"203",children:"连续登录失败"},"203"),t.jsx(i.Option,{value:"204",children:"用户信息异常"},"204")]})})}),t.jsx(d,{span:6,children:t.jsx(a.Item,{labelCol:{span:0},wrapperCol:{span:24},children:t.jsxs(X,{size:"small",children:[t.jsx(g,{type:"primary",htmlType:"submit",children:"查询"}),t.jsx(g,{htmlType:"button",onClick:()=>H(),children:"重置"})]})})})]})})}),t.jsxs("div",{className:"py-4 px-8 mb-4 mt-4 bg-white overflow-auto",style:{height:`calc(100% - ${v+16}px)`},children:[t.jsxs("div",{className:"flex justify-between items-center mb-4",children:[t.jsx("span",{className:"font-bold text-[0.8rem]",children:"数据列表"}),t.jsx(g,{danger:!0,ghost:!0,icon:t.jsx(Z,{}),onClick:()=>B(),children:"导出"})]}),t.jsx(se,{className:"mb-8",columns:A,dataSource:N,loading:F,bordered:!0,scroll:{x:"max-content"},onChange:G,pagination:{...r,total:r==null?void 0:r.total,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50"]}})]})]})};export{we as default};
