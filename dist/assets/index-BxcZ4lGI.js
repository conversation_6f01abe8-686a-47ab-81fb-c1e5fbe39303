import{r as o,F as c,j as t,u as S,d as ce,R as de,C as p,S as me,B as N,T as ue}from"./index-De_f0oL2.js";import{e as j}from"./service-DcPXuTuP.js";import{R as he,o as M,d as pe}from"./down-BCLNnN1h.js";import{s as g}from"./Index.module-CQ0G-enU.js";import{R as ge}from"./index-BmnYJy3v.js";import{s as fe}from"./index-BcPP1N8I.js";import{D as xe}from"./index-CdSZ9YgQ.js";import{C as we}from"./index-DZyVV6rP.js";import{S as U}from"./index-BWJehDyc.js";import{R as ve,a as Ce}from"./FullscreenOutlined-DzCTibKW.js";import"./Table-D-iLeFE-.js";import"./useMultipleSelect-B0dEIXT-.js";const Ee=()=>{var D;const w=[{title:"单位",key:"orgName",dataIndex:"orgName",align:"center",width:150,children:[],render:(e,s)=>t.jsx(ue,{title:e,children:t.jsx("div",{className:g.over_ellipsis,children:e})})},{title:"员工编码",key:"employeeId",dataIndex:"employeeId",align:"center",width:100},{title:"姓名",key:"employeeName",dataIndex:"employeeName",align:"center",width:100},{title:"原单位",key:"originalUnit",dataIndex:"originalUnit",align:"center",width:100},{title:"现单位",key:"presentUnit",dataIndex:"presentUnit",align:"center",width:100},{title:"原岗位名称",key:"originalPostName",dataIndex:"originalPostName",align:"center",width:100},{title:"现岗位名称",key:"presentPostName",dataIndex:"presentPostName",align:"center",width:100},{title:"原岗级",key:"originalPostLevel",dataIndex:"originalPostLevel",align:"center",width:100},{title:"现岗级",key:"presentPostLevel",dataIndex:"presentPostLevel",align:"center",width:100}],f=o.useRef(null),R=o.useRef(null),T=o.useRef(null),[l]=c.useForm(),[q,k]=o.useState(0),[B,G]=o.useState([]),[u,J]=o.useState([]),[K,P]=o.useState(w),[d,v]=o.useState([]),[Q,C]=o.useState(!1),[y,W]=o.useState(0),[F,L]=o.useState(!0),[r,E]=o.useState({total:0,pageNum:1,pageSize:50}),{runAsync:X}=S(j.exportStatisticDetailExcel,{manual:!0}),{runAsync:Z}=S(j.getEmployeeChangeDetailPag,{manual:!0}),{runAsync:O}=S(j.build4LevelOrgTree2,{manual:!0});o.useEffect(()=>(C(!1),f.current&&k(f.current.offsetHeight),A(!0),window.addEventListener("resize",_),()=>{window.removeEventListener("resize",_)}),[]);const _=()=>{h()};o.useEffect(()=>{y>0&&x()},[y]),o.useEffect(()=>{h()},[(document.querySelector(".statistics_detail_table .ant-table-header")||{}).offsetHeight]),o.useEffect(()=>{var e;if((u==null?void 0:u.length)>0){const s=(e=l.getFieldsValue())==null?void 0:e.alteration;["unitChange-up","unitChange-down"].includes(s)?P(w.map(n=>({...n,hidden:["originalPostName","presentPostName","originalPostLevel","presentPostLevel"].includes(n.key)}))):P(w.map(n=>({...n,hidden:["originalUnit","presentUnit"].includes(n.key)}))),h()}},[u]),o.useEffect(()=>{(r==null?void 0:r.total)>0&&x()},[r.pageNum,r.pageSize]);const h=()=>{var n;const e=(document.querySelector(".statistics_detail_table .ant-table-header")||{}).offsetHeight,s=(document.querySelector(".statistics_detail_table .ant-table-pagination")||{}).offsetHeight||26;e&&s&&k(((n=R.current)==null?void 0:n.offsetHeight)-(T.current.offsetHeight+e+s))},z=async()=>{var n;const[e,s]=await O({monthId:(n=l.getFieldValue("monthId"))==null?void 0:n.format("YYYYMM"),tag:"1"});e||s.STATUS==="0000"&&(G(s.DATA),v([]),l.setFieldValue("orgId",""))},x=async e=>{var H,V,Y;C(!0);const s=r,n=l.getFieldsValue(),a=d?d[d.length-1]:{},i=n.alteration.split("-"),[I,m]=await Z({map:{monthId:(V=(H=l.getFieldsValue())==null?void 0:H.monthId)==null?void 0:V.format("YYYYMM"),orgId:void 0,orgId4:Number(a==null?void 0:a.level)===4?a==null?void 0:a.orgId:"",orgId5:Number(a==null?void 0:a.level)===5?a==null?void 0:a.orgId:"",kind:n.ratio,unitChange:i[0]==="unitChange"?i[1]:void 0,postChange:i[0]==="postChange"?i[1]:void 0},pagination:{pageNum:s.pageNum,pageSize:s.pageSize}});if(C(!1),!I)if(m.STATUS==="0000"){const{DATA:{data:b}}=m,ie=(b==null?void 0:b.map(($,re)=>({...$,key:$.id||re})))||[];J(ie),E({...r,total:(Y=m.DATA)==null?void 0:Y.totalCount})}else fe.error(m==null?void 0:m.MESSAGE)},A=e=>{const s=ce();l.setFieldsValue({monthId:s,orgId:""}),z(),e&&x()},ee=e=>{e.monthId&&z()},te=()=>{x()},ae=async()=>{var e,s;try{M("正在导出",0,"loading");const n=l.getFieldsValue(),a=d?d[d.length-1]:{},i=n.alteration.split("-"),I=await X({map:{monthId:(s=(e=l.getFieldsValue())==null?void 0:e.monthId)==null?void 0:s.format("YYYYMM"),orgId:void 0,orgId4:Number(a==null?void 0:a.level)===4?a==null?void 0:a.orgId:"",orgId5:Number(a==null?void 0:a.level)===5?a==null?void 0:a.orgId:"",kind:n.ratio,unitChange:i[0]==="unitChange"?i[1]:void 0,postChange:i[0]==="postChange"?i[1]:void 0}});pe(I)}catch(n){M("导出失败",1,"error"),console.error("Download failed:",n)}},se=()=>{const e=y+1;l.resetFields(),v([]),A(),W(e)},ne=e=>{const s={total:e==null?void 0:e.total,pageNum:e==null?void 0:e.current,pageSize:e==null?void 0:e.pageSize};E(s)},oe=(e,s)=>s.some(n=>n.orgName.toLowerCase().indexOf(e.trim().toLowerCase())>-1),le=(e,s)=>{v(s)};return t.jsxs("div",{className:`h-full pt-[0.5rem] flex flex-col ${g.employment_page}`,children:[t.jsx("div",{ref:f,className:"bg-white pt-[0.5rem] px-8 mb-[0.5rem]",children:t.jsx(c,{form:l,initialValues:{tag:"",ratio:"HB",alteration:"unitChange-up"},onFinish:te,onValuesChange:ee,autoComplete:"off",children:t.jsxs(de,{gutter:24,children:[t.jsx(p,{span:4,children:t.jsx(c.Item,{label:"月份",name:"monthId",wrapperCol:{span:20},children:t.jsx(xe,{className:"w-full",picker:"month"})})}),t.jsx(p,{span:6,children:t.jsx(c.Item,{label:"单位",name:"orgId",wrapperCol:{span:20},children:t.jsx(we,{allowClear:!0,changeOnSelect:!0,expandTrigger:"hover",displayRender:e=>e[e.length-1],options:B,onChange:le,fieldNames:{value:"orgId",label:"orgName",children:"children"},placeholder:"请选择",showSearch:{filter:oe},onSearch:e=>console.log(e)})})}),t.jsx(p,{span:4,children:t.jsx(c.Item,{label:"环比定比",name:"ratio",wrapperCol:{span:20},children:t.jsx(U,{placeholder:"请选择",className:"w-full",options:[{label:"环比",value:"HB"},{label:"定比",value:"DB"}]})})}),t.jsx(p,{span:4,children:t.jsx(c.Item,{label:"变动",name:"alteration",wrapperCol:{span:20},children:t.jsx(U,{placeholder:"请选择",className:"w-full",options:[{label:"单位变动-增加",value:"unitChange-up"},{label:"单位变动-减少",value:"unitChange-down"},{label:"岗位变动-提升",value:"postChange-up"},{label:"岗位变动-下降",value:"postChange-down"}]})})}),t.jsx(p,{span:6,children:t.jsx(c.Item,{labelCol:{span:0},wrapperCol:{span:24},children:t.jsxs(me,{size:"small",children:[t.jsx(N,{type:"primary",htmlType:"submit",children:"查询"}),t.jsx(N,{htmlType:"button",onClick:()=>se(),children:"重置"})]})})})]})})}),t.jsxs("div",{ref:R,className:"relative bg-white px-5 pt-2.5",style:{height:`calc(100% - ${((D=f.current)==null?void 0:D.offsetHeight)+15}px)`},children:[t.jsxs("div",{ref:T,className:`flex justify-between items-center mb-2 overflow-hidden ${g.animation_box} ${F?"h-[1.6rem]":"h-0"}`,children:[t.jsxs("div",{className:"flex ",children:[F?t.jsx(ve,{className:`${g.shousuo_icon} text-[1rem]`,onClick:()=>{L(!1),setTimeout(()=>{h()},200)}}):t.jsx(Ce,{className:`${g.shousuo_icon} absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10`,onClick:()=>{L(!0),setTimeout(()=>{h()},200)}}),t.jsx("div",{className:"font-bold text-[0.8rem] ml-3",children:"数据列表"})]}),t.jsx(N,{danger:!0,ghost:!0,icon:t.jsx(he,{}),onClick:()=>ae(),children:"导出"})]}),t.jsx(ge,{className:"statistics_detail_table",rowClassName:(e,s)=>s%2===1?"customRow odd":"customRow even",columns:K,dataSource:u,loading:Q,bordered:!0,scroll:{y:`calc(${q}px - 0.625rem - 1.6rem - 0.5rem)`},onChange:ne,pagination:{...r,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50"]}})]})]})};export{Ee as default};
