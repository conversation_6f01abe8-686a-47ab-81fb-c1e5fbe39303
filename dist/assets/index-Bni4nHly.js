import{F as p,r as s,u as f,d as u,j as e,R as le,C as R,S as se,B as I,T as o}from"./index-De_f0oL2.js";import{p as y}from"./service-B_wWoC3F.js";import{R as ie,o as P,d as ce}from"./down-BCLNnN1h.js";import{R as oe}from"./index-BmnYJy3v.js";import{s as B}from"./index-BcPP1N8I.js";import{D as de}from"./index-CdSZ9YgQ.js";import{C as me}from"./index-DZyVV6rP.js";import{R as ue,a as he}from"./FullscreenOutlined-DzCTibKW.js";import{R as d}from"./InfoCircleOutlined-DUD7mNgc.js";import"./Table-D-iLeFE-.js";import"./index-BWJehDyc.js";import"./useMultipleSelect-B0dEIXT-.js";const ge="_salaryStructureCompare_page_10ywu_6",xe="_animation_box_10ywu_6",S={salaryStructureCompare_page:ge,animation_box:xe},ve=()=>{var F;const[h]=p.useForm(),b=s.useRef(null),T=s.useRef(null),C=s.useRef(null),[$,z]=s.useState(0),[A,N]=s.useState(!0),[H,v]=s.useState(!1),[L,O]=s.useState([]),[q,G]=s.useState([]),[c,Y]=s.useState({total:0,pageNum:1,pageSize:50}),{runAsync:_}=f(y.getEnumType,{manual:!0}),{runAsync:V}=f(y.getSalaryStructureCompare,{manual:!0}),{runAsync:pe}=f(y.downloadTemplate,{manual:!0}),{runAsync:J}=f(y.exportSalaryStructureCompare,{manual:!0}),K=[{title:"单位",dataIndex:"cityName",key:"cityName",align:"center",width:160,fixed:"left"},{title:"预算占比",key:"level",dataIndex:"level",align:"center",children:[{title:e.jsxs(e.Fragment,{children:[e.jsx("span",{children:"绩效薪酬 "}),e.jsx(o,{title:"积分",children:e.jsx(d,{})})]}),dataIndex:"perfSalaryPointsBudgetScale",key:"perfSalaryPointsBudgetScale",align:"center",width:130},{title:"增量收益分享",dataIndex:"incrementalSharingBudgetScale",key:"incrementalSharingBudgetScale",align:"center",width:130},{title:e.jsxs(e.Fragment,{children:[e.jsx("span",{children:"专项奖励 "}),e.jsx(o,{title:"业绩相关",children:e.jsx(d,{})})]}),dataIndex:"specialAwardBudgetScale",key:"specialAwardBudgetScale",align:"center",width:130}]},{title:"预算比例",key:"level",dataIndex:"level",align:"center",children:[{title:e.jsxs(e.Fragment,{children:[e.jsx("span",{children:"绩效薪酬 "}),e.jsx(o,{title:"积分",children:e.jsx(d,{})})]}),dataIndex:"perfSalaryPointsBudgetRatio",key:"perfSalaryPointsBudgetRatio",align:"center",width:130},{title:"增量收益分享",dataIndex:"incrementalSharingBudgetRatio",key:"incrementalSharingBudgetRatio",align:"center",width:130},{title:e.jsxs(e.Fragment,{children:[e.jsx("span",{children:"专项奖励 "}),e.jsx(o,{title:"业绩相关",children:e.jsx(d,{})})]}),dataIndex:"specialAwardBudgetRatio",key:"specialAwardBudgetRatio",align:"center",width:130}]},{title:"使用情况占比（累计口径）",key:"level",dataIndex:"level",align:"center",children:[{title:e.jsxs(e.Fragment,{children:[e.jsx("span",{children:"绩效薪酬 "}),e.jsx(o,{title:"积分",children:e.jsx(d,{})})]}),dataIndex:"perfSalaryPointsUsageScale",key:"perfSalaryPointsUsageScale",align:"center",width:130},{title:"增量收益分享",dataIndex:"incrementalSharingUsageScale",key:"incrementalSharingUsageScale",align:"center",width:130},{title:e.jsxs(e.Fragment,{children:[e.jsx("span",{children:"专项奖励 "}),e.jsx(o,{title:"业绩相关",children:e.jsx(d,{})})]}),dataIndex:"specialAwardUsageScale",key:"specialAwardUsageScale",align:"center",width:130}]},{title:"使用情况比例（累计口径）",key:"level",dataIndex:"level",align:"center",children:[{title:e.jsxs(e.Fragment,{children:[e.jsx("span",{children:"绩效薪酬 "}),e.jsx(o,{title:"积分",children:e.jsx(d,{})})]}),dataIndex:"perfSalaryPointsUsageRatio",key:"perfSalaryPointsUsageRatio",align:"center",width:130},{title:"增量收益分享",dataIndex:"incrementalSharingUsageRatio",key:"incrementalSharingUsageRatio",align:"center",width:130},{title:e.jsxs(e.Fragment,{children:[e.jsx("span",{children:"专项奖励 "}),e.jsx(o,{title:"业绩相关",children:e.jsx(d,{})})]}),dataIndex:"specialAwardUsageRatio",key:"specialAwardUsageRatio",align:"center",width:130}]}];s.useEffect(()=>(Z(),E(),window.addEventListener("resize",k),()=>{window.removeEventListener("resize",k)}),[]);const k=()=>{g()};s.useEffect(()=>{g()},[(document.querySelector(".salaryStructureCompare_table .ant-table-header")||{}).offsetHeight]);const g=()=>{var r;const t=(document.querySelector(".salaryStructureCompare_table .ant-table-header")||{}).offsetHeight||0,a=(document.querySelector(".salaryStructureCompare_table .ant-table-pagination")||{}).offsetHeight||26;t&&a&&z(((r=T.current)==null?void 0:r.offsetHeight)-(C.current.offsetHeight+t+a))},Q=async t=>{var a;try{P("正在导出",0,"loading");let r=null;if(t!==1){if(t===2){const{loginDate:l,cityId:i,category:m,cycleId:x,empId:w}=h.getFieldsValue(),n={beginTime:(l==null?void 0:l.length)>0?u(l[0]).format("YYYY-MM-DD"):null,endTime:(l==null?void 0:l.length)>0?u(l[1]).format("YYYY-MM-DD"):null,cycleId:x?(a=u(x))==null?void 0:a.format("YYYYMM"):"",cityId:i?i[(i==null?void 0:i.length)-1]:null,category:m,empId:w};r=await J(n)}}ce(r)}catch(r){P("导出失败",1,"error"),console.error("Download failed:",r)}},W=t=>{j({...t})},Z=async()=>{var i;const[[t,a],[r]]=await Promise.all([_({code:"1010",tag:1}),_({code:"EMPLOYEE_TRANSFER_CATEGORY"})]);if(t||r)return;a.STATUS==="0000"&&O((i=a.DATA)==null?void 0:i.filter(m=>(m==null?void 0:m.orgId)!=="49757"));const l={...h.getFieldsValue()};j(l)},j=async t=>{var D,M;v(!0);const{loginDate:a,cityId:r,category:l,cycleId:i,empId:m}=t,x={...c,beginTime:(a==null?void 0:a.length)>0?u(a[0]).format("YYYY-MM-DD"):null,endTime:(a==null?void 0:a.length)>0?u(a[1]).format("YYYY-MM-DD"):null,cycleId:i?(D=u(i))==null?void 0:D.format("YYYYMM"):"",cityId:r?r[(r==null?void 0:r.length)-1]:null,category:l,empId:m},[w,n]=await V(x);if(v(!1),w){B.error((n==null?void 0:n.DATA)||(n==null?void 0:n.MESSAGE)||"调用失败");return}if(n.STATUS==="0000"){const{DATA:{data:ae}}=n,re=ae.map((U,ne)=>({...U,key:U.id||ne}));G(re),Y({...c,total:(M=n.DATA)==null?void 0:M.totalCount})}else B.error((n==null?void 0:n.MESSAGE)||(n==null?void 0:n.DATA)||"调用失败")},X=()=>{h.resetFields(),E()},ee=t=>{const a={total:t==null?void 0:t.total,pageNum:t==null?void 0:t.current,pageSize:t==null?void 0:t.pageSize};Y(a)};s.useEffect(()=>{if((c==null?void 0:c.total)>0){const t={...h.getFieldsValue()};j(t)}},[c.pageNum,c.pageSize]);const E=()=>{const t=u().subtract(1,"month");h.setFieldsValue({cycleId:t})},te=(t,a)=>{var r,l;return(((r=a[0])==null?void 0:r.enumName)??"").toLowerCase().includes((l=t.toLowerCase())==null?void 0:l.trim())};return e.jsx("div",{className:`pt-[0.5rem] px-[0rem] h-full flex flex-col ${S.salaryStructureCompare_page}`,children:e.jsxs(e.Fragment,{children:[e.jsx("div",{ref:b,className:"bg-white pt-[0.5rem] px-[1.75rem] mb-[0.5rem]",children:e.jsx(p,{form:h,labelCol:{span:6},onFinish:W,initialValues:{loginDate:"",category:"KOU_JING_TIAO_ZHENG",cityId:"",empId:""},children:e.jsxs(le,{gutter:24,children:[e.jsx(R,{span:6,children:e.jsx(p.Item,{name:"cycleId",label:"账期",className:"mb-[0.5rem]",children:e.jsx(de,{className:"w-full",picker:"month"})})}),e.jsx(R,{span:5,children:e.jsx(p.Item,{name:"cityId",label:"单位",className:"mb-[0.5rem]",children:e.jsx(me,{allowClear:!0,changeOnSelect:!0,expandTrigger:"hover",displayRender:t=>t[t.length-1],options:L,fieldNames:{value:"enumId",label:"enumName",children:"children"},placeholder:"请选择单位",showSearch:{filter:te},onSearch:t=>console.log(t)})})}),e.jsx(R,{span:4,children:e.jsx("div",{className:"text-right",children:e.jsxs(se,{children:[e.jsx(I,{type:"primary",htmlType:"submit",children:"查询"}),e.jsx(I,{onClick:()=>X(),children:"重置"})]})})})]})})}),e.jsxs("div",{ref:T,className:"relative bg-white px-5 pt-2.5",style:{height:`calc(100% - ${((F=b.current)==null?void 0:F.offsetHeight)+15}px)`},children:[e.jsxs("div",{ref:C,className:`flex justify-between items-center overflow-hidden mb-[0.1rem] ${S.animation_box} ${A?"h-[1.8rem]":"h-0"}`,children:[e.jsxs("div",{className:"flex ",children:[A?e.jsx(ue,{className:`${S.shousuo_icon} text-[1rem]`,onClick:()=>{N(!1),setTimeout(()=>{g()},200)}}):e.jsx(he,{className:`${S.shousuo_icon} absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10`,onClick:()=>{N(!0),setTimeout(()=>{g()},200)}}),e.jsx("div",{className:"font-bold text-[0.8rem] ml-3",children:"数据列表"})]}),e.jsx("div",{className:"flex gap-x-[2.5rem]",children:e.jsx("div",{className:"flex gap-x-[0.75rem]",children:e.jsx(I,{danger:!0,ghost:!0,icon:e.jsx(ie,{}),onClick:()=>Q(2),children:"导出"})})})]}),e.jsx(oe,{className:"salaryStructureCompare_table",rowClassName:(t,a)=>a%2===1?"customRow odd":"customRow even",columns:K,dataSource:q,bordered:!0,scroll:{y:`calc(${$}px - 0.625rem - 1.6rem - 0.5rem)`},loading:H,onChange:ee,pagination:{...c,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50"]}})]})]})})};export{ve as default};
