import{r as i,j as e,F as y,d as k,u as p,R as Ie,C as M,S as ke,B as S}from"./index-De_f0oL2.js";import{e as w}from"./service-BvtRB_M7.js";import{R as Re,o as U,d as Ae}from"./down-BCLNnN1h.js";import{R as Be}from"./index-BmnYJy3v.js";import{f as a}from"./format-ChnqMkgG.js";import{s as h}from"./index-BcPP1N8I.js";import{D as Te}from"./index-CdSZ9YgQ.js";import{C as be}from"./index-DZyVV6rP.js";import{R as Ce,a as Pe}from"./FullscreenOutlined-DzCTibKW.js";import{S as W}from"./index-BWJehDyc.js";import"./Table-D-iLeFE-.js";import"./useMultipleSelect-B0dEIXT-.js";const De="_employment_page_10s9j_1",Me="_animation_box_10s9j_1",Ee="_columnTable_10s9j_4",I={employment_page:De,animation_box:Me,columnTable:Ee},Ye=v=>{const{tableData:j=[],tableHeight:N=0,columns:m=[],year:d=1e3}=v,[s,R]=i.useState({});i.useEffect(()=>{R(j[0]||{})},[j]);const o=A=>m.some(B=>B.key===A);return e.jsx("div",{className:"overflow-auto",style:{height:`calc(${N}px - 0.625rem - 0.5rem - 10px)`},children:e.jsxs("div",{className:`${I.columnTable} flex flex-col w-fit overflow-auto mb-5`,children:[e.jsx("div",{className:"row",children:e.jsx("div",{className:"area",children:s.cityName})}),o("list1")&&e.jsxs("div",{className:"flex",children:[e.jsxs("div",{className:"column col1",children:[d,"年业绩完成预算"]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col2",children:"主要营收"}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col3",children:"预算"}),e.jsx("div",{className:"value",children:a(s.mrBudget)})]}),e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col3",children:"完成情况"}),e.jsx("div",{className:"value",children:a(s.mrComplete)})]}),e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col3",children:"完成率"}),e.jsx("div",{className:"value",children:s.mrCompleteRate})]})]})]}),e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col2",children:"经营利润"}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col3",children:"预算"}),e.jsx("div",{className:"value",children:a(s.profitBudget)})]}),e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col3",children:"完成情况"}),e.jsx("div",{className:"value",children:a(s.profitComplete)})]}),e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col3",children:"完成率"}),e.jsx("div",{className:"value",children:s.profitCompleteRate})]})]})]}),e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col2",children:"终端收入"}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col3",children:"预算"}),e.jsx("div",{className:"value",children:a(s.ctBudget)})]}),e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col3",children:"完成情况"}),e.jsx("div",{className:"value",children:a(s.ctComplete)})]}),e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col3",children:"完成率"}),e.jsx("div",{className:"value",children:s.ctCompleteRate})]})]})]})]})]}),o("list2")&&e.jsxs("div",{className:"row",children:[e.jsxs("div",{className:"label",children:[d,"年当月薪酬预算合计"]}),e.jsx("div",{className:"value",children:a(s.salBudgetTotal)})]}),o("list3")&&e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"label",children:"基本保障薪酬(65%)"}),e.jsx("div",{className:"value",children:a(s.basicSal)})]}),o("list4")&&e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"label",children:"政策提低保障薪酬"}),e.jsx("div",{className:"value",children:a(s.salTd)})]}),o("list5")&&e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col1",children:"人才结构保障薪酬"}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col2",children:"挂钩人均创利薪酬(3%)"}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col3",children:"金额"}),e.jsx("div",{className:"value",children:a(s.abilityPerAmount)})]}),e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col3",children:"人均创利完成系数"}),e.jsx("div",{className:"value",children:s.abilityPerPercent})]}),e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col3",children:"人均创利预算完成率"}),e.jsx("div",{className:"value",children:s.abilityPerRate})]})]})]}),e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col2",children:"挂钩市场化退出率薪酬(2%)"}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col3",children:"金额"}),e.jsx("div",{className:"value",children:a(s.abilityQuitAmount)})]}),e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col3",children:"市场化退出率系数"}),e.jsx("div",{className:"value",children:s.abilityQuitPercent})]}),e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col3",children:"市场化退出率"}),e.jsx("div",{className:"value",children:s.abilityQuitRate})]})]})]})]})]}),o("list6")&&e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col1",children:"挂钩营业现金比率薪酬"}),e.jsx("div",{children:e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col2",children:"挂钩营业现金比率(5%)"}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col3",children:"金额"}),e.jsx("div",{className:"value",children:a(s.cashSalAmount)})]}),e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col3",children:"系数"}),e.jsx("div",{className:"value",children:s.cashSalPercent})]}),e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col3",children:"公众新增应收账款占收比完成率"}),e.jsx("div",{className:"value",children:s.cashSalPubAddRcvRate})]}),e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col3",children:"公众存量应收账款回款率完成率"}),e.jsx("div",{className:"value",children:s.cashSalPubStockRcvRate})]}),e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col3",children:"政企新增应收账款占收比完成率"}),e.jsx("div",{className:"value",children:s.cashSalZqAddRcvRate})]}),e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col3",children:"政企存量应收账款回款率完成率"}),e.jsx("div",{className:"value",children:s.cashSalZqStockRcvRate})]}),e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col3",children:"净预收账款占收比完成率"}),e.jsx("div",{className:"value",children:s.cashSalAdvanceRcvRate})]})]})]})})]}),o("list7")&&e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col1",children:"挂钩业绩"}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col2",children:"挂钩业绩预算(10%)"}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col3",children:"金额"}),e.jsx("div",{className:"value",children:a(s.achieveBudgetAmount)})]}),e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col3",children:"系数"}),e.jsx("div",{className:"value",children:s.achieveBudgetPercent})]}),e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col3",children:"联网通信收入完成率"}),e.jsx("div",{className:"value",children:s.achieveBudgetRcvRate})]}),e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col3",children:"算网数智收入完成率"}),e.jsx("div",{className:"value",children:s.achieveBudgetCiRcvRate})]}),e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col3",children:"经营利润完成率"}),e.jsx("div",{className:"value",children:s.achieveBudgetProfitRate})]})]})]}),e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col2",children:"主营收入增量受益分享"}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col3",children:"金额"}),e.jsx("div",{className:"value",children:a(s.achieveAddShareAmount)})]}),e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col3",children:"市场份额系数Q"}),e.jsx("div",{className:"value",children:s.achieveAddSharePercent})]}),e.jsxs("div",{className:"flex",children:[e.jsxs("div",{className:"column col3",children:[d-1,"年市场份额"]}),e.jsx("div",{className:"value",children:s.achieveAddShareLastMarketAmount})]}),e.jsxs("div",{className:"flex",children:[e.jsxs("div",{className:"column col3",children:[d,"年市场份额"]}),e.jsx("div",{className:"value",children:s.achieveAddShareMarketAmount})]})]})]}),e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col2",children:"终端收入增量收益分享"}),e.jsx("div",{children:e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col3",children:"金额"}),e.jsx("div",{className:"value",children:a(s.achieveTermAddShareAmount)})]})})]}),e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col2",children:"利润增量受益分享"}),e.jsx("div",{children:e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col3",children:"金额"}),e.jsx("div",{className:"value",children:a(s.achievePorfitAddShareAmount)})]})})]})]})]}),o("list8")&&e.jsxs("div",{className:"flex",children:[e.jsxs("div",{className:"column col1",children:[d,"年当月其他追加预算"]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col4",children:"合计"}),e.jsx("div",{className:"value",children:a(s.otherBudgetTotal)})]}),e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col4",children:"专项奖励"}),e.jsx("div",{className:"value",children:a(s.otherBudgetSpecialReward)})]}),e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col4",children:"交通补助"}),e.jsx("div",{className:"value",children:a(s.otherBudgetTransport)})]}),e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col4",children:"加班费核增（全年）"}),e.jsx("div",{className:"value",children:a(s.otherBudgetOvertime)})]}),e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col4",children:"过节费"}),e.jsx("div",{className:"value",children:a(s.otherBudgetHoliday)})]}),e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col4",children:"大学生租房补贴"}),e.jsx("div",{className:"value",children:a(s.otherBudgetCollegeHouse)})]}),e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col4",children:"退职"}),e.jsx("div",{className:"value",children:a(s.otherBudgetDismiss)})]}),e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col4",children:"墩苗、下沉、值守人员（全年）"}),e.jsx("div",{className:"value",children:a(s.otherBudgetDuty)})]}),e.jsxs("div",{className:"flex",children:[e.jsxs("div",{className:"column col4",children:[d,"年大学生"]}),e.jsx("div",{className:"value",children:a(s.otherBudgetGraduate)})]}),e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col4",children:"融通外包费用"}),e.jsx("div",{className:"value",children:a(s.otherBudgetOs)})]}),e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col4",children:"离职补偿金"}),e.jsx("div",{className:"value",children:a(s.otherBudgetQuit)})]}),e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col4",children:"其他"}),e.jsx("div",{className:"value",children:a(s.otherBudgetOther)})]}),e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col4",children:"转递薪酬"}),e.jsx("div",{className:"value",children:a(s.otherBudgetTrans)})]}),e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col4",children:"取暖补贴"}),e.jsx("div",{className:"value",children:a(s.otherBudgetWarm)})]})]})]}),o("list9")&&e.jsxs("div",{className:"flex",children:[e.jsxs("div",{className:"column col1 last",children:[d,"当月薪酬总量兑现情况"]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex",children:[e.jsxs("div",{className:"column col4",children:[d,"年当月薪酬预算"]}),e.jsx("div",{className:"value",children:a(s.salTotalDeliverBudget)})]}),e.jsxs("div",{className:"flex",children:[e.jsxs("div",{className:"column col4",children:[d,"年当月实际兑现"]}),e.jsx("div",{className:"value",children:a(s.salTotalDeliverReal)})]}),e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"column col4 last",children:"结余"}),e.jsx("div",{className:"value last",children:a(s.salTotalDeliverBalance)})]})]})]})]})})},Ue=()=>{var q;const v=i.useRef(null),j=i.useRef(null),N=i.useRef(null),[m]=y.useForm(),[d,s]=i.useState(k().format("YYYY")),[R,o]=i.useState(0),[A,B]=i.useState(0),[J,K]=i.useState([]),[u,X]=i.useState([]),[E,ee]=i.useState(["list1","list9"]),[le,ae]=i.useState([]),[se,T]=i.useState(!1),[te,Y]=i.useState(!1),[b,ce]=i.useState(0),[_,$]=i.useState(!0),[x,H]=i.useState({total:0,pageNum:1,pageSize:50}),[ie,L]=i.useState(!1),{runAsync:ne}=p(w.exportBranchTotalSalary,{manual:!0}),{runAsync:Q}=p(w.getEnumType,{manual:!0}),{runAsync:de}=p(w.getBranchTotalSalary,{manual:!0}),{runAsync:re}=p(w.getBranchTotalSalaryCalculate,{manual:!0}),{runAsync:oe}=p(w.getBranchTotalSalaryConfirm,{manual:!0}),C=[{title:"单位",key:"cityName",dataIndex:"cityName",align:"center",fixed:"left",width:130,children:[]},{title:`${d}年业绩完成预算`,key:"list1",dataIndex:"list1",align:"center",children:[{title:"主营收入",key:"orgIncMomLydec",dataIndex:"orgIncMomLydec",align:"center",children:[{title:"预算",key:"mrBudget",dataIndex:"mrBudget",align:"center",children:[],width:100,render:l=>e.jsx("span",{children:a(l)})},{title:"完成情况",key:"mrComplete",dataIndex:"mrComplete",align:"center",children:[],width:100,render:l=>e.jsx("span",{children:a(l)})},{title:"完成率",key:"mrCompleteRate",dataIndex:"mrCompleteRate",align:"center",children:[],width:100}]},{title:"经营利润",key:"orgDecMomLydec",dataIndex:"",align:"center",children:[{title:"预算",key:"profitBudget",dataIndex:"profitBudget",align:"center",children:[],width:100,render:l=>e.jsx("span",{children:a(l)})},{title:"完成情况",key:"profitComplete",dataIndex:"profitComplete",align:"center",children:[],width:100,render:l=>e.jsx("span",{children:a(l)})},{title:"完成率",key:"profitCompleteRate",dataIndex:"profitCompleteRate",align:"center",children:[],width:100}]},{title:"终端收入",key:"postNum",dataIndex:"postNum",align:"center",children:[{title:"预算",key:"ctBudget",dataIndex:"ctBudget",align:"center",children:[],width:100,render:l=>e.jsx("span",{children:a(l)})},{title:"完成情况",key:"ctComplete",dataIndex:"ctComplete",align:"center",children:[],width:100,render:l=>e.jsx("span",{children:a(l)})},{title:"完成率",key:"ctCompleteRate",dataIndex:"ctCompleteRate",align:"center",children:[],width:100}]}]},{title:`${d}年当月薪酬预算合计`,key:"list2",dataIndex:"salBudgetTotal",align:"center",children:[],width:100,render:l=>e.jsx("span",{children:a(l)})},{title:"基本保障薪酬（65%）",key:"list3",dataIndex:"basicSal",align:"center",children:[],width:100,render:l=>e.jsx("span",{children:a(l)})},{title:"政策提低保障薪酬",key:"list4",dataIndex:"salTd",align:"center",children:[],width:100,render:l=>e.jsx("span",{children:a(l)})},{title:"人才结构保障薪酬",key:"list5",dataIndex:"list5",align:"center",children:[{title:"挂钩人均创利薪酬（3%）",key:"",dataIndex:"",align:"center",children:[{title:"金额",key:"abilityPerAmount",dataIndex:"abilityPerAmount",align:"center",children:[],width:100,render:l=>e.jsx("span",{children:a(l)})},{title:"人均创利完成系数",key:"abilityPerPercent",dataIndex:"abilityPerPercent",align:"center",children:[],width:100},{title:"人均创利预算完成率",key:"abilityPerRate",dataIndex:"abilityPerRate",align:"center",children:[],width:100}]},{title:"挂钩市场化退出率薪酬(2%)",key:"orgDecMom",dataIndex:"orgDecMom",align:"center",children:[{title:"金额",key:"abilityQuitAmount",dataIndex:"abilityQuitAmount",align:"center",children:[],width:100,render:l=>e.jsx("span",{children:a(l)})},{title:"市场化退出率系数",key:"abilityQuitPercent",dataIndex:"abilityQuitPercent",align:"center",children:[],width:100},{title:"市场化退出率",key:"abilityQuitRate",dataIndex:"abilityQuitRate",align:"center",children:[],width:100}]}]},{title:"挂钩营业现金比率薪酬",key:"list6",dataIndex:"list6",align:"center",children:[{title:"挂钩营业现金比率(5%)",key:"orgIncMom",dataIndex:"orgIncMom",align:"center",children:[{title:"金额",key:"cashSalAmount",dataIndex:"cashSalAmount",align:"center",children:[],width:100,render:l=>e.jsx("span",{children:a(l)})},{title:"系数",key:"cashSalPercent",dataIndex:"cashSalPercent",align:"center",children:[],width:100},{title:"公众新增应收账款占收比完成率",key:"cashSalPubAddRcvRate",dataIndex:"cashSalPubAddRcvRate",align:"center",children:[],width:100},{title:"公众存量应收账款回款率完成率",key:"cashSalPubStockRcvRate",dataIndex:"cashSalPubStockRcvRate",align:"center",children:[],width:100},{title:"政企新增应收账款占收比完成率",key:"cashSalZqAddRcvRate",dataIndex:"cashSalZqAddRcvRate",align:"center",children:[],width:100},{title:"政企存量应收账款回款率完成率",key:"cashSalZqStockRcvRate",dataIndex:"cashSalZqStockRcvRate",align:"center",children:[],width:100},{title:"净预收账款占收比完成率",key:"cashSalAdvanceRcvRate",dataIndex:"cashSalAdvanceRcvRate",align:"center",children:[],width:100}]}]},{title:"挂钩业绩",key:"list7",dataIndex:"list7",align:"center",children:[{title:"挂钩业绩预算(10%)",key:"orgIncMom",dataIndex:"orgIncMom",align:"center",children:[{title:"金额",key:"achieveBudgetAmount",dataIndex:"achieveBudgetAmount",align:"center",children:[],width:100,render:l=>e.jsx("span",{children:a(l)})},{title:"系数",key:"achieveBudgetPercent",dataIndex:"achieveBudgetPercent",align:"center",children:[],width:100},{title:"联网通信收入完成率",key:"achieveBudgetRcvRate",dataIndex:"achieveBudgetRcvRate",align:"center",children:[],width:100},{title:"算网数智收入完成率",key:"achieveBudgetCiRcvRate",dataIndex:"achieveBudgetCiRcvRate",align:"center",children:[],width:100},{title:"经营利润完成率",key:"achieveBudgetProfitRate",dataIndex:"achieveBudgetProfitRate",align:"center",children:[],width:100}]},{title:"主营收入增量受益分享",key:"orgIncMom",dataIndex:"orgIncMom",align:"center",children:[{title:"金额",key:"achieveAddShareAmount",dataIndex:"achieveAddShareAmount",align:"center",children:[],width:100,render:l=>e.jsx("span",{children:a(l)})},{title:"市场份额系数Q",key:"achieveAddSharePercent",dataIndex:"achieveAddSharePercent",align:"center",children:[],width:100},{title:`${d-1}年市场份额`,key:"achieveAddShareLastMarketAmount",dataIndex:"achieveAddShareLastMarketAmount",align:"center",children:[],width:100},{title:`${d}年市场份额`,key:"achieveAddShareMarketAmount",dataIndex:"achieveAddShareMarketAmount",align:"center",children:[],width:100}]},{title:"终端收入增量收益分享",key:"orgIncMom",dataIndex:"",align:"center",children:[{title:"金额",key:"achieveTermAddShareAmount",dataIndex:"achieveTermAddShareAmount",align:"center",children:[],width:100,render:l=>e.jsx("span",{children:a(l)})}]},{title:"利润增量受益分享",key:"orgIncMom",dataIndex:"",align:"center",children:[{title:"金额",key:"achievePorfitAddShareAmount",dataIndex:"achievePorfitAddShareAmount",align:"center",children:[],width:100,render:l=>e.jsx("span",{children:a(l)})}]}]},{title:`${d}年当月其他追加预算`,key:"list8",dataIndex:"list8",align:"center",children:[{title:"合计",key:"otherBudgetTotal",dataIndex:"otherBudgetTotal",align:"center",width:100,render:l=>e.jsx("span",{children:a(l)})},{title:"专项奖励",key:"otherBudgetSpecialReward",dataIndex:"otherBudgetSpecialReward",align:"center",width:100,render:l=>e.jsx("span",{children:a(l)})},{title:"交通补助",key:"otherBudgetTransport",dataIndex:"otherBudgetTransport",align:"center",width:100,render:l=>e.jsx("span",{children:a(l)})},{title:"加班费核增（全年）",key:"otherBudgetOvertime",dataIndex:"otherBudgetOvertime",align:"center",width:100,render:l=>e.jsx("span",{children:a(l)})},{title:"过节费",key:"otherBudgetHoliday",dataIndex:"otherBudgetHoliday",align:"center",width:100,render:l=>e.jsx("span",{children:a(l)})},{title:"大学生租房补贴",key:"otherBudgetCollegeHouse",dataIndex:"otherBudgetCollegeHouse",align:"center",width:100,render:l=>e.jsx("span",{children:a(l)})},{title:"退职",key:"otherBudgetDismiss",dataIndex:"otherBudgetDismiss",align:"center",width:100,render:l=>e.jsx("span",{children:a(l)})},{title:"墩苗、下沉、值守人员（全年）",key:"otherBudgetDuty",dataIndex:"otherBudgetDuty",align:"center",width:100,render:l=>e.jsx("span",{children:a(l)})},{title:`${d}年大学生`,key:"otherBudgetGraduate",dataIndex:"otherBudgetGraduate",align:"center",width:100,render:l=>e.jsx("span",{children:a(l)})},{title:"融通外包费用",key:"otherBudgetOs",dataIndex:"otherBudgetOs",align:"center",width:100,render:l=>e.jsx("span",{children:a(l)})},{title:"离职补偿金",key:"otherBudgetQuit",dataIndex:"otherBudgetQuit",align:"center",width:100,render:l=>e.jsx("span",{children:a(l)})},{title:"其他",key:"otherBudgetOther",dataIndex:"otherBudgetOther",align:"center",width:100,render:l=>e.jsx("span",{children:a(l)})},{title:"转递薪酬",key:"otherBudgetTrans",dataIndex:"otherBudgetTrans",align:"center",width:100,render:l=>e.jsx("span",{children:a(l)})},{title:"取暖补贴",key:"otherBudgetWarm",dataIndex:"otherBudgetWarm",align:"center",width:100,render:l=>e.jsx("span",{children:a(l)})}]},{title:`${d}年当月薪酬总量兑现情况`,key:"list9",dataIndex:"list9",align:"center",children:[{title:`${d}年当月薪酬预算`,key:"salTotalDeliverBudget",dataIndex:"salTotalDeliverBudget",align:"center",width:100,render:l=>e.jsx("span",{children:a(l)})},{title:`${d}年当月实际兑现`,key:"salTotalDeliverReal",dataIndex:"salTotalDeliverReal",align:"center",width:100,render:l=>e.jsx("span",{children:a(l)})},{title:"结余",key:"salTotalDeliverBalance",dataIndex:"salTotalDeliverBalance",align:"center",width:100,render:l=>e.jsx("span",{children:a(l)})}]}],[P,F]=i.useState(C.filter(l=>["cityName","list1","list2"].includes(l.key)));i.useEffect(()=>(T(!1),v.current&&o(v.current.offsetHeight),he(),g(),window.addEventListener("resize",O),()=>{window.removeEventListener("resize",O)}),[]);const O=()=>{f()};i.useEffect(()=>{b>0&&g()},[b]),i.useEffect(()=>{F(C.filter(l=>["cityName","list1","list2"].includes(l.key))),z(E)},[d]),i.useEffect(()=>{setTimeout(()=>{f()},100)},[P]),i.useEffect(()=>{(u==null?void 0:u.length)>0&&f()},[u]),i.useEffect(()=>{(x==null?void 0:x.total)>0&&g()},[x.pageNum,x.pageSize]);const f=()=>{var n,t;const l=(document.querySelector(".salaryBudget_table .ant-table-header")||{}).offsetHeight||0,c=(document.querySelector(".salaryBudget_table .ant-table-pagination")||{}).offsetHeight||26;l&&c&&o(((n=j.current)==null?void 0:n.offsetHeight)-(N.current.offsetHeight+l+c)),B(((t=j.current)==null?void 0:t.offsetHeight)-N.current.offsetHeight)},he=async()=>{const[[l,c],[n,t]]=await Promise.all([Q({code:"1010",tag:1}),Q({code:"salaryBudgetMainList"})]);l||n||(c.STATUS==="0000"&&K(c.DATA),t.STATUS==="0000"&&ae(t.DATA))},g=async l=>{var G,V;T(!0);const c=x,n=m.getFieldsValue(),t=n==null?void 0:n.orgaId,D=(t==null?void 0:t.length)>0?t[(t==null?void 0:t.length)-1]:"",[ye,r]=await de({...c,orgaId:D,cycleId:(G=n==null?void 0:n.cycleId)==null?void 0:G.format("YYYYMM")});if(T(!1),ye){h.error((r==null?void 0:r.DATA)||(r==null?void 0:r.MESSAGE)||"调用失败");return}if(r.STATUS==="0000"){const{DATA:{data:pe}}=r,Se=pe.map((Z,we)=>({...Z,key:Z.id||we}));X(Se),H({...x,total:(V=r.DATA)==null?void 0:V.totalCount})}else h.error((r==null?void 0:r.MESSAGE)||(r==null?void 0:r.DATA)||"调用失败")},me=async()=>{var c;const l=m.getFieldsValue();if(l.cycleId){L(!0);const[n,t]=await re({cycleId:(c=l==null?void 0:l.cycleId)==null?void 0:c.format("YYYYMM")});if(L(!1),n){h.error((t==null?void 0:t.DATA)||(t==null?void 0:t.MESSAGE)||"调用失败");return}t.STATUS==="0000"?(h.success(t.DATA),g()):h.error((t==null?void 0:t.MESSAGE)||(t==null?void 0:t.DATA)||"调用失败")}else h.warning("请选择账期")},xe=async()=>{var c;const l=m.getFieldsValue();if(l.cycleId){Y(!0);const[n,t]=await oe({cycleId:(c=l==null?void 0:l.cycleId)==null?void 0:c.format("YYYYMM")});if(Y(!1),n){h.error((t==null?void 0:t.DATA)||(t==null?void 0:t.MESSAGE)||"调用失败");return}t.STATUS==="0000"?(h.success(t.DATA),g()):h.error((t==null?void 0:t.MESSAGE)||(t==null?void 0:t.DATA)||"调用失败")}else h.warning("请选择账期")},ue=()=>{const l=k().subtract(1,"month");m.setFieldsValue({monthId:l,org:""})},ve=l=>{console.log(l),s(k(l==null?void 0:l.cycleId).format("YYYY")),g()},z=l=>{const c=C.filter(t=>l.some(D=>D===t.key)),n=[{title:"单位",key:"cityName",dataIndex:"cityName",align:"center",fixed:"left",width:130},...c];F(n),ee(l)},je=async()=>{try{U("正在导出",0,"loading");const{orgaId:l,cycleId:c}=m.getFieldsValue();console.log("orgId",l);const n=await ne({orgaId:(l==null?void 0:l.length)>0?l[(l==null?void 0:l.length)-1]:"",cycleId:c==null?void 0:c.format("YYYYMM")});Ae(n)}catch(l){U("导出失败",1,"error"),console.error("Download failed:",l)}},ge=()=>{const l=b+1;m.resetFields(),ue(),ce(l)},Ne=l=>{const c={total:l==null?void 0:l.total,pageNum:l==null?void 0:l.current,pageSize:l==null?void 0:l.pageSize};H(c)},fe=(l,c)=>{var n,t;return(((n=c[0])==null?void 0:n.enumName)??"").toLowerCase().includes((t=l.toLowerCase())==null?void 0:t.trim())};return e.jsxs("div",{className:`pt-[0.5rem] px-[0rem] h-full flex flex-col ${I.employment_page}`,children:[e.jsx("div",{ref:v,className:"bg-white pt-2 px-8 mb-2 h-[2.6rem]",children:e.jsx(y,{form:m,initialValues:{tag:"",cycleId:k()},onFinish:ve,autoComplete:"off",children:e.jsxs(Ie,{gutter:24,children:[e.jsx(M,{span:6,children:e.jsx(y.Item,{label:"月份",name:"cycleId",wrapperCol:{span:20},children:e.jsx(Te,{className:"w-full",picker:"month",allowClear:!1})})}),e.jsx(M,{span:6,children:e.jsx(y.Item,{label:"单位",name:"orgaId",wrapperCol:{span:20},children:e.jsx(be,{allowClear:!0,changeOnSelect:!0,expandTrigger:"hover",displayRender:l=>l[l.length-1],options:J,fieldNames:{value:"enumId",label:"enumName",children:"children"},placeholder:"请选择单位",showSearch:{filter:fe},onSearch:l=>console.log(l)})})}),e.jsx(M,{span:12,children:e.jsx(y.Item,{labelCol:{span:0},wrapperCol:{span:24},children:e.jsxs(ke,{size:"small",children:[e.jsx(S,{type:"primary",htmlType:"submit",children:"查询"}),e.jsx(S,{htmlType:"button",onClick:()=>ge(),children:"重置"}),e.jsx(S,{danger:!0,ghost:!0,icon:e.jsx(Re,{}),onClick:()=>je(),children:"导出"})]})})})]})})}),e.jsxs("div",{ref:j,className:"relative bg-white px-5 pt-2.5",style:{height:`calc(100% - ${((q=v.current)==null?void 0:q.offsetHeight)+15}px)`},children:[e.jsxs("div",{ref:N,className:`flex justify-between items-center overflow-hidden mb-2 ${I.animation_box} ${_?"h-[1.6rem]":"h-0"}`,children:[e.jsxs("div",{className:"flex ",children:[_?e.jsx(Ce,{className:`${I.shousuo_icon} text-[1rem]`,onClick:()=>{$(!1),setTimeout(()=>{f()},200)}}):e.jsx(Pe,{className:`${I.shousuo_icon} absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10`,onClick:()=>{$(!0),setTimeout(()=>{f()},200)}}),e.jsxs("div",{className:"font-bold text-[0.8rem] ml-3",children:["数据列表",e.jsx("span",{className:"text-[0.7rem]",style:{color:"#939393"},children:"（单位：万元）"})]})]}),e.jsxs("div",{style:{display:"flex",justifyContent:"space-between"},children:[e.jsx(W,{placeholder:"请选择",className:"w-full",allowClear:!1,showSearch:!0,value:E,mode:"multiple",style:{marginRight:"0.4rem",width:"15rem"},maxTagCount:1,onChange:z,children:le.map(l=>{const{enumId:c,enumName:n}=l;return e.jsx(W.Option,{value:c,children:n},c)})}),e.jsx(S,{danger:!0,ghost:!0,onClick:()=>me(),loading:ie,style:{marginRight:"0.4rem"},children:"计算"}),e.jsx(S,{danger:!0,type:"primary",onClick:()=>{xe()},style:{marginLeft:"0.4rem"},loading:te,children:"确认"})]})]}),u.length===1?e.jsx(Ye,{tableData:u,tableHeight:A,columns:P,year:d}):e.jsx(Be,{className:"salaryBudget_table",rowClassName:(l,c)=>c%2===1?"customRow odd":"customRow even",columns:P,dataSource:u,loading:se,bordered:!0,scroll:{y:`calc(${R}px - 0.625rem - 1.6rem - 0.5rem)`},onChange:Ne,pagination:{...x,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50"]}})]})]})};export{Ue as default};
