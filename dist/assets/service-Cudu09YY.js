import{D as o}from"./index-De_f0oL2.js";const r={getQueryConditions:e=>o.get("/zhyy/getQueryConditions",{params:e}),queryList:e=>o.get("/zhyy/blue/sql/queryList",{params:e}),loginLog:e=>o.get("/zhyy/manager/extra/loginlog/list",{params:e}),loginlogDownload:e=>o.get("/zhyy/manager/extra/loginlog/download",{params:e,headers:{"Content-Type":"application/json;charset=UTF-8"},responseType:"blob"})};export{r as e};
