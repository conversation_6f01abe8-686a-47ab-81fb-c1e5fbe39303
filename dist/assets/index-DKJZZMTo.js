import{F as b,r as o,A as Se,u as S,d as c,j as e,R as J,C as Y,S as we,B as g,a4 as Te,T as h}from"./index-De_f0oL2.js";import{p as w}from"./service-B_wWoC3F.js";import{R as Ie,o as K,d as be}from"./down-BCLNnN1h.js";import{R as Ye}from"./index-BmnYJy3v.js";import{s as f}from"./index-BcPP1N8I.js";import{D as Ae}from"./index-CdSZ9YgQ.js";import{C as Ne}from"./index-DZyVV6rP.js";import{R as _e,a as Ce}from"./FullscreenOutlined-DzCTibKW.js";import{R as ke,U as ve,a as Re}from"./index-BH5uxjwl.js";import{M as Ee}from"./index-Dck5cc4J.js";import{P as De}from"./index-TkzW9Zmk.js";import{R as u}from"./InfoCircleOutlined-DUD7mNgc.js";import"./Table-D-iLeFE-.js";import"./index-BWJehDyc.js";import"./useMultipleSelect-B0dEIXT-.js";import"./DeleteOutlined-D-FcgX8f.js";const Fe="_detail_modal_19om6_2",Me="_salaryUsage_page_19om6_6",Ue="_animation_box_19om6_6",T={detail_modal:Fe,salaryUsage_page:Me,animation_box:Ue},{Dragger:Pe}=ve,tt=()=>{var O,$,H,z,G;const[m]=b.useForm(),N=o.useRef(null),_=o.useRef(null),C=o.useRef(null),[Q,W]=o.useState(0),[k,v]=o.useState(!0),[Z,R]=o.useState(!1),[X,A]=o.useState(!1),[x,ee]=o.useState([]),[te,ae]=o.useState([]),[ne,se]=o.useState([]),{currentUser:E}=Se(),[d,D]=o.useState({total:0,pageNum:1,pageSize:50}),{runAsync:F}=S(w.getEnumType,{manual:!0}),{runAsync:le}=S(w.getSalaryUsage,{manual:!0}),{runAsync:re}=S(w.downloadTemplate,{manual:!0}),{runAsync:oe}=S(w.exportSalaryUsage,{manual:!0}),{runAsync:ie}=S(w.importSalaryUsage,{manual:!0}),ce=[{title:"单位",dataIndex:"cityName",key:"cityName",align:"center",width:160,fixed:"left"},{title:"薪酬发放时间",dataIndex:"payTime",key:"payTime",align:"center",width:130},{title:e.jsxs(e.Fragment,{children:[e.jsxs("span",{children:["2025年薪酬",e.jsx("br",{}),"预算合计（当月口径）"]}),e.jsx(h,{title:"口径解释：按月填报完成情况；第一次分解1-3月数据。",children:e.jsx(u,{})})]}),dataIndex:"annualBudgetCurrent",key:"annualBudgetCurrent",align:"center",width:200},{title:e.jsxs(e.Fragment,{children:[e.jsx("span",{children:"基本工资 "}),e.jsx(h,{title:"包含薪资项：岗位工资、综合补贴、餐补、保留工资、补发工资、提低",children:e.jsx(u,{})})]}),dataIndex:"basicSalary",key:"basicSalary",align:"center",width:130},{title:e.jsxs(e.Fragment,{children:[e.jsx("span",{children:"津补贴 "}),e.jsx(h,{title:"过节费、交通费、高温津贴、取暖补贴、信访岗位津贴、技能津贴、租房补贴、特殊补贴、加班费、夜班费等",children:e.jsx(u,{})})]}),dataIndex:"allowances",key:"allowances",align:"center",width:130},{title:e.jsxs(e.Fragment,{children:[e.jsx("span",{children:"薪酬绩效 "}),e.jsx(h,{title:"不含增量收益分享、专项奖励、业绩奖励部分",children:e.jsx(u,{})})]}),key:"level",dataIndex:"level",align:"center",children:[{title:e.jsxs(e.Fragment,{children:[e.jsx("span",{children:"组织支撑岗 "}),e.jsx(h,{title:"岗位绩效",children:e.jsx(u,{})})]}),dataIndex:"orgSupportPosPerf",key:"orgSupportPosPerf",align:"center",width:130},{title:e.jsxs(e.Fragment,{children:[e.jsx("span",{children:"生产现业岗 "}),e.jsx(h,{title:"积分绩效",children:e.jsx(u,{})})]}),dataIndex:"prodOperPosPoints",key:"prodOperPosPoints",align:"center",width:130},{title:e.jsxs(e.Fragment,{children:[e.jsx("span",{children:"其他人员 "}),e.jsx(h,{title:"四级经理、营服中心、各部室下设基层责任单元负责人、高级经理、高级总监、24年新入职大学生、待岗及长病人员等",children:e.jsx(u,{})})]}),dataIndex:"otherPersonnel",key:"otherPersonnel",align:"center",width:130}]},{title:"增量收益分享",dataIndex:"incrementalSharing",key:"incrementalSharing",align:"center",width:130},{title:"专项奖励",key:"level",dataIndex:"level",align:"center",children:[{title:"本单位业绩相关",dataIndex:"unitPerformance",key:"unitPerformance",align:"center",width:150},{title:"荣誉/集团级",dataIndex:"honorGroupLevel",key:"honorGroupLevel",align:"center",width:150}]},{title:"业绩奖励",dataIndex:"performanceAward",key:"performanceAward",align:"center",width:130},{title:"其他：可补充",key:"level",dataIndex:"level",align:"center",children:[{title:"金额",dataIndex:"amount",key:"amount",align:"center",width:120},{title:"事项说明",dataIndex:"description",key:"description",align:"center",width:120}]}];o.useEffect(()=>(me(),L(),window.addEventListener("resize",M),()=>{window.removeEventListener("resize",M)}),[]);const M=()=>{y()};o.useEffect(()=>{y()},[(document.querySelector(".salaryUsage_table .ant-table-header")||{}).offsetHeight]);const y=()=>{var a;const t=(document.querySelector(".salaryUsage_table .ant-table-header")||{}).offsetHeight||0,n=(document.querySelector(".salaryUsage_table .ant-table-pagination")||{}).offsetHeight||26;t&&n&&W(((a=_.current)==null?void 0:a.offsetHeight)-(C.current.offsetHeight+t+n))},U=async t=>{var n;try{K("正在导出",0,"loading");let a=null;if(t===1)a=await re({templateId:"SUB_COMP_MONTHLY_SAL_USAGE"});else if(t===2){const{loginDate:s,cityId:r,category:i,cycleId:p,empId:j}=m.getFieldsValue(),l={beginTime:(s==null?void 0:s.length)>0?c(s[0]).format("YYYY-MM-DD"):null,endTime:(s==null?void 0:s.length)>0?c(s[1]).format("YYYY-MM-DD"):null,cycleId:p?(n=c(p))==null?void 0:n.format("YYYYMM"):"",cityId:r?r[(r==null?void 0:r.length)-1]:null,category:i,empId:j};a=await oe(l)}be(a)}catch(a){K("导出失败",1,"error"),console.error("Download failed:",a)}},de=t=>{I({...t})},me=async()=>{var r;const[[t,n],[a]]=await Promise.all([F({code:"1010",tag:1}),F({code:"EMPLOYEE_TRANSFER_CATEGORY"})]);if(t||a)return;n.STATUS==="0000"&&ae((r=n.DATA)==null?void 0:r.filter(i=>(i==null?void 0:i.orgId)!=="49757"));const s={...m.getFieldsValue()};I(s)},I=async t=>{var V,B;R(!0);const{loginDate:n,cityId:a,category:s,cycleId:r,empId:i}=t,p={...d,beginTime:(n==null?void 0:n.length)>0?c(n[0]).format("YYYY-MM-DD"):null,endTime:(n==null?void 0:n.length)>0?c(n[1]).format("YYYY-MM-DD"):null,cycleId:r?(V=c(r))==null?void 0:V.format("YYYYMM"):"",cityId:a?a[(a==null?void 0:a.length)-1]:null,category:s,empId:i},[j,l]=await le(p);if(R(!1),j){f.error((l==null?void 0:l.DATA)||(l==null?void 0:l.MESSAGE)||"调用失败");return}if(l.STATUS==="0000"){const{DATA:{data:fe}}=l,ye=fe.map((q,je)=>({...q,key:q.id||je}));se(ye),D({...d,total:(B=l.DATA)==null?void 0:B.totalCount})}else f.error((l==null?void 0:l.MESSAGE)||(l==null?void 0:l.DATA)||"调用失败")},he=async()=>{const t=new FormData;x.map(s=>s==null?void 0:s.originFileObj).forEach(s=>{t.append("file",s)});const[n,a]=await ie(t);if(n){f.error((a==null?void 0:a.DATA)||(a==null?void 0:a.MESSAGE)||"调用失败");return}if(a.STATUS==="0000"){const{loginDate:s,yearVal:r,cityId:i,category:p}=m.getFieldsValue(),j={beginTime:s.length>0?c(s[0]).format("YYYY-MM-DD"):null,endTime:s.length>0?c(s[1]).format("YYYY-MM-DD"):null,yearVal:r?c(r).format("YYYY"):"",cityId:i[(i==null?void 0:i.length)-1],category:p};y(),I(j),A(!1),f.success(a==null?void 0:a.DATA)}else f.error((a==null?void 0:a.MESSAGE)||(a==null?void 0:a.DATA)||"调用失败")},ue=()=>{(x==null?void 0:x.length)>0?he():f.error("请先选择文件上传")},P=()=>{A(!1)},xe=()=>{m.resetFields(),L()},pe=t=>{const n={total:t==null?void 0:t.total,pageNum:t==null?void 0:t.current,pageSize:t==null?void 0:t.pageSize};D(n)};o.useEffect(()=>{if((d==null?void 0:d.total)>0){const t={...m.getFieldsValue()};I(t)}},[d.pageNum,d.pageSize]);const L=()=>{const t=c().subtract(1,"month");m.setFieldsValue({cycleId:t})},ge=(t,n)=>{var a,s;return(((a=n[0])==null?void 0:a.enumName)??"").toLowerCase().includes((s=t.toLowerCase())==null?void 0:s.trim())};return e.jsxs("div",{className:`pt-[0.5rem] px-[0rem] h-full flex flex-col ${T.salaryUsage_page}`,children:[e.jsxs(e.Fragment,{children:[e.jsx("div",{ref:N,className:"bg-white pt-[0.5rem] px-[1.75rem] mb-[0.5rem]",children:e.jsx(b,{form:m,labelCol:{span:6},onFinish:de,initialValues:{loginDate:"",category:"KOU_JING_TIAO_ZHENG",cityId:"",empId:""},children:e.jsxs(J,{gutter:24,children:[e.jsx(Y,{span:6,children:e.jsx(b.Item,{name:"cycleId",label:"账期",className:"mb-[0.5rem]",children:e.jsx(Ae,{className:"w-full",picker:"month"})})}),e.jsx(Y,{span:5,children:e.jsx(b.Item,{name:"cityId",label:"单位",className:"mb-[0.5rem]",children:e.jsx(Ne,{allowClear:!0,changeOnSelect:!0,expandTrigger:"hover",displayRender:t=>t[t.length-1],options:te,fieldNames:{value:"enumId",label:"enumName",children:"children"},placeholder:"请选择单位",showSearch:{filter:ge},onSearch:t=>console.log(t)})})}),e.jsx(Y,{span:4,children:e.jsx("div",{className:"text-right",children:e.jsxs(we,{children:[e.jsx(g,{type:"primary",htmlType:"submit",children:"查询"}),e.jsx(g,{onClick:()=>xe(),children:"重置"})]})})})]})})}),e.jsxs("div",{ref:_,className:"relative bg-white px-5 pt-2.5",style:{height:`calc(100% - ${((O=N.current)==null?void 0:O.offsetHeight)+15}px)`},children:[e.jsxs("div",{ref:C,className:`flex justify-between items-center overflow-hidden mb-[0.1rem] ${T.animation_box} ${k?"h-[1.8rem]":"h-0"}`,children:[e.jsxs("div",{className:"flex ",children:[k?e.jsx(_e,{className:`${T.shousuo_icon} text-[1rem]`,onClick:()=>{v(!1),setTimeout(()=>{y()},200)}}):e.jsx(Ce,{className:`${T.shousuo_icon} absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10`,onClick:()=>{v(!0),setTimeout(()=>{y()},200)}}),e.jsx("div",{className:"font-bold text-[0.8rem] ml-3",children:"数据列表"})]}),e.jsx("div",{className:"flex gap-x-[2.5rem]",children:e.jsxs("div",{className:"flex gap-x-[0.75rem]",children:[!((H=($=E.roleInfo)==null?void 0:$.roleCode)!=null&&H.includes("ATJ0001"))&&e.jsxs("div",{className:"flex items-center gap-x-[0.25rem]",children:[e.jsx(Te,{name:"excel",width:20,height:20}),e.jsx("span",{className:"text-[#E60027] cursor-pointer",onClick:()=>U(1),children:"下载导入模版"})]}),!((G=(z=E.roleInfo)==null?void 0:z.roleCode)!=null&&G.includes("ATJ0001"))&&e.jsx(g,{danger:!0,ghost:!0,icon:e.jsx(ke,{}),onClick:()=>A(!0),children:"导入"}),e.jsx(g,{danger:!0,ghost:!0,icon:e.jsx(Ie,{}),onClick:()=>U(2),children:"导出"})]})})]}),e.jsx(Ye,{className:"salaryUsage_table",rowClassName:(t,n)=>n%2===1?"customRow odd":"customRow even",columns:ce,dataSource:ne,bordered:!0,scroll:{y:`calc(${Q}px - 0.625rem - 1.6rem - 0.5rem)`},loading:Z,onChange:pe,pagination:{...d,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50"]}})]})]}),e.jsx(Ee,{title:"文件上传",destroyOnClose:!0,open:X,centered:!0,className:T.detail_modal,footer:null,onCancel:P,children:e.jsxs("div",{className:"mt-4 mb-8",style:{marginBottom:"0px"},children:[e.jsx(J,{children:e.jsx(Y,{span:22,offset:1,className:"h-[10rem]",children:e.jsxs(Pe,{action:"",maxCount:1,multiple:!1,fileList:x,accept:".xls,.xlsx",beforeUpload(t,n){return console.log(t,n),!1},onChange(t){const{status:n}=t.file;n!=="uploading"&&(console.log(t.file,t.fileList),ee(t.fileList))},children:[e.jsx("p",{className:"ant-upload-drag-icon",children:e.jsx(Re,{style:{color:"#F14846"}})}),e.jsx("p",{className:"ant-upload-text",children:"点击或将文件拖拽到这里上传"}),e.jsx("p",{className:"ant-upload-hint",children:"支持excel格式的文件。"})]})})}),e.jsxs("div",{style:{display:"flex",justifyContent:"center",marginTop:"2rem"},children:[e.jsx(g,{danger:!0,onClick:P,children:"取消"}),e.jsx(De,{title:"",description:"导入文件如有重复数据，将会被覆盖，请确认是否上传。",onConfirm:ue,okText:"确认",cancelText:"取消",children:e.jsx(g,{danger:!0,type:"primary",style:{marginLeft:"1rem"},disabled:x.length<1,children:"上传"})})]})]})})]})};export{tt as default};
