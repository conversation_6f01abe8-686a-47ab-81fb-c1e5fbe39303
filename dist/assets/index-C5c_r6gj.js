import{D as I,F as r,r as o,u as f,d as et,j as t,R as M,C as i,y as le,S as se,B as S,a4 as oe,T as tt}from"./index-De_f0oL2.js";import{p as nt}from"./service-ClCV2GnI.js";import{s as T}from"./Index.module-B93HR2f3.js";import{R as at,o as re,d as lt}from"./down-BCLNnN1h.js";import{e as st}from"./service-DcPXuTuP.js";import{R as ot}from"./index-BmnYJy3v.js";import{C as rt}from"./index-DZyVV6rP.js";import{S as m}from"./index-BWJehDyc.js";import{R as it,a as dt}from"./FullscreenOutlined-DzCTibKW.js";import{R as mt,U as ct,a as pt}from"./index-BH5uxjwl.js";import{S as ht}from"./Table-D-iLeFE-.js";import{M as ut}from"./index-Dck5cc4J.js";import{T as xt}from"./index-CwwLkcJF.js";import{s as y}from"./index-BcPP1N8I.js";import"./useMultipleSelect-B0dEIXT-.js";import"./DeleteOutlined-D-FcgX8f.js";const N={getEmployeePostPag:d=>I.post("/zhyy/employee/getEmployeePostPag",d,{headers:{"Manager-Operate-Flag":2}}),downloadEmployeeTemplate:d=>I.post("/zhyy/employee/downloadEmployeeTemplate",d,{responseType:"blob",headers:{"Manager-Operate-Flag":2}}),exportEmployeeExcel:d=>I.post("/zhyy/employee/exportEmployeeExcel",d,{responseType:"blob",headers:{"Manager-Operate-Flag":2}}),downloadEmployeeCompare:d=>I.post("/zhyy/employee/downloadEmployeeCompare",d,{responseType:"blob",headers:{"Manager-Operate-Flag":2}}),uploadEmployeeExcel:d=>I.post("/zhyy/employee/uploadEmployeeExcel",d,{headers:{"Manager-Operate-Flag":2}}),updateEmployeePost:d=>I.post("/zhyy/employee/updateEmployeePost",d,{headers:{"Manager-Operate-Flag":2}})},{Dragger:gt}=ct,Rt=()=>{var te;const[d]=r.useForm(),[u]=r.useForm(),[U,W]=o.useState(!0),[ie,q]=o.useState(!1),[de,H]=o.useState(!1),[c,$]=o.useState(null),[me,v]=o.useState(!1),[b,ce]=o.useState([]),[k,pe]=o.useState([]),[he,Y]=o.useState([]),[ue,B]=o.useState([]),[xe,P]=o.useState([]),[ge,fe]=o.useState([]),[E,je]=o.useState([]),[ye,we]=o.useState([]),[Ie,Se]=o.useState([]),[Te,Ne]=o.useState([]),[be,ke]=o.useState([]),[w,G]=o.useState([]),[x,J]=o.useState({total:0,pageNum:1,pageSize:50}),K=o.useRef(null),Q=o.useRef(null),X=o.useRef(null),[Ae,Ee]=o.useState(0),{runAsync:g}=f(nt.getEnumType,{manual:!0}),{runAsync:Ce}=f(N.getEmployeePostPag,{manual:!0}),{runAsync:Fe}=f(N.downloadEmployeeTemplate,{manual:!0}),{runAsync:De}=f(N.downloadEmployeeCompare,{manual:!0}),{runAsync:Le}=f(N.exportEmployeeExcel,{manual:!0}),{runAsync:Re}=f(N.uploadEmployeeExcel,{manual:!0}),{runAsync:$e}=f(N.updateEmployeePost,{manual:!0}),{runAsync:ve}=f(st.build4LevelOrgTree,{manual:!0}),Pe={正式:"blue",紧密型外包:"cyan",其他:"orange"},_e=[{title:"员工编码",width:75,minWidth:75,dataIndex:"employeeId",key:"employeeId",align:"center",fixed:"left"},{title:"员工名称",width:75,minWidth:75,dataIndex:"employeeName",key:"employeeName",align:"center",fixed:"left"},{title:"所属单位",dataIndex:"orgName5",key:"orgName5",align:"center",width:150,minWidth:150,render:(e,n)=>t.jsx(tt,{title:e,children:t.jsx("div",{className:T.over_ellipsis,children:e})})},{title:"所属区域",dataIndex:"region",key:"region",align:"center",width:50,minWidth:50},{title:"年龄",dataIndex:"age",key:"age",align:"center",width:50,minWidth:50},{title:"性别",dataIndex:"gender",key:"gender",align:"center",width:50,minWidth:50},{title:"最高学历",dataIndex:"edu",key:"edu",align:"center",width:75,minWidth:75},{title:"用工类型",dataIndex:"employeeType",key:"employeeType",align:"center",width:75,minWidth:75,render:(e,n)=>t.jsx(xt,{color:Pe[e]||"purple",children:e})},{title:"岗位ID",dataIndex:"postId",key:"postId",align:"center",width:75,minWidth:75},{title:"岗位名称",dataIndex:"postName",key:"postName",align:"center",width:90,minWidth:90},{title:"岗级",dataIndex:"postLevel",key:"postLevel",align:"center",width:50,minWidth:50},{title:"备注1",dataIndex:"remarkOne",key:"remarkOne",align:"center",width:100,minWidth:100},{title:"备注2",dataIndex:"remarkTwo",key:"remarkTwo",align:"center",width:100,minWidth:100},{title:"备注3",dataIndex:"remarkThree",key:"remarkThree",align:"center",width:100,minWidth:100},{title:"操作",dataIndex:"action",key:"action",fixed:"right",align:"center",width:100,minWidth:100,render:(e,n)=>t.jsx("span",{className:"cursor-pointer",style:{color:"#F14846"},onClick:()=>Ue(n),children:"编辑"})}];o.useEffect(()=>(Ve(),window.addEventListener("resize",Z),()=>{window.removeEventListener("resize",Z)}),[]);const Z=()=>{C()};o.useEffect(()=>{if(!c&&(E==null?void 0:E.length)>0){const e={tag:"2",...d.getFieldsValue()};F(e)}},[c]),o.useEffect(()=>{C()},[(document.querySelector(".position_perFiliale_table .ant-table-header")||{}).offsetHeight,(document.querySelector(".position_perFiliale_table .ant-table-pagination")||{}).offsetHeight]);const C=()=>{var a;const e=(document.querySelector(".position_perFiliale_table .ant-table-header")||{}).offsetHeight||0,n=(document.querySelector(".position_perFiliale_table .ant-table-pagination")||{}).offsetHeight||26;e&&n&&Ee(((a=Q.current)==null?void 0:a.offsetHeight)-(X.current.offsetHeight+e+n))},_=async e=>{var n;try{re("正在导出",0,"loading");let a=null;if(e===1)a=await Fe({tag:"2"});else if(e===2){const l=d.getFieldsValue(),s=w?w[w.length-1]:{};a=await Le({...l,postName:(n=l==null?void 0:l.postName)==null?void 0:n.split(",")[0],org4:Number(s==null?void 0:s.level)===4?s==null?void 0:s.orgId:"",org5:Number(s==null?void 0:s.level)===5?s==null?void 0:s.orgId:"",org6:Number(s==null?void 0:s.level)===6?s==null?void 0:s.orgId:"",unitId:void 0,tag:"2"})}else e===3&&(a=await De({tag:"2"}));lt(a)}catch(a){re("导出失败",1,"error"),console.error("Download failed:",a)}},Oe=e=>{console.log("Success:",e),F({tag:"2",...e})},ze=async e=>{var l;console.log("Success:",e,e==null?void 0:e.postName.split(",")[1]),H(!0);const[n,a]=await $e({tag:"2",postId:e.postId.split(",")[0],postName:(l=e==null?void 0:e.postName)!=null&&l.includes(",")?e==null?void 0:e.postName.split(",")[1]:e==null?void 0:e.postName,postLevel:e==null?void 0:e.postLevel,remarkOne:e==null?void 0:e.remark1,remarkTwo:e==null?void 0:e.remark2,remarkThree:e==null?void 0:e.remark3,employeeId:c==null?void 0:c.employeeId});if(H(!1),n){y.error("修改失败");return}a.STATUS==="0000"?(a.DATA==="修改成功!"?y.success(a.DATA):y.error(a.DATA),$(null)):y.error(a==null?void 0:a.MESSAGE)},ee=async(e,n)=>{var h,p;let a=null;n?a=n:a=(p=(h=u.getFieldsValue())==null?void 0:h.postName)==null?void 0:p.split(",")[1];const[l,s]=await g({code:"1020",ename:a});l||s.STATUS==="0000"&&Y(s.DATA)},O=async e=>{var s;let n=null;if(!e){const h=(s=d.getFieldsValue())==null?void 0:s.postName;n=k==null?void 0:k.find(p=>`${p==null?void 0:p.enumName},${p==null?void 0:p.region}`===h)}const[a,l]=await g({code:"1021",region:e||(n==null?void 0:n.enumId)});a||l.STATUS==="0000"&&(e?P(l.DATA):B(l.DATA))},Ve=async()=>{var ae;const[[e,n],[a,l],[s,h],[p,A],[z,D],[L,R],[Xe,ne]]=await Promise.all([g({code:"1020",region:""}),g({code:"1021",region:""}),ve({monthId:et().format("YYYYMM"),tag:"1"}),g({code:"1009",region:""}),g({code:"1006",region:""}),g({code:"1007",region:""}),g({code:"1008",region:""})]);if(e||a||s||p||z||L||Xe)return;n.STATUS==="0000"&&(pe(n.DATA),Y(l.DATA)),l.STATUS==="0000"&&(B(l.DATA),P(l.DATA)),h.STATUS==="0000"&&fe((ae=h.DATA)==null?void 0:ae.filter(V=>(V==null?void 0:V.orgId)==="49757")),A.STATUS==="0000"&&je(A.DATA),D.STATUS==="0000"&&we(D.DATA),R.STATUS==="0000"&&Se(R.DATA),ne.STATUS==="0000"&&Ne(ne.DATA);const Ze={tag:"2",...d.getFieldsValue()};F(Ze)},F=async e=>{var s,h,p,A;q(!0);const n=w?w[w.length-1]:{},[a,l]=await Ce({...e,...x,code:(s=e==null?void 0:e.code)==null?void 0:s.trim(),name:(h=e==null?void 0:e.name)==null?void 0:h.trim(),postName:(p=e==null?void 0:e.postName)==null?void 0:p.split(",")[0],org4:Number(n==null?void 0:n.level)===4?n==null?void 0:n.orgId:"",org5:Number(n==null?void 0:n.level)===5?n==null?void 0:n.orgId:"",org6:Number(n==null?void 0:n.level)===6?n==null?void 0:n.orgId:"",unitId:void 0});if(q(!1),!a&&l.STATUS==="0000"){const{DATA:{data:z}}=l,D=z.map((L,R)=>({...L,key:L.id||R}));ke(D),J({...x,total:(A=l.DATA)==null?void 0:A.totalCount})}},Me=async()=>{const e=new FormData;b.map(l=>l==null?void 0:l.originFileObj).forEach(l=>{e.append("file",l)}),e.append("tag","2");const[n,a]=await Re(e);n||(a.STATUS==="0000"?(v(!1),y.success(a==null?void 0:a.DATA)):y.error(a==null?void 0:a.MESSAGE))},Ue=e=>{$(e),ee(2,e==null?void 0:e.postName),O(e==null?void 0:e.postId),u.setFieldsValue({...e,remark1:(e==null?void 0:e.remarkOne)||(e==null?void 0:e.remark1),remark2:(e==null?void 0:e.remarkTwo)||(e==null?void 0:e.remark2),remark3:(e==null?void 0:e.remarkThree)||(e==null?void 0:e.remark3)})},We=()=>{(b==null?void 0:b.length)>0?Me():y.error("请先选择文件上传")},qe=()=>{v(!1)},He=()=>{G([]),d.resetFields()},Ye=e=>{const n={total:e==null?void 0:e.total,pageNum:e==null?void 0:e.current,pageSize:e==null?void 0:e.pageSize};J(n)};o.useEffect(()=>{if((x==null?void 0:x.total)>0){const e={tag:"2",...d.getFieldsValue()};F(e)}},[x.pageNum,x.pageSize]);const j=(e,n)=>{const a=e.trim();return n.children.toLowerCase().includes(a.toLowerCase())},Be=()=>{d.setFieldsValue({level:""}),O()},Ge=()=>{u.setFieldsValue({postLevel:null,postId:null}),P([]),ee()},Je=()=>{var e,n;u.setFieldsValue({postLevel:null}),O((n=(e=u.getFieldsValue())==null?void 0:e.postId)==null?void 0:n.split(",")[0])},Ke=(e,n)=>{G(n)},Qe=(e,n)=>n.some(a=>a.orgName.toLowerCase().indexOf(e.trim().toLowerCase())>-1);return t.jsxs("div",{className:`pt-[0.5rem] px-[0rem] h-full flex flex-col ${T.position_page}`,children:[c?t.jsx("div",{className:"bg-white h-[calc(100%-1rem)] pt-[1rem] px-[2.8rem]",children:t.jsxs(ht,{spinning:de,children:[t.jsx("div",{className:"text-[0.8rem] font-bold mb-[0.8rem]",children:"员工信息"}),t.jsx(r,{form:u,labelCol:{span:6},onFinish:ze,initialValues:{...c},children:t.jsxs(M,{children:[t.jsx(i,{span:6,children:t.jsx(r.Item,{name:"employeeName",label:"员工姓名",children:t.jsx("span",{children:c.employeeName})})}),t.jsx(i,{span:6,children:t.jsx(r.Item,{name:"employeeId",label:"员工编号",children:t.jsx("span",{children:c.employeeId})})}),t.jsx(i,{span:6,children:t.jsx(r.Item,{name:"unit",label:"所属单位",children:t.jsx("span",{children:c.orgName5})})}),t.jsx(i,{span:6,children:t.jsx(r.Item,{name:"region",label:"所属区域",children:t.jsx("span",{children:c.region})})}),t.jsx(i,{span:6,children:t.jsx(r.Item,{name:"age",label:"年龄",children:t.jsx("span",{children:c.age})})}),t.jsx(i,{span:6,children:t.jsx(r.Item,{name:"gender",label:"性别",children:t.jsx("span",{children:c.gender})})}),t.jsx(i,{span:6,children:t.jsx(r.Item,{name:"edu",label:"学历",children:t.jsx("span",{children:c.edu})})}),t.jsx(i,{span:6,children:t.jsx(r.Item,{name:"employeeType",label:"用工类型",children:t.jsx("span",{children:c.employeeType})})}),t.jsx(i,{span:6,children:t.jsx(r.Item,{name:"postName",label:"岗位名称",rules:[{required:!0,message:"请选择岗位名称!"}],children:t.jsx(m,{placeholder:"请选择岗位名称",showSearch:!0,allowClear:!0,filterOption:j,onChange:Ge,children:k.map(e=>{const{region:n,enumId:a,enumName:l}=e;return t.jsx(m.Option,{value:`${a},${l}`,children:l},`${l},${n}`)})})})}),t.jsx(i,{span:6,children:t.jsx(r.Item,{name:"postId",label:"岗位ID",rules:[{required:!0,message:"请选择岗位ID!"}],children:t.jsx(m,{placeholder:"请选择岗位ID",allowClear:!0,disabled:!u.getFieldValue("postName"),showSearch:!0,filterOption:j,onChange:Je,children:he.map(e=>{const{enumId:n,enumName:a,region:l}=e;return t.jsx(m.Option,{value:`${n},${a}`,children:n},`${a},${l}`)})})})}),t.jsx(i,{span:6,children:t.jsx(r.Item,{name:"postLevel",label:"岗级",rules:[{required:!0,message:"请选择岗级!"}],children:t.jsx(m,{placeholder:"请选择岗级",allowClear:!0,showSearch:!0,disabled:!u.getFieldValue("postId"),filterOption:j,children:xe.map(e=>{const{enumId:n,enumName:a}=e;return t.jsx(m.Option,{value:n,children:a},n)})})})}),t.jsx(i,{span:6}),t.jsx(i,{span:6,children:t.jsx(r.Item,{name:"remark1",label:"备注1",rules:[{required:!0,message:"请选择备注1!"}],children:t.jsx(m,{placeholder:"请选择",allowClear:!0,showSearch:!0,filterOption:j,children:ye.map(e=>{const{enumId:n,enumName:a}=e;return t.jsx(m.Option,{value:a,children:a},n)})})})}),t.jsx(i,{span:6,children:t.jsx(r.Item,{name:"remark2",label:"备注2",rules:[{required:!1,message:"请选择备注2!"}],children:t.jsx(m,{placeholder:"请选择",allowClear:!0,showSearch:!0,filterOption:j,children:Ie.map(e=>{const{enumId:n,enumName:a}=e;return t.jsx(m.Option,{value:a,children:a},n)})})})}),t.jsx(i,{span:6,children:t.jsx(r.Item,{name:"remark3",label:"备注3",rules:[{required:!1,message:"请选择备注3!"}],children:t.jsx(m,{placeholder:"请选择",allowClear:!0,showSearch:!0,filterOption:j,children:Te.map(e=>{const{enumId:n,enumName:a}=e;return t.jsx(m.Option,{value:a,children:a},n)})})})}),t.jsx(i,{span:24,className:"text-center mt-[2rem]",children:t.jsxs(se,{children:[t.jsx(S,{onClick:()=>{$(null),u.resetFields()},children:"返回"}),t.jsx(S,{type:"primary",htmlType:"submit",children:"保存"})]})})]})})]})}):t.jsxs(t.Fragment,{children:[t.jsx("div",{ref:K,className:"bg-white pt-[0.2rem] px-[0.75rem] mb-[0.5rem]",children:t.jsx(r,{form:d,labelCol:{span:6},onFinish:Oe,initialValues:{},children:t.jsxs(M,{gutter:24,children:[t.jsx(i,{span:6,children:t.jsx(r.Item,{name:"unitId",label:"组织",children:t.jsx(rt,{allowClear:!0,changeOnSelect:!0,expandTrigger:"hover",displayRender:e=>e[e.length-1],options:ge,onChange:Ke,fieldNames:{value:"orgId",label:"orgName",children:"children"},placeholder:"请选择组织",showSearch:{filter:Qe},onSearch:e=>console.log(e)})})}),t.jsx(i,{span:6,children:t.jsx(r.Item,{name:"code",label:"员工编码",children:t.jsx(le,{placeholder:"请输入员工编码",autoComplete:"off",allowClear:!0})})}),t.jsx(i,{span:6,children:t.jsx(r.Item,{name:"name",label:"员工姓名",children:t.jsx(le,{placeholder:"请输入员工姓名",autoComplete:"off",allowClear:!0})})}),t.jsx(i,{span:6,children:t.jsx(r.Item,{name:"type",label:"用工类型",children:t.jsx(m,{placeholder:"请选择用工类型",allowClear:!0,children:E.map(e=>{const{enumId:n,enumName:a}=e;return t.jsx(m.Option,{value:a,children:a},n)})})})}),t.jsx(i,{span:6,children:t.jsx(r.Item,{name:"postName",label:"岗位",children:t.jsx(m,{placeholder:"请选择岗位",showSearch:!0,allowClear:!0,onChange:Be,filterOption:j,children:k.map(e=>{const{region:n,enumName:a}=e;return t.jsx(m.Option,{value:`${a},${n}`,children:a},`${a},${n}`)})})})}),t.jsx(i,{span:6,children:t.jsx(r.Item,{name:"level",label:"岗级",children:t.jsx(m,{placeholder:"请选择岗级",showSearch:!0,allowClear:!0,children:ue.map(e=>{const{enumId:n,enumName:a}=e;return t.jsx(m.Option,{value:n,children:a},n)})})})}),t.jsx(i,{span:12,children:t.jsx("div",{className:"text-right",children:t.jsxs(se,{children:[t.jsx(S,{type:"primary",htmlType:"submit",children:"查询"}),t.jsx(S,{onClick:()=>He(),children:"重置"}),t.jsx(S,{icon:t.jsx(at,{}),onClick:()=>_(2),children:"导出"})]})})})]})})}),t.jsxs("div",{ref:Q,className:"relative bg-white px-5 pt-2.5",style:{height:`calc(100% - ${((te=K.current)==null?void 0:te.offsetHeight)+15}px)`},children:[t.jsxs("div",{ref:X,className:`flex justify-between items-center mb-2 overflow-hidden ${T.animation_box} ${U?"h-[1.6rem]":"h-0"}`,children:[t.jsxs("div",{className:"flex ",children:[U?t.jsx(it,{className:`${T.shousuo_icon} text-[1rem]`,onClick:()=>{W(!1),setTimeout(()=>{C()},200)}}):t.jsx(dt,{className:`${T.shousuo_icon} absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10`,onClick:()=>{W(!0),setTimeout(()=>{C()},200)}}),t.jsx("div",{className:"font-bold text-[0.8rem] ml-3",children:"数据列表"})]}),t.jsxs("div",{className:"flex gap-x-[0.75rem]",children:[t.jsxs("div",{className:"flex items-center gap-x-[0.25rem]",children:[t.jsx(oe,{name:"excel",width:20,height:20}),t.jsx("span",{className:"text-[#E60027] cursor-pointer",onClick:()=>_(1),children:"下载导入模版"})]}),t.jsxs("div",{className:"flex items-center gap-x-[0.25rem] ml-[0.4rem]",children:[t.jsx(oe,{name:"excel",width:20,height:20}),t.jsx("span",{className:"text-[#E60027] cursor-pointer",onClick:()=>_(3),children:"下载对照表"})]}),t.jsx(S,{className:"ml-[0.4rem]",danger:!0,ghost:!0,icon:t.jsx(mt,{}),onClick:()=>v(!0),children:"导入"})]})]}),t.jsx(ot,{className:"position_perFiliale_table",rowClassName:(e,n)=>n%2===1?"customRow odd":"customRow even",columns:_e,dataSource:be,bordered:!0,scroll:{y:`calc(${Ae}px - 0.625rem - 1.6rem - 0.5rem)`},loading:ie,onChange:Ye,pagination:{...x,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50"]}})]})]}),t.jsx(ut,{title:"文件上传",destroyOnClose:!0,open:me,centered:!0,className:T.detail_modal,okText:"上传",onOk:We,onCancel:qe,children:t.jsx("div",{className:"mt-4 mb-8",children:t.jsx(M,{children:t.jsx(i,{span:22,offset:1,className:"h-[10rem]",children:t.jsxs(gt,{action:"",maxCount:1,multiple:!1,fileList:b,beforeUpload(e,n){return console.log(e,n),!1},onChange(e){const{status:n}=e.file;n!=="uploading"&&(console.log(e.file,e.fileList),ce(e.fileList))},children:[t.jsx("p",{className:"ant-upload-drag-icon",children:t.jsx(pt,{style:{color:"#F14846"}})}),t.jsx("p",{className:"ant-upload-text",children:"点击或将文件拖拽到这里上传"}),t.jsx("p",{className:"ant-upload-hint",children:"支持excel格式的文件。"})]})})})})})]})};export{Rt as default};
