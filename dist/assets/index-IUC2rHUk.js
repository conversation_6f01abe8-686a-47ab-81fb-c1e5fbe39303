import{D as d,F as o,r as t,j as e,y as R,B as f,cf as xs,u as Ss,R as bs,C as F,O as Cs,ce as js,T as Ts}from"./index-De_f0oL2.js";import{s as i}from"./index-BcPP1N8I.js";import{S as V}from"./index-BWJehDyc.js";import{d as ws}from"./utils-BHq6Um7U.js";import{e as Us}from"./service-DcPXuTuP.js";import{s as As}from"./Index.module-C_CsuTds.js";import{R as Y,F as Os,e as Es}from"./Table-D-iLeFE-.js";import{M as N}from"./index-Dck5cc4J.js";import{R as Rs}from"./ExclamationCircleOutlined-CkUVbQBi.js";import{T as Ls}from"./index-BxLWNlAG.js";import{P as Ns}from"./index-TkzW9Zmk.js";import"./useMultipleSelect-B0dEIXT-.js";const m={getQueryConditions:r=>d.get("/zhyy/getQueryConditions",{params:r}),queryAllEnableRole:r=>d.get("/zhyy/manager/core/role/queryAllEnableRole",r),queryUserInfo:r=>d.post("/zhyy/extra/manager/user/queryUserInfo",r),addUser:r=>d.post("/zhyy/manager/core/user/addUser",r),setUserRole:r=>d.post("/zhyy/manager/core/role/setUserRole",r),userListSetRole:r=>d.post("/zhyy/manager/extra/role/userListSetRole",r),queryUserRole:r=>d.post("/zhyy/manager/core/role/queryUserRole",r),deleteUser:r=>d.post("/zhyy/manager/core/user/deleteUser",r),updateUser:r=>d.post("/zhyy/manager/core/user/updateUser",r),buildUserOrgSelectTree:r=>d.post("/zhyy/manager/core/org/buildUserOrgSelectTree",r),setUserOrg:r=>d.post("/zhyy/manager/core/org/setUserOrg",r),buildUserOrgAuthSelectTree:r=>d.post("/zhyy/manager/core/org/buildUserOrgAuthSelectTree",r),setUserOrgAuth:r=>d.post("/zhyy/manager/core/org/setUserOrgAuth",r),extraEnableUser:r=>d.post("/zhyy/extra/manager/user/extraEnableUser",r),extraDisableUser:r=>d.post("/zhyy/extra/manager/user/extraDisableUser",r),exportUserInfo:r=>d.get("/zhyy/extra/manager/user/exportUserInfo",{params:r,headers:{"Content-Type":"application/json;charset=UTF-8"},responseType:"blob"})},ks=({userType:r,userInfo:u,cancelAddModal:A})=>{const[w]=o.useForm(),[O,b]=t.useState(!1);t.useEffect(()=>{if(r==="edit"){const{userName:x,fullName:C,phoneNumber:j,oa:n}=u;w.setFieldsValue({account:x,fullName:C,phoneNumber:j,oa:n})}},[]);const E=x=>{var T;const{account:C,fullName:j,password:n,phoneNumber:h,oa:p}=x,y={userName:C,fullName:j,phoneNumber:h,oa:p};r==="add"&&(y.password=(T=xs)==null?void 0:T.MD5(n).toString()),r==="edit"&&(y.userId=u==null?void 0:u.userKey);const S=r==="add"?m.addUser:m.updateUser;b(!0),S(y).then(k=>{const B=k[1];B.STATUS==="0000"?(i.success(`${r==="add"?"新增":"修改"}用户成功！`),A()):i.error(B.MESSAGE),b(!1)}).catch(()=>{b(!1)})};return e.jsxs(o,{form:w,labelCol:{span:6},wrapperCol:{span:14},labelAlign:"right",onFinish:E,children:[e.jsx(o.Item,{label:"账号",name:"account",rules:[{required:!0,message:"请输入账号!"}],children:e.jsx(R,{placeholder:"请输入账号!",disabled:r==="edit"})}),e.jsx(o.Item,{label:"姓名",name:"fullName",rules:[{required:!0,message:"请输入姓名!"}],children:e.jsx(R,{placeholder:"请输入姓名!",disabled:r==="edit"})}),r==="add"&&e.jsx(o.Item,{label:"密码",name:"password",rules:[{required:!0,validator:(x,C)=>new RegExp("(?=.*[0-9])(?=.*[A-Z])(?=.*[a-z])(?=.*[^a-zA-Z0-9]).{8,30}").test(C)?Promise.resolve():(Promise.resolve(),Promise.reject("8-16位字符，必须包括大小写字母、数字和特殊符号"))}],children:e.jsx(R.Password,{placeholder:"请输入密码!",autoComplete:"new-password"})}),e.jsx(o.Item,{label:"电话号码",name:"phoneNumber",rules:[{required:!0,message:"请输入电话号码!"}],children:e.jsx(R,{placeholder:"请输入电话号码!",disabled:r==="edit"})}),e.jsx(o.Item,{label:"员工编码",name:"oa",rules:[{required:!0,message:"请输入员工编码!"}],children:e.jsx(R,{placeholder:"请输入员工编码"})}),e.jsx(o.Item,{wrapperCol:{offset:6,span:14},children:e.jsx(f,{loading:O,type:"primary",htmlType:"submit",children:"确定"})})]})},vs=({roleList:r,roleParams:u,cancelRoleModal:A})=>{const[w]=o.useForm(),[O,b]=t.useState(!1),[E,x]=t.useState(!1);t.useEffect(()=>{typeof u=="string"&&C()},[]);const C=()=>{const n={userKey:u};b(!0),m.queryUserRole(n).then(h=>{var y;const p=h[1];if(p.STATUS==="0000"){const S=(y=p.DATA)==null?void 0:y.map(T=>T==null?void 0:T.roleCode);w.setFieldsValue({roleCodes:S})}else i.error(p.MESSAGE);b(!1)}).catch(()=>{b(!1)})},j=n=>{let h={};h[`${typeof u=="string"?"roleCodes":"roleCode"}`]=n==null?void 0:n.roleCodes,h[`${typeof u=="string"?"userKey":"list"}`]=u;const p=typeof u=="string"?m.setUserRole:m.userListSetRole;x(!0),p(h).then(y=>{const S=y[1];S.STATUS==="0000"?(i.success("修改角色成功"),A()):i.error(S.MESSAGE),x(!1)}).catch(()=>{x(!1)})};return e.jsxs(o,{form:w,labelCol:{span:4},wrapperCol:{span:19},labelAlign:"right",onFinish:j,children:[e.jsx(o.Item,{label:"角色",name:"roleCodes",children:e.jsx(V,{placeholder:"请选择用户角色",loading:O,mode:"multiple",allowClear:!0,showSearch:!0,getPopupContainer:n=>n.parentElement||document.body,filterOption:(n,h)=>h.children.toLowerCase().includes(n.toLowerCase()),style:{width:"100%"},children:r.map(n=>{const{roleCode:h,roleName:p}=n;return e.jsx(V.Option,{value:h,children:p},h)})})}),e.jsx(o.Item,{wrapperCol:{offset:4,span:19},children:e.jsx(f,{loading:E,type:"primary",htmlType:"submit",children:"确定"})})]})},Xs=()=>{const[r,u]=t.useState(!0),[A,w]=t.useState(""),[O,b]=t.useState(""),[E,x]=t.useState(""),[C,j]=t.useState([]),[n,h]=t.useState(""),[p,y]=t.useState(""),[S,T]=t.useState([]),[k,B]=t.useState(""),[v,fe]=t.useState("1"),[ye,G]=t.useState(!1),[xe,P]=t.useState(!1),[ee,Se]=t.useState(0),[be,Ce]=t.useState([]),[z,je]=t.useState(1),[D,Te]=t.useState(10),[we,Ue]=t.useState(0),[se,te]=t.useState([]),[re,ae]=t.useState([]),[c,K]=t.useState({}),[Ae,$]=t.useState(!1),[le,oe]=t.useState(""),[Oe,I]=t.useState(!1),[Ee,ne]=t.useState(""),[ie,Q]=t.useState(null),[Re,J]=t.useState(!1),[Le,Z]=t.useState(!1),[Ne,ce]=t.useState(!1),[ke,zs]=t.useState(""),[ve,Ds]=t.useState(!1),[ze,_]=t.useState(!1),[de,De]=t.useState([]),[Ke,Ks]=t.useState([]),[Ie,Me]=t.useState([]),[qe,ue]=t.useState(!1),[Fe,Is]=t.useState(""),[Ve,Ms]=t.useState(!1),[Be,H]=t.useState(!1),[he,Ge]=t.useState([]),[Pe,qs]=t.useState([]),[$e,Qe]=t.useState([]),{runAsync:Je}=Ss(Us.build4LevelOrgTree2,{manual:!0});t.useEffect(()=>{_e(),(async()=>await Ze())()},[]),t.useEffect(()=>{},[]);const Ze=async()=>{const s={menuType:"",centerType:"UserManager",tag:"1"},[a,l]=await Je(s);if(!a&&l.STATUS==="0000"){const g=l.DATA;j(g),M()}},_e=()=>{m.queryAllEnableRole({}).then(s=>{const a=s[1];a.STATUS==="0000"?T(a.DATA):i.error(a.MESSAGE)})},M=s=>{Ue(l=>l+1);const a={queryName:A,areaCode:O,areaLevel:E,districtName:n,roleCode:p,sourceSystem:k,enable:v,pageNum:z,pageSize:D};r||P(!0),m.queryUserInfo(a).then(l=>{const g=l[1];if(g.STATUS==="0000"){const{data:L,totalCount:W}=g.DATA,q=L.map((pe,ge)=>{const{roleDOList:X}=pe;return{...pe,key:ge,num:(z-1)*D+ge+1,roleName:X==null?void 0:X.map(ys=>ys.roleName).join(",")}});Se(W),Ce(q)}else i.error(g.MESSAGE);r||P(!1),r&&u(!1)}).catch(()=>{r||P(!1),r&&u(!1)})},He=s=>{const{userKey:a,enable:l}=s,g={userKey:a};(l==="0"?m.extraEnableUser:m.extraDisableUser)(g).then(W=>{const q=W[1];q.STATUS==="0000"?(i.success("切换成功！"),U()):i.error(q.MESSAGE)})},We=s=>{w(s.target.value)},Xe=s=>{y(s)};t.useEffect(()=>{v!=="1"&&U()},[v]);const Ye=s=>{fe(s.target.value)},es=()=>{const s={queryName:A,areaCode:O,areaLevel:E,roleCode:p,sourceSystem:k,enable:v};i.warning("文件正在导出，请稍后......"),G(!0),m.exportUserInfo(s).then(a=>{var L;const l=a[1];if(!Error)return;let g=((L=l.headers["content-disposition"])==null?void 0:L.match(/filename=(.*)/)[1])||new Date().valueOf();g=`${decodeURI(g)}`,ws({fileName:g,res:l.data}),G(!1)}).catch(()=>{G(!1)})},me=(s,a)=>{$(!0),oe(s),s==="edit"&&K(a)},ss=()=>{$(!1),oe(""),K({}),M()},ts=()=>{if(se.length===0&&re.length===0){i.warning("没有勾选角色，请先勾选要操作的角色！");return}I(!0),ne("批量修改用户角色"),Q(re)},rs=()=>{I(!1),Q(null),typeof ie!="string"&&(te([]),ae([])),U()},as=s=>{J(!0),K(s)},ls=()=>{const s={userKey:c==null?void 0:c.userKey};Z(!0),m.deleteUser(s).then(a=>{const l=a[1];l.STATUS==="0000"?(i.success("删除用户成功！"),J(!1),K({}),U()):i.error(l.MESSAGE),Z(!1)}).catch(()=>{Z(!1)})},os=s=>{const{userKey:a,fullName:l}=s;I(!0),ne(l),Q(a)},ns=()=>{const s={userKey:c==null?void 0:c.userKey,orgCodeList:de};_(!0),m.setUserOrg(s).then(a=>{const l=a[1];l.STATUS==="0000"?(i.success("修改所属组织成功！"),ce(!1),U()):i.error(l.MESSAGE),_(!1)}).catch(()=>{_(!1)})},is=s=>{De([s])},cs=s=>{Me(s)},ds=()=>{const s={userKey:c==null?void 0:c.userKey,orgCodeList:he};H(!0),m.setUserOrgAuth(s).then(a=>{const l=a[1];l.STATUS==="0000"?(i.success("修改组织授权成功！"),ue(!1),U()):i.error(l.MESSAGE),H(!1)}).catch(()=>{H(!1)})},us=s=>{Ge(s)},hs=s=>{Qe(s)};t.useEffect(()=>{we>0&&M()},[D,z]);const U=()=>{M()},ms=(s,a)=>{je(s),Te(a)},ps={onChange:(s,a)=>{te(s),ae(a.map(l=>l.userName))},selectedRowKeys:se},gs=[{title:"序号",key:"num",dataIndex:"num",width:100,align:"center"},{title:"账号",key:"userName",dataIndex:"userName",width:120,align:"center"},{title:"姓名",key:"fullName",dataIndex:"fullName",width:120,align:"center"},{title:"是否启用",key:"enable",dataIndex:"enable",width:150,align:"center",render:(s,a)=>e.jsx(Ns,{title:"确定切换启用状态?",onConfirm:()=>He(a),okText:" 确认",cancelText:"取消",children:e.jsx(js,{checked:s==="1"})})},{title:"角色",key:"roleName",dataIndex:"roleName",width:150,align:"center",ellipsis:{showTitle:!1},render:s=>e.jsx(Ts,{placement:"topLeft",title:s,getPopupContainer:a=>(a==null?void 0:a.closest("div.ant-table-body"))||document.body,children:s})},{title:"操作",width:300,fixed:"right",align:"center",render:(s,a)=>{const{sourceSystem:l}=a;return e.jsxs("div",{children:[e.jsx(f,{size:"small",type:"link",onClick:()=>me("edit",a),children:"修改"}),e.jsx(f,{size:"small",type:"link",disabled:l!=="20",onClick:()=>as(a),children:"删除"}),e.jsx(f,{size:"small",type:"link",onClick:()=>os(a),children:"角色"})]})}}],[fs]=o.useForm();return e.jsxs("div",{className:`pt-[0.5rem] px-[0rem] h-full ${As.rbac_page}`,children:[e.jsx("div",{className:"bg-white pt-[0.5rem] px-[1.75rem] mb-[0.5rem]",children:e.jsx(o,{form:fs,labelCol:{span:6},children:e.jsxs(bs,{gutter:24,children:[e.jsx(F,{span:6,children:e.jsx(o.Item,{name:"code",label:"账号",children:e.jsx(R,{placeholder:"请输入姓名或账号",allowClear:!0,onChange:We,style:{width:"100%"}})})}),e.jsx(F,{span:6,children:e.jsx(o.Item,{name:"unit",label:"角色",children:e.jsx(V,{placeholder:"请选择用户角色",allowClear:!0,showSearch:!0,getPopupContainer:s=>s.parentElement||document.body,filterOption:(s,a)=>a.children.toLowerCase().includes(s.toLowerCase()),onChange:Xe,style:{width:"100%"},children:S.map(s=>{const{roleCode:a,roleName:l}=s;return e.jsx(V.Option,{value:a,children:l},a)})})})}),e.jsx(F,{span:6,children:e.jsx(o.Item,{name:"level",label:"状态",children:e.jsxs(Y.Group,{defaultValue:"1",onChange:Ye,buttonStyle:"solid",children:[e.jsx(Y.Button,{value:"1",children:"启用"}),e.jsx(Y.Button,{value:"0",children:"禁用"})]})})}),e.jsx(F,{span:6,children:e.jsxs("div",{className:"text-right",children:[e.jsx(f,{type:"primary",onClick:U,className:"ml-[0.4rem]",children:"查询"}),e.jsxs(e.Fragment,{children:[e.jsx(f,{type:"primary",onClick:ts,className:"ml-[0.4rem]",children:"批量赋权"}),e.jsx(f,{type:"primary",onClick:()=>me("add",{}),className:"ml-[0.4rem]",children:"新增用户"})]}),e.jsx(f,{type:"primary",loading:ye,onClick:es,className:"ml-[0.4rem]",children:"导出"})]})})]})})}),e.jsx("div",{className:"bg-white pt-[0.5rem] px-[0.5rem] h-[calc(100%-4.8rem)] overflow-y-auto",children:e.jsx(Os,{loading:xe,columns:gs,dataSource:be,rowSelection:ps,size:"small",bordered:!0,scroll:{x:1140},pagination:{total:ee,showTotal:()=>`共 ${ee} 条`,defaultCurrent:1,defaultPageSize:10,current:z,pageSize:D,showSizeChanger:!0,showQuickJumper:!0,onChange:ms}})}),e.jsx(N,{title:le==="add"?"添加用户信息":"修改用户信息",open:Ae,destroyOnClose:!0,onCancel:()=>{$(!1)},footer:null,children:e.jsx(ks,{userInfo:c,userType:le,cancelAddModal:ss})}),e.jsx(N,{title:e.jsxs("div",{children:["修改用户角色",e.jsx("span",{style:{color:"#bfbfbf"},children:`(${Ee})`})]}),open:Oe,destroyOnClose:!0,onCancel:()=>{I(!1)},footer:null,children:e.jsx(vs,{roleParams:ie,roleList:S,cancelRoleModal:rs})}),e.jsx(N,{width:400,open:Re,destroyOnClose:!0,closable:!1,onOk:ls,confirmLoading:Le,onCancel:()=>{J(!1)},children:e.jsxs("div",{style:{fontSize:"0.8rem"},children:[e.jsx(Rs,{style:{color:"#faad14",marginRight:"0.8rem"}}),e.jsxs("span",{children:["是否删除",e.jsx("span",{style:{color:"#ff0000"},children:c==null?void 0:c.fullName}),"!"]})]})}),e.jsx(N,{title:e.jsxs("div",{children:["修改所属组织",e.jsx("span",{style:{color:"#bfbfbf"},children:`(${ke})`})]}),open:Ne,destroyOnClose:!0,onCancel:()=>{ce(!1)},footer:null,children:e.jsxs(o,{labelCol:{span:4},wrapperCol:{span:19},labelAlign:"right",onFinish:ns,children:[e.jsx(o.Item,{label:"所属组织",children:e.jsx(Ls,{placeholder:"请选择所属组织",loading:ve,value:de,treeExpandedKeys:Ie,treeData:Ke,allowClear:!0,showSearch:!0,treeDefaultExpandAll:!1,dropdownStyle:{maxHeight:"20rem",overflow:"auto"},fieldNames:{label:"orgName",value:"orgCode"},getPopupContainer:s=>s.parentElement||document.body,filterTreeNode:(s,a)=>{var l;return(l=a==null?void 0:a.orgName)==null?void 0:l.includes(s)},onChange:is,onTreeExpand:cs})}),e.jsx(o.Item,{wrapperCol:{offset:4,span:19},children:e.jsx(f,{disabled:(c==null?void 0:c.sourceSystem)!=="20",loading:ze,type:"primary",htmlType:"submit",children:"确定"})})]})}),e.jsx(N,{title:e.jsxs("div",{children:["修改授权组织",e.jsx("span",{style:{color:"#bfbfbf"},children:`(${Fe})`})]}),open:qe,destroyOnClose:!0,getContainer:!1,onCancel:()=>{ue(!1)},footer:null,children:Ve?e.jsx(Cs,{paragraph:{rows:4},title:!1,active:!0}):e.jsxs(o,{labelCol:{span:4},wrapperCol:{span:19},labelAlign:"right",onFinish:ds,children:[e.jsx(o.Item,{label:"组织",children:e.jsx(Es,{checkedKeys:he,expandedKeys:$e,treeData:Pe,checkable:!0,fieldNames:{key:"areaCode",title:"areaName"},onCheck:us,onExpand:hs})}),e.jsx(o.Item,{wrapperCol:{offset:4,span:19},children:e.jsx(f,{loading:Be,type:"primary",htmlType:"submit",children:"确定"})})]})})]})};export{Xs as default};
