import{D as r}from"./index-De_f0oL2.js";const t={getEnumType:a=>r.post("/zhyy/employee/getEnumType",a),build4LevelOrgTree2:a=>r.post("/zhyy/employee/org/build4LevelOrgTree2",a),getBranchTotalSalary:a=>r.get("/zhyy/salary/salaryBudgetMain/list",{params:a}),exportBranchTotalSalary:a=>r.get("/zhyy/salary/salaryBudgetMain/exportExcel",{params:a,responseType:"blob"}),getBranchTotalSalaryCalculate:a=>r.post("/zhyy/salary/salaryBudgetMain/calculate",a),getBranchTotalSalaryConfirm:a=>r.post("/zhyy/salary/salaryBudgetMain/confirm",a),getSalaryForwardList:a=>r.get("/zhyy/salary/salaryForward/list",{params:a}),exportSalaryForward:a=>r.get("/zhyy/salary/salaryForward/exportExcel",{params:a,responseType:"blob"}),getSalaryForwardSaveBatch:a=>r.post("/zhyy/salary/salaryForward/saveBatch",a),getFlowComment:a=>r.get("/zhyy/salary/salaryForward/getFlowComment",{params:a}),getCompleteTask:a=>r.post("/zhyy/salary/salaryForward/completeTask",a),getTerminateTask:a=>r.post("/zhyy/salary/salaryForward/terminateTask",a),getRejectTask:a=>r.post("/zhyy/salary/salaryForward/rejectTask",a),saveEditBatch:a=>r.post("/zhyy/salary/salaryForward/saveBatch",a)};export{t as e};
