import{D as a}from"./index-De_f0oL2.js";const t={getEnumType:e=>a.post("/zhyy/employee/getEnumType",e),getEmployeePostPag:e=>a.get("/zhyy/salary/yearlyBaseData/list",{params:e}),downloadEmployeeTemplate:e=>a.get("/zhyy/salary/common/downloadTemplate",{params:e,responseType:"blob"}),downloadEmployeeCompare:e=>a.post("/zhyy/employee/downloadEmployeeCompare",e,{responseType:"blob"}),exportEmployeeExcel:e=>a.get("/zhyy/salary/yearlyBaseData/exportExcel",{params:e,responseType:"blob"}),uploadEmployeeExcel:e=>a.post("/zhyy/salary/yearlyBaseData/uploadExcel",e),updateEmployeeTransfer:e=>a.get("/zhyy/salary/employeeTransfer/list",{params:e}),importEmployeeTransfer:e=>a.post("/zhyy/salary/employeeTransfer/uploadExcel",e,{}),emportEmployeeTransfer:e=>a.get("/zhyy/salary/employeeTransfer/exportExcel",{params:e,responseType:"blob"}),updateOther:e=>a.get("/zhyy/salary/additionalBudget/list",{params:e}),importOther:e=>a.post("/zhyy/salary/additionalBudget/uploadExcel",e,{}),emportOther:e=>a.get("/zhyy/salary/additionalBudget/exportExcel",{params:e,responseType:"blob"}),addSaveBatch:e=>a.post("/zhyy/salary/yearlyBaseData/saveBatch",e),addEmployeeSaveBatch:e=>a.post("/zhyy/salary/employeeTransfer/saveBatch",e),additionalBudget:e=>a.post("/zhyy/salary/additionalBudget/saveBatch",e)};export{t as p};
