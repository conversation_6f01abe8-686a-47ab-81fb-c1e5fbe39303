import{r as l,F as u,j as a,u as g,d as I,R as re,C as w,S as ie,B as S,T as c}from"./index-De_f0oL2.js";import{R as oe,o as z,d as ce}from"./down-BCLNnN1h.js";import{s as i,i as j}from"./service-D0isGGPv.js";import{R as de}from"./index-BmnYJy3v.js";import{s as x}from"./index-BcPP1N8I.js";import{D as he}from"./index-CdSZ9YgQ.js";import{C as me}from"./index-DZyVV6rP.js";import{R as ue,a as fe}from"./FullscreenOutlined-DzCTibKW.js";import"./Table-D-iLeFE-.js";import"./index-BWJehDyc.js";import"./useMultipleSelect-B0dEIXT-.js";const Re=()=>{var Y;const d=[{title:"单位",key:"cityName",dataIndex:"cityName",align:"center",fixed:"left",width:130,children:[],render:(e,t)=>a.jsx(c,{title:e,children:a.jsx("div",{className:i.over_ellipsis,children:e})})},{title:"部室/营服中心",key:"orgName",dataIndex:"orgName",align:"center",fixed:"left",width:160,children:[],render:(e,t)=>a.jsx(c,{title:e,children:a.jsx("div",{className:i.over_ellipsis,children:e})})},{title:"员工编号",key:"staffId",dataIndex:"staffId",align:"center",fixed:"left",width:100,children:[]},{title:"员工姓名",key:"staffName",dataIndex:"staffName",align:"center",fixed:"left",width:100,children:[]},{title:"单元名称",key:"unitName",dataIndex:"unitName",align:"center",fixed:"left",width:100,children:[],render:(e,t)=>a.jsx(c,{title:e,children:a.jsx("div",{className:i.over_ellipsis,children:e})})},{title:"年度考评结果",key:"name",dataIndex:"name",align:"center",children:[{title:"考评价结果",key:"yearEvalVal2023",dataIndex:"yearEvalVal2023",align:"center",width:100,children:[]},{title:"KPI得分",key:"yearKpi2023",dataIndex:"yearKpi2023",align:"center",width:100,children:[]},{title:"单元名称",key:"yearUnit2023",dataIndex:"yearUnit2023",align:"center",width:100,children:[],render:(e,t)=>a.jsx(c,{title:e,children:a.jsx("div",{className:i.over_ellipsis,children:e})})},{title:"单位",key:"yearOrgName2023",dataIndex:"yearOrgName2023",align:"center",width:100,children:[],render:(e,t)=>a.jsx(c,{title:e,children:a.jsx("div",{className:i.over_ellipsis,children:e})})},{title:"年考评价结果",key:"yearEvalVal2024",dataIndex:"yearEvalVal2024",align:"center",width:100,children:[]},{title:"KPI得分",key:"yearKpi2024",dataIndex:"yearKpi2024",align:"center",width:100,children:[]},{title:"单元名称",key:"yearUnit2024",dataIndex:"yearUnit2024",align:"center",width:100,children:[],render:(e,t)=>a.jsx(c,{title:e,children:a.jsx("div",{className:i.over_ellipsis,children:e})})},{title:"单位",key:"yearOrgName2024",dataIndex:"yearOrgName2024",align:"center",width:100,children:[],render:(e,t)=>a.jsx(c,{title:e,children:a.jsx("div",{className:i.over_ellipsis,children:e})})}]}],v=l.useRef(null),N=l.useRef(null),b=l.useRef(null),[h]=u.useForm(),[R,E]=l.useState(!0),[F,O]=l.useState(0),[y,T]=l.useState(""),[C,H]=l.useState([]),[K,k]=l.useState(!1),[V,L]=l.useState(d),[p,P]=l.useState(0),[U,q]=l.useState([]),[r,A]=l.useState({total:0,pageNum:1,pageSize:50}),{runAsync:G}=g(j.getStatEvalYearMonth,{manual:!0}),{runAsync:B}=g(j.exportStatEvalYearMonth,{manual:!0}),{runAsync:J}=g(j.build4LevelOrgTree2,{manual:!0});l.useEffect(()=>($(),Q(),f(),window.addEventListener("resize",_),()=>{window.removeEventListener("resize",_)}),[]);const _=()=>{m()};l.useEffect(()=>{y&&W()},[y]),l.useEffect(()=>{m()},[(document.querySelector(".assessmentResults_table .ant-table-header")||{}).offsetHeight]),l.useEffect(()=>{m()},[C]),l.useEffect(()=>{p>0&&f()},[p]),l.useEffect(()=>{(r==null?void 0:r.total)>0&&f()},[r.pageNum,r.pageSize]);const m=()=>{var o;const e=(document.querySelector(".assessmentResults_table .ant-table-header")||{}).offsetHeight||0,t=(document.querySelector(".assessmentResults_table .ant-table-pagination")||{}).offsetHeight||26;e&&t&&O(((o=N.current)==null?void 0:o.offsetHeight)-(b.current.offsetHeight+e+t))},Q=async()=>{const[e,t]=await J({monthId:I().format("YYYYMM"),tag:"1"});if(e){x.error((t==null?void 0:t.DATA)||(t==null?void 0:t.MESSAGE)||"调用失败");return}t.STATUS==="0000"?q(t.DATA):x.error((t==null?void 0:t.MESSAGE)||(t==null?void 0:t.DATA)||"调用失败")},f=async()=>{var n,D;const e=h.getFieldsValue(),[t]=e.cityId?e.cityId.slice(-1):[void 0];k(!0);const[o,s]=await G({cycleId:(n=e==null?void 0:e.cycleId)==null?void 0:n.format("YYYY"),cityId:t,pageNum:r.pageNum,pageSize:r.pageSize});if(k(!1),o){x.error((s==null?void 0:s.DATA)||(s==null?void 0:s.MESSAGE)||"调用失败");return}if(s.STATUS==="0000"){const{DATA:{data:se}}=s,le=se.map((M,ne)=>({...M,key:M.id||ne}));H(le),A({...r,total:(D=s.DATA)==null?void 0:D.totalCount})}else x.error((s==null?void 0:s.MESSAGE)||(s==null?void 0:s.DATA)||"调用失败")},$=()=>{const e=I();h.setFieldsValue({cycleId:e}),T(e)},W=()=>{const e=I(y),t=e.year();d[d.findIndex(s=>(s==null?void 0:s.title)==="年度考评结果")].children[0].title=`${t-1}年考评价结果`,d[d.findIndex(s=>(s==null?void 0:s.title)==="年度考评结果")].children[4].title=`${t}年考评价结果`;const o={title:"月度考评结果",key:"name",dataIndex:"name",align:"center",children:[]};for(let s=12;s>=0;s--){const n=`${e.endOf("year").subtract(s,"month").format("YYYYMM")}`;o.children.push({title:n,key:`monthEvalVal${s===12?"2023":"2024"}${n==null?void 0:n.slice(4)}`,dataIndex:`monthEvalVal${s===12?"2023":"2024"}${n==null?void 0:n.slice(4)}`,align:"center",width:100,children:[]},{title:"KPI得分",key:`monthKpi${s===12?"2023":"2024"}${n==null?void 0:n.slice(4)}`,dataIndex:`monthKpi${s===12?"2023":"2024"}${n==null?void 0:n.slice(4)}`,align:"center",width:100,children:[]})}L([...d,o])},X=e=>{console.log("Success:",e),f()},Z=async()=>{var e;try{z("正在导出",0,"loading");const t=h.getFieldsValue(),[o]=t.cityId?t.cityId.slice(-1):[void 0],s=await B({cycleId:(e=t==null?void 0:t.cycleId)==null?void 0:e.format("YYYY"),orgaId:o});ce(s)}catch(t){z("导出失败",1,"error"),console.error("Download failed:",t)}},ee=()=>{const e=p+1;h.resetFields(),$(),P(e)},te=e=>{const t={total:e==null?void 0:e.total,pageNum:e==null?void 0:e.current,pageSize:e==null?void 0:e.pageSize};A(t)},ae=(e,t)=>{console.log(e,t),T(e)};return a.jsxs("div",{className:`pt-[0.5rem] px-[0rem] h-full flex flex-col ${i.employment_page}`,children:[a.jsx("div",{ref:v,className:"bg-white pt-2 px-8 mb-2",children:a.jsx(u,{form:h,initialValues:{tag:""},onFinish:X,autoComplete:"off",children:a.jsxs(re,{gutter:24,children:[a.jsx(w,{span:6,children:a.jsx(u.Item,{label:"年份",name:"cycleId",wrapperCol:{span:20},className:"mb-[0.5rem]",children:a.jsx(he,{className:"w-full",allowClear:!1,onChange:ae,picker:"year"})})}),a.jsx(w,{span:6,children:a.jsx(u.Item,{label:"单位",name:"cityId",wrapperCol:{span:20},className:"mb-[0.5rem]",children:a.jsx(me,{style:{width:"75%"},allowClear:!0,changeOnSelect:!0,expandTrigger:"hover",displayRender:e=>e[e.length-1],options:U,fieldNames:{value:"orgId",label:"orgName",children:"children"},placeholder:"请选择"})})}),a.jsx(w,{span:6,children:a.jsx(u.Item,{labelCol:{span:0},wrapperCol:{span:24},className:"mb-[0.5rem]",children:a.jsxs(ie,{size:"small",children:[a.jsx(S,{type:"primary",htmlType:"submit",children:"查询"}),a.jsx(S,{htmlType:"button",onClick:()=>ee(),children:"重置"})]})})})]})})}),a.jsxs("div",{ref:N,className:"relative bg-white px-5 pt-2.5",style:{height:`calc(100% - ${((Y=v.current)==null?void 0:Y.offsetHeight)+15}px)`},children:[a.jsxs("div",{ref:b,className:`flex justify-between items-center overflow-hidden mb-2 ${i.animation_box} ${R?"h-[1.6rem]":"h-0"}`,children:[a.jsxs("div",{className:"flex ",children:[R?a.jsx(ue,{className:`${i.shousuo_icon} text-[1rem]`,onClick:()=>{E(!1),setTimeout(()=>{m()},200)}}):a.jsx(fe,{className:`${i.shousuo_icon} absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10`,onClick:()=>{E(!0),setTimeout(()=>{m()},200)}}),a.jsx("div",{className:"font-bold text-[0.8rem] ml-3",children:"数据列表"})]}),a.jsx(S,{danger:!0,ghost:!0,icon:a.jsx(oe,{}),onClick:()=>Z(),children:"导出"})]}),a.jsx(de,{className:"assessmentResults_table",rowClassName:(e,t)=>t%2===1?"customRow odd":"customRow even",columns:V,dataSource:C,loading:K,bordered:!0,scroll:{y:`calc(${F}px - 0.625rem - 1.6rem - 0.5rem)`},onChange:te,pagination:{...r,total:r==null?void 0:r.total,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50"]}})]})]})};export{Re as default};
