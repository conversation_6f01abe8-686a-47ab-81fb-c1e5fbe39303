import{D as x,F as l,r as s,j as e,R as je,C as b,y,B as T,O as be,T as we}from"./index-De_f0oL2.js";import{s as Me}from"./Index.module-C_CsuTds.js";import{s as i}from"./index-BcPP1N8I.js";import{S as G}from"./index-BWJehDyc.js";import{F as Ne,R as O}from"./Table-D-iLeFE-.js";import{M as V}from"./index-Dck5cc4J.js";import{T as Fe}from"./index-BxLWNlAG.js";import"./useMultipleSelect-B0dEIXT-.js";const g={buildMenuTree:r=>x.get("/zhyy/manager/core/menu/buildMenuTree",{params:r}),queryRole:r=>x.post("/zhyy/manager/extra/core/resource/queryResource",r),addResource:r=>x.post("/zhyy/manager/core/resource/addResource",r),updateResource:r=>x.post("/zhyy/manager/core/resource/updateResource",r),deleteMenu:r=>x.post("/zhyy/manager/core/resource/deleteResource",r)},Le=()=>{const[r]=l.useForm(),[d,k]=s.useState(!0),[U,B]=s.useState(""),[_,K]=s.useState(""),[Q,H]=s.useState(""),[J,w]=s.useState(!1),[v,W]=s.useState(0),[X,Y]=s.useState([]),[S,E]=s.useState(1),[C,R]=s.useState(10),[Z,ee]=s.useState(0),[c,m]=s.useState({}),[te,M]=s.useState(!0),[ae,N]=s.useState(!1),[se,F]=s.useState(!1),[ne,re]=s.useState([]),[oe,le]=s.useState(""),[j,I]=s.useState(""),[ce,z]=s.useState(!1),[ue,A]=s.useState(!1);s.useEffect(()=>{L()},[]);const L=()=>{const t={resourceName:U,menuName:_,resourceType:Q,subResourceType:"",pageNum:S,pageSize:C};d||w(!0),g.queryRole(t).then(a=>{const n=a[1];if(n.STATUS==="0000"){const{data:o,totalCount:p}=n.DATA,h=o==null?void 0:o.map((u,f)=>(u.index=(S-1)*C+f+1,u));Y(h),W(p)}else i.error(n.MESSAGE);d||w(!1),d&&k(!1)}).catch(()=>{d||w(!1),d&&k(!1)})},ie=()=>new Promise((t,a)=>{g.buildMenuTree({}).then(n=>{const o=n[1];o.STATUS==="0000"?re(o.DATA):i.error(o.MESSAGE),t(o)}).catch(n=>a(n))}),de=t=>{B(t.target.value)},me=t=>{K(t.target.value)},pe=t=>{H(t)},q=(t,a)=>{F(!0),I(t),m(a),ie();let n={funcCode:"",funcName:"",funcType:"100",menuTreeCode:void 0,sortId:""};if(t==="edit"){const{resourceCode:o,resourceName:p,resourceType:h,menuCode:u,sortId:f}=a;n={funcCode:o,funcName:p,funcType:h,menuTreeCode:u,sortId:f}}setTimeout(()=>{M(!1),r.setFieldsValue(n)})},he=()=>{F(!1),I(""),m({}),M(!0)},fe=(t,a)=>{le(a[0])},xe=t=>{const{funcCode:a,funcName:n,funcType:o,menuTreeCode:p,sortId:h}=t,u={resourceCode:a,resourceName:n,resourceType:o,subResourceType:"",realCode:"",menuCode:p,menuName:oe,sortId:h,remark:""};j==="edit"&&(u.id=c==null?void 0:c.id);const f=j==="add"?g.addResource:g.updateResource;N(!0),f(u).then(Ce=>{const $=Ce[1];$.STATUS==="0000"?(i.success(`${j==="add"?"新建":"修改"}菜单成功`),F(!1),I(""),m({}),M(!0),D()):i.error($.MESSAGE),N(!1)}).catch(()=>{N(!1)})},ye=t=>{z(!0),m(t)},P=()=>{z(!1),m({})},Te=()=>{const t={id:c==null?void 0:c.id};A(!0),g.deleteMenu(t).then(a=>{const n=a[1];n.STATUS==="0000"?(i.success("删除成功！"),P(),D()):i.error(n.MESSAGE),A(!1)}).catch(()=>{A(!1)})};s.useEffect(()=>{L()},[C,S,Z]);const D=()=>{E(1),R(10),ee(t=>t+1)},ge=(t,a)=>{E(t),R(a)},Se=[{title:"序号",key:"index",dataIndex:"index",width:100,align:"center"},{title:"功能编码",key:"resourceCode",dataIndex:"resourceCode",width:200,align:"center",ellipsis:{showTitle:!1},render:t=>e.jsx(we,{placement:"topLeft",title:t,getPopupContainer:a=>(a==null?void 0:a.closest("div.ant-table-body"))||document.body,children:t})},{title:"功能名称",key:"resourceName",dataIndex:"resourceName",width:200,align:"center"},{title:"功能类型",key:"resourceType",dataIndex:"resourceType",width:200,align:"center",render:t=>t==="100"?"按钮":t},{title:"菜单",key:"menuName",dataIndex:"menuName",width:200,align:"center"},{title:"操作",width:200,align:"center",render:(t,a)=>e.jsxs(e.Fragment,{children:[e.jsx(T,{type:"link",onClick:()=>q("edit",a),children:"修改"}),e.jsx(T,{type:"link",onClick:()=>ye(a),children:"删除"})]})}];return e.jsxs("div",{className:`pt-[0.5rem] px-[0rem] ${Me.rbac_page}`,children:[e.jsx("div",{className:"bg-white pt-[0.5rem] px-[1.75rem] mb-[0.5rem]",children:e.jsx(l,{labelCol:{span:6},children:e.jsxs(je,{gutter:24,children:[e.jsx(b,{span:6,children:e.jsx(l.Item,{name:"code1",label:"功能名称",children:e.jsx(y,{placeholder:"请输入功能名称",allowClear:!0,onChange:de,style:{width:"100%"}})})}),e.jsx(b,{span:6,children:e.jsx(l.Item,{name:"code2",label:"菜单名称",children:e.jsx(y,{placeholder:"请输入菜单名称",allowClear:!0,onChange:me,style:{width:"100%"}})})}),e.jsx(b,{span:6,children:e.jsx(l.Item,{name:"code3",label:"功能类型",children:e.jsx(G,{placeholder:"请选择功能类型",allowClear:!0,onChange:pe,children:e.jsx(G.Option,{value:"100",children:"按钮"},"100")})})}),e.jsx(b,{span:6,children:e.jsxs("div",{className:"text-right",children:[e.jsx(T,{type:"primary",onClick:D,children:"查询"}),e.jsx(T,{type:"primary",className:"ml-[0.4rem]",onClick:()=>q("add",{}),children:"新增功能"})]})})]})})}),e.jsx("div",{className:"bg-white pt-[0.5rem] px-[0.5rem]",children:e.jsx(Ne,{loading:J,dataSource:X,columns:Se,scroll:{x:"max-content"},bordered:!0,size:"small",sticky:!0,pagination:{total:v,showTotal:()=>`共 ${v} 条`,defaultCurrent:1,defaultPageSize:10,current:S,pageSize:C,showSizeChanger:!0,showQuickJumper:!0,onChange:ge}})}),e.jsx(V,{title:`${j==="add"?"新建":"修改"}`,open:se,destroyOnClose:!0,onCancel:he,footer:null,children:te?e.jsx(be,{paragraph:{rows:10},title:!1,active:!0}):e.jsxs(l,{form:r,labelCol:{span:6},wrapperCol:{span:16},labelAlign:"right",onFinish:xe,children:[e.jsx(l.Item,{label:"功能编码",name:"funcCode",rules:[{required:!0,message:"请输入功能编码！"}],children:e.jsx(y,{placeholder:"请输入功能编码"})}),e.jsx(l.Item,{label:"功能名称",name:"funcName",rules:[{required:!0,message:"请输入功能名称！"}],children:e.jsx(y,{placeholder:"请输入功能名称"})}),e.jsx(l.Item,{label:"功能类型",name:"funcType",children:e.jsx(O.Group,{buttonStyle:"solid",children:e.jsx(O.Button,{value:"100",children:"按钮"})})}),e.jsx(l.Item,{label:"所在菜单",name:"menuTreeCode",children:e.jsx(Fe,{placeholder:"请选择所在菜单",treeData:ne,showSearch:!0,treeDefaultExpandAll:!1,dropdownStyle:{maxHeight:400,overflow:"auto"},fieldNames:{label:"menuName",value:"menuCode"},getPopupContainer:t=>t.parentElement||document.body,filterTreeNode:(t,a)=>{var n;return(n=a==null?void 0:a.menuName)==null?void 0:n.includes(t)},onChange:fe})}),e.jsx(l.Item,{label:"排序字段",name:"sortId",rules:[{pattern:/^[+]{0,1}(\d+)$/,message:"只支持数字，请正确输入！"}],children:e.jsx(y,{placeholder:"请输入排序字段"})}),e.jsx(l.Item,{wrapperCol:{offset:6,span:16},children:e.jsx(T,{loading:ae,type:"primary",htmlType:"submit",children:"确定"})})]})}),e.jsxs(V,{width:"20%",open:ce,confirmLoading:ue,destroyOnClose:!0,closable:!1,onOk:Te,onCancel:P,children:["是否删除",e.jsx("span",{style:{color:"#ff0000"},children:c==null?void 0:c.resourceName}),"?"]})]})};export{Le as default};
