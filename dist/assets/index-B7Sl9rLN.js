import{V as Tt,r as o,t as We,az as Ge,c as D,b as qe,E as Ye,G as Ue,a as v,J as Ot,k as Et,aA as It,M as Nt,q as Xe,p as Dt,m as Mt,aB as Pt,A as zt,F as J,u as _,d as he,j as l,R as Rt,C as U,y as Q,B as R,z as Lt,T as Re}from"./index-De_f0oL2.js";import Bt from"./index-DyOF5WIj.js";import{e as F}from"./service-BvtRB_M7.js";import{s as Z}from"./Index.module-OEpj_sYj.js";import{R as Ht,o as Le,d as _t}from"./down-BCLNnN1h.js";import{R as Ft}from"./index-BmnYJy3v.js";import{f as Wt}from"./format-ChnqMkgG.js";import{s as y}from"./index-BcPP1N8I.js";import{D as Gt}from"./index-CdSZ9YgQ.js";import{C as Be}from"./index-DZyVV6rP.js";import{S as fe}from"./index-BWJehDyc.js";import{R as He}from"./Table-D-iLeFE-.js";import{R as qt,a as Yt}from"./FullscreenOutlined-DzCTibKW.js";import{M as ye}from"./index-Dck5cc4J.js";import{T as Ut}from"./index-CwwLkcJF.js";import{R as Xt}from"./ExclamationCircleOutlined-CkUVbQBi.js";import"./debounce-D4mn-XUD.js";import"./service-CEuf3VDV.js";import"./useMultipleSelect-B0dEIXT-.js";const Vt={xxl:3,xl:3,lg:3,md:3,sm:2,xs:1},Te=Tt.createContext({});var Kt=function(e,t){var i={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(i[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,a=Object.getOwnPropertySymbols(e);s<a.length;s++)t.indexOf(a[s])<0&&Object.prototype.propertyIsEnumerable.call(e,a[s])&&(i[a[s]]=e[a[s]]);return i};const Jt=e=>We(e).map(t=>Object.assign(Object.assign({},t==null?void 0:t.props),{key:t.key}));function Qt(e,t,i){const a=o.useMemo(()=>t||Jt(i),[t,i]);return o.useMemo(()=>a.map(g=>{var{span:m}=g,b=Kt(g,["span"]);return m==="filled"?Object.assign(Object.assign({},b),{filled:!0}):Object.assign(Object.assign({},b),{span:typeof m=="number"?m:Ge(e,m)})}),[a,e])}var Zt=function(e,t){var i={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(i[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,a=Object.getOwnPropertySymbols(e);s<a.length;s++)t.indexOf(a[s])<0&&Object.prototype.propertyIsEnumerable.call(e,a[s])&&(i[a[s]]=e[a[s]]);return i};function kt(e,t){let i=[],a=[],s=!1,g=0;return e.filter(m=>m).forEach(m=>{const{filled:b}=m,j=Zt(m,["filled"]);if(b){a.push(j),i.push(a),a=[],g=0;return}const C=t-g;g+=m.span||1,g>=t?(g>t?(s=!0,a.push(Object.assign(Object.assign({},j),{span:C}))):a.push(j),i.push(a),a=[],g=0):a.push(j)}),a.length>0&&i.push(a),i=i.map(m=>{const b=m.reduce((j,C)=>j+(C.span||1),0);if(b<t){const j=m[m.length-1];return j.span=t-(b-(j.span||1)),m}return m}),[i,s]}const ea=(e,t)=>{const[i,a]=o.useMemo(()=>kt(t,e),[t,e]);return i},ta=({children:e})=>e;function _e(e){return e!=null}const Ae=e=>{const{itemPrefixCls:t,component:i,span:a,className:s,style:g,labelStyle:m,contentStyle:b,bordered:j,label:C,content:p,colon:x,type:O,styles:h}=e,A=i,u=o.useContext(Te),{classNames:$}=u;return j?o.createElement(A,{className:D({[`${t}-item-label`]:O==="label",[`${t}-item-content`]:O==="content",[`${$==null?void 0:$.label}`]:O==="label",[`${$==null?void 0:$.content}`]:O==="content"},s),style:g,colSpan:a},_e(C)&&o.createElement("span",{style:Object.assign(Object.assign({},m),h==null?void 0:h.label)},C),_e(p)&&o.createElement("span",{style:Object.assign(Object.assign({},m),h==null?void 0:h.content)},p)):o.createElement(A,{className:D(`${t}-item`,s),style:g,colSpan:a},o.createElement("div",{className:`${t}-item-container`},(C||C===0)&&o.createElement("span",{className:D(`${t}-item-label`,$==null?void 0:$.label,{[`${t}-item-no-colon`]:!x}),style:Object.assign(Object.assign({},m),h==null?void 0:h.label)},C),(p||p===0)&&o.createElement("span",{className:D(`${t}-item-content`,$==null?void 0:$.content),style:Object.assign(Object.assign({},b),h==null?void 0:h.content)},p)))};function Ce(e,{colon:t,prefixCls:i,bordered:a},{component:s,type:g,showLabel:m,showContent:b,labelStyle:j,contentStyle:C,styles:p}){return e.map(({label:x,children:O,prefixCls:h=i,className:A,style:u,labelStyle:$,contentStyle:f,span:X=1,key:L,styles:T},S)=>typeof s=="string"?o.createElement(Ae,{key:`${g}-${L||S}`,className:A,style:u,styles:{label:Object.assign(Object.assign(Object.assign(Object.assign({},j),p==null?void 0:p.label),$),T==null?void 0:T.label),content:Object.assign(Object.assign(Object.assign(Object.assign({},C),p==null?void 0:p.content),f),T==null?void 0:T.content)},span:X,colon:t,component:s,itemPrefixCls:h,bordered:a,label:m?x:null,content:b?O:null,type:g}):[o.createElement(Ae,{key:`label-${L||S}`,className:A,style:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},j),p==null?void 0:p.label),u),$),T==null?void 0:T.label),span:1,colon:t,component:s[0],itemPrefixCls:h,bordered:a,label:x,type:"label"}),o.createElement(Ae,{key:`content-${L||S}`,className:A,style:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},C),p==null?void 0:p.content),u),f),T==null?void 0:T.content),span:X*2-1,component:s[1],itemPrefixCls:h,bordered:a,content:O,type:"content"})])}const aa=e=>{const t=o.useContext(Te),{prefixCls:i,vertical:a,row:s,index:g,bordered:m}=e;return a?o.createElement(o.Fragment,null,o.createElement("tr",{key:`label-${g}`,className:`${i}-row`},Ce(s,e,Object.assign({component:"th",type:"label",showLabel:!0},t))),o.createElement("tr",{key:`content-${g}`,className:`${i}-row`},Ce(s,e,Object.assign({component:"td",type:"content",showContent:!0},t)))):o.createElement("tr",{key:g,className:`${i}-row`},Ce(s,e,Object.assign({component:m?["th","td"]:"td",type:"item",showLabel:!0,showContent:!0},t)))},la=e=>{const{componentCls:t,labelBg:i}=e;return{[`&${t}-bordered`]:{[`> ${t}-view`]:{border:`${v(e.lineWidth)} ${e.lineType} ${e.colorSplit}`,"> table":{tableLayout:"auto"},[`${t}-row`]:{borderBottom:`${v(e.lineWidth)} ${e.lineType} ${e.colorSplit}`,"&:first-child":{"> th:first-child, > td:first-child":{borderStartStartRadius:e.borderRadiusLG}},"&:last-child":{borderBottom:"none","> th:first-child, > td:first-child":{borderEndStartRadius:e.borderRadiusLG}},[`> ${t}-item-label, > ${t}-item-content`]:{padding:`${v(e.padding)} ${v(e.paddingLG)}`,borderInlineEnd:`${v(e.lineWidth)} ${e.lineType} ${e.colorSplit}`,"&:last-child":{borderInlineEnd:"none"}},[`> ${t}-item-label`]:{color:e.colorTextSecondary,backgroundColor:i,"&::after":{display:"none"}}}},[`&${t}-middle`]:{[`${t}-row`]:{[`> ${t}-item-label, > ${t}-item-content`]:{padding:`${v(e.paddingSM)} ${v(e.paddingLG)}`}}},[`&${t}-small`]:{[`${t}-row`]:{[`> ${t}-item-label, > ${t}-item-content`]:{padding:`${v(e.paddingXS)} ${v(e.padding)}`}}}}}},na=e=>{const{componentCls:t,extraColor:i,itemPaddingBottom:a,itemPaddingEnd:s,colonMarginRight:g,colonMarginLeft:m,titleMarginBottom:b}=e;return{[t]:Object.assign(Object.assign(Object.assign({},Ue(e)),la(e)),{"&-rtl":{direction:"rtl"},[`${t}-header`]:{display:"flex",alignItems:"center",marginBottom:b},[`${t}-title`]:Object.assign(Object.assign({},Ot),{flex:"auto",color:e.titleColor,fontWeight:e.fontWeightStrong,fontSize:e.fontSizeLG,lineHeight:e.lineHeightLG}),[`${t}-extra`]:{marginInlineStart:"auto",color:i,fontSize:e.fontSize},[`${t}-view`]:{width:"100%",borderRadius:e.borderRadiusLG,table:{width:"100%",tableLayout:"fixed",borderCollapse:"collapse"}},[`${t}-row`]:{"> th, > td":{paddingBottom:a,paddingInlineEnd:s},"> th:last-child, > td:last-child":{paddingInlineEnd:0},"&:last-child":{borderBottom:"none","> th, > td":{paddingBottom:0}}},[`${t}-item-label`]:{color:e.labelColor,fontWeight:"normal",fontSize:e.fontSize,lineHeight:e.lineHeight,textAlign:"start","&::after":{content:'":"',position:"relative",top:-.5,marginInline:`${v(m)} ${v(g)}`},[`&${t}-item-no-colon::after`]:{content:'""'}},[`${t}-item-no-label`]:{"&::after":{margin:0,content:'""'}},[`${t}-item-content`]:{display:"table-cell",flex:1,color:e.contentColor,fontSize:e.fontSize,lineHeight:e.lineHeight,wordBreak:"break-word",overflowWrap:"break-word"},[`${t}-item`]:{paddingBottom:0,verticalAlign:"top","&-container":{display:"flex",[`${t}-item-label`]:{display:"inline-flex",alignItems:"baseline"},[`${t}-item-content`]:{display:"inline-flex",alignItems:"baseline",minWidth:"1em"}}},"&-middle":{[`${t}-row`]:{"> th, > td":{paddingBottom:e.paddingSM}}},"&-small":{[`${t}-row`]:{"> th, > td":{paddingBottom:e.paddingXS}}}})}},ra=e=>({labelBg:e.colorFillAlter,labelColor:e.colorTextTertiary,titleColor:e.colorText,titleMarginBottom:e.fontSizeSM*e.lineHeightSM,itemPaddingBottom:e.padding,itemPaddingEnd:e.padding,colonMarginRight:e.marginXS,colonMarginLeft:e.marginXXS/2,contentColor:e.colorText,extraColor:e.colorText}),ia=qe("Descriptions",e=>{const t=Ye(e,{});return na(t)},ra);var sa=function(e,t){var i={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(i[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,a=Object.getOwnPropertySymbols(e);s<a.length;s++)t.indexOf(a[s])<0&&Object.prototype.propertyIsEnumerable.call(e,a[s])&&(i[a[s]]=e[a[s]]);return i};const le=e=>{const{prefixCls:t,title:i,extra:a,column:s,colon:g=!0,bordered:m,layout:b,children:j,className:C,rootClassName:p,style:x,size:O,labelStyle:h,contentStyle:A,styles:u,items:$,classNames:f}=e,X=sa(e,["prefixCls","title","extra","column","colon","bordered","layout","children","className","rootClassName","style","size","labelStyle","contentStyle","styles","items","classNames"]),{getPrefixCls:L,direction:T,className:S,style:H,classNames:E,styles:P}=Et("descriptions"),I=L("descriptions",t),W=It(),V=o.useMemo(()=>{var G;return typeof s=="number"?s:(G=Ge(W,Object.assign(Object.assign({},Vt),s)))!==null&&G!==void 0?G:3},[W,s]),ne=Qt(W,$,j),k=Nt(O),de=ea(V,ne),[re,me,ie]=ia(I),ue=o.useMemo(()=>({labelStyle:h,contentStyle:A,styles:{content:Object.assign(Object.assign({},P.content),u==null?void 0:u.content),label:Object.assign(Object.assign({},P.label),u==null?void 0:u.label)},classNames:{label:D(E.label,f==null?void 0:f.label),content:D(E.content,f==null?void 0:f.content)}}),[h,A,u,f,E,P]);return re(o.createElement(Te.Provider,{value:ue},o.createElement("div",Object.assign({className:D(I,S,E.root,f==null?void 0:f.root,{[`${I}-${k}`]:k&&k!=="default",[`${I}-bordered`]:!!m,[`${I}-rtl`]:T==="rtl"},C,p,me,ie),style:Object.assign(Object.assign(Object.assign(Object.assign({},H),P.root),u==null?void 0:u.root),x)},X),(i||a)&&o.createElement("div",{className:D(`${I}-header`,E.header,f==null?void 0:f.header),style:Object.assign(Object.assign({},P.header),u==null?void 0:u.header)},i&&o.createElement("div",{className:D(`${I}-title`,E.title,f==null?void 0:f.title),style:Object.assign(Object.assign({},P.title),u==null?void 0:u.title)},i),a&&o.createElement("div",{className:D(`${I}-extra`,E.extra,f==null?void 0:f.extra),style:Object.assign(Object.assign({},P.extra),u==null?void 0:u.extra)},a)),o.createElement("div",{className:`${I}-view`},o.createElement("table",null,o.createElement("tbody",null,de.map((G,se)=>o.createElement(aa,{key:se,index:se,colon:g,prefixCls:I,vertical:b==="vertical",bordered:m,row:G}))))))))};le.Item=ta;const oa=e=>{const{componentCls:t,calc:i}=e;return{[t]:Object.assign(Object.assign({},Ue(e)),{margin:0,padding:0,listStyle:"none",[`${t}-item`]:{position:"relative",margin:0,paddingBottom:e.itemPaddingBottom,fontSize:e.fontSize,listStyle:"none","&-tail":{position:"absolute",insetBlockStart:e.itemHeadSize,insetInlineStart:i(i(e.itemHeadSize).sub(e.tailWidth)).div(2).equal(),height:`calc(100% - ${v(e.itemHeadSize)})`,borderInlineStart:`${v(e.tailWidth)} ${e.lineType} ${e.tailColor}`},"&-pending":{[`${t}-item-head`]:{fontSize:e.fontSizeSM,backgroundColor:"transparent"},[`${t}-item-tail`]:{display:"none"}},"&-head":{position:"absolute",width:e.itemHeadSize,height:e.itemHeadSize,backgroundColor:e.dotBg,border:`${v(e.dotBorderWidth)} ${e.lineType} transparent`,borderRadius:"50%","&-blue":{color:e.colorPrimary,borderColor:e.colorPrimary},"&-red":{color:e.colorError,borderColor:e.colorError},"&-green":{color:e.colorSuccess,borderColor:e.colorSuccess},"&-gray":{color:e.colorTextDisabled,borderColor:e.colorTextDisabled}},"&-head-custom":{position:"absolute",insetBlockStart:i(e.itemHeadSize).div(2).equal(),insetInlineStart:i(e.itemHeadSize).div(2).equal(),width:"auto",height:"auto",marginBlockStart:0,paddingBlock:e.customHeadPaddingVertical,lineHeight:1,textAlign:"center",border:0,borderRadius:0,transform:"translate(-50%, -50%)"},"&-content":{position:"relative",insetBlockStart:i(i(e.fontSize).mul(e.lineHeight).sub(e.fontSize)).mul(-1).add(e.lineWidth).equal(),marginInlineStart:i(e.margin).add(e.itemHeadSize).equal(),marginInlineEnd:0,marginBlockStart:0,marginBlockEnd:0,wordBreak:"break-word"},"&-last":{[`> ${t}-item-tail`]:{display:"none"},[`> ${t}-item-content`]:{minHeight:i(e.controlHeightLG).mul(1.2).equal()}}},[`&${t}-alternate,
        &${t}-right,
        &${t}-label`]:{[`${t}-item`]:{"&-tail, &-head, &-head-custom":{insetInlineStart:"50%"},"&-head":{marginInlineStart:i(e.marginXXS).mul(-1).equal(),"&-custom":{marginInlineStart:i(e.tailWidth).div(2).equal()}},"&-left":{[`${t}-item-content`]:{insetInlineStart:`calc(50% - ${v(e.marginXXS)})`,width:`calc(50% - ${v(e.marginSM)})`,textAlign:"start"}},"&-right":{[`${t}-item-content`]:{width:`calc(50% - ${v(e.marginSM)})`,margin:0,textAlign:"end"}}}},[`&${t}-right`]:{[`${t}-item-right`]:{[`${t}-item-tail,
            ${t}-item-head,
            ${t}-item-head-custom`]:{insetInlineStart:`calc(100% - ${v(i(i(e.itemHeadSize).add(e.tailWidth)).div(2).equal())})`},[`${t}-item-content`]:{width:`calc(100% - ${v(i(e.itemHeadSize).add(e.marginXS).equal())})`}}},[`&${t}-pending
        ${t}-item-last
        ${t}-item-tail`]:{display:"block",height:`calc(100% - ${v(e.margin)})`,borderInlineStart:`${v(e.tailWidth)} dotted ${e.tailColor}`},[`&${t}-reverse
        ${t}-item-last
        ${t}-item-tail`]:{display:"none"},[`&${t}-reverse ${t}-item-pending`]:{[`${t}-item-tail`]:{insetBlockStart:e.margin,display:"block",height:`calc(100% - ${v(e.margin)})`,borderInlineStart:`${v(e.tailWidth)} dotted ${e.tailColor}`},[`${t}-item-content`]:{minHeight:i(e.controlHeightLG).mul(1.2).equal()}},[`&${t}-label`]:{[`${t}-item-label`]:{position:"absolute",insetBlockStart:i(i(e.fontSize).mul(e.lineHeight).sub(e.fontSize)).mul(-1).add(e.tailWidth).equal(),width:`calc(50% - ${v(e.marginSM)})`,textAlign:"end"},[`${t}-item-right`]:{[`${t}-item-label`]:{insetInlineStart:`calc(50% + ${v(e.marginSM)})`,width:`calc(50% - ${v(e.marginSM)})`,textAlign:"start"}}},"&-rtl":{direction:"rtl",[`${t}-item-head-custom`]:{transform:"translate(50%, -50%)"}}})}},ca=e=>({tailColor:e.colorSplit,tailWidth:e.lineWidthBold,dotBorderWidth:e.wireframe?e.lineWidthBold:e.lineWidth*3,dotBg:e.colorBgContainer,itemPaddingBottom:e.padding*1.25}),da=qe("Timeline",e=>{const t=Ye(e,{itemHeadSize:10,customHeadPaddingVertical:e.paddingXXS,paddingInlineEnd:2});return[oa(t)]},ca);var ma=function(e,t){var i={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(i[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,a=Object.getOwnPropertySymbols(e);s<a.length;s++)t.indexOf(a[s])<0&&Object.prototype.propertyIsEnumerable.call(e,a[s])&&(i[a[s]]=e[a[s]]);return i};const Ve=e=>{var{prefixCls:t,className:i,color:a="blue",dot:s,pending:g=!1,position:m,label:b,children:j}=e,C=ma(e,["prefixCls","className","color","dot","pending","position","label","children"]);const{getPrefixCls:p}=o.useContext(Xe),x=p("timeline",t),O=D(`${x}-item`,{[`${x}-item-pending`]:g},i),h=/blue|red|green|gray/.test(a||"")?void 0:a,A=D(`${x}-item-head`,{[`${x}-item-head-custom`]:!!s,[`${x}-item-head-${a}`]:!h});return o.createElement("li",Object.assign({},C,{className:O}),b&&o.createElement("div",{className:`${x}-item-label`},b),o.createElement("div",{className:`${x}-item-tail`}),o.createElement("div",{className:A,style:{borderColor:h,color:h}},s),o.createElement("div",{className:`${x}-item-content`},j))};var Fe=function(e,t){var i={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(i[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,a=Object.getOwnPropertySymbols(e);s<a.length;s++)t.indexOf(a[s])<0&&Object.prototype.propertyIsEnumerable.call(e,a[s])&&(i[a[s]]=e[a[s]]);return i};const ua=e=>{var{prefixCls:t,className:i,pending:a=!1,children:s,items:g,rootClassName:m,reverse:b=!1,direction:j,hashId:C,pendingDot:p,mode:x=""}=e,O=Fe(e,["prefixCls","className","pending","children","items","rootClassName","reverse","direction","hashId","pendingDot","mode"]);const h=(S,H)=>x==="alternate"?S==="right"?`${t}-item-right`:S==="left"?`${t}-item-left`:H%2===0?`${t}-item-left`:`${t}-item-right`:x==="left"?`${t}-item-left`:x==="right"?`${t}-item-right`:S==="right"?`${t}-item-right`:"",A=Dt(g||[]),u=typeof a=="boolean"?null:a;a&&A.push({pending:!!a,dot:p||o.createElement(Mt,null),children:u}),b&&A.reverse();const $=A.length,f=`${t}-item-last`,X=A.filter(S=>!!S).map((S,H)=>{var E;const P=H===$-2?f:"",I=H===$-1?f:"",{className:W}=S,V=Fe(S,["className"]);return o.createElement(Ve,Object.assign({},V,{className:D([W,!b&&a?P:I,h((E=S==null?void 0:S.position)!==null&&E!==void 0?E:"",H)]),key:(S==null?void 0:S.key)||H}))}),L=A.some(S=>!!(S!=null&&S.label)),T=D(t,{[`${t}-pending`]:!!a,[`${t}-reverse`]:!!b,[`${t}-${x}`]:!!x&&!L,[`${t}-label`]:L,[`${t}-rtl`]:j==="rtl"},i,m,C);return o.createElement("ol",Object.assign({},O,{className:T}),X)};function pa(e,t){return e&&Array.isArray(e)?e:We(t).map(i=>{var a,s;return Object.assign({children:(s=(a=i==null?void 0:i.props)===null||a===void 0?void 0:a.children)!==null&&s!==void 0?s:""},i.props)})}var ga=function(e,t){var i={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(i[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,a=Object.getOwnPropertySymbols(e);s<a.length;s++)t.indexOf(a[s])<0&&Object.prototype.propertyIsEnumerable.call(e,a[s])&&(i[a[s]]=e[a[s]]);return i};const we=e=>{const{getPrefixCls:t,direction:i,timeline:a}=o.useContext(Xe),{prefixCls:s,children:g,items:m,className:b,style:j}=e,C=ga(e,["prefixCls","children","items","className","style"]),p=t("timeline",s),x=Pt(p),[O,h,A]=da(p,x),u=pa(m,g);return O(o.createElement(ua,Object.assign({},C,{className:D(a==null?void 0:a.className,b,A,x),style:Object.assign(Object.assign({},a==null?void 0:a.style),j),prefixCls:p,direction:i,items:u,hashId:h})))};we.Item=Ve;const Pa=()=>{var ze;const{currentUser:e}=zt(),[t]=J.useForm(),i=o.useRef(null),a=o.useRef(null),s=o.useRef(null),[g,m]=o.useState(0),[b,j]=o.useState(!0),[C,p]=o.useState(!1),[x,O]=o.useState([]),[h,A]=o.useState(!1),[u,$]=o.useState([]),[f,X]=o.useState([]),[L,T]=o.useState([]),[S,H]=o.useState([]),[E,P]=o.useState(""),[I,W]=o.useState(null),[V,ne]=o.useState(null),[k,de]=o.useState(""),[re,me]=o.useState(""),[ie,ue]=o.useState(null),[G,se]=o.useState(null),[Ke,be]=o.useState(""),[Se,Je]=o.useState(0),[pe,ee]=o.useState(""),[Qe,Ze]=o.useState(""),[ke,Oe]=o.useState(!1),[et,te]=o.useState(!1),[tt,xe]=o.useState(!1),[at,Ee]=o.useState(!1),[ve,lt]=o.useState([]),[N,Ie]=o.useState({total:0,pageNum:1,pageSize:10}),[ae,Ne]=o.useState(!1),[q,De]=o.useState(!1),{runAsync:Me}=_(F.getEnumType,{manual:!0}),{runAsync:nt}=_(F.getSalaryForwardList,{manual:!0}),{runAsync:rt}=_(F.exportSalaryForward,{manual:!0}),{runAsync:it}=_(F.getSalaryForwardSaveBatch,{manual:!0}),{runAsync:st}=_(F.getFlowComment,{manual:!0}),{runAsync:ot}=_(F.getCompleteTask,{manual:!0}),{runAsync:ct}=_(F.getTerminateTask,{manual:!0}),{runAsync:dt}=_(F.getRejectTask,{manual:!0}),{runAsync:mt}=_(F.saveEditBatch,{manual:!0}),{runAsync:ut}=_(F.build4LevelOrgTree2,{manual:!0});o.useEffect(()=>($e(),gt(),Y(),window.addEventListener("resize",Pe),()=>{window.removeEventListener("resize",Pe)}),[]);const Pe=()=>{ge()};o.useEffect(()=>{ge()},[(document.querySelector(".wagesPage_table .ant-table-header")||{}).offsetHeight]),o.useEffect(()=>{(N==null?void 0:N.total)>0&&Y()},[N.pageNum,N.pageSize]),o.useEffect(()=>{Y()},[q]);const ge=()=>{var r;const n=(document.querySelector(".wagesPage_table .ant-table-header")||{}).offsetHeight||0,d=(document.querySelector(".wagesPage_table .ant-table-pagination")||{}).offsetHeight||26;n&&d&&m(((r=a.current)==null?void 0:r.offsetHeight)-(s.current.offsetHeight+n+d))},pt=n=>{$(n)},Y=async()=>{var oe;p(!0);const n=N,[d]=V?V.slice(-1):[void 0],[r]=I?I.slice(-1):[void 0],[z,w]=await nt({...n,cycleId:E?he(E).format("YYYYMM"):"",cityOutId:d,cityInId:r,empId:k,approvalNode:ie,approvalState:G,operateType:q,empName:re});if(p(!1),z){y.error((w==null?void 0:w.DATA)||(w==null?void 0:w.MESSAGE)||"调用失败");return}if(w.STATUS==="0000"){const{DATA:{data:K}}=w,c=K.map((B,M)=>({...B,key:B.id||M}));O(c),Ie({...N,total:(oe=w.DATA)==null?void 0:oe.totalCount})}else y.error((w==null?void 0:w.MESSAGE)||(w==null?void 0:w.DATA)||"调用失败")},gt=async()=>{const[[n,d],[r,z]]=await Promise.all([Me({code:"salaryForwardApprovalState"}),Me({code:"salaryForwardApprovalNode"})]);n||r||(d.STATUS==="0000"&&X(d.DATA),z.STATUS==="0000"&&H(z.DATA))},ht=()=>{P(""),W(null),ne(null),de(""),me(""),ue(""),se(null),$e()},ft=async()=>{try{Le("正在导出",0,"loading");const n=await rt({cycleId:E?he(E).format("YYYYMM"):"",cityOutId:V,cityInId:I,empId:k,empName:re,approvalNode:ie,approvalState:G,operateType:q});_t(n)}catch{Le("导出失败",1,"error")}},yt=[{title:"月份",key:"cycleId",dataIndex:"cycleId",align:"center",width:80},{title:"转出单位",key:"cityOutName",dataIndex:"cityOutName",align:"center",width:130,render:(n,d)=>l.jsx(Re,{title:n,children:l.jsx("div",{className:Z.over_ellipsis,children:n})})},{title:"人员编号",key:"empId",dataIndex:"empId",align:"center",width:80},{title:"人员姓名",key:"empName",dataIndex:"empName",align:"center",width:80},{title:"转递金额",key:"forwardAmount",dataIndex:"forwardAmount",align:"center",width:80,render:n=>l.jsx("span",{children:Wt(n)})},{title:"转递内容说明",key:"forwardContent",dataIndex:"forwardContent",align:"center",width:180},{title:"转入单位",key:"cityInName",dataIndex:"cityInName",align:"center",width:130,render:(n,d)=>l.jsx(Re,{title:n,children:l.jsx("div",{className:Z.over_ellipsis,children:n})})},{title:"审批环节",key:"approvalNode",dataIndex:"approvalNode",align:"center",width:80,render:n=>{const d=S.filter(r=>r.enumId===n)[0];return l.jsx("p",{children:d==null?void 0:d.enumName})}},{title:"审批状态",key:"approvalState",dataIndex:"approvalState",align:"center",width:80,render:n=>{const d=f.filter(r=>r.enumId===n)[0];return l.jsx("p",{children:d==null?void 0:d.enumName})}},{title:"操作",align:"center",width:80,render:(n,d)=>{var r,z;return l.jsxs(l.Fragment,{children:[l.jsx(R,{type:"link",onClick:()=>bt(d),children:"详情"}),ae&&((r=e==null?void 0:e.userInfo)==null?void 0:r.userName)===d.createBy&&((z=e==null?void 0:e.userInfo)==null?void 0:z.userName)===d.approvalUsers&&d.approvalState==="TuiHui"&&l.jsx(R,{type:"link",onClick:()=>{Ct(d)},children:"修改"})]})}}],bt=async n=>{Oe(!0);const[d,r]=await st({busiId:n.id});if(d){y.error((r==null?void 0:r.DATA)||(r==null?void 0:r.MESSAGE)||"调用失败");return}r.STATUS==="0000"?lt(r.DATA):y.error((r==null?void 0:r.MESSAGE)||(r==null?void 0:r.DATA)||"调用失败")},St=[{title:"人员编号",key:"empId",dataIndex:"empId",align:"center",width:120},{title:"人员姓名",key:"empName",dataIndex:"empName",align:"center",width:120},{title:"转递金额",key:"forwardAmount",dataIndex:"forwardAmount",align:"center",width:80},{title:"转递内容说明",key:"forwardContent",dataIndex:"forwardContent",align:"center",width:180}],xt={selectedRowKeys:u,onChange:pt},vt=n=>{Ie({total:n==null?void 0:n.total,pageNum:n==null?void 0:n.current,pageSize:n==null?void 0:n.pageSize})},jt=n=>{switch(n){case"5":return"发起流程";case"0":return"退回";case"1":return"通过";case"3":return"待审批";case"6":return"流程结束";case"4":return"作废"}},$t=async n=>{const[d,r]=await it(n);if(p(!1),d){y.error((r==null?void 0:r.DATA)||(r==null?void 0:r.MESSAGE)||"调用失败");return}r.STATUS==="0000"?(y.success(r.DATA),Y(),A(!1)):y.error((r==null?void 0:r.MESSAGE)||(r==null?void 0:r.DATA)||"调用失败")},je=n=>{if(u.length===0){y.warning("请先选择要操作的数据！");return}te(!0),Je(n),n===1&&be("将提交至人力资源部审批"),n===2&&be("将退回至上一步工位审批"),n===3&&be("将作废此流程")},$e=async(n=null)=>{const[d,r]=await ut({monthId:n?he(n).format("YYYYMM"):he().format("YYYYMM"),tag:"1"});if(d){y.error((r==null?void 0:r.DATA)||(r==null?void 0:r.MESSAGE)||"调用失败");return}r.STATUS==="0000"?(ne(null),W(null),console.log("@@@",r),T(r.DATA)):y.error((r==null?void 0:r.MESSAGE)||(r==null?void 0:r.DATA)||"调用失败")},At=async()=>{var n,d,r,z,w,oe;if(Se===1){const[K,c]=await ot({ids:u,remark:pe});if(K){y.error((c==null?void 0:c.DATA)||(c==null?void 0:c.MESSAGE)||"调用失败");return}if(c.STATUS==="0000"){if(c.DATA.totalFailure!==0){if(Array.isArray((n=c.DATA)==null?void 0:n.auditDetailList)&&((d=c.DATA)==null?void 0:d.auditDetailList.length)>0){const B=c.DATA.auditDetailList.filter(M=>M.result!=="SUCCESS");if(B.length>0){const M=B.map(ce=>ce.message).join(`
`);y.success(M);return}}y.success(`共提交${c.DATA.total}条数据，成功${c.DATA.totalSuccess}条数据，失败${c.DATA.totalFailure}条数据`);return}y.success(`共提交${c.DATA.total}条数据，成功${c.DATA.totalSuccess}条数据，失败${c.DATA.totalFailure}条数据`),te(!1),ee(""),$([]),Y()}else y.error((c==null?void 0:c.MESSAGE)||(c==null?void 0:c.DATA)||"调用失败")}if(Se===2){const[K,c]=await dt({ids:u,remark:pe});if(K){y.error((c==null?void 0:c.DATA)||(c==null?void 0:c.MESSAGE)||"调用失败");return}if(c.STATUS==="0000"){if(c.DATA.totalFailure!==0){if(Array.isArray((r=c.DATA)==null?void 0:r.auditDetailList)&&((z=c.DATA)==null?void 0:z.auditDetailList.length)>0){const B=c.DATA.auditDetailList.filter(M=>M.result!=="SUCCESS");if(B.length>0){const M=B.map(ce=>ce.message).join(`
`);y.success(M);return}}y.success(`共提交${c.DATA.total}条数据，成功${c.DATA.totalSuccess}条数据，失败${c.DATA.totalFailure}条数据`);return}y.success(`共提交${c.DATA.total}条数据，成功${c.DATA.totalSuccess}条数据，失败${c.DATA.totalSuccess}条数据`),te(!1),$([]),ee(""),Y()}else y.error((c==null?void 0:c.MESSAGE)||(c==null?void 0:c.DATA)||"调用失败")}if(Se===3){const[K,c]=await ct({ids:u,remark:pe});if(K){y.error((c==null?void 0:c.DATA)||(c==null?void 0:c.MESSAGE)||"调用失败");return}if(c.STATUS==="0000"){if(c.DATA.totalFailure!==0){if(Array.isArray((w=c.DATA)==null?void 0:w.auditDetailList)&&((oe=c.DATA)==null?void 0:oe.auditDetailList.length)>0){const B=c.DATA.auditDetailList.filter(M=>M.result!=="SUCCESS");if(B.length>0){const M=B.map(ce=>ce.message).join(`
`);y.success(M);return}}y.success(`共提交${c.DATA.total}条数据，成功${c.DATA.totalSuccess}条数据，失败${c.DATA.totalFailure}条数据`);return}y.success(`共提交${c.DATA.total}条数据，成功${c.DATA.totalSuccess}条数据，失败${c.DATA.totalFailure}条数据`),te(!1),$([]),ee(""),Y()}else y.error((c==null?void 0:c.MESSAGE)||(c==null?void 0:c.DATA)||"调用失败")}},Ct=n=>{const{empId:d,empName:r,realForwardAmount:z,forwardContent:w}=n;xe(!0),Ze(n.id),t.setFieldsValue({empId:d,empName:r,forwardAmount:z,forwardContent:w})},wt=async n=>{Ee(!0);const[d,r]=await mt([{id:Qe,...n}]);if(Ee(!1),d){y.error((r==null?void 0:r.DATA)||(r==null?void 0:r.MESSAGE)||"调用失败");return}r.STATUS==="0000"?(y.success(r.DATA),xe(!1),Y()):y.error((r==null?void 0:r.MESSAGE)||(r==null?void 0:r.DATA)||"调用失败")};return l.jsxs("div",{className:`pt-[0.5rem] px-[0rem] h-full flex flex-col ${Z.processManagement_page}`,children:[l.jsx("div",{ref:i,className:"bg-white pt-[0.5rem] pb-[0.5rem] px-[1.75rem] mb-[0.5rem]",children:l.jsxs(Rt,{gutter:24,children:[l.jsxs(U,{span:6,style:{display:"flex"},children:[l.jsx("div",{style:{width:"25%",margin:"auto 0"},children:"月份："}),l.jsx(Gt,{picker:"month",style:{width:"75%"},value:E,format:"YYYY-MM",placeholder:"请选择月份",size:"small",onChange:n=>{P(n||""),$e(n)}})]}),l.jsxs(U,{span:6,style:{display:"flex"},children:[l.jsx("div",{style:{width:"25%",margin:"auto 0"},children:"转出单位："}),l.jsx(Be,{style:{width:"75%"},allowClear:!0,changeOnSelect:!0,expandTrigger:"hover",displayRender:n=>n[n.length-1],options:L,value:V,onChange:n=>{ne(n)},fieldNames:{value:"orgId",label:"orgName",children:"children"},placeholder:"请选择转出单位"})]}),l.jsxs(U,{span:6,style:{display:"flex"},children:[l.jsx("div",{style:{width:"25%",margin:"auto 0"},children:"转入单位："}),l.jsx(Be,{style:{width:"75%"},allowClear:!0,changeOnSelect:!0,expandTrigger:"hover",displayRender:n=>n[n.length-1],options:L,value:I,onChange:n=>{W(n)},fieldNames:{value:"orgId",label:"orgName",children:"children"},placeholder:"请选择转入单位"})]}),l.jsxs(U,{span:6,style:{display:"flex"},children:[l.jsx("div",{style:{width:"25%",margin:"auto 0"},children:"人员编号："}),l.jsx(Q,{placeholder:"请输入人员编号",allowClear:!0,style:{width:"75%"},onChange:n=>de(n.target.value)})]}),l.jsxs(U,{span:6,style:{display:"flex",marginTop:12},children:[l.jsx("div",{style:{width:"25%",margin:"auto 0"},children:"员工姓名："}),l.jsx(Q,{placeholder:"请输入员工姓名",allowClear:!0,style:{width:"75%"},onChange:n=>me(n.target.value),value:re})]}),l.jsxs(U,{span:6,style:{display:"flex",marginTop:12},children:[l.jsx("div",{style:{width:"25%",margin:"auto 0"},children:"审批环节："}),l.jsx(fe,{value:ie,placeholder:"请选择审批环节",allowClear:!0,onChange:n=>ue(n),style:{width:"75%"},children:S.map(n=>{const{enumId:d,enumName:r}=n;return l.jsx(fe.Option,{value:d,children:r},d)})})]}),l.jsxs(U,{span:4,style:{display:"flex",marginTop:12},children:[l.jsx("div",{style:{width:"38%",margin:"auto 0"},children:"审批状态："}),l.jsx(fe,{value:G,style:{width:"62%"},placeholder:"请选择审批状态",allowClear:!0,onChange:n=>{se(n)},children:f.map(n=>{const{enumId:d,enumName:r}=n;return l.jsx(fe.Option,{value:d,children:r},d)})})]}),l.jsxs(U,{span:5,style:{display:"flex",marginTop:12},children:[l.jsx("div",{style:{width:"40%",margin:"auto 0"},children:"操作类型："}),l.jsx(He,{style:{width:"30%",margin:"auto 0"},checked:ae,onClick:()=>{!ae&&q&&De(!1),Ne(!ae)},children:"申请"}),l.jsx(He,{style:{width:"30%",margin:"auto 0"},checked:q,onClick:()=>{!q&&ae&&Ne(!1),De(!q)},children:"审批"})]}),l.jsx(U,{span:3,style:{marginTop:12},children:l.jsxs("div",{className:"text-right",children:[l.jsxs(R,{type:"primary",onClick:Y,children:[" ","查询"," "]}),l.jsx(R,{className:"ml-[0.4rem]",onClick:ht,children:"重置"})]})})]})}),l.jsxs("div",{ref:a,className:"relative bg-white px-5 pt-2.5",style:{height:`calc(100% - ${((ze=i.current)==null?void 0:ze.offsetHeight)+15}px)`},children:[l.jsxs("div",{ref:s,className:`flex justify-between items-center overflow-hidden mb-2 ${Z.animation_box} ${b?"h-[1.6rem]":"h-0"}`,children:[l.jsxs("div",{className:"flex ",children:[b?l.jsx(qt,{className:`${Z.shousuo_icon} text-[1rem]`,onClick:()=>{j(!1),setTimeout(()=>{ge()},200)}}):l.jsx(Yt,{className:`${Z.shousuo_icon} absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10`,onClick:()=>{j(!0),setTimeout(()=>{ge()},200)}}),l.jsxs("div",{className:"font-bold text-[0.8rem] ml-[1rem]",children:["数据列表",l.jsx("span",{className:"text-[0.7rem]",style:{color:"#939393"},children:"（单位：元）"})]})]}),l.jsxs("div",{children:[ae&&l.jsxs(R,{type:"primary",onClick:()=>{A(!0)},children:[l.jsx(Lt,{}),"新增"]}),l.jsxs(R,{className:"ml-[0.4rem]",danger:!0,onClick:()=>ft(),children:[l.jsx(Ht,{}),"导出"]}),q&&l.jsxs(l.Fragment,{children:[l.jsx(R,{className:"ml-[0.4rem]",danger:!0,onClick:()=>je(1),children:"通过"}),l.jsx(R,{className:"ml-[0.4rem]",onClick:()=>je(2),children:"退回"}),l.jsx(R,{className:"ml-[0.4rem]",onClick:()=>je(3),children:"作废"})]})]})]}),l.jsx(Ft,{className:"wagesPage_table",rowClassName:(n,d)=>d%2===1?"customRow odd":"customRow even",loading:C,dataSource:x,columns:yt,scroll:{y:`calc(${g}px - 0.625rem - 1.6rem - 0.5rem)`},bordered:!0,rowSelection:q?xt:null,onChange:vt,pagination:{total:N==null?void 0:N.total,showTotal:()=>`共 ${N==null?void 0:N.total} 条`,defaultCurrent:1,defaultPageSize:50,current:N==null?void 0:N.pageNum,pageSize:50,showSizeChanger:!0}})]}),l.jsx(ye,{title:l.jsx("p",{style:{textAlign:"center",color:"#E60027"},children:"-新增数据-"}),destroyOnClose:!0,open:h,centered:!0,footer:null,onCancel:()=>A(!1),width:"60%",children:l.jsx(Bt,{columns:St,submitData:n=>$t(n)})}),l.jsx(ye,{title:l.jsx("p",{style:{textAlign:"center",color:"#E60027"},children:"-审批环节详情-"}),destroyOnClose:!0,open:ke,centered:!0,footer:null,onCancel:()=>Oe(!1),width:"50%",className:Z.modal,children:l.jsx(we,{style:{marginTop:"1rem"},children:ve==null?void 0:ve.map((n,d)=>l.jsx(we.Item,{color:(n==null?void 0:n.status)==="1"?"green":"#FBCC48",children:l.jsx(le,{title:l.jsxs("div",{style:{display:"flex"},children:[l.jsx("p",{style:{marginRight:"8px"},children:n.nodeName}),l.jsx(Ut,{color:n.operation==="0"?"red":n.operation==="3"?"orange":n.operation==="4"?"default":"success",children:jt(n.operation)})]}),children:n.operation!=="6"&&n.operation!=="4"&&l.jsxs(l.Fragment,{children:[l.jsx(le.Item,{label:"审批人",children:l.jsxs("p",{children:[n.assigneeName,"(",n.assigneeOrgaName,")"]})}),l.jsx(le.Item,{label:"创建时间",children:n.createTime}),l.jsx(le.Item,{label:"审批时间",children:n.endDate}),l.jsx(le.Item,{label:"审批意见",children:n.opinion})]})})},d))})}),l.jsx(ye,{title:l.jsx("div",{style:{textAlign:"center",color:"#E60027"},children:"-提示-"}),destroyOnClose:!0,open:et,centered:!0,footer:l.jsxs("div",{style:{display:"flex",justifyContent:"center"},children:[l.jsx(R,{danger:!0,type:"primary",style:{marginRight:50},onClick:At,children:"确认"}),l.jsx(R,{danger:!0,onClick:()=>{te(!1),ee("")},children:"返回"})]}),onCancel:()=>{te(!1),ee("")},width:"25%",children:l.jsxs("div",{style:{marginTop:12,marginBottom:20},children:[l.jsxs("div",{style:{display:"flex",justifyContent:"left",fontSize:12,fontWeight:500},children:[l.jsx(Xt,{style:{marginRight:12}}),l.jsx("div",{children:Ke})]}),l.jsxs("div",{style:{display:"flex",marginTop:12},children:[l.jsx("div",{style:{margin:"auto 0"},children:"审批意见："}),l.jsx(Q,{placeholder:"请输入审批意见",allowClear:!0,style:{width:"75%"},onChange:n=>ee(n.target.value),value:pe})]})]})}),l.jsx(ye,{title:l.jsx("div",{style:{textAlign:"center",color:"#E60027"},children:"-修改数据-"}),destroyOnClose:!0,open:tt,centered:!0,footer:null,onCancel:()=>xe(!1),width:"30%",children:l.jsxs(J,{form:t,labelCol:{span:6},wrapperCol:{span:16},style:{maxWidth:600},initialValues:{remember:!0},onFinish:wt,autoComplete:"off",children:[l.jsx(J.Item,{label:"人员编号",name:"empId",rules:[{required:!0,message:"请输入人员编号!"}],children:l.jsx(Q,{})}),l.jsx(J.Item,{label:"人员姓名",name:"empName",rules:[{required:!0,message:"请输入人员姓名!"}],children:l.jsx(Q,{})}),l.jsx(J.Item,{label:"转递金额",name:"forwardAmount",rules:[{required:!0,message:"请输入转递金额!"}],children:l.jsx(Q,{})}),l.jsx(J.Item,{label:"转递内容说明",name:"forwardContent",rules:[{required:!0,message:"请输入转递内容说明!"}],children:l.jsx(Q,{})}),l.jsx(J.Item,{wrapperCol:{offset:10,span:12},children:l.jsx(R,{type:"primary",htmlType:"submit",loading:at,children:"提交"})})]})})]})};export{Pa as default};
