import{ca as Ft,r as n,a8 as X,p as ke,t as jt,a9 as Ot,af as ft,al as zt,a7 as Vt,bp as Et,a5 as re,i as Bt,K as Ye,_ as Kt,w as kt,b as Ut,E as Ht,a as Nt,q as Xt,k as qt,bq as Gt,bg as Yt,aB as Dt,L as Jt,c as _t,$ as Zt,v as $t,M as Qt,ax as er,a0 as tr,bi as rr,br as Tt,a1 as nr,bk as ar}from"./index-De_f0oL2.js";import{c as lr,d as or,B as ir,e as ur,f as sr,u as cr,D as dr,m as fr}from"./index-BWJehDyc.js";import{a as St,c as vr,U as hr,T as pr,b as mr,g as gr,d as Cr}from"./Table-D-iLeFE-.js";import{g as Sr}from"./useMultipleSelect-B0dEIXT-.js";function yr(e,t){var r=typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=Ft(e))||t){r&&(e=r);var a=0,l=function(){};return{s:l,n:function(){return a>=e.length?{done:!0}:{done:!1,value:e[a++]}},e:function(c){throw c},f:l}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var s,o=!0,i=!1;return{s:function(){r=r.call(e)},n:function(){var c=r.next();return o=c.done,c},e:function(c){i=!0,s=c},f:function(){try{o||r.return==null||r.return()}finally{if(i)throw s}}}}const br=function(e){var t=n.useRef({valueLabels:new Map});return n.useMemo(function(){var r=t.current.valueLabels,a=new Map,l=e.map(function(s){var o=s.value,i=s.label,u=i??r.get(o);return a.set(o,u),X(X({},s),{},{label:u})});return t.current.valueLabels=a,[l]},[e])};var xr=function(t,r,a,l){return n.useMemo(function(){var s=function(E){return E.map(function(P){var O=P.value;return O})},o=s(t),i=s(r),u=o.filter(function(L){return!l[L]}),c=o,f=i;if(a){var T=St(o,!0,l);c=T.checkedKeys,f=T.halfCheckedKeys}return[Array.from(new Set([].concat(ke(u),ke(c)))),f]},[t,r,a,l])},wr=function(t){return Array.isArray(t)?t:t!==void 0?[t]:[]},Ir=function(t){var r=t||{},a=r.label,l=r.value,s=r.children;return{_title:a?[a]:["title","label"],value:l||"value",key:l||"value",children:s||"children"}},yt=function(t){return!t||t.disabled||t.disableCheckbox||t.checkable===!1},Er=function(t,r){var a=[],l=function s(o){o.forEach(function(i){var u=i[r.children];u&&(a.push(i[r.value]),s(u))})};return l(t),a},Mt=function(t){return t==null};const kr=function(e,t){return n.useMemo(function(){var r=vr(e,{fieldNames:t,initWrapper:function(l){return X(X({},l),{},{valueEntities:new Map})},processEntity:function(l,s){var o=l.node[t.value];s.valueEntities.set(o,l)}});return r},[e,t])};var xt=function(){return null},Nr=["children","value"];function Rt(e){return jt(e).map(function(t){if(!n.isValidElement(t)||!t.type)return null;var r=t,a=r.key,l=r.props,s=l.children,o=l.value,i=Ot(l,Nr),u=X({key:a,value:o},i),c=Rt(s);return c.length&&(u.children=c),u}).filter(function(t){return t})}function bt(e){if(!e)return e;var t=X({},e);return"props"in t||Object.defineProperty(t,"props",{get:function(){return ft(!1,"New `rc-tree-select` not support return node instance as argument anymore. Please consider to remove `props` access."),t}}),t}function Dr(e,t,r,a,l,s){var o=null,i=null;function u(){function c(f){var T=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"0",L=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;return f.map(function(E,P){var O="".concat(T,"-").concat(P),N=E[s.value],K=r.includes(N),Y=c(E[s.children]||[],O,K),J=n.createElement(xt,E,Y.map(function(H){return H.node}));if(t===N&&(o=J),K){var he={pos:O,node:J,children:Y};return L||i.push(he),he}return null}).filter(function(E){return E})}i||(i=[],c(a),i.sort(function(f,T){var L=f.node.props.value,E=T.node.props.value,P=r.indexOf(L),O=r.indexOf(E);return P-O}))}Object.defineProperty(e,"triggerNode",{get:function(){return ft(!1,"`triggerNode` is deprecated. Please consider decoupling data with node."),u(),o}}),Object.defineProperty(e,"allCheckedNodes",{get:function(){return ft(!1,"`allCheckedNodes` is deprecated. Please consider decoupling data with node."),u(),l?i:i.map(function(f){var T=f.node;return T})}})}var _r=function(t,r,a){var l=a.fieldNames,s=a.treeNodeFilterProp,o=a.filterTreeNode,i=l.children;return n.useMemo(function(){if(!r||o===!1)return t;var u=typeof o=="function"?o:function(f,T){return String(T[s]).toUpperCase().includes(r.toUpperCase())},c=function f(T){var L=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;return T.reduce(function(E,P){var O=P[i],N=L||u(r,bt(P)),K=f(O||[],N);return(N||K.length)&&E.push(X(X({},P),{},zt({isLeaf:void 0},i,K))),E},[])};return c(t)},[t,r,i,s,o])};function Pt(e){var t=n.useRef();t.current=e;var r=n.useCallback(function(){return t.current.apply(t,arguments)},[]);return r}function Tr(e,t){var r=t.id,a=t.pId,l=t.rootPId,s=new Map,o=[];return e.forEach(function(i){var u=i[r],c=X(X({},i),{},{key:i.key||u});s.set(u,c)}),s.forEach(function(i){var u=i[a],c=s.get(u);c?(c.children=c.children||[],c.children.push(i)):(u===l||l===null)&&o.push(i)}),o}function Mr(e,t,r){return n.useMemo(function(){if(e){if(r){var a=X({id:"id",pId:"pId",rootPId:null},Vt(r)==="object"?r:{});return Tr(e,a)}return e}return Rt(t)},[t,r,e])}var At=n.createContext(null),Wt=n.createContext(null),Pr={width:0,height:0,display:"flex",overflow:"hidden",opacity:0,border:0,padding:0,margin:0},Lr=function(t,r){var a=lr(),l=a.prefixCls,s=a.multiple,o=a.searchValue,i=a.toggleOpen,u=a.open,c=a.notFoundContent,f=n.useContext(Wt),T=f.virtual,L=f.listHeight,E=f.listItemHeight,P=f.listItemScrollOffset,O=f.treeData,N=f.fieldNames,K=f.onSelect,Y=f.dropdownMatchSelectWidth,J=f.treeExpandAction,he=f.treeTitleRender,H=f.onPopupScroll,M=f.leftMaxCount,Pe=f.leafCountOnly,ne=f.valueEntities,x=n.useContext(At),ie=x.checkable,$=x.checkedKeys,Le=x.halfCheckedKeys,ue=x.treeExpandedKeys,Oe=x.treeDefaultExpandAll,Ve=x.treeDefaultExpandedKeys,se=x.onTreeExpand,Ne=x.treeIcon,Ke=x.showTreeIcon,He=x.switcherIcon,$e=x.treeLine,Re=x.treeNodeFilterProp,Ae=x.loadData,De=x.treeLoadedKeys,We=x.treeMotion,pe=x.onTreeLoad,ce=x.keyEntities,Z=n.useRef(),A=Et(function(){return O},[u,O],function(v,d){return d[0]&&v[1]!==d[1]}),W=n.useMemo(function(){return ie?{checked:$,halfChecked:Le}:null},[ie,$,Le]);n.useEffect(function(){if(u&&!s&&$.length){var v;(v=Z.current)===null||v===void 0||v.scrollTo({key:$[0]})}},[u]);var de=function(d){d.preventDefault()},me=function(d,k){var C=k.node;ie&&yt(C)||(K(C.key,{selected:!$.includes(C.key)}),s||i(!1))},fe=n.useState(Ve),ve=re(fe,2),q=ve[0],Fe=ve[1],Q=n.useState(null),_e=re(Q,2),ge=_e[0],ae=_e[1],F=n.useMemo(function(){return ue?ke(ue):o?ge:q},[q,ge,ue,o]),Ce=function(d){Fe(d),ae(d),se&&se(d)},Se=String(o).toLowerCase(),g=function(d){return Se?String(d[Re]).toLowerCase().includes(Se):!1};n.useEffect(function(){o&&ae(Er(O,N))},[o]);var je=n.useState(function(){return new Map}),ee=re(je,2),G=ee[0],Ze=ee[1];n.useEffect(function(){M&&Ze(new Map)},[M]);function B(v){var d=v[N.value];if(!G.has(d)){var k=ne.get(d),C=(k.children||[]).length===0;if(C)G.set(d,!1);else{var I=k.children.filter(function(V){return!V.node.disabled&&!V.node.disableCheckbox&&!$.includes(V.node[N.value])}),z=I.length;G.set(d,z>M)}}return G.get(d)}var w=Bt(function(v){var d=v[N.value];return $.includes(d)||M===null?!1:M<=0?!0:Pe&&M?B(v):!1}),Qe=function v(d){var k=yr(d),C;try{for(k.s();!(C=k.n()).done;){var I=C.value;if(!(I.disabled||I.selectable===!1)){if(o){if(g(I))return I}else return I;if(I[N.children]){var z=v(I[N.children]);if(z)return z}}}}catch(V){k.e(V)}finally{k.f()}return null},Te=n.useState(null),ye=re(Te,2),be=ye[0],ze=ye[1],j=ce[be];n.useEffect(function(){if(u){var v=null,d=function(){var C=Qe(A);return C?C[N.value]:null};!s&&$.length&&!o?v=$[0]:v=d(),ze(v)}},[u,o]),n.useImperativeHandle(r,function(){var v;return{scrollTo:(v=Z.current)===null||v===void 0?void 0:v.scrollTo,onKeyDown:function(k){var C,I=k.which;switch(I){case Ye.UP:case Ye.DOWN:case Ye.LEFT:case Ye.RIGHT:(C=Z.current)===null||C===void 0||C.onKeyDown(k);break;case Ye.ENTER:{if(j){var z=w(j.node),V=(j==null?void 0:j.node)||{},Ue=V.selectable,le=V.value,Xe=V.disabled;Ue!==!1&&!Xe&&!z&&me(null,{node:{key:be},selected:!$.includes(le)})}break}case Ye.ESC:i(!1)}},onKeyUp:function(){}}});var Be=Et(function(){return!o},[o,ue||q],function(v,d){var k=re(v,1),C=k[0],I=re(d,2),z=I[0],V=I[1];return C!==z&&!!(z||V)}),D=Be?Ae:null;if(A.length===0)return n.createElement("div",{role:"listbox",className:"".concat(l,"-empty"),onMouseDown:de},c);var R={fieldNames:N};return De&&(R.loadedKeys=De),F&&(R.expandedKeys=F),n.createElement("div",{onMouseDown:de},j&&u&&n.createElement("span",{style:Pr,"aria-live":"assertive"},j.node.value),n.createElement(hr.Provider,{value:{nodeDisabled:w}},n.createElement(pr,Kt({ref:Z,focusable:!1,prefixCls:"".concat(l,"-tree"),treeData:A,height:L,itemHeight:E,itemScrollOffset:P,virtual:T!==!1&&Y!==!1,multiple:s,icon:Ne,showIcon:Ke,switcherIcon:He,showLine:$e,loadData:D,motion:We,activeKey:be,checkable:ie,checkStrictly:!0,checkedKeys:W,selectedKeys:ie?[]:$,defaultExpandAll:Oe,titleRender:he},R,{onActiveChange:ze,onSelect:me,onCheck:me,onExpand:Ce,onLoad:pe,filterTreeNode:g,expandAction:J,onScroll:H}))))},Or=n.forwardRef(Lr),wt="SHOW_ALL",It="SHOW_PARENT",vt="SHOW_CHILD";function Lt(e,t,r,a){var l=new Set(e);return t===vt?e.filter(function(s){var o=r[s];return!o||!o.children||!o.children.some(function(i){var u=i.node;return l.has(u[a.value])})||!o.children.every(function(i){var u=i.node;return yt(u)||l.has(u[a.value])})}):t===It?e.filter(function(s){var o=r[s],i=o?o.parent:null;return!i||yt(i.node)||!l.has(i.key)}):e}var Vr=["id","prefixCls","value","defaultValue","onChange","onSelect","onDeselect","searchValue","inputValue","onSearch","autoClearSearchValue","filterTreeNode","treeNodeFilterProp","showCheckedStrategy","treeNodeLabelProp","multiple","treeCheckable","treeCheckStrictly","labelInValue","maxCount","fieldNames","treeDataSimpleMode","treeData","children","loadData","treeLoadedKeys","onTreeLoad","treeDefaultExpandAll","treeExpandedKeys","treeDefaultExpandedKeys","onTreeExpand","treeExpandAction","virtual","listHeight","listItemHeight","listItemScrollOffset","onDropdownVisibleChange","dropdownMatchSelectWidth","treeLine","treeIcon","showTreeIcon","switcherIcon","treeMotion","treeTitleRender","onPopupScroll"];function Kr(e){return!e||Vt(e)!=="object"}var Hr=n.forwardRef(function(e,t){var r=e.id,a=e.prefixCls,l=a===void 0?"rc-tree-select":a,s=e.value,o=e.defaultValue,i=e.onChange,u=e.onSelect,c=e.onDeselect,f=e.searchValue,T=e.inputValue,L=e.onSearch,E=e.autoClearSearchValue,P=E===void 0?!0:E,O=e.filterTreeNode,N=e.treeNodeFilterProp,K=N===void 0?"value":N,Y=e.showCheckedStrategy,J=e.treeNodeLabelProp,he=e.multiple,H=e.treeCheckable,M=e.treeCheckStrictly,Pe=e.labelInValue,ne=e.maxCount,x=e.fieldNames,ie=e.treeDataSimpleMode,$=e.treeData,Le=e.children,ue=e.loadData,Oe=e.treeLoadedKeys,Ve=e.onTreeLoad,se=e.treeDefaultExpandAll,Ne=e.treeExpandedKeys,Ke=e.treeDefaultExpandedKeys,He=e.onTreeExpand,$e=e.treeExpandAction,Re=e.virtual,Ae=e.listHeight,De=Ae===void 0?200:Ae,We=e.listItemHeight,pe=We===void 0?20:We,ce=e.listItemScrollOffset,Z=ce===void 0?0:ce,A=e.onDropdownVisibleChange,W=e.dropdownMatchSelectWidth,de=W===void 0?!0:W,me=e.treeLine,fe=e.treeIcon,ve=e.showTreeIcon,q=e.switcherIcon,Fe=e.treeMotion,Q=e.treeTitleRender,_e=e.onPopupScroll,ge=Ot(e,Vr),ae=or(r),F=H&&!M,Ce=H||M,Se=M||Pe,g=Ce||he,je=kt(o,{value:s}),ee=re(je,2),G=ee[0],Ze=ee[1],B=n.useMemo(function(){return H?Y||vt:wt},[Y,H]),w=n.useMemo(function(){return Ir(x)},[JSON.stringify(x)]),Qe=kt("",{value:f!==void 0?f:T,postState:function(h){return h||""}}),Te=re(Qe,2),ye=Te[0],be=Te[1],ze=function(h){be(h),L==null||L(h)},j=Mr($,Le,ie),Be=kr(j,w),D=Be.keyEntities,R=Be.valueEntities,v=n.useCallback(function(p){var h=[],m=[];return p.forEach(function(S){R.has(S)?m.push(S):h.push(S)}),{missingRawValues:h,existRawValues:m}},[R]),d=_r(j,ye,{fieldNames:w,treeNodeFilterProp:K,filterTreeNode:O}),k=n.useCallback(function(p){if(p){if(J)return p[J];for(var h=w._title,m=0;m<h.length;m+=1){var S=p[h[m]];if(S!==void 0)return S}}},[w,J]),C=n.useCallback(function(p){var h=wr(p);return h.map(function(m){return Kr(m)?{value:m}:m})},[]),I=n.useCallback(function(p){var h=C(p);return h.map(function(m){var S=m.label,te=m.value,_=m.halfChecked,y,b=R.get(te);if(b){var U;S=Q?Q(b.node):(U=S)!==null&&U!==void 0?U:k(b.node),y=b.node.disabled}else if(S===void 0){var Ie=C(G).find(function(at){return at.value===te});S=Ie.label}return{label:S,value:te,halfChecked:_,disabled:y}})},[R,k,C,G]),z=n.useMemo(function(){return C(G===null?[]:G)},[C,G]),V=n.useMemo(function(){var p=[],h=[];return z.forEach(function(m){m.halfChecked?h.push(m):p.push(m)}),[p,h]},[z]),Ue=re(V,2),le=Ue[0],Xe=Ue[1],it=n.useMemo(function(){return le.map(function(p){return p.value})},[le]),ht=xr(le,Xe,F,D),ut=re(ht,2),xe=ut[0],we=ut[1],pt=n.useMemo(function(){var p=Lt(xe,B,D,w),h=p.map(function(_){var y,b;return(y=(b=D[_])===null||b===void 0||(b=b.node)===null||b===void 0?void 0:b[w.value])!==null&&y!==void 0?y:_}),m=h.map(function(_){var y=le.find(function(U){return U.value===_}),b=Pe?y==null?void 0:y.label:Q==null?void 0:Q(y);return{value:_,label:b}}),S=I(m),te=S[0];return!g&&te&&Mt(te.value)&&Mt(te.label)?[]:S.map(function(_){var y;return X(X({},_),{},{label:(y=_.label)!==null&&y!==void 0?y:_.value})})},[w,g,xe,le,I,B,D]),mt=br(pt),st=re(mt,1),et=st[0],ct=n.useMemo(function(){return g&&(B==="SHOW_CHILD"||M||!H)?ne:null},[ne,g,M,B,H]),qe=Pt(function(p,h,m){var S=Lt(p,B,D,w);if(!(ct&&S.length>ct)){var te=I(p);if(Ze(te),P&&be(""),i){var _=p;F&&(_=S.map(function(oe){var Ee=R.get(oe);return Ee?Ee.node[w.value]:oe}));var y=h||{triggerValue:void 0,selected:void 0},b=y.triggerValue,U=y.selected,Ie=_;if(M){var at=Xe.filter(function(oe){return!_.includes(oe.value)});Ie=[].concat(ke(Ie),ke(at))}var lt=I(Ie),Me={preValue:le,triggerValue:b},Ge=!0;(M||m==="selection"&&!U)&&(Ge=!1),Dr(Me,b,p,j,Ge,w),Ce?Me.checked=U:Me.selected=U;var dt=Se?lt:lt.map(function(oe){return oe.value});i(g?dt:dt[0],Se?null:lt.map(function(oe){return oe.label}),Me)}}}),tt=n.useCallback(function(p,h){var m,S=h.selected,te=h.source,_=D[p],y=_==null?void 0:_.node,b=(m=y==null?void 0:y[w.value])!==null&&m!==void 0?m:p;if(!g)qe([b],{selected:!0,triggerValue:b},"option");else{var U=S?[].concat(ke(it),[b]):xe.filter(function(Ee){return Ee!==b});if(F){var Ie=v(U),at=Ie.missingRawValues,lt=Ie.existRawValues,Me=lt.map(function(Ee){return R.get(Ee).key}),Ge;if(S){var dt=St(Me,!0,D);Ge=dt.checkedKeys}else{var oe=St(Me,{halfCheckedKeys:we},D);Ge=oe.checkedKeys}U=[].concat(ke(at),ke(Ge.map(function(Ee){return D[Ee].node[w.value]})))}qe(U,{selected:S,triggerValue:b},te||"option")}S||!g?u==null||u(b,bt(y)):c==null||c(b,bt(y))},[v,R,D,w,g,it,qe,F,u,c,xe,we,ne]),gt=n.useCallback(function(p){if(A){var h={};Object.defineProperty(h,"documentClickClose",{get:function(){return ft(!1,"Second param of `onDropdownVisibleChange` has been removed."),!1}}),A(p,h)}},[A]),Ct=Pt(function(p,h){var m=p.map(function(S){return S.value});if(h.type==="clear"){qe(m,{},"selection");return}h.values.length&&tt(h.values[0].value,{selected:!1,source:"selection"})}),rt=n.useMemo(function(){return{virtual:Re,dropdownMatchSelectWidth:de,listHeight:De,listItemHeight:pe,listItemScrollOffset:Z,treeData:d,fieldNames:w,onSelect:tt,treeExpandAction:$e,treeTitleRender:Q,onPopupScroll:_e,leftMaxCount:ne===void 0?null:ne-et.length,leafCountOnly:B==="SHOW_CHILD"&&!M&&!!H,valueEntities:R}},[Re,de,De,pe,Z,d,w,tt,$e,Q,_e,ne,et.length,B,M,H,R]),nt=n.useMemo(function(){return{checkable:Ce,loadData:ue,treeLoadedKeys:Oe,onTreeLoad:Ve,checkedKeys:xe,halfCheckedKeys:we,treeDefaultExpandAll:se,treeExpandedKeys:Ne,treeDefaultExpandedKeys:Ke,onTreeExpand:He,treeIcon:fe,treeMotion:Fe,showTreeIcon:ve,switcherIcon:q,treeLine:me,treeNodeFilterProp:K,keyEntities:D}},[Ce,ue,Oe,Ve,xe,we,se,Ne,Ke,He,fe,Fe,ve,q,me,K,D]);return n.createElement(Wt.Provider,{value:rt},n.createElement(At.Provider,{value:nt},n.createElement(ir,Kt({ref:t},ge,{id:ae,prefixCls:l,mode:g?"multiple":void 0,displayValues:et,onDisplayValuesChange:Ct,searchValue:ye,onSearch:ze,OptionList:Or,emptyOptions:!j.length,onDropdownVisibleChange:gt,dropdownMatchSelectWidth:de}))))}),ot=Hr;ot.TreeNode=xt;ot.SHOW_ALL=wt;ot.SHOW_PARENT=It;ot.SHOW_CHILD=vt;const $r=e=>{const{componentCls:t,treePrefixCls:r,colorBgElevated:a}=e,l=`.${r}`;return[{[`${t}-dropdown`]:[{padding:`${Nt(e.paddingXS)} ${Nt(e.calc(e.paddingXS).div(2).equal())}`},gr(r,Ht(e,{colorBgContainer:a}),!1),{[l]:{borderRadius:0,[`${l}-list-holder-inner`]:{alignItems:"stretch",[`${l}-treenode`]:{[`${l}-node-content-wrapper`]:{flex:"auto"}}}}},Sr(`${r}-checkbox`,e),{"&-rtl":{direction:"rtl",[`${l}-switcher${l}-switcher_close`]:{[`${l}-switcher-icon svg`]:{transform:"rotate(90deg)"}}}}]}]};function Rr(e,t,r){return Ut("TreeSelect",a=>{const l=Ht(a,{treePrefixCls:t});return[$r(l)]},mr)(e,r)}var Ar=function(e,t){var r={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(r[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var l=0,a=Object.getOwnPropertySymbols(e);l<a.length;l++)t.indexOf(a[l])<0&&Object.prototype.propertyIsEnumerable.call(e,a[l])&&(r[a[l]]=e[a[l]]);return r};const Wr=(e,t)=>{var r,a,l,s,o;const{prefixCls:i,size:u,disabled:c,bordered:f=!0,style:T,className:L,rootClassName:E,treeCheckable:P,multiple:O,listHeight:N=256,listItemHeight:K,placement:Y,notFoundContent:J,switcherIcon:he,treeLine:H,getPopupContainer:M,popupClassName:Pe,dropdownClassName:ne,treeIcon:x=!1,transitionName:ie,choiceTransitionName:$="",status:Le,treeExpandAction:ue,builtinPlacements:Oe,dropdownMatchSelectWidth:Ve,popupMatchSelectWidth:se,allowClear:Ne,variant:Ke,dropdownStyle:He,dropdownRender:$e,popupRender:Re,onDropdownVisibleChange:Ae,onOpenChange:De,tagRender:We,maxCount:pe,showCheckedStrategy:ce,treeCheckStrictly:Z,styles:A,classNames:W}=e,de=Ar(e,["prefixCls","size","disabled","bordered","style","className","rootClassName","treeCheckable","multiple","listHeight","listItemHeight","placement","notFoundContent","switcherIcon","treeLine","getPopupContainer","popupClassName","dropdownClassName","treeIcon","transitionName","choiceTransitionName","status","treeExpandAction","builtinPlacements","dropdownMatchSelectWidth","popupMatchSelectWidth","allowClear","variant","dropdownStyle","dropdownRender","popupRender","onDropdownVisibleChange","onOpenChange","tagRender","maxCount","showCheckedStrategy","treeCheckStrictly","styles","classNames"]),{getPopupContainer:me,getPrefixCls:fe,renderEmpty:ve,direction:q,virtual:Fe,popupMatchSelectWidth:Q,popupOverflow:_e}=n.useContext(Xt),{styles:ge,classNames:ae}=qt("treeSelect"),[,F]=Gt(),Ce=K??(F==null?void 0:F.controlHeightSM)+(F==null?void 0:F.paddingXXS),Se=fe(),g=fe("select",i),je=fe("select-tree",i),ee=fe("tree-select",i),{compactSize:G,compactItemClassnames:Ze}=Yt(g,q),B=Dt(g),w=Dt(ee),[Qe,Te,ye]=ur(g,B),[be]=Rr(ee,je,w),[ze,j]=Jt("treeSelect",Ke,f),Be=_t(((r=W==null?void 0:W.popup)===null||r===void 0?void 0:r.root)||((a=ae==null?void 0:ae.popup)===null||a===void 0?void 0:a.root)||Pe||ne,`${ee}-dropdown`,{[`${ee}-dropdown-rtl`]:q==="rtl"},E,ae.root,W==null?void 0:W.root,ye,B,w,Te),D=((l=A==null?void 0:A.popup)===null||l===void 0?void 0:l.root)||((s=ge==null?void 0:ge.popup)===null||s===void 0?void 0:s.root)||He,R=Re||$e,v=De||Ae,d=!!(P||O),k=n.useMemo(()=>{if(!(pe&&(ce==="SHOW_ALL"&&!Z||ce==="SHOW_PARENT")))return pe},[pe,ce,Z]),C=sr(e.suffixIcon,e.showArrow),I=(o=se??Ve)!==null&&o!==void 0?o:Q,{status:z,hasFeedback:V,isFormItemInput:Ue,feedbackIcon:le}=n.useContext(Zt),Xe=nr(z,Le),{suffixIcon:it,removeIcon:ht,clearIcon:ut}=cr(Object.assign(Object.assign({},de),{multiple:d,showSuffixIcon:C,hasFeedback:V,feedbackIcon:le,prefixCls:g,componentName:"TreeSelect"})),xe=Ne===!0?{clearIcon:ut}:Ne;let we;J!==void 0?we=J:we=(ve==null?void 0:ve("Select"))||n.createElement(dr,{componentName:"Select"});const pt=$t(de,["suffixIcon","removeIcon","clearIcon","itemIcon","switcherIcon","style"]),mt=n.useMemo(()=>Y!==void 0?Y:q==="rtl"?"bottomRight":"bottomLeft",[Y,q]),st=Qt(rt=>{var nt;return(nt=u??G)!==null&&nt!==void 0?nt:rt}),et=n.useContext(er),ct=c??et,qe=_t(!i&&ee,{[`${g}-lg`]:st==="large",[`${g}-sm`]:st==="small",[`${g}-rtl`]:q==="rtl",[`${g}-${ze}`]:j,[`${g}-in-form-item`]:Ue},tr(g,Xe,V),Ze,L,E,ae.root,W==null?void 0:W.root,ye,B,w,Te),tt=rt=>n.createElement(Cr,{prefixCls:je,switcherIcon:he,treeNodeProps:rt,showLine:H}),[gt]=rr("SelectLike",D==null?void 0:D.zIndex),Ct=n.createElement(ot,Object.assign({virtual:Fe,disabled:ct},pt,{dropdownMatchSelectWidth:I,builtinPlacements:fr(Oe,_e),ref:t,prefixCls:g,className:qe,style:Object.assign(Object.assign({},A==null?void 0:A.root),T),listHeight:N,listItemHeight:Ce,treeCheckable:P&&n.createElement("span",{className:`${g}-tree-checkbox-inner`}),treeLine:!!H,suffixIcon:it,multiple:d,placement:mt,removeIcon:ht,allowClear:xe,switcherIcon:tt,showTreeIcon:x,notFoundContent:we,getPopupContainer:M||me,treeMotion:null,dropdownClassName:Be,dropdownStyle:Object.assign(Object.assign({},D),{zIndex:gt}),dropdownRender:R,onDropdownVisibleChange:v,choiceTransitionName:Tt(Se,"",$),transitionName:Tt(Se,"slide-up",ie),treeExpandAction:ue,tagRender:d?We:void 0,maxCount:k,showCheckedStrategy:ce,treeCheckStrictly:Z}));return Qe(be(Ct))},Fr=n.forwardRef(Wr),Je=Fr,jr=ar(Je,"dropdownAlign",e=>$t(e,["visible"]));Je.TreeNode=xt;Je.SHOW_ALL=wt;Je.SHOW_PARENT=It;Je.SHOW_CHILD=vt;Je._InternalPanelDoNotUseOrYouWillBeFired=jr;export{Je as T};
