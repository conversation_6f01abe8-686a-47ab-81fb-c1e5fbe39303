import{r as o,F as s,j as n,u as m,d as c,R as W,C as h,y as I,S as X,B as y,T as f}from"./index-De_f0oL2.js";import{R as Z,o as v,d as _}from"./down-BCLNnN1h.js";import{e as x}from"./service-Cudu09YY.js";import{s as ee}from"./index-BcPP1N8I.js";import{D as te}from"./index-CdSZ9YgQ.js";import{C as ne}from"./index-DZyVV6rP.js";import{S as r}from"./index-BWJehDyc.js";import{F as ae}from"./Table-D-iLeFE-.js";import"./useMultipleSelect-B0dEIXT-.js";const{RangePicker:le}=te,me=()=>{const R=[{title:"序号",dataIndex:"num",align:"center",width:50},{title:"账号",dataIndex:"loginName",align:"center",width:100},{title:"姓名",dataIndex:"fullName",align:"center",width:100},{title:"地市",dataIndex:"areaName",align:"center",width:100},{title:"部门",dataIndex:"orgName",align:"center",width:150,ellipsis:{showTitle:!1},render:e=>n.jsx(f,{title:e,placement:"topLeft",getPopupContainer:t=>(t==null?void 0:t.closest("div.ant-table-body"))||document.body,children:e})},{title:"区县",dataIndex:"districtName",align:"center",width:150,ellipsis:{showTitle:!1},render:e=>n.jsx(f,{title:e,placement:"topLeft",getPopupContainer:t=>(t==null?void 0:t.closest("div.ant-table-body"))||document.body,children:e})},{title:"网格",dataIndex:"gridName",align:"center",width:200,ellipsis:{showTitle:!1},render:e=>n.jsx(f,{title:e,placement:"topLeft",getPopupContainer:t=>(t==null?void 0:t.closest("div.ant-table-body"))||document.body,children:e})},{title:"角色",dataIndex:"roleNames",align:"center",width:200,ellipsis:{showTitle:!1},render:e=>n.jsx(f,{title:e,placement:"topLeft",getPopupContainer:t=>(t==null?void 0:t.closest("div.ant-table-body"))||document.body,children:e})},{title:"登录时间",dataIndex:"loginDate",align:"center",width:200},{title:"登录来源",dataIndex:"loginFrom",align:"center",width:100,render:e=>e==="20"?"执行力":e==="uac"?"门户":""},{title:"事件类型",dataIndex:"eventType",key:"eventType",align:"center",width:100,render:e=>{switch(e){case"100":return n.jsx("div",{children:"登录成功"});case"200":return n.jsx("div",{style:{color:"#ff0000"},children:"登录失败"});case"201":return n.jsx("div",{style:{color:"#ff0000"},children:"验证码错误"});case"202":return n.jsx("div",{style:{color:"#ff0000"},children:"用户禁用"});case"203":return n.jsx("div",{style:{color:"#ff0000"},children:"连续登录失败"});case"204":return n.jsx("div",{style:{color:"#ff0000"},children:"用户信息异常"})}}},{title:"IP",dataIndex:"hostAddress",align:"center",width:120}],j=o.useRef(null),[p]=s.useForm(),[L,A]=o.useState(0),[F]=o.useState(R),[Y,M]=o.useState([]),[O,g]=o.useState(!1),[i,C]=o.useState([]),[P,E]=o.useState([]),[b,N]=o.useState([]),[l,D]=o.useState({total:0,pageNum:1,pageSize:50}),{runAsync:k}=m(x.getQueryConditions,{manual:!0}),{runAsync:q}=m(x.loginlogDownload,{manual:!0}),{runAsync:V}=m(x.queryList,{manual:!0}),{runAsync:z}=m(x.loginLog,{manual:!0});o.useEffect(()=>{j.current&&A(j.current.offsetHeight),J(),Q()},[]),o.useEffect(()=>{(l==null?void 0:l.total)>0&&w()},[JSON.stringify(l)]);const J=async()=>{const[e,t]=await k({menuType:"",centerType:"loginJournal"});if(!e&&t.STATUS==="0000"){const{orgTree:a}=t.DATA,d=a[0]||{};N(a),C([d]),p.setFieldsValue({areaRecord:d==null?void 0:d.areaCode}),w(d)}},Q=async()=>{const[e,t]=await V({key:"getRoleList"});e||t.STATUS==="0000"&&E(t.DATA)},w=async(e=null)=>{var T;const t=p.getFieldsValue(),a=e||i[(i==null?void 0:i.length)-1]||{};g(!0);const[d,u]=await z({loginDateStart:t!=null&&t.loginDete?t==null?void 0:t.loginDete[0].format("YYYYMMDD"):"",loginDateEnd:t!=null&&t.loginDete?t==null?void 0:t.loginDete[1].format("YYYYMMDD"):"",adCode:a==null?void 0:a.areaCode,level:a==null?void 0:a.areaLevel,...t,...l,loginDete:void 0,areaRecord:void 0});if(g(!1),!d)if(u.STATUS==="0000"){const{DATA:{list:K}}=u;M(K),D({...l,total:(T=u.DATA)==null?void 0:T.total})}else ee.error(u==null?void 0:u.MESSAGE)},U=e=>{console.log("Success:",e),w()},B=async()=>{try{v("正在导出",0,"loading");const e=p.getFieldsValue(),t=i[(i==null?void 0:i.length)-1]||{},a=await q({loginDateStart:e!=null&&e.loginDete?e==null?void 0:e.loginDete[0].format("YYYYMMDD"):"",loginDateEnd:e!=null&&e.loginDete?e==null?void 0:e.loginDete[1].format("YYYYMMDD"):"",adCode:t==null?void 0:t.areaCode,level:t==null?void 0:t.areaLevel,...e,...l,loginDete:void 0,areaRecord:void 0});_(a)}catch(e){v("导出失败",1,"error"),console.error("Download failed:",e)}},H=()=>{p.resetFields();const e=b[0]||{};p.setFieldsValue({areaRecord:e==null?void 0:e.areaCode})},G=(e,t)=>t.some(a=>a.label.toLowerCase().indexOf(e.trim().toLowerCase())>-1),$=e=>{D({...e,pageNum:e==null?void 0:e.current})},S=(e,t)=>{const a=e.trim();return t.children.toLowerCase().includes(a.toLowerCase())};return n.jsxs("div",{className:"h-full pt-4",children:[n.jsx("div",{ref:j,className:"bg-white pt-4 px-8 mb-4",children:n.jsx(s,{form:p,initialValues:{loginDete:[c().subtract(1,"day"),c()]},onFinish:U,autoComplete:"off",children:n.jsxs(W,{gutter:24,children:[n.jsx(h,{span:6,children:n.jsx(s.Item,{label:"用户",name:"queryName",wrapperCol:{span:20},children:n.jsx(I,{placeholder:"请输入用户"})})}),n.jsx(h,{span:6,children:n.jsx(s.Item,{label:"登录时间",name:"loginDete",wrapperCol:{span:20},children:n.jsx(le,{style:{width:"100%"},allowClear:!1,ranges:{近一天:[c().subtract(1,"day"),c()],近三天:[c().subtract(3,"day"),c()],近七天:[c().subtract(7,"day"),c()]}})})}),n.jsx(h,{span:6,children:n.jsx(s.Item,{label:"组织机构",name:"areaRecord",wrapperCol:{span:20},children:n.jsx(ne,{allowClear:!1,changeOnSelect:!0,expandTrigger:"hover",displayRender:e=>e[e.length-1],options:b,fieldNames:{value:"areaCode",label:"areaName",children:"children"},onChange:(e,t)=>{console.log(e,t),C(t)},placeholder:"请选择组织机构",showSearch:{filter:G},onSearch:e=>console.log(e)})})}),n.jsx(h,{span:6,children:n.jsx(s.Item,{label:"部门",name:"orgName",wrapperCol:{span:20},children:n.jsx(I,{placeholder:"请输入部门"})})}),n.jsx(h,{span:6,children:n.jsx(s.Item,{label:"角色",name:"roleCodes",wrapperCol:{span:20},children:n.jsx(r,{placeholder:"请选择角色",className:"w-full",allowClear:!0,showSearch:!0,filterOption:S,children:P.map(e=>{const{roleCode:t,roleName:a}=e;return n.jsx(r.Option,{value:t,children:a},t)})})})}),n.jsx(h,{span:6,children:n.jsx(s.Item,{label:"事件类型",name:"eventType",wrapperCol:{span:20},children:n.jsxs(r,{placeholder:"请选择",className:"w-full",allowClear:!0,showSearch:!0,filterOption:S,children:[n.jsx(r.Option,{value:"100",children:"登录成功"},"100"),n.jsx(r.Option,{value:"200",children:"登录失败"},"200"),n.jsx(r.Option,{value:"201",children:"验证码错误"},"201"),n.jsx(r.Option,{value:"202",children:"用户禁用"},"202"),n.jsx(r.Option,{value:"203",children:"连续登录失败"},"203"),n.jsx(r.Option,{value:"204",children:"用户信息异常"},"204")]})})}),n.jsx(h,{span:6,children:n.jsx(s.Item,{labelCol:{span:0},wrapperCol:{span:24},children:n.jsxs(X,{size:"small",children:[n.jsx(y,{type:"primary",htmlType:"submit",children:"查询"}),n.jsx(y,{htmlType:"button",onClick:()=>H(),children:"重置"})]})})})]})})}),n.jsxs("div",{className:"py-4 px-8 mb-4 mt-4 bg-white overflow-auto",style:{height:`calc(100% - ${L+16}px)`},children:[n.jsxs("div",{className:"flex justify-between items-center mb-4",children:[n.jsx("span",{className:"font-bold text-[0.8rem]",children:"数据列表"}),n.jsx(y,{danger:!0,ghost:!0,icon:n.jsx(Z,{}),onClick:()=>B(),children:"导出"})]}),n.jsx(ae,{className:"mb-8",columns:F,dataSource:Y,loading:O,bordered:!0,scroll:{x:"max-content"},onChange:$,pagination:{...l,total:l==null?void 0:l.total,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50"]}})]})]})};export{me as default};
