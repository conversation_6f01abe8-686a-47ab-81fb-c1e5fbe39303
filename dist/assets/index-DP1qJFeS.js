import{F as k,r as o,A as we,u as w,d as c,j as e,R as J,C as I,S as Se,B as f,a4 as Te,T as h}from"./index-De_f0oL2.js";import{p as S}from"./service-B_wWoC3F.js";import{R as De,o as K,d as ke}from"./down-BCLNnN1h.js";import{R as Ie}from"./index-BmnYJy3v.js";import{s as g}from"./index-BcPP1N8I.js";import{D as be}from"./index-CdSZ9YgQ.js";import{C as Ye}from"./index-DZyVV6rP.js";import{R as Ae,a as Ne}from"./FullscreenOutlined-DzCTibKW.js";import{R as _e,U as Ce,a as ve}from"./index-BH5uxjwl.js";import{M as Re}from"./index-Dck5cc4J.js";import{P as Ee}from"./index-TkzW9Zmk.js";import{R as u}from"./InfoCircleOutlined-DUD7mNgc.js";import"./Table-D-iLeFE-.js";import"./index-BWJehDyc.js";import"./useMultipleSelect-B0dEIXT-.js";import"./DeleteOutlined-D-FcgX8f.js";const Fe="_detail_modal_1798o_2",Me="_salaryBreakDown_page_1798o_6",Be="_animation_box_1798o_6",T={detail_modal:Fe,salaryBreakDown_page:Me,animation_box:Be},{Dragger:Pe}=Ce,tt=()=>{var O,$,U,z,H;const[m]=k.useForm(),Y=o.useRef(null),A=o.useRef(null),N=o.useRef(null),[Q,W]=o.useState(0),[_,C]=o.useState(!0),[Z,v]=o.useState(!1),[X,b]=o.useState(!1),[x,ee]=o.useState([]),[te,ae]=o.useState([]),[ne,re]=o.useState([]),{currentUser:R}=we(),[d,E]=o.useState({total:0,pageNum:1,pageSize:50}),{runAsync:F}=w(S.getEnumType,{manual:!0}),{runAsync:se}=w(S.getSalaryBreakDown,{manual:!0}),{runAsync:le}=w(S.downloadTemplate,{manual:!0}),{runAsync:oe}=w(S.exportSalaryBreakDown,{manual:!0}),{runAsync:ie}=w(S.importSalaryBreakDown,{manual:!0}),ce=[{title:"单位",dataIndex:"cityName",key:"cityName",align:"center",width:160,fixed:"left"},{title:e.jsxs(e.Fragment,{children:[e.jsxs("span",{children:["2025年薪酬",e.jsx("br",{}),"预算合计（年）"]}),e.jsx(h,{title:"口径解释：填写占比、调整时填报",children:e.jsx(u,{})})]}),dataIndex:"annualSalaryBudget",key:"annualSalaryBudget",align:"center",width:180},{title:e.jsxs(e.Fragment,{children:[e.jsx("span",{children:"基本工资 "}),e.jsx(h,{title:"过节费、交通费、高温津贴、取暖补贴、信访岗位津贴、技能津贴、租房补贴、特殊补贴、加班费、夜班费等",children:e.jsx(u,{})})]}),dataIndex:"basicSalary",key:"basicSalary",align:"center",width:120},{title:e.jsxs(e.Fragment,{children:[e.jsx("span",{children:"津补贴 "}),e.jsx(h,{title:"过节费、交通费、高温津贴、取暖补贴、信访岗位津贴、技能津贴、租房补贴、特殊补贴、加班费、夜班费等",children:e.jsx(u,{})})]}),dataIndex:"allowances",key:"allowances",align:"center",width:120},{title:e.jsxs(e.Fragment,{children:[e.jsx("span",{children:"薪酬绩效 "}),e.jsx(h,{title:"不含增量收益分享、专项奖励、业绩奖励部分",children:e.jsx(u,{})})]}),key:"level",dataIndex:"level",align:"center",children:[{title:e.jsxs(e.Fragment,{children:[e.jsx("span",{children:"组织支撑岗 "}),e.jsx(h,{title:"岗位绩效",children:e.jsx(u,{})})]}),dataIndex:"orgSupportPosPerf",key:"orgSupportPosPerf",align:"center",width:130},{title:e.jsxs(e.Fragment,{children:[e.jsx("span",{children:"生产现业岗 "}),e.jsx(h,{title:"积分绩效",children:e.jsx(u,{})})]}),dataIndex:"prodOperPosPoints",key:"prodOperPosPoints",align:"center",width:130},{title:e.jsxs(e.Fragment,{children:[e.jsx("span",{children:"其他人员 "}),e.jsx(h,{title:"四级经理、营服中心、各部室下设基层责任单元负责人、高级经理、高级总监、24年新入职大学生、待岗及长病人员等",children:e.jsx(u,{})})]}),dataIndex:"otherPersonnel",key:"otherPersonnel",align:"center",width:130}]},{title:"增量收益分享",dataIndex:"incrementalSharing",key:"incrementalSharing",align:"center",width:130},{title:"专项奖励",key:"level",dataIndex:"level",align:"center",children:[{title:"本单位业绩相关",dataIndex:"unitPerformance",key:"unitPerformance",align:"center",width:150},{title:"荣誉/集团级",dataIndex:"honorGroupLevel",key:"honorGroupLevel",align:"center",width:150}]},{title:"业绩奖励",dataIndex:"performanceAward",key:"performanceAward",align:"center",width:130},{title:"其他：可补充",key:"level",dataIndex:"level",align:"center",children:[{title:"占比",dataIndex:"proportion",key:"proportion",align:"center",width:120},{title:"事项说明",dataIndex:"description",key:"description",align:"center",width:120}]}];o.useEffect(()=>(me(),L(),window.addEventListener("resize",M),()=>{window.removeEventListener("resize",M)}),[]);const M=()=>{y()};o.useEffect(()=>{y()},[(document.querySelector(".salaryBreakDown_table .ant-table-header")||{}).offsetHeight]);const y=()=>{var a;const t=(document.querySelector(".salaryBreakDown_table .ant-table-header")||{}).offsetHeight||0,n=(document.querySelector(".salaryBreakDown_table .ant-table-pagination")||{}).offsetHeight||26;t&&n&&W(((a=A.current)==null?void 0:a.offsetHeight)-(N.current.offsetHeight+t+n))},B=async t=>{var n;try{K("正在导出",0,"loading");let a=null;if(t===1)a=await le({templateId:"SUB_COMP_SAL_BUDGET_BKDN"});else if(t===2){const{loginDate:r,cityId:l,category:i,cycleId:p,empId:j}=m.getFieldsValue(),s={beginTime:(r==null?void 0:r.length)>0?c(r[0]).format("YYYY-MM-DD"):null,endTime:(r==null?void 0:r.length)>0?c(r[1]).format("YYYY-MM-DD"):null,cycleId:p?(n=c(p))==null?void 0:n.format("YYYY"):"",cityId:l?l[(l==null?void 0:l.length)-1]:null,category:i,empId:j};a=await oe(s)}ke(a)}catch(a){K("导出失败",1,"error"),console.error("Download failed:",a)}},de=t=>{D({...t})},me=async()=>{var l;const[[t,n],[a]]=await Promise.all([F({code:"1010",tag:1}),F({code:"EMPLOYEE_TRANSFER_CATEGORY"})]);if(t||a)return;n.STATUS==="0000"&&ae((l=n.DATA)==null?void 0:l.filter(i=>(i==null?void 0:i.orgId)!=="49757"));const r={...m.getFieldsValue()};D(r)},D=async t=>{var G,V;v(!0);const{loginDate:n,cityId:a,category:r,cycleId:l,empId:i}=t,p={...d,beginTime:(n==null?void 0:n.length)>0?c(n[0]).format("YYYY-MM-DD"):null,endTime:(n==null?void 0:n.length)>0?c(n[1]).format("YYYY-MM-DD"):null,cycleId:l?(G=c(l))==null?void 0:G.format("YYYY"):"",cityId:a?a[(a==null?void 0:a.length)-1]:null,category:r,empId:i},[j,s]=await se(p);if(v(!1),j){g.error((s==null?void 0:s.DATA)||(s==null?void 0:s.MESSAGE)||"调用失败");return}if(s.STATUS==="0000"){const{DATA:{data:ge}}=s,ye=ge.map((q,je)=>({...q,key:q.id||je}));re(ye),E({...d,total:(V=s.DATA)==null?void 0:V.totalCount})}else g.error((s==null?void 0:s.MESSAGE)||(s==null?void 0:s.DATA)||"调用失败")},he=async()=>{const t=new FormData;x.map(r=>r==null?void 0:r.originFileObj).forEach(r=>{t.append("file",r)});const[n,a]=await ie(t);if(n){g.error((a==null?void 0:a.DATA)||(a==null?void 0:a.MESSAGE)||"调用失败");return}if(a.STATUS==="0000"){const{loginDate:r,yearVal:l,cityId:i,category:p}=m.getFieldsValue(),j={beginTime:r.length>0?c(r[0]).format("YYYY-MM-DD"):null,endTime:r.length>0?c(r[1]).format("YYYY-MM-DD"):null,yearVal:l?c(l).format("YYYY"):"",cityId:i[(i==null?void 0:i.length)-1],category:p};y(),D(j),b(!1),g.success(a==null?void 0:a.DATA)}else g.error((a==null?void 0:a.MESSAGE)||(a==null?void 0:a.DATA)||"调用失败")},ue=()=>{(x==null?void 0:x.length)>0?he():g.error("请先选择文件上传")},P=()=>{b(!1)},xe=()=>{m.resetFields(),L()},pe=t=>{const n={total:t==null?void 0:t.total,pageNum:t==null?void 0:t.current,pageSize:t==null?void 0:t.pageSize};E(n)};o.useEffect(()=>{if((d==null?void 0:d.total)>0){const t={...m.getFieldsValue()};D(t)}},[d.pageNum,d.pageSize]);const L=()=>{const t=c();m.setFieldsValue({cycleId:t})},fe=(t,n)=>{var a,r;return(((a=n[0])==null?void 0:a.enumName)??"").toLowerCase().includes((r=t.toLowerCase())==null?void 0:r.trim())};return e.jsxs("div",{className:`pt-[0.5rem] px-[0rem] h-full flex flex-col ${T.salaryBreakDown_page}`,children:[e.jsxs(e.Fragment,{children:[e.jsx("div",{ref:Y,className:"bg-white pt-[0.5rem] px-[1.75rem] mb-[0.5rem]",children:e.jsx(k,{form:m,labelCol:{span:6},onFinish:de,initialValues:{loginDate:"",category:"KOU_JING_TIAO_ZHENG",cityId:"",empId:""},children:e.jsxs(J,{gutter:24,children:[e.jsx(I,{span:6,children:e.jsx(k.Item,{name:"cycleId",label:"账期",className:"mb-[0.5rem]",children:e.jsx(be,{className:"w-full",picker:"year"})})}),e.jsx(I,{span:5,children:e.jsx(k.Item,{name:"cityId",label:"单位",className:"mb-[0.5rem]",children:e.jsx(Ye,{allowClear:!0,changeOnSelect:!0,expandTrigger:"hover",displayRender:t=>t[t.length-1],options:te,fieldNames:{value:"enumId",label:"enumName",children:"children"},placeholder:"请选择单位",showSearch:{filter:fe},onSearch:t=>console.log(t)})})}),e.jsx(I,{span:4,children:e.jsx("div",{className:"text-right",children:e.jsxs(Se,{children:[e.jsx(f,{type:"primary",htmlType:"submit",children:"查询"}),e.jsx(f,{onClick:()=>xe(),children:"重置"})]})})})]})})}),e.jsxs("div",{ref:A,className:"relative bg-white px-5 pt-2.5",style:{height:`calc(100% - ${((O=Y.current)==null?void 0:O.offsetHeight)+15}px)`},children:[e.jsxs("div",{ref:N,className:`flex justify-between items-center overflow-hidden mb-[0.1rem] ${T.animation_box} ${_?"h-[1.8rem]":"h-0"}`,children:[e.jsxs("div",{className:"flex ",children:[_?e.jsx(Ae,{className:`${T.shousuo_icon} text-[1rem]`,onClick:()=>{C(!1),setTimeout(()=>{y()},200)}}):e.jsx(Ne,{className:`${T.shousuo_icon} absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10`,onClick:()=>{C(!0),setTimeout(()=>{y()},200)}}),e.jsx("div",{className:"font-bold text-[0.8rem] ml-3",children:"数据列表"})]}),e.jsx("div",{className:"flex gap-x-[2.5rem]",children:e.jsxs("div",{className:"flex gap-x-[0.75rem]",children:[!((U=($=R.roleInfo)==null?void 0:$.roleCode)!=null&&U.includes("ATJ0001"))&&e.jsxs("div",{className:"flex items-center gap-x-[0.25rem]",children:[e.jsx(Te,{name:"excel",width:20,height:20}),e.jsx("span",{className:"text-[#E60027] cursor-pointer",onClick:()=>B(1),children:"下载导入模版"})]}),!((H=(z=R.roleInfo)==null?void 0:z.roleCode)!=null&&H.includes("ATJ0001"))&&e.jsx(f,{danger:!0,ghost:!0,icon:e.jsx(_e,{}),onClick:()=>b(!0),children:"导入"}),e.jsx(f,{danger:!0,ghost:!0,icon:e.jsx(De,{}),onClick:()=>B(2),children:"导出"})]})})]}),e.jsx(Ie,{className:"salaryBreakDown_table",rowClassName:(t,n)=>n%2===1?"customRow odd":"customRow even",columns:ce,dataSource:ne,bordered:!0,scroll:{y:`calc(${Q}px - 0.625rem - 1.6rem - 0.5rem)`},loading:Z,onChange:pe,pagination:{...d,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50"]}})]})]}),e.jsx(Re,{title:"文件上传",destroyOnClose:!0,open:X,centered:!0,className:T.detail_modal,footer:null,onCancel:P,children:e.jsxs("div",{className:"mt-4 mb-8",style:{marginBottom:"0px"},children:[e.jsx(J,{children:e.jsx(I,{span:22,offset:1,className:"h-[10rem]",children:e.jsxs(Pe,{action:"",maxCount:1,multiple:!1,fileList:x,accept:".xls,.xlsx",beforeUpload(t,n){return console.log(t,n),!1},onChange(t){const{status:n}=t.file;n!=="uploading"&&(console.log(t.file,t.fileList),ee(t.fileList))},children:[e.jsx("p",{className:"ant-upload-drag-icon",children:e.jsx(ve,{style:{color:"#F14846"}})}),e.jsx("p",{className:"ant-upload-text",children:"点击或将文件拖拽到这里上传"}),e.jsx("p",{className:"ant-upload-hint",children:"支持excel格式的文件。"})]})})}),e.jsxs("div",{style:{display:"flex",justifyContent:"center",marginTop:"2rem"},children:[e.jsx(f,{danger:!0,onClick:P,children:"取消"}),e.jsx(Ee,{title:"",description:"导入文件如有重复数据，将会被覆盖，请确认是否上传。",onConfirm:ue,okText:"确认",cancelText:"取消",children:e.jsx(f,{danger:!0,type:"primary",style:{marginLeft:"1rem"},disabled:x.length<1,children:"上传"})})]})]})})]})};export{tt as default};
