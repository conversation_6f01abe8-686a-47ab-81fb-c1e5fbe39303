import{r as o,F as i,u as f,d as te,j as s,R as se,C as h,S as ae,B as x}from"./index-De_f0oL2.js";import{R as oe,o as M,d as re}from"./down-BCLNnN1h.js";import{s as m,i as p}from"./service-D0isGGPv.js";import{R as ne}from"./index-BmnYJy3v.js";import{s as u}from"./index-BcPP1N8I.js";import{D as le}from"./index-CdSZ9YgQ.js";import{S as ie}from"./index-BWJehDyc.js";import{R as ce,a as me}from"./FullscreenOutlined-DzCTibKW.js";import"./Table-D-iLeFE-.js";import"./useMultipleSelect-B0dEIXT-.js";const Re=()=>{var N;const g=[{title:"单位",key:"cityName",dataIndex:"cityName",align:"center",fixed:"left",width:300,children:[]},{title:"市场化退出率",key:"marketExitRate",dataIndex:"marketExitRate",align:"center",width:150,children:[]},{title:"市场化退出人数",key:"marketExitPerson",dataIndex:"marketExitPerson",align:"center",width:150,children:[]},{title:"合同制人数",key:"contractPerson",dataIndex:"contractPerson",align:"center",width:150,children:[]}],y=o.useRef(null),S=o.useRef(null),w=o.useRef(null),[n]=i.useForm(),[_,z]=o.useState(0),[R,F]=o.useState([]),[$,b]=o.useState(!1),[v,H]=o.useState(g),[d,L]=o.useState(0),[P,Y]=o.useState([]),[j,E]=o.useState(!0),[r,T]=o.useState({total:0,pageNum:1,pageSize:50}),{runAsync:q}=f(p.getEnumType,{manual:!0}),{runAsync:G}=f(p.getMarketExitRate,{manual:!0}),{runAsync:O}=f(p.exportMarketExitRate,{manual:!0});o.useEffect(()=>(H(g),V(),C(),c(),window.addEventListener("resize",k),()=>{window.removeEventListener("resize",k)}),[]);const k=()=>{l()};o.useEffect(()=>{l()},[(document.querySelector(".market_exitRate_table .ant-table-header")||{}).offsetHeight]),o.useEffect(()=>{l()},[R]),o.useEffect(()=>{d>0&&c()},[d]),o.useEffect(()=>{(r==null?void 0:r.total)>0&&c()},[r.pageNum,r.pageSize]);const l=()=>{var a;const t=(document.querySelector(".market_exitRate_table .ant-table-header")||{}).offsetHeight||0,e=(document.querySelector(".market_exitRate_table .ant-table-pagination")||{}).offsetHeight||26;t&&e&&z(((a=S.current)==null?void 0:a.offsetHeight)-(w.current.offsetHeight+t+e))},V=async()=>{const[t,e]=await q({code:"1010",tag:1});if(t){u.error((e==null?void 0:e.DATA)||(e==null?void 0:e.MESSAGE)||"调用失败");return}e.STATUS==="0000"?Y(e.DATA):u.error((e==null?void 0:e.MESSAGE)||(e==null?void 0:e.DATA)||"调用失败")},c=async()=>{var A,I;const t=n.getFieldsValue();b(!0);const[e,a]=await G({...t,cycleId:(A=t==null?void 0:t.cycleId)==null?void 0:A.format("YYYYMM"),...r});if(b(!1),e){u.error((a==null?void 0:a.DATA)||(a==null?void 0:a.MESSAGE)||"调用失败");return}if(a.STATUS==="0000"){const{DATA:{data:X}}=a,Z=X.map((D,ee)=>({...D,key:D.id||ee}));F(Z),T({...r,total:(I=a.DATA)==null?void 0:I.totalCount})}else u.error((a==null?void 0:a.MESSAGE)||(a==null?void 0:a.DATA)||"调用失败")},C=()=>{const t=te().subtract(1,"month");n.setFieldsValue({cycleId:t})},B=t=>{console.log("Success:",t),c()},U=async()=>{var t;try{M("正在导出",0,"loading");const e=n.getFieldsValue(),a=await O({...e,cycleId:(t=e==null?void 0:e.cycleId)==null?void 0:t.format("YYYYMM"),orgaId:e==null?void 0:e.cityId});re(a)}catch(e){M("导出失败",1,"error"),console.error("Download failed:",e)}},J=()=>{const t=d+1;n.resetFields(),C(),L(t)},K=(t,e)=>{var a;return((e==null?void 0:e.enumName)??"").toLowerCase().includes((a=t.toLowerCase())==null?void 0:a.trim())},Q=t=>{const e={total:t==null?void 0:t.total,pageNum:t==null?void 0:t.current,pageSize:t==null?void 0:t.pageSize};T(e)},W=()=>{};return s.jsxs("div",{className:`pt-[0.5rem] px-[0rem] h-full flex flex-col ${m.employment_page}`,children:[s.jsx("div",{ref:y,className:"bg-white pt-2 px-8 mb-2",children:s.jsx(i,{form:n,initialValues:{tag:""},onFinish:B,autoComplete:"off",children:s.jsxs(se,{gutter:24,children:[s.jsx(h,{span:6,children:s.jsx(i.Item,{label:"月份",name:"cycleId",wrapperCol:{span:20},className:"mb-[0.5rem]",children:s.jsx(le,{className:"w-full",allowClear:!1,onChange:W,picker:"month"})})}),s.jsx(h,{span:6,children:s.jsx(i.Item,{label:"单位",name:"cityId",wrapperCol:{span:20},className:"mb-[0.5rem]",children:s.jsx(ie,{placeholder:"请选择",className:"w-full",allowClear:!0,showSearch:!0,filterOption:K,options:P,fieldNames:{value:"enumId",label:"enumName"}})})}),s.jsx(h,{span:6,children:s.jsx(i.Item,{labelCol:{span:0},wrapperCol:{span:24},className:"mb-[0.5rem]",children:s.jsxs(ae,{size:"small",children:[s.jsx(x,{type:"primary",htmlType:"submit",children:"查询"}),s.jsx(x,{htmlType:"button",onClick:()=>J(),children:"重置"})]})})})]})})}),s.jsxs("div",{ref:S,className:"relative bg-white px-5 pt-2.5",style:{height:`calc(100% - ${((N=y.current)==null?void 0:N.offsetHeight)+15}px)`},children:[s.jsxs("div",{ref:w,className:`flex justify-between items-center overflow-hidden mb-2 ${m.animation_box} ${j?"h-[1.6rem]":"h-0"}`,children:[s.jsxs("div",{className:"flex ",children:[j?s.jsx(ce,{className:`${m.shousuo_icon} text-[1rem]`,onClick:()=>{E(!1),setTimeout(()=>{l()},200)}}):s.jsx(me,{className:`${m.shousuo_icon} absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10`,onClick:()=>{E(!0),setTimeout(()=>{l()},200)}}),s.jsx("div",{className:"font-bold text-[0.8rem] ml-3",children:"数据列表"})]}),s.jsx(x,{danger:!0,ghost:!0,icon:s.jsx(oe,{}),onClick:()=>U(),children:"导出"})]}),s.jsx(ne,{className:"market_exitRate_table",rowClassName:(t,e)=>e%2===1?"customRow odd":"customRow even",columns:v,dataSource:R,loading:$,bordered:!0,scroll:{y:`calc(${_}px - 0.625rem - 1.6rem - 0.5rem)`},onChange:Q,pagination:{...r,total:r==null?void 0:r.total,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50"]}})]})]})};export{Re as default};
