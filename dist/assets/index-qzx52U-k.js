import{D as x,F as a,r,j as e,y as c,B as T,u as g,R as he,C as d,S as pe,d as fe}from"./index-De_f0oL2.js";import{R as xe,o as M,d as ge}from"./down-BCLNnN1h.js";import{s as S}from"./Index.module-C_CsuTds.js";import{s as be}from"./index-BcPP1N8I.js";import{C as P}from"./index-DZyVV6rP.js";import{S as ye}from"./index-BWJehDyc.js";import{R as je,a as Se}from"./FullscreenOutlined-DzCTibKW.js";import{F as Te}from"./Table-D-iLeFE-.js";import{M as we}from"./index-Dck5cc4J.js";import"./useMultipleSelect-B0dEIXT-.js";const b={queryOperateLog:n=>x.post("/zhyy/manager/extra/operate/queryOperateLog",n),exportOperateLogExcel:n=>x.post("/zhyy/manager/extra/operate/exportOperateLogExcel",n,{responseType:"blob"}),build4LevelOrgTreeAll:n=>x.post("/zhyy/employee/org/build4LevelOrgTreeAll",n),buildEventTypeTree:n=>x.post("/zhyy/manager/extra/operate/buildEventTypeTree",n),queryAllEnableRole:()=>x.get("/zhyy/manager/core/role/queryAllEnableRole")},Ce=n=>{const{record:h,roleTypeList:w}=n,[p]=a.useForm(),[m,A]=r.useState(!1),[C,I]=r.useState(!1);r.useEffect(()=>{h&&p.setFieldsValue({...h})},[]);const N=v=>{};return e.jsxs(a,{form:p,labelCol:{span:6},wrapperCol:{span:14},labelAlign:"right",onFinish:N,children:[e.jsx(a.Item,{label:"工号",name:"roleName",rules:[{required:!0,message:"请输入工号！"}],children:e.jsx(c,{placeholder:"请输入工号"})}),e.jsx(a.Item,{label:"姓名",name:"roleName",rules:[{required:!0,message:"请输入姓名！"}],children:e.jsx(c,{placeholder:"请输入姓名"})}),e.jsx(a.Item,{label:"部门",name:"roleName",rules:[{required:!0,message:"请输入部门！"}],children:e.jsx(c,{placeholder:"请输入部门"})}),e.jsx(a.Item,{label:"角色",name:"roleName",rules:[{required:!0,message:"请输入角色！"}],children:e.jsx(c,{placeholder:"请输入角色"})}),e.jsx(a.Item,{label:"事件类型",name:"roleName",rules:[{required:!0,message:"请输入事件类型！"}],children:e.jsx(c,{placeholder:"请输入事件类型"})}),e.jsx(a.Item,{label:"操作时间",name:"roleName",rules:[{required:!0,message:"请输入操作时间！"}],children:e.jsx(c,{placeholder:"请输入操作时间"})}),e.jsx(a.Item,{wrapperCol:{offset:6,span:14},children:e.jsx(T,{loading:C,type:"primary",htmlType:"submit",children:"确定"})})]})},ze=()=>{var H,V;const n=[{title:"序号",key:"num",dataIndex:"num",align:"center",width:50,render:(t,l,s)=>(o==null?void 0:o.pageSize)*((o==null?void 0:o.pageNum)-1)+s+1},{title:"工号",key:"oa",dataIndex:"oa",align:"center",fixed:"left",width:50,children:[]},{title:"姓名",key:"fullName",dataIndex:"fullName",align:"center",fixed:"left",width:75,children:[]},{title:"部门",key:"unitName",dataIndex:"unitName",align:"center",fixed:"left",width:150,children:[]},{title:"角色",key:"roleName",dataIndex:"roleName",align:"center",fixed:"left",width:100,children:[]},{title:"事件类型",key:"operateDetail",dataIndex:"operateDetail",align:"center",fixed:"left",width:100,children:[]},{title:"操作时间",key:"operateDate",dataIndex:"operateDate",align:"center",fixed:"left",width:100,children:[]}],h=r.useRef(null),w=r.useRef(null),p=r.useRef(null),[m]=a.useForm(),[A,C]=r.useState(0),[I,N]=r.useState([]),[v,E]=r.useState(!1),[U,Y]=r.useState(n),[u,L]=r.useState([]),[B,G]=r.useState(0),[o,q]=r.useState({total:0,pageNum:1,pageSize:50}),[J,O]=r.useState([]),[Q,K]=r.useState([]),[W,X]=r.useState([]),[Z,F]=r.useState({}),[ee,D]=r.useState(!1),[z,k]=r.useState(!0),{runAsync:te}=g(b.build4LevelOrgTreeAll,{manual:!0}),{runAsync:le}=g(b.buildEventTypeTree,{manual:!0}),{runAsync:se}=g(b.queryAllEnableRole,{manual:!0}),{runAsync:re}=g(b.queryOperateLog,{manual:!0}),{runAsync:ae}=g(b.exportOperateLogExcel,{manual:!0});r.useEffect(()=>{oe(),Y(n),R()},[]),r.useEffect(()=>{y()},[(document.querySelector(".assessmentResults_table .ant-table-header")||{}).offsetHeight]),r.useEffect(()=>{y()},[I]),r.useEffect(()=>{(o==null?void 0:o.total)>0&&R()},[o.pageNum,o.pageSize]);const y=()=>{const t=(document.querySelector(".assessmentResults_table .ant-table-header")||{}).offsetHeight,l=(document.querySelector(".assessmentResults_table .ant-table-pagination")||{}).offsetHeight||26;t&&C(p.current.offsetHeight+t+l)},oe=async()=>{const[[t,l],[s,i],[j,f]]=await Promise.all([te({code:"1002",region:""}),le({code:"1003",region:""}),se({code:"1009",region:""})]);t||s||j||(l.STATUS==="0000"&&O(l.DATA),i.STATUS==="0000"&&K(i.DATA),f.STATUS==="0000"&&X(f.DATA))},R=async()=>{var j,f;const t=m.getFieldsValue(),l=u?u[u.length-1]:{};E(!0);const[s,i]=await re({...t,eventType:t!=null&&t.eventType?t==null?void 0:t.eventType[((j=t==null?void 0:t.eventType)==null?void 0:j.length)-1]:"",orgId4:Number(l==null?void 0:l.level)===4?l==null?void 0:l.orgId:"",orgId5:Number(l==null?void 0:l.level)===5?l==null?void 0:l.orgId:"",orgId6:Number(l==null?void 0:l.level)===6?l==null?void 0:l.orgId:"",...o});if(E(!1),!s)if(i.STATUS==="0000"){const{DATA:{data:ue}}=i;N(ue),q({...o,total:(f=i.DATA)==null?void 0:f.totalCount})}else be.error(i==null?void 0:i.MESSAGE)},ne=()=>{const t=fe().subtract(1,"month");m.setFieldsValue({month:t,org:[]})},$=(t,l)=>{L(l)},ie=t=>{console.log("Success:",t),R()},ce=async()=>{var t;try{M("正在导出",0,"loading");const l=m.getFieldsValue(),s=u?u[u.length-1]:{},i=await ae({...l,orgId4:Number(s==null?void 0:s.level)===4?s==null?void 0:s.orgId:"",orgId5:Number(s==null?void 0:s.level)===5?s==null?void 0:s.orgId:"",orgId6:Number(s==null?void 0:s.level)===6?s==null?void 0:s.orgId:"",month:(t=l==null?void 0:l.month)==null?void 0:t.format("YYYYMM")});ge(i)}catch(l){M("导出失败",1,"error"),console.error("Download failed:",l)}},de=()=>{const t=B+1;m.resetFields(),L([]),ne(),G(t)},_=(t,l)=>l.some(s=>s.orgName.toLowerCase().indexOf(t.trim().toLowerCase())>-1),me=t=>{const l={total:t==null?void 0:t.total,pageNum:t==null?void 0:t.current,pageSize:t==null?void 0:t.pageSize};q(l)};return e.jsxs("div",{className:`h-full pt-[0.5rem] px-[0rem] ${S.rbac_page}`,children:[e.jsx("div",{ref:h,className:"bg-white pt-2 px-8 mb-[0.5rem]",children:e.jsx(a,{form:m,labelCol:{span:5},wrapperCol:{span:19},initialValues:{tag:""},onFinish:ie,autoComplete:"off",children:e.jsxs(he,{gutter:24,children:[e.jsx(d,{span:6,children:e.jsx(a.Item,{label:"工号",name:"oa",children:e.jsx(c,{placeholder:"请输入工号",allowClear:!0})})}),e.jsx(d,{span:6,children:e.jsx(a.Item,{label:"组织",name:"org",children:e.jsx(P,{allowClear:!0,changeOnSelect:!0,expandTrigger:"hover",displayRender:t=>t[t.length-1],options:J,onChange:$,fieldNames:{value:"orgId",label:"orgName",children:"children"},placeholder:"请选择组织",showSearch:{filter:_},onSearch:t=>console.log(t)})})}),e.jsx(d,{span:6,children:e.jsx(a.Item,{label:"事件类型",name:"eventType",children:e.jsx(P,{allowClear:!0,changeOnSelect:!0,expandTrigger:"hover",displayRender:t=>t[t.length-1],options:Q,onChange:$,fieldNames:{value:"id",label:"name",children:"children"},placeholder:"请选择事件类型",showSearch:{filter:_},onSearch:t=>console.log(t)})})}),e.jsx(d,{span:6,children:e.jsx(a.Item,{label:"角色",name:"roleCode",children:e.jsx(ye,{className:"w-full",placeholder:"请选择角色",allowClear:!0,showSearch:!0,options:W,fieldNames:{value:"roleCode",label:"roleName"}})})}),e.jsx(d,{span:6,children:e.jsx(a.Item,{label:"页面名称",name:"menuName",children:e.jsx(c,{placeholder:"请输入页面名称",allowClear:!0})})}),e.jsx(d,{span:6,children:e.jsx(a.Item,{label:"姓名",name:"fullName",children:e.jsx(c,{placeholder:"请输入姓名",allowClear:!0})})}),e.jsx(d,{span:6,children:e.jsx(a.Item,{labelCol:{span:0},wrapperCol:{span:24},children:e.jsxs(pe,{size:"small",children:[e.jsx(T,{type:"primary",htmlType:"submit",children:"查询"}),e.jsx(T,{htmlType:"button",onClick:()=>de(),children:"重置"})]})})})]})})}),e.jsxs("div",{ref:w,className:"relative pb-[0.5rem] pt-[0.1rem] px-2 mt-[0.5rem] bg-white",style:{height:`calc(100% - ${((H=h.current)==null?void 0:H.offsetHeight)+17}px)`},children:[e.jsxs("div",{ref:p,className:`flex justify-between items-center overflow-hidden mb-[0.1rem] ${S.animation_box} ${z?"h-[1.6rem]":"h-0"}`,children:[e.jsx(e.Fragment,{children:z?e.jsx(je,{className:`${S.shousuo_icon} absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10`,onClick:()=>{k(!1),setTimeout(()=>{y()},300)}}):e.jsx(Se,{className:`${S.shousuo_icon} absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10`,onClick:()=>{k(!0),setTimeout(()=>{y()},300)}})}),e.jsx("span",{className:"font-bold text-[0.8rem] ml-[1rem]",children:"数据列表"}),e.jsx(T,{danger:!0,ghost:!0,icon:e.jsx(xe,{}),onClick:()=>ce(),children:"导出"})]}),e.jsx(Te,{className:"assessmentResults_table",columns:U,dataSource:I,loading:v,bordered:!0,scroll:{y:`calc(${((V=w.current)==null?void 0:V.offsetHeight)-A}px - 1.4rem)`},onChange:me,pagination:{...o,total:o==null?void 0:o.total,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50"]}})]}),e.jsx(we,{title:"系统日志编辑",open:ee,destroyOnClose:!0,onCancel:()=>{D(!1),F({})},footer:null,children:e.jsx(Ce,{record:Z,cancelRoleModal:()=>{D(!1),F({})}})})]})};export{ze as default};
