import{aS as x,aT as P,aU as v,aM as F,V as c,aN as O,aO as j,r as b,q as R,aP as E,aV as M,p as w}from"./index-De_f0oL2.js";let a=null,l=e=>e(),g=[],d={};function h(){const{getContainer:e,duration:n,rtl:r,maxCount:s,top:t}=d,o=(e==null?void 0:e())||document.body;return{getContainer:()=>o,duration:n,rtl:r,maxCount:s,top:t}}const G=c.forwardRef((e,n)=>{const{messageConfig:r,sync:s}=e,{getPrefixCls:t}=b.useContext(R),o=d.prefixCls||t("message"),u=b.useContext(E),[f,C]=M(Object.assign(Object.assign(Object.assign({},r),{prefixCls:o}),u.message));return c.useImperativeHandle(n,()=>{const i=Object.assign({},f);return Object.keys(i).forEach(m=>{i[m]=(...y)=>(s(),f[m].apply(f,y))}),{instance:i,sync:s}}),C}),I=c.forwardRef((e,n)=>{const[r,s]=c.useState(h),t=()=>{s(h)};c.useEffect(t,[]);const o=j(),u=o.getRootPrefixCls(),f=o.getIconPrefixCls(),C=o.getTheme(),i=c.createElement(G,{ref:n,sync:t,messageConfig:r});return c.createElement(O,{prefixCls:u,iconPrefixCls:f,theme:C},o.holderRender?o.holderRender(i):i)});function p(){if(!a){const e=document.createDocumentFragment(),n={fragment:e};a=n,l(()=>{F()(c.createElement(I,{ref:s=>{const{instance:t,sync:o}=s||{};Promise.resolve().then(()=>{!n.instance&&t&&(n.instance=t,n.sync=o,p())})}}),e)});return}a.instance&&(g.forEach(e=>{const{type:n,skipped:r}=e;if(!r)switch(n){case"open":{l(()=>{const s=a.instance.open(Object.assign(Object.assign({},d),e.config));s==null||s.then(e.resolve),e.setCloseFn(s)});break}case"destroy":l(()=>{a==null||a.instance.destroy(e.key)});break;default:l(()=>{var s;const t=(s=a.instance)[n].apply(s,w(e.args));t==null||t.then(e.resolve),e.setCloseFn(t)})}}),g=[])}function _(e){d=Object.assign(Object.assign({},d),e),l(()=>{var n;(n=a==null?void 0:a.sync)===null||n===void 0||n.call(a)})}function S(e){const n=v(r=>{let s;const t={type:"open",config:e,resolve:r,setCloseFn:o=>{s=o}};return g.push(t),()=>{s?l(()=>{s()}):t.skipped=!0}});return p(),n}function k(e,n){const r=v(s=>{let t;const o={type:e,args:n,resolve:s,setCloseFn:u=>{t=u}};return g.push(o),()=>{t?l(()=>{t()}):o.skipped=!0}});return p(),r}const H=e=>{g.push({type:"destroy",key:e}),p()},N=["success","info","warning","error","loading"],A={open:S,destroy:H,config:_,useMessage:P,_InternalPanelDoNotUseOrYouWillBeFired:x},D=A;N.forEach(e=>{D[e]=(...n)=>k(e,n)});export{D as s};
