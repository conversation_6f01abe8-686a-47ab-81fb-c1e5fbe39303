import{F as x,r,u,d as o,j as a,R as P,C as Y,S as we,B as h,a4 as Te,T as De}from"./index-De_f0oL2.js";import{p}from"./service-CUJHac7r.js";import{R as Ae,o as U,d as Ee}from"./down-BCLNnN1h.js";import Ne from"./index-B0EitxS2.js";import{R as be}from"./index-BmnYJy3v.js";import{f as _e}from"./format-ChnqMkgG.js";import{s as d}from"./index-BcPP1N8I.js";import{D as G}from"./index-CdSZ9YgQ.js";import{C as Ce}from"./index-DZyVV6rP.js";import{S as D}from"./index-BWJehDyc.js";import{R as Ie,a as Me}from"./FullscreenOutlined-DzCTibKW.js";import{R as ve,U as Re,a as ke}from"./index-BH5uxjwl.js";import{M as z}from"./index-Dck5cc4J.js";import{P as Ve}from"./index-TkzW9Zmk.js";import"./Table-D-iLeFE-.js";import"./useMultipleSelect-B0dEIXT-.js";import"./debounce-D4mn-XUD.js";import"./service-CEuf3VDV.js";import"./DeleteOutlined-D-FcgX8f.js";const Fe="_detail_modal_1o71m_2",Oe="_salaryBase_page_1o71m_13",Be="_animation_box_1o71m_13",Le="_over_ellipsis_1o71m_16",g={detail_modal:Fe,salaryBase_page:Oe,animation_box:Be,over_ellipsis:Le},{Dragger:He}=Re,{RangePicker:$e}=G,it=()=>{var H;const[f]=x.useForm(),C=r.useRef(null),I=r.useRef(null),M=r.useRef(null),[q,J]=r.useState(0),[X,A]=r.useState(!1),[K,E]=r.useState(!1),[y,Q]=r.useState([]),[W,N]=r.useState(!1),[b,Z]=r.useState("XIN_CHOU_JI_SHU"),[S,ee]=r.useState([]),[v,te]=r.useState([]),[ae,le]=r.useState([]),[R,k]=r.useState(!0),[m,V]=r.useState({total:0,pageNum:1,pageSize:50}),{runAsync:F}=u(p.getEnumType,{manual:!0}),{runAsync:ne}=u(p.getEmployeePostPag,{manual:!0}),{runAsync:oe}=u(p.downloadEmployeeTemplate,{manual:!0}),{runAsync:se}=u(p.downloadEmployeeCompare,{manual:!0}),{runAsync:re}=u(p.exportEmployeeExcel,{manual:!0}),{runAsync:ie}=u(p.uploadEmployeeExcel,{manual:!0}),{runAsync:ce}=u(p.addSaveBatch,{manual:!0}),de=[{title:"单位",width:130,dataIndex:"cityName",key:"cityName",align:"center",fixed:"left"},{title:"年份",width:50,dataIndex:"yearVal",key:"yearVal",align:"center",fixed:"left"},{title:"金额",dataIndex:"amount",key:"amount",align:"center",width:60,render:t=>a.jsx("span",{children:_e(t)})},{title:"类型",dataIndex:"category",key:"category",align:"center",width:80},{title:"导入人名称",dataIndex:"createName",key:"createName",align:"center",width:80},{title:"导入人编号",dataIndex:"createEmpId",key:"createEmpId",align:"center",width:80},{title:"导入人组织",dataIndex:"createOrgaName",key:"createOrgaName",align:"center",width:80,render:(t,l)=>a.jsx(De,{title:t,children:a.jsx("div",{className:g.over_ellipsis,children:t})})},{title:"导入时间",dataIndex:"createTime",key:"createTime",align:"center",width:100}],me=[{title:"单位",width:"30%",dataIndex:"cityId",key:"cityId",select:!0,fixed:"left",children:S},{title:"年份",width:"30%",dataIndex:"yearVal",key:"yearVal",fixed:"left",date:"year"},{title:"金额",dataIndex:"amount",key:"amount",width:"30%"}];r.useEffect(()=>(he(),window.addEventListener("resize",O),()=>{window.removeEventListener("resize",O)}),[]);const O=()=>{w()};r.useEffect(()=>{w()},[(document.querySelector(".salaryBase_table .ant-table-header")||{}).offsetHeight]);const w=()=>{var e;const t=(document.querySelector(".salaryBase_table .ant-table-header")||{}).offsetHeight||0,l=(document.querySelector(".salaryBase_table .ant-table-pagination")||{}).offsetHeight||26;t&&l&&J(((e=I.current)==null?void 0:e.offsetHeight)-(M.current.offsetHeight+t+l))},B=async t=>{try{U("正在导出",0,"loading");let l=null;if(t===1)l=await oe({templateId:"YEARLY_BASE"});else if(t===2){const{loginDate:e,yearVal:n,cityId:s,category:i}=f.getFieldsValue(),c={beginTime:e.length>0?o(e[0]).format("YYYY-MM-DD"):null,endTime:e.length>0?o(e[1]).format("YYYY-MM-DD"):null,yearVal:n?o(n).format("YYYY"):"",cityId:s[(s==null?void 0:s.length)-1],category:i};l=await re(c)}else t===3&&(l=await se());Ee(l)}catch(l){U("导出失败",1,"error"),console.error("Download failed:",l)}},ue=t=>{const{loginDate:l,yearVal:e,cityId:n,category:s}=t,i={beginTime:l.length>0?o(l[0]).format("YYYY-MM-DD"):null,endTime:l.length>0?o(l[1]).format("YYYY-MM-DD"):null,yearVal:e?o(e).format("YYYY"):"",cityId:n[(n==null?void 0:n.length)-1],category:s};console.log("Success:",i),T(i)},he=async()=>{var $;const[[t,l],[e,n]]=await Promise.all([F({code:"1010",tag:1}),F({code:"YEARLY_BASEDATA_CATEGORY"})]);if(t||e)return;l.STATUS==="0000"&&ee(($=l.DATA)==null?void 0:$.filter(_=>(_==null?void 0:_.orgId)!=="49757")),n.STATUS==="0000"&&te(n.DATA);const{loginDate:s,yearVal:i,cityId:c,category:j}=f.getFieldsValue(),Se={beginTime:s.length>0?o(s[0]).format("YYYY-MM-DD"):null,endTime:s.length>0?o(s[1]).format("YYYY-MM-DD"):null,yearVal:i?o(i).format("YYYY"):"",cityId:c[(c==null?void 0:c.length)-1],category:j};T(Se)},T=async t=>{var n;A(!0);const[l,e]=await ne({...t,...m});if(A(!1),l){d.error((e==null?void 0:e.DATA)||(e==null?void 0:e.MESSAGE)||"调用失败");return}if(e.STATUS==="0000"){const{DATA:{data:s}}=e,i=s.map((c,j)=>({...c,key:c.id||j}));le(i),V({...m,total:(n=e.DATA)==null?void 0:n.totalCount})}else d.error((e==null?void 0:e.MESSAGE)||(e==null?void 0:e.DATA)||"调用失败")},pe=async()=>{const t=new FormData;y.map(n=>n==null?void 0:n.originFileObj).forEach(n=>{t.append("file",n)}),t.append("category",b);const[l,e]=await ie(t);if(console.log("error, res",l,e),l){d.error((e==null?void 0:e.DATA)||(e==null?void 0:e.MESSAGE)||"调用失败");return}e.STATUS==="0000"?(E(!1),d.success(e==null?void 0:e.DATA)):d.error((e==null?void 0:e.MESSAGE)||(e==null?void 0:e.DATA)||"调用失败")},ge=()=>{(y==null?void 0:y.length)>0?pe():d.error("请先选择文件上传")},L=()=>{E(!1)},fe=()=>{f.resetFields()},ye=t=>{const l={total:t==null?void 0:t.total,pageNum:t==null?void 0:t.current,pageSize:t==null?void 0:t.pageSize};V(l)};r.useEffect(()=>{if((m==null?void 0:m.total)>0){const{loginDate:t,yearVal:l,cityId:e,category:n}=f.getFieldsValue(),s={beginTime:t.length>0?o(t[0]).format("YYYY-MM-DD"):null,endTime:t.length>0?o(t[1]).format("YYYY-MM-DD"):null,yearVal:l?o(l).format("YYYY"):"",cityId:e[(e==null?void 0:e.length)-1],category:n};T(s)}},[m.pageNum,m.pageSize]);const xe=(t,l)=>{var e,n;return(((e=l[0])==null?void 0:e.enumName)??"").toLowerCase().includes((n=t.toLowerCase())==null?void 0:n.trim())},Ye=async t=>{t.forEach(n=>{const s=S.filter(i=>i.enumId===n.cityId)[0].enumName;n.cityName=s,n.category=b,n.yearVal=o(n.yearVal).format("YYYY")}),console.log("record",t);const[l,e]=await ce(t);if(A(!1),l){d.error((e==null?void 0:e.DATA)||(e==null?void 0:e.MESSAGE)||"调用失败");return}if(e.STATUS==="0000"){d.success(e.DATA);const{loginDate:n,yearVal:s,cityId:i,category:c}=f.getFieldsValue(),j={beginTime:n.length>0?o(n[0]).format("YYYY-MM-DD"):null,endTime:n.length>0?o(n[1]).format("YYYY-MM-DD"):null,yearVal:s?o(s).format("YYYY"):"",cityId:i[(i==null?void 0:i.length)-1],category:c};T(j),N(!1)}else d.error((e==null?void 0:e.MESSAGE)||(e==null?void 0:e.DATA)||"调用失败")},je=t=>{console.log(t)};return a.jsxs("div",{className:`pt-[0.5rem] px-[0rem] h-full flex flex-col overflow-hidden ${g.salaryBase_page}`,children:[a.jsxs(a.Fragment,{children:[a.jsx("div",{ref:C,className:"bg-white pt-[0.5rem] px-[0.5rem] mb-[0.5rem]",children:a.jsx(x,{form:f,labelCol:{span:6},onFinish:ue,initialValues:{loginDate:[],yearVal:"",cityId:"",category:"XIN_CHOU_JI_SHU"},children:a.jsxs(P,{gutter:24,children:[a.jsx(Y,{span:7,children:a.jsx(x.Item,{label:"导入时间",name:"loginDate",wrapperCol:{span:24},className:"mb-[0rem]",children:a.jsx($e,{style:{width:"100%"},allowClear:!0,ranges:{近一天:[o().subtract(1,"day"),o()],近三天:[o().subtract(3,"day"),o()],近七天:[o().subtract(7,"day"),o()]}})})}),a.jsx(Y,{span:4,children:a.jsx(x.Item,{name:"yearVal",label:"年份",className:"mb-[0.5rem]",children:a.jsx(G,{className:"w-full",picker:"year",allowClear:!0})})}),a.jsx(Y,{span:5,children:a.jsx(x.Item,{name:"cityId",label:"单位",className:"mb-[0.5rem]",children:a.jsx(Ce,{allowClear:!0,changeOnSelect:!0,expandTrigger:"hover",displayRender:t=>t[t.length-1],options:S,fieldNames:{value:"enumId",label:"enumName",children:"children"},placeholder:"请选择",showSearch:{filter:xe}})})}),a.jsx(Y,{span:4,children:a.jsx(x.Item,{name:"category",label:"类型",className:"mb-[0.5rem]",children:a.jsx(D,{placeholder:"请选择类型",allowClear:!0,children:v.map(t=>{const{enumId:l,enumName:e}=t;return a.jsx(D.Option,{value:l,children:e},l)})})})}),a.jsx(Y,{span:4,children:a.jsx("div",{className:"text-right",children:a.jsxs(we,{children:[a.jsx(h,{type:"primary",htmlType:"submit",children:"查询"}),a.jsx(h,{danger:!0,ghost:!0,icon:a.jsx(Ae,{}),onClick:()=>B(2),children:"导出"}),a.jsx(h,{onClick:()=>fe(),children:"重置"})]})})})]})})}),a.jsxs("div",{ref:I,className:"relative bg-white px-5 pt-2.5",style:{height:`calc(100% - ${((H=C.current)==null?void 0:H.offsetHeight)+15}px)`},children:[a.jsxs("div",{ref:M,className:`flex justify-between items-center overflow-hidden mb-2 ${g.animation_box} ${R?"h-[1.8rem]":"h-0"}`,children:[a.jsxs("div",{className:"flex ",children:[R?a.jsx(Ie,{className:`${g.shousuo_icon} text-[1rem]`,onClick:()=>{k(!1),setTimeout(()=>{w()},200)}}):a.jsx(Me,{className:`${g.shousuo_icon} absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10`,onClick:()=>{k(!0),setTimeout(()=>{w()},200)}}),a.jsx("div",{className:"font-bold text-[0.8rem] ml-3",children:"数据列表"})]}),a.jsx("div",{className:"flex gap-x-[2.5rem]",children:a.jsxs("div",{className:"flex gap-x-[0.75rem]",children:[a.jsxs("div",{className:"flex items-center gap-x-[0.25rem]",children:[a.jsx(Te,{name:"excel",width:20,height:20}),a.jsx("span",{className:"text-[#E60027] cursor-pointer",onClick:()=>B(1),children:"下载导入模版"})]}),a.jsx(D,{placeholder:"请选择类型",defaultValue:b,allowClear:!0,style:{width:150},onChange:t=>{Z(t)},children:v.map(t=>{const{enumId:l,enumName:e}=t;return a.jsx(D.Option,{value:l,children:e},l)})}),a.jsx(h,{danger:!0,ghost:!0,icon:a.jsx(ve,{}),onClick:()=>E(!0),children:"导入"}),a.jsx(h,{danger:!0,type:"primary",onClick:()=>N(!0),children:"新增"})]})})]}),a.jsx(be,{className:"salaryBase_table",rowClassName:(t,l)=>l%2===1?"customRow odd":"customRow even",columns:de,dataSource:ae,scroll:{y:`calc(${q}px - 0.625rem - 1.6rem - 0.5rem)`},loading:X,onChange:ye,pagination:{...m,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50"]}})]})]}),a.jsx(z,{title:"新增数据",destroyOnClose:!0,open:W,centered:!0,className:g.detail_modal,footer:null,onCancel:()=>N(!1),width:"50%",children:a.jsx(Ne,{columns:me,addData:Ye,unitList:S})}),a.jsx(z,{title:"文件上传",destroyOnClose:!0,open:K,centered:!0,className:g.detail_modal,footer:null,onCancel:L,children:a.jsxs("div",{className:"mt-4 mb-8",style:{marginBottom:"0px"},children:[a.jsx(P,{children:a.jsx(Y,{span:22,offset:1,className:"h-[10rem]",children:a.jsxs(He,{action:"",maxCount:1,multiple:!1,fileList:y,beforeUpload(t,l){return console.log(t,l),!1},onChange(t){const{status:l}=t.file;l!=="uploading"&&(console.log(t.file,t.fileList),Q(t.fileList))},children:[a.jsx("p",{className:"ant-upload-drag-icon",children:a.jsx(ke,{style:{color:"#F14846"}})}),a.jsx("p",{className:"ant-upload-text",children:"点击或将文件拖拽到这里上传"}),a.jsx("p",{className:"ant-upload-hint",children:"支持excel格式的文件。"})]})})}),a.jsxs("div",{style:{display:"flex",justifyContent:"center",marginTop:"2rem"},children:[a.jsx(h,{danger:!0,onClick:L,children:"取消"}),a.jsx(Ve,{title:"",description:"导入文件如有重复数据，将会被覆盖，请确认是否上传。",onConfirm:ge,onCancel:je,okText:"确认",cancelText:"取消",children:a.jsx(h,{danger:!0,type:"primary",style:{marginLeft:"1rem"},disabled:y.length<1,children:"上传"})})]})]})})]})};export{it as default};
