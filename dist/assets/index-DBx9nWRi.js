import{F as i,r as l,u as f,d as p,j as a,R as te,C as x,S as ae,B as g,T as se}from"./index-De_f0oL2.js";import{s as c,c as y}from"./Index.module-CMg3LkdG.js";import{R as le,o as M,d as ne}from"./down-BCLNnN1h.js";import{R as re}from"./index-BmnYJy3v.js";import{s as u}from"./index-BcPP1N8I.js";import{D as oe}from"./index-CdSZ9YgQ.js";import{S as ie}from"./index-BWJehDyc.js";import{R as ce,a as de}from"./FullscreenOutlined-DzCTibKW.js";import"./Table-D-iLeFE-.js";import"./useMultipleSelect-B0dEIXT-.js";const be=()=>{var k;const[r]=i.useForm(),S=l.useRef(null),w=l.useRef(null),b=l.useRef(null),[P,D]=l.useState(0),[R,F]=l.useState([]),[$,j]=l.useState(!1),[z,E]=l.useState([]),[m,_]=l.useState(0),[H,Q]=l.useState([]),[I,T]=l.useState(!0),[n,N]=l.useState({total:0,pageNum:1,pageSize:50}),A=()=>{const e=r.getFieldValue("cycleId"),t=e?p(e).subtract(1,"year").endOf("year"):p().subtract(1,"year").endOf("year");return[{title:"单位",key:"unit",dataIndex:"unit",align:"center",fixed:"left",width:120,children:[],render:(s,h)=>a.jsx(se,{title:s,children:a.jsx("div",{className:c.over_ellipsis,children:s})})},{title:`上年末人数(${t.format("YYYY.MM")})`,key:"lastYearEndPeople",dataIndex:"lastYearEndPeople",align:"center",children:[{title:"三级经理",key:"managerLevel3LastYear",dataIndex:"managerLevel3LastYear",align:"center",children:[],width:120},{title:"基层责任单元经理",key:"unitManagerLastYear",dataIndex:"unitManagerLastYear",align:"center",children:[],width:120},{title:"合计",key:"totalLastYear",dataIndex:"totalLastYear",align:"center",children:[],width:100}]},{title:"退出数（月度取数，不含提拔）",key:"exitPeople",dataIndex:"exitPeople",align:"center",children:[{title:"三级经理",key:"managerLevel3Quit",dataIndex:"managerLevel3Quit",align:"center",children:[],width:120},{title:"基层责任单元经理",key:"unitManagerQuit",dataIndex:"unitManagerQuit",align:"center",children:[],width:120},{title:"合计",key:"totalQuit",dataIndex:"totalQuit",align:"center",children:[],width:100}]},{title:"退出比例",key:"exitRatio",dataIndex:"exitRatio",align:"center",children:[{title:"三级经理",key:"managerLevel3Scale",dataIndex:"managerLevel3Scale",align:"center",children:[],width:120},{title:"基层责任单元经理",key:"unitManagerScale",dataIndex:"unitManagerScale",align:"center",children:[],width:120},{title:"合计",key:"totalScale",dataIndex:"totalScale",align:"center",children:[],width:100}]}]},{runAsync:O}=f(y.getEnumType,{manual:!0}),{runAsync:V}=f(y.getExitPeopleReportList,{manual:!0}),{runAsync:q}=f(y.exportExitPeopleReport,{manual:!0});l.useEffect(()=>(E(A()),G(),L(),d(),window.addEventListener("resize",C),()=>{window.removeEventListener("resize",C)}),[]),l.useEffect(()=>{E(A())},[r.getFieldValue("cycleId")]);const C=()=>{o()};l.useEffect(()=>{o()},[(document.querySelector(".exitPeopleReport_table .ant-table-header")||{}).offsetHeight]),l.useEffect(()=>{o()},[R]),l.useEffect(()=>{m>0&&d()},[m]),l.useEffect(()=>{(n==null?void 0:n.total)>0&&d()},[n.pageNum,n.pageSize]);const o=()=>{var s;const e=(document.querySelector(".exitPeopleReport_table .ant-table-header")||{}).offsetHeight||0,t=(document.querySelector(".exitPeopleReport_table .ant-table-pagination")||{}).offsetHeight||26;e&&t&&D(((s=w.current)==null?void 0:s.offsetHeight)-(b.current.offsetHeight+e+t))},G=async()=>{const[e,t]=await O({code:"1010",tag:1});if(e){u.error((t==null?void 0:t.DATA)||(t==null?void 0:t.MESSAGE)||"调用失败");return}t.STATUS==="0000"?Q(t.DATA):u.error((t==null?void 0:t.MESSAGE)||(t==null?void 0:t.DATA)||"调用失败")},d=async()=>{var h,Y;const e=r.getFieldsValue();j(!0);const[t,s]=await V({map:{unit:(e==null?void 0:e.unit)??"",cycleId:(h=e==null?void 0:e.cycleId)==null?void 0:h.format("YYYYMM")},pagination:{...n}});if(j(!1),t){u.error((s==null?void 0:s.DATA)||(s==null?void 0:s.MESSAGE)||"调用失败");return}if(s.STATUS==="0000"){const{DATA:{data:X}}=s,Z=X.map((v,ee)=>({...v,key:v.id||ee}));F(Z),N({...n,total:(Y=s.DATA)==null?void 0:Y.totalCount})}else u.error((s==null?void 0:s.MESSAGE)||(s==null?void 0:s.DATA)||"调用失败")},L=()=>{const e=p().subtract(1,"month");r.setFieldsValue({cycleId:e})},B=e=>{console.log("Success:",e),d()},U=async()=>{var e;try{M("正在导出",0,"loading");const t=r.getFieldsValue(),s=await q({map:{...t,cycleId:(e=t==null?void 0:t.cycleId)==null?void 0:e.format("YYYYMM")}});ne(s)}catch(t){M("导出失败",1,"error"),console.error("Download failed:",t)}},J=()=>{const e=m+1;r.resetFields(),L(),_(e)},K=e=>{const t={total:e==null?void 0:e.total,pageNum:e==null?void 0:e.current,pageSize:e==null?void 0:e.pageSize};N(t)},W=(e,t)=>{var s;return((t==null?void 0:t.enumName)??"").toLowerCase().includes((s=e.toLowerCase())==null?void 0:s.trim())};return a.jsxs("div",{className:`pt-[0.5rem] px-[0rem] h-full flex flex-col ${c.employment_page}`,children:[a.jsx("div",{ref:S,className:"bg-white pt-2 px-8 mb-2",children:a.jsx(i,{form:r,initialValues:{tag:""},onFinish:B,autoComplete:"off",children:a.jsxs(te,{gutter:24,children:[a.jsx(x,{span:6,children:a.jsx(i.Item,{label:"月份",name:"cycleId",wrapperCol:{span:20},className:"mb-[0.5rem]",children:a.jsx(oe,{className:"w-full",allowClear:!1,picker:"month"})})}),a.jsx(x,{span:6,children:a.jsx(i.Item,{label:"单位",name:"unit",wrapperCol:{span:20},className:"mb-[0.5rem]",children:a.jsx(ie,{placeholder:"请选择",className:"w-full",allowClear:!0,showSearch:!0,filterOption:W,options:H,fieldNames:{value:"enumName",label:"enumName"}})})}),a.jsx(x,{span:6,children:a.jsx(i.Item,{labelCol:{span:0},wrapperCol:{span:24},className:"mb-[0.5rem]",children:a.jsxs(ae,{size:"small",children:[a.jsx(g,{type:"primary",htmlType:"submit",children:"查询"}),a.jsx(g,{htmlType:"button",onClick:()=>J(),children:"重置"})]})})})]})})}),a.jsxs("div",{ref:w,className:"relative bg-white px-5 pt-2.5",style:{height:`calc(100% - ${((k=S.current)==null?void 0:k.offsetHeight)+15}px)`},children:[a.jsxs("div",{ref:b,className:`flex justify-between items-center overflow-hidden mb-2 ${c.animation_box} ${I?"h-[1.8rem]":"h-0"}`,children:[a.jsxs("div",{className:"flex ",children:[I?a.jsx(ce,{className:`${c.shousuo_icon} text-[1rem]`,onClick:()=>{T(!1),setTimeout(()=>{o()},200)}}):a.jsx(de,{className:`${c.shousuo_icon} absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10`,onClick:()=>{T(!0),setTimeout(()=>{o()},200)}}),a.jsxs("div",{className:"font-bold text-[0.8rem] ml-3",children:["数据列表",a.jsx("span",{className:"text-[0.7rem]",style:{color:"#939393"},children:"（单位：万元）"})]})]}),a.jsx(g,{danger:!0,ghost:!0,icon:a.jsx(le,{}),onClick:()=>U(),children:"导出"})]}),a.jsx(re,{className:"exitPeopleReport_table",rowClassName:(e,t)=>t%2===1?"customRow odd":"customRow even",columns:z,dataSource:R,loading:$,bordered:!0,scroll:{y:`calc(${P}px - 0.625rem - 1.6rem - 0.5rem)`},onChange:K,pagination:{...n,total:n==null?void 0:n.total,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50"]}})]})]})};export{be as default};
