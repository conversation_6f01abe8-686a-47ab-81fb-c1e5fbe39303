import{D as j,F as u,u as C,r as i,cb as E,j as e,a4 as L,cc as _,y as f,W as G,S as z,B as y,aN as H,R as J,C as V,cd as W}from"./index-De_f0oL2.js";import{a as K,S as $}from"./index-BWJehDyc.js";import{R as k,F as Q}from"./Table-D-iLeFE-.js";import{s as X}from"./Index.module-C_CsuTds.js";import{M as Y}from"./index-Dck5cc4J.js";import{P as Z}from"./index-TkzW9Zmk.js";import"./useMultipleSelect-B0dEIXT-.js";const ee={DownOutlined:K},g={getMenuTree:()=>j.get("/zhyy/manager/core/menu/buildMenuTree"),addMenu:s=>j.post("/zhyy/manager/core/menu/addMenu",s),updateMenu:s=>j.post("/zhyy/manager/core/menu/updateMenu",s),deleteMenu:s=>j.post("/zhyy/manager/core/menu/deleteMenu",s),obtainNextMenuCode:s=>j.post("/zhyy/manager/core/menu/obtainNextMenuCode",s),getFilterMenuData:s=>j.post("/zhyy/manager/core/menu/queryMenu",s)},ne={display:"flex",maxWidth:"25rem",flexWrap:"wrap"},te=({modeType:s,record:h,closeModel:M})=>{const[T]=u.useForm(),{runAsync:F}=C(g.addMenu,{manual:!0}),{loading:S,runAsync:A}=C(g.updateMenu,{manual:!0}),b=(a,r,l)=>a.map(t=>{const c=r[t.menuCode],p=(l==null?void 0:l.parentPaths)||[],m=(l?`${p.at(-1)}${t.url}`:t.url)||"";return{...t,path:m,parentPaths:p,menuLink:`${m}`,menuTag:t==null?void 0:t.tag,displayType:t==null?void 0:t.showType,children:c!=null&&c.length?b(c,r,{...t,parentPaths:[...p,m||""].filter(w=>w)}):void 0}}),N=a=>{T.setFieldValue("menuTag",a)},v=i.useMemo(()=>E.map(a=>({key:a,label:e.jsx(L,{name:a,color:"#F66A5C",onClick:N.bind(null,a)},a)})),[E]),R=i.useMemo(()=>{if(s==="edit"){const a=_([h]),r=a.reduce((l,t)=>(t.parentCode&&(l[t.parentCode]||(l[t.parentCode]=[]),l[t.parentCode].push(t)),l),{});return b(a,r)[0]}if(s==="add"){const{menuCode:a,nextMenuCode:r}=h;return{parentMenuCode:a,menuCode:r,sortId:r,menuType:"1",displayType:"1"}}},[h]),q=async()=>{const{parentCode:a,menuCode:r,menuName:l,menuType:t,outMenuCode:c,outMenuLink:p,menuLink:m,displayType:w,menuTag:O,sortId:U}=T.getFieldsValue(),I={parentCode:a,menuCode:r,menuName:l,menuType:t,aliasCode:c,referUrl:p,url:m,showType:w,tag:O,sortId:U};s==="edit"&&(I.id=h==null?void 0:h.id);const o=s==="edit"?A:F,[n,x]=await o(I);n||x.STATUS==="0000"&&M()};return e.jsxs(u,{form:T,labelCol:{span:6},initialValues:R,onFinish:q,children:[e.jsx(u.Item,{name:"parentCode",label:"父级菜单编码",children:e.jsx(f,{placeholder:"请输入父级菜单编码",autoComplete:"off"})}),e.jsx(u.Item,{label:"菜单编码",name:"menuCode",children:e.jsx(f,{placeholder:"请输入菜单编码",autoComplete:"off"})}),e.jsx(u.Item,{label:"菜单名称",name:"menuName",rules:[{required:!0,message:"请输入菜单名称"}],children:e.jsx(f,{placeholder:"请输入菜单名称",autoComplete:"off"})}),e.jsx(u.Item,{label:"菜单类型",name:"menuType",children:e.jsxs($,{placeholder:"请选择菜单类型",children:[e.jsx($.Option,{value:"1",children:"内部菜单"},"1"),e.jsx($.Option,{value:"2",children:"外部链接"},"2")]})}),e.jsx(u.Item,{label:"链接",name:"menuLink",children:e.jsx(f,{placeholder:"请输入链接",allowClear:!0})}),e.jsx(u.Item,{label:"显示类型",name:"displayType",children:e.jsxs(k.Group,{buttonStyle:"solid",children:[e.jsx(k.Button,{value:"1",children:"默认"}),e.jsx(k.Button,{value:"2",children:"嵌入"}),e.jsx(k.Button,{value:"3",children:"跳转"}),e.jsx(k.Button,{value:"4",children:"无侧边栏菜单"})]})}),e.jsx(u.Item,{name:"menuTag",noStyle:!0}),e.jsx(u.Item,{label:"菜单图标",shouldUpdate:(a,r)=>a.menuTag!==r.menuTag,children:({getFieldValue:a})=>{const r=a("menuTag")||"";return e.jsx(G,{trigger:["click"],menu:{items:v},dropdownRender:l=>e.jsx("div",{children:i.cloneElement(l,{style:ne})}),children:e.jsx("a",{onClick:l=>l.preventDefault(),children:e.jsxs(z,{children:[r?e.jsx(L,{name:r,color:"#F66A5C"}):"请选择图标",i.createElement(ee.DownOutlined)]})})})}}),e.jsx(u.Item,{label:"排序字段",name:"sortId",rules:[{pattern:/^[+]{0,1}(\d+)$/,message:"只支持数字，请正确输入！"}],children:e.jsx(f,{placeholder:"请输入排序字段",autoComplete:"off"})}),e.jsx(u.Item,{wrapperCol:{offset:6},children:e.jsx(y,{loading:S,type:"primary",htmlType:"submit",children:"确定"})})]})},ie=()=>{const[s]=u.useForm(),[h,M]=i.useState(!1),[T,F]=i.useState(null),[S,A]=i.useState(""),[b,N]=i.useState([]),{loading:v,runAsync:R}=C(g.getMenuTree,{manual:!0}),{runAsync:q}=C(g.deleteMenu,{manual:!0}),{runAsync:a}=C(g.obtainNextMenuCode,{manual:!0}),{runAsync:r}=C(g.getFilterMenuData,{manual:!0}),l=[{title:"菜单名称",dataIndex:"menuName",key:"menuName",width:200},{title:"菜单编码",dataIndex:"menuCode",key:"menuCode",width:200},{title:"URL",key:"url",dataIndex:"url",width:400},{title:"操作",key:"action",width:250,render:(o,n)=>e.jsxs(z,{children:[e.jsx(y,{type:"link",onClick:m.bind(null,"edit",n),children:"修改"}),e.jsx(y,{type:"link",onClick:O.bind(null,n),children:"删除"}),n.children?e.jsx(y,{type:"link",onClick:m.bind(null,"add",n),children:"新增子节点"}):e.jsx(Z,{title:"该节点是叶子节点,增加子节点后该节点将不再是叶子节点,确定新增么?",onConfirm:m.bind(null,"add",n),okText:"确定",cancelText:"取消",children:e.jsx(y,{type:"link",children:"新增子节点"})})]})}];i.useEffect(()=>{t()},[]);const t=async(o={})=>{const n=Object.keys(o).length?r:R,[x,d]=await n(o);if(!x&&d.STATUS==="0000"){const D=Object.keys(o).length?d==null?void 0:d.DATA.data:d==null?void 0:d.DATA;N(D)}},c=o=>o==null?void 0:o.map(n=>({...n,key:n==null?void 0:n.menuCode,children:n!=null&&n.children?c(n==null?void 0:n.children):[]})),p=i.useMemo(()=>c(b),[b]),m=async(o,n)=>{if(o==="add"){const[x,d]=await a({parentCode:n==null?void 0:n.menuCode});if(x)return;d.STATUS==="0000"&&(n.nextMenuCode=d==null?void 0:d.DATA)}M(!0),A(o),F(n)},w=()=>{M(!1),t()},O=o=>{const{menuName:n,menuCode:x,children:d}=o;W.modal.confirm({title:e.jsxs("div",{children:["确定删除",e.jsx("span",{className:"text-[#ff0000]",children:n}),"菜单吗？"]}),onOk:async()=>{const D={menuCode:x,deleteCascade:d.length},[B,P]=await q(D);B||P.STATUS==="0000"&&(W.message.success("删除成功！"),t())},footer:(D,{OkBtn:B,CancelBtn:P})=>e.jsxs(e.Fragment,{children:[e.jsx(B,{}),e.jsx(P,{})]})})},U=()=>{const{queryName:o}=s.getFieldsValue();t(o?{queryName:o}:{})},I=()=>{s.resetFields()};return e.jsxs("div",{className:`pt-[0.5rem] px-[0rem] h-full ${X.rbac_page}`,children:[e.jsx("div",{className:"bg-white pt-[0.5rem] px-[1.75rem] mb-[0.5rem]",children:e.jsx(H,{theme:{components:{Form:{itemMarginBottom:0}}},children:e.jsx(u,{form:s,labelCol:{span:6},onFinish:U,children:e.jsxs(J,{gutter:24,children:[e.jsx(V,{span:6,children:e.jsx(u.Item,{name:"queryName",label:"菜单",children:e.jsx(f,{placeholder:"请输入菜单名称或菜单编码",autoComplete:"off",allowClear:!0})})}),e.jsx(V,{span:18,children:e.jsxs(z,{children:[e.jsx(y,{type:"primary",htmlType:"submit",children:"查询"}),e.jsx(y,{onClick:I,children:"重置"})]})})]})})})}),e.jsx("div",{className:"bg-white p-[0.5rem] px-[0.5rem] h-[calc(100%-3.8rem)] overflow-y-auto",children:e.jsx(Q,{loading:v,columns:l,dataSource:p})}),e.jsx(Y,{title:`${S==="add"?"新增":"修改"}菜单`,open:h,destroyOnClose:!0,onCancel:()=>{M(!1)},footer:null,children:e.jsx(te,{modeType:S,record:T,closeModel:w})})]})};export{ie as default};
