import{D as t}from"./index-De_f0oL2.js";const y={getEnumType:e=>t.post("/zhyy/employee/getEnumType",e),getEmployeeMonthlyList:e=>t.post("/zhyy/cadre/employeeMonthlyReport/list",e),getEmployeeByEmpId:e=>t.get("/zhyy/cadre/employeeMonthlyReport/getEmpInfo",{params:e}),addEmployeeMonthlyReport:e=>t.post("/zhyy/cadre/employeeMonthlyReport/save",e),updateEmployeeMonthlyReport:e=>t.post("/zhyy/cadre/employeeMonthlyReport/update",e),deleteEmployeeMonthlyList:e=>t.post("/zhyy/cadre/employeeMonthlyReport/deleteByEmpId",e),exportPeopleMonthReport:e=>t.post("/zhyy/cadre/employeeMonthlyReport/exportExcel",e,{responseType:"blob"}),getExitPeopleReportList:e=>t.post("/zhyy/cadre/exitStatistics/list",e),exportExitPeopleReport:e=>t.post("/zhyy/cadre/exitStatistics/exportExcel",e,{responseType:"blob"}),getPeopleAnalysisList:e=>t.post("/zhyy/cadre/personnelSituation/list",e),exportPeopleAnalysis:e=>t.post("/zhyy/cadre/personnelSituation/exportExcel",e,{responseType:"blob"})},o="_employment_page_s2sgh_2",p="_animation_box_s2sgh_2",r="_add_table_modal_s2sgh_100",s="_over_ellipsis_s2sgh_111",n={employment_page:o,animation_box:p,add_table_modal:r,over_ellipsis:s};export{y as c,n as s};
