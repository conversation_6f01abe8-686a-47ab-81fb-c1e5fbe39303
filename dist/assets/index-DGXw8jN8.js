import{r as l,F as p,j as s,u as R,d as P,R as fe,C as N,S as ge,B as T,T as q}from"./index-De_f0oL2.js";import{e as v}from"./service-DcPXuTuP.js";import{R as pe,o as J,d as we}from"./down-BCLNnN1h.js";import{s as d}from"./Index.module-CQ0G-enU.js";import{R as Ce}from"./index-BmnYJy3v.js";import{s as be}from"./index-BcPP1N8I.js";import{D as xe}from"./index-CdSZ9YgQ.js";import{C as Ne}from"./index-DZyVV6rP.js";import{S as ye}from"./index-BWJehDyc.js";import{R as Se,a as je}from"./FullscreenOutlined-DzCTibKW.js";import{T as Q}from"./index-CwwLkcJF.js";import"./Table-D-iLeFE-.js";import"./useMultipleSelect-B0dEIXT-.js";const Ie=[{value:"专业线",label:"专业线"},{value:"岗位",label:"岗位"},{value:"重点岗位",label:"重点岗位"}],Le=()=>{var D;const b=(e,t,o,n)=>{if(!t[n])return;function r(a){const f=t[n].monthId===t[a].monthId,C=t[n].orgName5===t[a].orgName5,g=t[n].orgName6===t[a].orgName6,I=t[n].oneClass===t[a].oneClass;return o==="monthId"||o==="orgName5"&&f||o==="orgName6"&&f&&C||o==="oneClass"&&f&&C&&g||o==="twoClass"&&f&&C&&g&&I}if(t[n-1]&&n!==0&&e===t[n-1][o]&&r(n-1)&&t[n].rowno==="3"&&["3"].includes(t[n-1].rowno))return{rowSpan:0};let i=1;for(let a=n+1;a<t.length&&!(t[a]&&e!==t[a][o]||!r(a));a++)if(["3"].includes(t[n].rowno)&&["3"].includes(t[a].rowno))i++;else return{rowSpan:i};return{rowSpan:t[n].rowno==="0"?1:i}},B={公众线:"cyan",政企线:"blue",网络线:"green",职能线:"geekblue",生产现业人员:"lime",组织支撑人员:"gold",营业员:"purple",渠道经理:"processing",商客经理:"orange",智家工程师:"volcano",要客客户经理:"red",商企客户经理:"magenta"},U={专业线:"cyan",岗位:"blue",重点岗位:"green"},_=[{title:"单位",key:"orgName5",dataIndex:"orgName5",fixed:"left",align:"center",width:150,onCell:(e,t)=>b(e==null?void 0:e.orgName5,m,"orgName5",t),render:(e,t)=>s.jsx(q,{title:e,children:s.jsx("div",{className:d.over_ellipsis,children:e})})},{title:"部门",key:"orgName6",dataIndex:"orgName6",fixed:"left",align:"center",width:200,onCell:(e,t)=>b(e==null?void 0:e.orgName6,m,"orgName6",t),render:(e,t)=>s.jsx(q,{title:e,children:s.jsx("div",{className:d.over_ellipsis,children:e&&e.startsWith("天津市分公司")?e.slice(6):e})})},{title:"人员分类",key:"name",dataIndex:"name",children:[{title:"分类",key:"oneClass",dataIndex:"oneClass",align:"center",fixed:"left",width:60,onCell:(e,t)=>b(e==null?void 0:e.oneClass,m,"oneClass",t),render:(e,t)=>e==="-"?e:s.jsx(Q,{color:U[e]||"purple",style:{marginRight:"0"},children:e})},{title:"角色",key:"twoClass",dataIndex:"twoClass",align:"center",fixed:"left",width:80,onCell:(e,t)=>b(e==null?void 0:e.twoClass,m,"twoClass",t),render:(e,t)=>e==="-"?e:s.jsx(Q,{color:B[e]||"purple",style:{marginRight:"0"},children:e})},{title:"合计",key:"ant",dataIndex:"ant",align:"center",fixed:"left",width:50}]},{title:"用工类型",key:"name",dataIndex:"name",children:[{title:"合同制",key:"antHt",dataIndex:"antHt",align:"center",width:60},{title:"劳务派遣",key:"antLp",dataIndex:"antLp",align:"center",width:75},{title:"紧密型外包",key:"antJm",dataIndex:"antJm",align:"center",width:85},{title:"其他外包",key:"antQt",dataIndex:"antQt",align:"center",width:75}]}],E=l.useRef(null),k=l.useRef(null),A=l.useRef(null),[c]=p.useForm(),[Y,F]=l.useState(!0),[W,$]=l.useState(!1),[G,K]=l.useState(0),[m,X]=l.useState([]),[Z,O]=l.useState(_),[ee,te]=l.useState([]),[u,y]=l.useState([]),[S,oe]=l.useState(0),[h,M]=l.useState({total:0,pageNum:1,pageSize:50}),{runAsync:se}=R(v.exportEmployeeOrgExcel,{manual:!0}),{runAsync:ne}=R(v.build4LevelOrgTree,{manual:!0}),{runAsync:le}=R(v.getEmployeeOrgPag,{manual:!0});l.useEffect(()=>(L(),x(),window.addEventListener("resize",z),()=>{window.removeEventListener("resize",z)}),[]);const z=()=>{w()};l.useEffect(()=>{w()},[(document.querySelector(".report_org_table .ant-table-header")||{}).offsetHeight]),l.useEffect(()=>{S>0&&x()},[S]),l.useEffect(()=>{(m==null?void 0:m.length)>0&&(V(),w())},[m]),l.useEffect(()=>{(h==null?void 0:h.total)>0&&x()},[h.pageNum,h.pageSize]);const w=()=>{var o;const e=(document.querySelector(".report_org_table .ant-table-header")||{}).offsetHeight,t=(document.querySelector(".report_org_table .ant-table-pagination")||{}).offsetHeight||26;e&&t&&K(((o=k.current)==null?void 0:o.offsetHeight)-(A.current.offsetHeight+e+t))},H=async()=>{var o;const[e,t]=await ne({monthId:(o=c.getFieldValue("monthId"))==null?void 0:o.format("YYYYMM"),tag:"1"});e||t.STATUS==="0000"&&(te(t.DATA),y([]),c.setFieldValue("unit",""))},x=async e=>{var j,a;$(!0),V();const t=c.getFieldsValue(),o=u?u[u.length-1]:{},n=h,[r,i]=await le({monthId:(j=t==null?void 0:t.monthId)==null?void 0:j.format("YYYYMM"),org4:Number(o==null?void 0:o.level)===4?o==null?void 0:o.orgId:"",org5:Number(o==null?void 0:o.level)===5?o==null?void 0:o.orgId:"",org6:Number(o==null?void 0:o.level)===6?o==null?void 0:o.orgId:"",oneClass:t==null?void 0:t.oneClass,...n});if($(!1),!r)if(i.STATUS==="0000"){const{DATA:{data:f}}=i,C=f.map((g,I)=>({...g,key:g.id||I,ant12:g.ant}));X(C),M({...h,total:(a=i.DATA)==null?void 0:a.totalCount})}else be.error(i==null?void 0:i.MESSAGE)},L=()=>{const e=P();c.setFieldsValue({monthId:e,unit:[]}),H()},V=()=>{const e=P(c.getFieldValue("monthId")),t=e.month(),o=[..._];for(let n=t;n>=0;n--){const r=(t-n+1).toString();o.push({title:`${e.subtract(n,"month").format("YYYYMM")}`,key:`ant${r.length>1?r:"0"+r}`,dataIndex:`ant${r.length>1?r:"0"+r}`,align:"center",onCell:void 0,width:65})}O(o)},ae=()=>{x()},re=(e,t)=>{y(t)},ie=async()=>{var e;try{J("正在导出",0,"loading");const t=c.getFieldsValue(),o=u?u[u.length-1]:{},n=await se({monthId:(e=t==null?void 0:t.monthId)==null?void 0:e.format("YYYYMM"),org4:Number(o==null?void 0:o.level)===4?o==null?void 0:o.orgId:"",org5:Number(o==null?void 0:o.level)===5?o==null?void 0:o.orgId:"",org6:Number(o==null?void 0:o.level)===6?o==null?void 0:o.orgId:"",oneClass:t==null?void 0:t.oneClass});we(n)}catch(t){J("导出失败",1,"error"),console.error("Download failed:",t)}},me=()=>{const e=S+1;c.resetFields(),y([]),L(),oe(e)},ce=(e,t)=>t.some(o=>o.orgName.toLowerCase().indexOf(e.toLowerCase())>-1),he=e=>{},de=e=>{const t={total:e==null?void 0:e.total,pageNum:e==null?void 0:e.current,pageSize:e==null?void 0:e.pageSize};M(t)},ue=e=>{e.monthId&&H()};return s.jsxs("div",{className:`h-full pt-[0.5rem] flex flex-col ${d.employment_page}`,children:[s.jsx("div",{ref:E,className:"bg-white pt-[0.5rem] px-8 mb-[0.5rem]",children:s.jsx(p,{form:c,initialValues:{tag:""},onFinish:ae,onValuesChange:ue,autoComplete:"off",children:s.jsxs(fe,{gutter:24,children:[s.jsx(N,{span:6,children:s.jsx(p.Item,{label:"月份",name:"monthId",wrapperCol:{span:20},children:s.jsx(xe,{className:"w-full",allowClear:!1,onChange:he,picker:"month"})})}),s.jsx(N,{span:6,children:s.jsx(p.Item,{label:"组织",name:"unit",wrapperCol:{span:20},children:s.jsx(Ne,{allowClear:!0,changeOnSelect:!0,expandTrigger:"hover",displayRender:e=>e[e.length-1],options:ee,onChange:re,fieldNames:{value:"orgId",label:"orgName",children:"children"},placeholder:"请选择",showSearch:{filter:ce},onSearch:e=>console.log(e)})})}),s.jsx(N,{span:6,children:s.jsx(p.Item,{label:"人员分类",name:"oneClass",wrapperCol:{span:20},children:s.jsx(ye,{placeholder:"请选择",className:"w-full",allowClear:!0,options:Ie})})}),s.jsx(N,{span:6,children:s.jsx(p.Item,{labelCol:{span:0},wrapperCol:{span:24},children:s.jsxs(ge,{size:"small",children:[s.jsx(T,{type:"primary",htmlType:"submit",children:"查询"}),s.jsx(T,{htmlType:"button",onClick:()=>me(),children:"重置"})]})})})]})})}),s.jsxs("div",{ref:k,className:"relative bg-white px-5 pt-2.5",style:{height:`calc(100% - ${((D=E.current)==null?void 0:D.offsetHeight)+15}px)`},children:[s.jsxs("div",{ref:A,className:`flex justify-between items-center mb-2 overflow-hidden ${d.animation_box} ${Y?"h-[1.6rem]":"h-0"}`,children:[s.jsxs("div",{className:"flex ",children:[Y?s.jsx(Se,{className:`${d.shousuo_icon} text-[1rem]`,onClick:()=>{F(!1),setTimeout(()=>{w()},200)}}):s.jsx(je,{className:`${d.shousuo_icon} absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10`,onClick:()=>{F(!0),setTimeout(()=>{w()},200)}}),s.jsx("div",{className:"font-bold text-[0.8rem] ml-3",children:"数据列表"})]}),s.jsx(T,{danger:!0,ghost:!0,icon:s.jsx(pe,{}),onClick:()=>ie(),children:"导出"})]}),s.jsx(Ce,{className:"report_org_table",columns:Z,dataSource:m,loading:W,bordered:!0,rowClassName:e=>["合计","小计","总计"].includes(e.orgName6)?d.highlight_row:d.customRow,scroll:{y:`calc(${G}px - 0.625rem - 1.6rem - 0.5rem)`},onChange:de,pagination:{...h,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50"]}})]})]})};export{Le as default};
