import{r as l,a5 as Dt,a6 as jt,a7 as ve,a8 as Ae,a9 as nt,_ as ue,c as _,aa as Me,T as at,b as ot,E as it,G as st,a as ee,ab as Te,ac as Rt,q as Oe,v as lt,ad as Lt,a3 as Nt,ae as kt,I as we,af as At,ag as pe,ah as K,p as se,ai as ct,aj as ut,ak as dt,al as T,am as G,an as pt,V as Ue,ao as Mt,H as mt,J as ft,ap as Tt,aq as qe,ar as Ut,as as _t,at as gt,au as zt,av as Wt,aw as Bt,f as Ve,B as Ge,m as Ke,ax as Ht,w as Xt,s as qt,P as Vt,ay as Je}from"./index-De_f0oL2.js";import{R as Gt}from"./DeleteOutlined-D-FcgX8f.js";import{R as Kt}from"./down-BCLNnN1h.js";import{R as Jt}from"./index-BWJehDyc.js";var Yt={percent:0,prefixCls:"rc-progress",strokeColor:"#2db7f5",strokeLinecap:"round",strokeWidth:1,trailColor:"#D9D9D9",trailWidth:1,gapPosition:"bottom"},Zt=function(){var t=l.useRef([]),r=l.useRef(null);return l.useEffect(function(){var n=Date.now(),o=!1;t.current.forEach(function(a){if(a){o=!0;var i=a.style;i.transitionDuration=".3s, .3s, .3s, .06s",r.current&&n-r.current<100&&(i.transitionDuration="0s, 0s")}}),o&&(r.current=Date.now())}),t.current},Ye=0,Qt=jt();function er(){var e;return Qt?(e=Ye,Ye+=1):e="TEST_OR_SSR",e}const tr=function(e){var t=l.useState(),r=Dt(t,2),n=r[0],o=r[1];return l.useEffect(function(){o("rc_progress_".concat(er()))},[]),e||n};var Ze=function(t){var r=t.bg,n=t.children;return l.createElement("div",{style:{width:"100%",height:"100%",background:r}},n)};function Qe(e,t){return Object.keys(e).map(function(r){var n=parseFloat(r),o="".concat(Math.floor(n*t),"%");return"".concat(e[r]," ").concat(o)})}var rr=l.forwardRef(function(e,t){var r=e.prefixCls,n=e.color,o=e.gradientId,a=e.radius,i=e.style,s=e.ptg,c=e.strokeLinecap,u=e.strokeWidth,d=e.size,p=e.gapDegree,f=n&&ve(n)==="object",g=f?"#FFF":void 0,$=d/2,w=l.createElement("circle",{className:"".concat(r,"-circle-path"),r:a,cx:$,cy:$,stroke:g,strokeLinecap:c,strokeWidth:u,opacity:s===0?0:1,style:i,ref:t});if(!f)return w;var v="".concat(o,"-conic"),h=p?"".concat(180+p/2,"deg"):"0deg",b=Qe(n,(360-p)/360),S=Qe(n,1),C="conic-gradient(from ".concat(h,", ").concat(b.join(", "),")"),m="linear-gradient(to ".concat(p?"bottom":"top",", ").concat(S.join(", "),")");return l.createElement(l.Fragment,null,l.createElement("mask",{id:v},w),l.createElement("foreignObject",{x:0,y:0,width:d,height:d,mask:"url(#".concat(v,")")},l.createElement(Ze,{bg:m},l.createElement(Ze,{bg:C}))))}),$e=100,je=function(t,r,n,o,a,i,s,c,u,d){var p=arguments.length>10&&arguments[10]!==void 0?arguments[10]:0,f=n/100*360*((360-i)/360),g=i===0?0:{bottom:0,top:180,left:90,right:-90}[s],$=(100-o)/100*r;u==="round"&&o!==100&&($+=d/2,$>=r&&($=r-.01));var w=$e/2;return{stroke:typeof c=="string"?c:void 0,strokeDasharray:"".concat(r,"px ").concat(t),strokeDashoffset:$+p,transform:"rotate(".concat(a+f+g,"deg)"),transformOrigin:"".concat(w,"px ").concat(w,"px"),transition:"stroke-dashoffset .3s ease 0s, stroke-dasharray .3s ease 0s, stroke .3s, stroke-width .06s ease .3s, opacity .3s ease 0s",fillOpacity:0}},nr=["id","prefixCls","steps","strokeWidth","trailWidth","gapDegree","gapPosition","trailColor","strokeLinecap","style","className","strokeColor","percent"];function et(e){var t=e??[];return Array.isArray(t)?t:[t]}var ar=function(t){var r=Ae(Ae({},Yt),t),n=r.id,o=r.prefixCls,a=r.steps,i=r.strokeWidth,s=r.trailWidth,c=r.gapDegree,u=c===void 0?0:c,d=r.gapPosition,p=r.trailColor,f=r.strokeLinecap,g=r.style,$=r.className,w=r.strokeColor,v=r.percent,h=nt(r,nr),b=$e/2,S=tr(n),C="".concat(S,"-gradient"),m=b-i/2,P=Math.PI*2*m,x=u>0?90+u/2:-90,D=P*((360-u)/360),N=ve(a)==="object"?a:{count:a,gap:2},E=N.count,te=N.gap,re=et(v),J=et(w),B=J.find(function(A){return A&&ve(A)==="object"}),Y=B&&ve(B)==="object",U=Y?"butt":f,j=je(P,D,0,100,x,u,d,p,U,i),Q=Zt(),H=function(){var X=0;return re.map(function(y,R){var q=J[R]||J[J.length-1],V=je(P,D,X,y,x,u,d,q,U,i);return X+=y,l.createElement(rr,{key:R,color:q,ptg:y,radius:m,prefixCls:o,gradientId:C,style:V,strokeLinecap:U,strokeWidth:i,gapDegree:u,ref:function(ae){Q[R]=ae},size:$e})}).reverse()},z=function(){var X=Math.round(E*(re[0]/100)),y=100/E,R=0;return new Array(E).fill(null).map(function(q,V){var ne=V<=X-1?J[0]:p,ae=ne&&ve(ne)==="object"?"url(#".concat(C,")"):void 0,le=je(P,D,R,y,x,u,d,ne,"butt",i,te);return R+=(D-le.strokeDashoffset+te)*100/D,l.createElement("circle",{key:V,className:"".concat(o,"-circle-path"),r:m,cx:b,cy:b,stroke:ae,strokeWidth:i,opacity:1,style:le,ref:function(oe){Q[V]=oe}})})};return l.createElement("svg",ue({className:_("".concat(o,"-circle"),$),viewBox:"0 0 ".concat($e," ").concat($e),style:g,id:n,role:"presentation"},h),!E&&l.createElement("circle",{className:"".concat(o,"-circle-trail"),r:m,cx:b,cy:b,stroke:p,strokeLinecap:U,strokeWidth:s||i,style:j}),E?z():H())};function me(e){return!e||e<0?0:e>100?100:e}function Ee({success:e,successPercent:t}){let r=t;return e&&"progress"in e&&(r=e.progress),e&&"percent"in e&&(r=e.percent),r}const or=({percent:e,success:t,successPercent:r})=>{const n=me(Ee({success:t,successPercent:r}));return[n,me(me(e)-n)]},ir=({success:e={},strokeColor:t})=>{const{strokeColor:r}=e;return[r||Me.green,t||null]},Ie=(e,t,r)=>{var n,o,a,i;let s=-1,c=-1;if(t==="step"){const u=r.steps,d=r.strokeWidth;typeof e=="string"||typeof e>"u"?(s=e==="small"?2:14,c=d??8):typeof e=="number"?[s,c]=[e,e]:[s=14,c=8]=Array.isArray(e)?e:[e.width,e.height],s*=u}else if(t==="line"){const u=r==null?void 0:r.strokeWidth;typeof e=="string"||typeof e>"u"?c=u||(e==="small"?6:8):typeof e=="number"?[s,c]=[e,e]:[s=-1,c=8]=Array.isArray(e)?e:[e.width,e.height]}else(t==="circle"||t==="dashboard")&&(typeof e=="string"||typeof e>"u"?[s,c]=e==="small"?[60,60]:[120,120]:typeof e=="number"?[s,c]=[e,e]:Array.isArray(e)&&(s=(o=(n=e[0])!==null&&n!==void 0?n:e[1])!==null&&o!==void 0?o:120,c=(i=(a=e[0])!==null&&a!==void 0?a:e[1])!==null&&i!==void 0?i:120));return[s,c]},sr=3,lr=e=>sr/e*100,cr=e=>{const{prefixCls:t,trailColor:r=null,strokeLinecap:n="round",gapPosition:o,gapDegree:a,width:i=120,type:s,children:c,success:u,size:d=i,steps:p}=e,[f,g]=Ie(d,"circle");let{strokeWidth:$}=e;$===void 0&&($=Math.max(lr(f),6));const w={width:f,height:g,fontSize:f*.15+6},v=l.useMemo(()=>{if(a||a===0)return a;if(s==="dashboard")return 75},[a,s]),h=or(e),b=o||s==="dashboard"&&"bottom"||void 0,S=Object.prototype.toString.call(e.strokeColor)==="[object Object]",C=ir({success:u,strokeColor:e.strokeColor}),m=_(`${t}-inner`,{[`${t}-circle-gradient`]:S}),P=l.createElement(ar,{steps:p,percent:p?h[1]:h,strokeWidth:$,trailWidth:$,strokeColor:p?C[1]:C,strokeLinecap:n,trailColor:r,prefixCls:t,gapDegree:v,gapPosition:b}),x=f<=20,D=l.createElement("div",{className:m,style:w},P,!x&&c);return x?l.createElement(at,{title:c},D):D},xe="--progress-line-stroke-color",ht="--progress-percent",tt=e=>{const t=e?"100%":"-100%";return new Te(`antProgress${e?"RTL":"LTR"}Active`,{"0%":{transform:`translateX(${t}) scaleX(0)`,opacity:.1},"20%":{transform:`translateX(${t}) scaleX(0)`,opacity:.5},to:{transform:"translateX(0) scaleX(1)",opacity:0}})},ur=e=>{const{componentCls:t,iconCls:r}=e;return{[t]:Object.assign(Object.assign({},st(e)),{display:"inline-block","&-rtl":{direction:"rtl"},"&-line":{position:"relative",width:"100%",fontSize:e.fontSize},[`${t}-outer`]:{display:"inline-flex",alignItems:"center",width:"100%"},[`${t}-inner`]:{position:"relative",display:"inline-block",width:"100%",flex:1,overflow:"hidden",verticalAlign:"middle",backgroundColor:e.remainingColor,borderRadius:e.lineBorderRadius},[`${t}-inner:not(${t}-circle-gradient)`]:{[`${t}-circle-path`]:{stroke:e.defaultColor}},[`${t}-success-bg, ${t}-bg`]:{position:"relative",background:e.defaultColor,borderRadius:e.lineBorderRadius,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOutCirc}`},[`${t}-layout-bottom`]:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",[`${t}-text`]:{width:"max-content",marginInlineStart:0,marginTop:e.marginXXS}},[`${t}-bg`]:{overflow:"hidden","&::after":{content:'""',background:{_multi_value_:!0,value:["inherit",`var(${xe})`]},height:"100%",width:`calc(1 / var(${ht}) * 100%)`,display:"block"},[`&${t}-bg-inner`]:{minWidth:"max-content","&::after":{content:"none"},[`${t}-text-inner`]:{color:e.colorWhite,[`&${t}-text-bright`]:{color:"rgba(0, 0, 0, 0.45)"}}}},[`${t}-success-bg`]:{position:"absolute",insetBlockStart:0,insetInlineStart:0,backgroundColor:e.colorSuccess},[`${t}-text`]:{display:"inline-block",marginInlineStart:e.marginXS,color:e.colorText,lineHeight:1,width:"2em",whiteSpace:"nowrap",textAlign:"start",verticalAlign:"middle",wordBreak:"normal",[r]:{fontSize:e.fontSize},[`&${t}-text-outer`]:{width:"max-content"},[`&${t}-text-outer${t}-text-start`]:{width:"max-content",marginInlineStart:0,marginInlineEnd:e.marginXS}},[`${t}-text-inner`]:{display:"flex",justifyContent:"center",alignItems:"center",width:"100%",height:"100%",marginInlineStart:0,padding:`0 ${ee(e.paddingXXS)}`,[`&${t}-text-start`]:{justifyContent:"start"},[`&${t}-text-end`]:{justifyContent:"end"}},[`&${t}-status-active`]:{[`${t}-bg::before`]:{position:"absolute",inset:0,backgroundColor:e.colorBgContainer,borderRadius:e.lineBorderRadius,opacity:0,animationName:tt(),animationDuration:e.progressActiveMotionDuration,animationTimingFunction:e.motionEaseOutQuint,animationIterationCount:"infinite",content:'""'}},[`&${t}-rtl${t}-status-active`]:{[`${t}-bg::before`]:{animationName:tt(!0)}},[`&${t}-status-exception`]:{[`${t}-bg`]:{backgroundColor:e.colorError},[`${t}-text`]:{color:e.colorError}},[`&${t}-status-exception ${t}-inner:not(${t}-circle-gradient)`]:{[`${t}-circle-path`]:{stroke:e.colorError}},[`&${t}-status-success`]:{[`${t}-bg`]:{backgroundColor:e.colorSuccess},[`${t}-text`]:{color:e.colorSuccess}},[`&${t}-status-success ${t}-inner:not(${t}-circle-gradient)`]:{[`${t}-circle-path`]:{stroke:e.colorSuccess}}})}},dr=e=>{const{componentCls:t,iconCls:r}=e;return{[t]:{[`${t}-circle-trail`]:{stroke:e.remainingColor},[`&${t}-circle ${t}-inner`]:{position:"relative",lineHeight:1,backgroundColor:"transparent"},[`&${t}-circle ${t}-text`]:{position:"absolute",insetBlockStart:"50%",insetInlineStart:0,width:"100%",margin:0,padding:0,color:e.circleTextColor,fontSize:e.circleTextFontSize,lineHeight:1,whiteSpace:"normal",textAlign:"center",transform:"translateY(-50%)",[r]:{fontSize:e.circleIconFontSize}},[`${t}-circle&-status-exception`]:{[`${t}-text`]:{color:e.colorError}},[`${t}-circle&-status-success`]:{[`${t}-text`]:{color:e.colorSuccess}}},[`${t}-inline-circle`]:{lineHeight:1,[`${t}-inner`]:{verticalAlign:"bottom"}}}},pr=e=>{const{componentCls:t}=e;return{[t]:{[`${t}-steps`]:{display:"inline-block","&-outer":{display:"flex",flexDirection:"row",alignItems:"center"},"&-item":{flexShrink:0,minWidth:e.progressStepMinWidth,marginInlineEnd:e.progressStepMarginInlineEnd,backgroundColor:e.remainingColor,transition:`all ${e.motionDurationSlow}`,"&-active":{backgroundColor:e.defaultColor}}}}}},mr=e=>{const{componentCls:t,iconCls:r}=e;return{[t]:{[`${t}-small&-line, ${t}-small&-line ${t}-text ${r}`]:{fontSize:e.fontSizeSM}}}},fr=e=>({circleTextColor:e.colorText,defaultColor:e.colorInfo,remainingColor:e.colorFillSecondary,lineBorderRadius:100,circleTextFontSize:"1em",circleIconFontSize:`${e.fontSize/e.fontSizeSM}em`}),gr=ot("Progress",e=>{const t=e.calc(e.marginXXS).div(2).equal(),r=it(e,{progressStepMarginInlineEnd:t,progressStepMinWidth:t,progressActiveMotionDuration:"2.4s"});return[ur(r),dr(r),pr(r),mr(r)]},fr);var hr=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const vr=e=>{let t=[];return Object.keys(e).forEach(r=>{const n=parseFloat(r.replace(/%/g,""));Number.isNaN(n)||t.push({key:n,value:e[r]})}),t=t.sort((r,n)=>r.key-n.key),t.map(({key:r,value:n})=>`${n} ${r}%`).join(", ")},br=(e,t)=>{const{from:r=Me.blue,to:n=Me.blue,direction:o=t==="rtl"?"to left":"to right"}=e,a=hr(e,["from","to","direction"]);if(Object.keys(a).length!==0){const s=vr(a),c=`linear-gradient(${o}, ${s})`;return{background:c,[xe]:c}}const i=`linear-gradient(${o}, ${r}, ${n})`;return{background:i,[xe]:i}},$r=e=>{const{prefixCls:t,direction:r,percent:n,size:o,strokeWidth:a,strokeColor:i,strokeLinecap:s="round",children:c,trailColor:u=null,percentPosition:d,success:p}=e,{align:f,type:g}=d,$=i&&typeof i!="string"?br(i,r):{[xe]:i,background:i},w=s==="square"||s==="butt"?0:void 0,v=o??[-1,a||(o==="small"?6:8)],[h,b]=Ie(v,"line",{strokeWidth:a}),S={backgroundColor:u||void 0,borderRadius:w},C=Object.assign(Object.assign({width:`${me(n)}%`,height:b,borderRadius:w},$),{[ht]:me(n)/100}),m=Ee(e),P={width:`${me(m)}%`,height:b,borderRadius:w,backgroundColor:p==null?void 0:p.strokeColor},x={width:h<0?"100%":h},D=l.createElement("div",{className:`${t}-inner`,style:S},l.createElement("div",{className:_(`${t}-bg`,`${t}-bg-${g}`),style:C},g==="inner"&&c),m!==void 0&&l.createElement("div",{className:`${t}-success-bg`,style:P})),N=g==="outer"&&f==="start",E=g==="outer"&&f==="end";return g==="outer"&&f==="center"?l.createElement("div",{className:`${t}-layout-bottom`},D,c):l.createElement("div",{className:`${t}-outer`,style:x},N&&c,D,E&&c)},yr=e=>{const{size:t,steps:r,rounding:n=Math.round,percent:o=0,strokeWidth:a=8,strokeColor:i,trailColor:s=null,prefixCls:c,children:u}=e,d=n(r*(o/100)),f=t??[t==="small"?2:14,a],[g,$]=Ie(f,"step",{steps:r,strokeWidth:a}),w=g/r,v=Array.from({length:r});for(let h=0;h<r;h++){const b=Array.isArray(i)?i[h]:i;v[h]=l.createElement("div",{key:h,className:_(`${c}-steps-item`,{[`${c}-steps-item-active`]:h<=d-1}),style:{backgroundColor:h<=d-1?b:s,width:w,height:$}})}return l.createElement("div",{className:`${c}-steps-outer`},v,u)};var wr=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const Cr=["normal","exception","active","success"],Sr=l.forwardRef((e,t)=>{const{prefixCls:r,className:n,rootClassName:o,steps:a,strokeColor:i,percent:s=0,size:c="default",showInfo:u=!0,type:d="line",status:p,format:f,style:g,percentPosition:$={}}=e,w=wr(e,["prefixCls","className","rootClassName","steps","strokeColor","percent","size","showInfo","type","status","format","style","percentPosition"]),{align:v="end",type:h="outer"}=$,b=Array.isArray(i)?i[0]:i,S=typeof i=="string"||Array.isArray(i)?i:void 0,C=l.useMemo(()=>{if(b){const H=typeof b=="string"?b:Object.values(b)[0];return new Rt(H).isLight()}return!1},[i]),m=l.useMemo(()=>{var H,z;const A=Ee(e);return parseInt(A!==void 0?(H=A??0)===null||H===void 0?void 0:H.toString():(z=s??0)===null||z===void 0?void 0:z.toString(),10)},[s,e.success,e.successPercent]),P=l.useMemo(()=>!Cr.includes(p)&&m>=100?"success":p||"normal",[p,m]),{getPrefixCls:x,direction:D,progress:N}=l.useContext(Oe),E=x("progress",r),[te,re,J]=gr(E),B=d==="line",Y=B&&!a,U=l.useMemo(()=>{if(!u)return null;const H=Ee(e);let z;const A=f||(y=>`${y}%`),X=B&&C&&h==="inner";return h==="inner"||f||P!=="exception"&&P!=="success"?z=A(me(s),me(H)):P==="exception"?z=B?l.createElement(Lt,null):l.createElement(Nt,null):P==="success"&&(z=B?l.createElement(kt,null):l.createElement(Jt,null)),l.createElement("span",{className:_(`${E}-text`,{[`${E}-text-bright`]:X,[`${E}-text-${v}`]:Y,[`${E}-text-${h}`]:Y}),title:typeof z=="string"?z:void 0},z)},[u,s,m,P,d,E,f]);let j;d==="line"?j=a?l.createElement(yr,Object.assign({},e,{strokeColor:S,prefixCls:E,steps:typeof a=="object"?a.count:a}),U):l.createElement($r,Object.assign({},e,{strokeColor:b,prefixCls:E,direction:D,percentPosition:{align:v,type:h}}),U):(d==="circle"||d==="dashboard")&&(j=l.createElement(cr,Object.assign({},e,{strokeColor:b,prefixCls:E,progressStatus:P}),U));const Q=_(E,`${E}-status-${P}`,{[`${E}-${d==="dashboard"&&"circle"||d}`]:d!=="line",[`${E}-inline-circle`]:d==="circle"&&Ie(c,"circle")[0]<=20,[`${E}-line`]:Y,[`${E}-line-align-${v}`]:Y,[`${E}-line-position-${h}`]:Y,[`${E}-steps`]:a,[`${E}-show-info`]:u,[`${E}-${c}`]:typeof c=="string",[`${E}-rtl`]:D==="rtl"},N==null?void 0:N.className,n,o,re,J);return te(l.createElement("div",Object.assign({ref:t,style:Object.assign(Object.assign({},N==null?void 0:N.style),g),className:Q,role:"progressbar","aria-valuenow":m,"aria-valuemin":0,"aria-valuemax":100},lt(w,["trailColor","strokeWidth","width","gapDegree","gapPosition","strokeLinecap","success","successPercent"])),j))});var Er={icon:function(t,r){return{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M534 352V136H232v752h560V394H576a42 42 0 01-42-42z",fill:r}},{tag:"path",attrs:{d:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM602 137.8L790.2 326H602V137.8zM792 888H232V136h302v216a42 42 0 0042 42h216v494z",fill:t}}]}},name:"file",theme:"twotone"},xr=function(t,r){return l.createElement(we,ue({},t,{ref:r,icon:Er}))},Or=l.forwardRef(xr),Ir={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M885.2 446.3l-.2-.8-112.2-285.1c-5-16.1-19.9-27.2-36.8-27.2H281.2c-17 0-32.1 11.3-36.9 27.6L139.4 443l-.3.7-.2.8c-1.3 4.9-1.7 9.9-1 14.8-.1 1.6-.2 3.2-.2 4.8V830a60.9 60.9 0 0060.8 60.8h627.2c33.5 0 60.8-27.3 60.9-60.8V464.1c0-1.3 0-2.6-.1-3.7.4-4.9 0-9.6-1.3-14.1zm-295.8-43l-.3 15.7c-.8 44.9-31.8 75.1-77.1 75.1-22.1 0-41.1-7.1-54.8-20.6S436 441.2 435.6 419l-.3-15.7H229.5L309 210h399.2l81.7 193.3H589.4zm-375 76.8h157.3c24.3 57.1 76 90.8 140.4 90.8 33.7 0 65-9.4 90.3-27.2 22.2-15.6 39.5-37.4 50.7-63.6h156.5V814H214.4V480.1z"}}]},name:"inbox",theme:"outlined"},Pr=function(t,r){return l.createElement(we,ue({},t,{ref:r,icon:Ir}))},hn=l.forwardRef(Pr),Fr={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M779.3 196.6c-94.2-94.2-247.6-94.2-341.7 0l-261 260.8c-1.7 1.7-2.6 4-2.6 6.4s.9 4.7 2.6 6.4l36.9 36.9a9 9 0 0012.7 0l261-260.8c32.4-32.4 75.5-50.2 121.3-50.2s88.9 17.8 121.2 50.2c32.4 32.4 50.2 75.5 50.2 121.2 0 45.8-17.8 88.8-50.2 121.2l-266 265.9-43.1 43.1c-40.3 40.3-105.8 40.3-146.1 0-19.5-19.5-30.2-45.4-30.2-73s10.7-53.5 30.2-73l263.9-263.8c6.7-6.6 15.5-10.3 24.9-10.3h.1c9.4 0 18.1 3.7 24.7 10.3 6.7 6.7 10.3 15.5 10.3 24.9 0 9.3-3.7 18.1-10.3 24.7L372.4 653c-1.7 1.7-2.6 4-2.6 6.4s.9 4.7 2.6 6.4l36.9 36.9a9 9 0 0012.7 0l215.6-215.6c19.9-19.9 30.8-46.3 30.8-74.4s-11-54.6-30.8-74.4c-41.1-41.1-107.9-41-149 0L463 364 224.8 602.1A172.22 172.22 0 00174 724.8c0 46.3 18.1 89.8 50.8 122.5 33.9 33.8 78.3 50.7 122.7 50.7 44.4 0 88.8-16.9 122.6-50.7l309.2-309C824.8 492.7 850 432 850 367.5c.1-64.6-25.1-125.3-70.7-170.9z"}}]},name:"paper-clip",theme:"outlined"},Dr=function(t,r){return l.createElement(we,ue({},t,{ref:r,icon:Fr}))},jr=l.forwardRef(Dr),Rr={icon:function(t,r){return{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 632H136v-39.9l138.5-164.3 150.1 178L658.1 489 888 761.6V792zm0-129.8L664.2 396.8c-3.2-3.8-9-3.8-12.2 0L424.6 666.4l-144-170.7c-3.2-3.8-9-3.8-12.2 0L136 652.7V232h752v430.2z",fill:t}},{tag:"path",attrs:{d:"M424.6 765.8l-150.1-178L136 752.1V792h752v-30.4L658.1 489z",fill:r}},{tag:"path",attrs:{d:"M136 652.7l132.4-157c3.2-3.8 9-3.8 12.2 0l144 170.7L652 396.8c3.2-3.8 9-3.8 12.2 0L888 662.2V232H136v420.7zM304 280a88 88 0 110 176 88 88 0 010-176z",fill:r}},{tag:"path",attrs:{d:"M276 368a28 28 0 1056 0 28 28 0 10-56 0z",fill:r}},{tag:"path",attrs:{d:"M304 456a88 88 0 100-176 88 88 0 000 176zm0-116c15.5 0 28 12.5 28 28s-12.5 28-28 28-28-12.5-28-28 12.5-28 28-28z",fill:t}}]}},name:"picture",theme:"twotone"},Lr=function(t,r){return l.createElement(we,ue({},t,{ref:r,icon:Rr}))},Nr=l.forwardRef(Lr),kr={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M400 317.7h73.9V656c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V317.7H624c6.7 0 10.4-7.7 6.3-12.9L518.3 163a8 8 0 00-12.6 0l-112 141.7c-4.1 5.3-.4 13 6.3 13zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"}}]},name:"upload",theme:"outlined"},Ar=function(t,r){return l.createElement(we,ue({},t,{ref:r,icon:kr}))},vn=l.forwardRef(Ar);const Re=function(e,t){if(e&&t){var r=Array.isArray(t)?t:t.split(","),n=e.name||"",o=e.type||"",a=o.replace(/\/.*$/,"");return r.some(function(i){var s=i.trim();if(/^\*(\/\*)?$/.test(i))return!0;if(s.charAt(0)==="."){var c=n.toLowerCase(),u=s.toLowerCase(),d=[u];return(u===".jpg"||u===".jpeg")&&(d=[".jpg",".jpeg"]),d.some(function(p){return c.endsWith(p)})}return/\/\*$/.test(s)?a===s.replace(/\/.*$/,""):o===s?!0:/^\w+$/.test(s)?(At(!1,"Upload takes an invalidate 'accept' type '".concat(s,"'.Skip for check.")),!0):!1})}return!0};function Mr(e,t){var r="cannot ".concat(e.method," ").concat(e.action," ").concat(t.status,"'"),n=new Error(r);return n.status=t.status,n.method=e.method,n.url=e.action,n}function rt(e){var t=e.responseText||e.response;if(!t)return t;try{return JSON.parse(t)}catch{return t}}function Tr(e){var t=new XMLHttpRequest;e.onProgress&&t.upload&&(t.upload.onprogress=function(a){a.total>0&&(a.percent=a.loaded/a.total*100),e.onProgress(a)});var r=new FormData;e.data&&Object.keys(e.data).forEach(function(o){var a=e.data[o];if(Array.isArray(a)){a.forEach(function(i){r.append("".concat(o,"[]"),i)});return}r.append(o,a)}),e.file instanceof Blob?r.append(e.filename,e.file,e.file.name):r.append(e.filename,e.file),t.onerror=function(a){e.onError(a)},t.onload=function(){return t.status<200||t.status>=300?e.onError(Mr(e,t),rt(t)):e.onSuccess(rt(t),t)},t.open(e.method,e.action,!0),e.withCredentials&&"withCredentials"in t&&(t.withCredentials=!0);var n=e.headers||{};return n["X-Requested-With"]!==null&&t.setRequestHeader("X-Requested-With","XMLHttpRequest"),Object.keys(n).forEach(function(o){n[o]!==null&&t.setRequestHeader(o,n[o])}),t.send(r),{abort:function(){t.abort()}}}var Ur=function(){var e=pe(K().mark(function t(r,n){var o,a,i,s,c,u,d,p;return K().wrap(function(g){for(;;)switch(g.prev=g.next){case 0:u=function(){return u=pe(K().mark(function w(v){return K().wrap(function(b){for(;;)switch(b.prev=b.next){case 0:return b.abrupt("return",new Promise(function(S){v.file(function(C){n(C)?(v.fullPath&&!C.webkitRelativePath&&(Object.defineProperties(C,{webkitRelativePath:{writable:!0}}),C.webkitRelativePath=v.fullPath.replace(/^\//,""),Object.defineProperties(C,{webkitRelativePath:{writable:!1}})),S(C)):S(null)})}));case 1:case"end":return b.stop()}},w)})),u.apply(this,arguments)},c=function(w){return u.apply(this,arguments)},s=function(){return s=pe(K().mark(function w(v){var h,b,S,C,m;return K().wrap(function(x){for(;;)switch(x.prev=x.next){case 0:h=v.createReader(),b=[];case 2:return x.next=5,new Promise(function(D){h.readEntries(D,function(){return D([])})});case 5:if(S=x.sent,C=S.length,C){x.next=9;break}return x.abrupt("break",12);case 9:for(m=0;m<C;m++)b.push(S[m]);x.next=2;break;case 12:return x.abrupt("return",b);case 13:case"end":return x.stop()}},w)})),s.apply(this,arguments)},i=function(w){return s.apply(this,arguments)},o=[],a=[],r.forEach(function($){return a.push($.webkitGetAsEntry())}),d=function(){var $=pe(K().mark(function w(v,h){var b,S;return K().wrap(function(m){for(;;)switch(m.prev=m.next){case 0:if(v){m.next=2;break}return m.abrupt("return");case 2:if(v.path=h||"",!v.isFile){m.next=10;break}return m.next=6,c(v);case 6:b=m.sent,b&&o.push(b),m.next=15;break;case 10:if(!v.isDirectory){m.next=15;break}return m.next=13,i(v);case 13:S=m.sent,a.push.apply(a,se(S));case 15:case"end":return m.stop()}},w)}));return function(v,h){return $.apply(this,arguments)}}(),p=0;case 9:if(!(p<a.length)){g.next=15;break}return g.next=12,d(a[p]);case 12:p++,g.next=9;break;case 15:return g.abrupt("return",o);case 16:case"end":return g.stop()}},t)}));return function(r,n){return e.apply(this,arguments)}}(),_r=+new Date,zr=0;function Le(){return"rc-upload-".concat(_r,"-").concat(++zr)}var Wr=["component","prefixCls","className","classNames","disabled","id","name","style","styles","multiple","accept","capture","children","directory","openFileDialogOnClick","onMouseEnter","onMouseLeave","hasControlInside"],Br=function(e){ct(r,e);var t=ut(r);function r(){var n;dt(this,r);for(var o=arguments.length,a=new Array(o),i=0;i<o;i++)a[i]=arguments[i];return n=t.call.apply(t,[this].concat(a)),T(G(n),"state",{uid:Le()}),T(G(n),"reqs",{}),T(G(n),"fileInput",void 0),T(G(n),"_isMounted",void 0),T(G(n),"onChange",function(s){var c=n.props,u=c.accept,d=c.directory,p=s.target.files,f=se(p).filter(function(g){return!d||Re(g,u)});n.uploadFiles(f),n.reset()}),T(G(n),"onClick",function(s){var c=n.fileInput;if(c){var u=s.target,d=n.props.onClick;if(u&&u.tagName==="BUTTON"){var p=c.parentNode;p.focus(),u.blur()}c.click(),d&&d(s)}}),T(G(n),"onKeyDown",function(s){s.key==="Enter"&&n.onClick(s)}),T(G(n),"onDataTransferFiles",function(){var s=pe(K().mark(function c(u,d){var p,f,g,$,w,v,h;return K().wrap(function(S){for(;;)switch(S.prev=S.next){case 0:if(p=n.props,f=p.multiple,g=p.accept,$=p.directory,w=se(u.items||[]),v=se(u.files||[]),(v.length>0||w.some(function(C){return C.kind==="file"}))&&(d==null||d()),!$){S.next=11;break}return S.next=7,Ur(Array.prototype.slice.call(w),function(C){return Re(C,n.props.accept)});case 7:v=S.sent,n.uploadFiles(v),S.next=14;break;case 11:h=se(v).filter(function(C){return Re(C,g)}),f===!1&&(h=v.slice(0,1)),n.uploadFiles(h);case 14:case"end":return S.stop()}},c)}));return function(c,u){return s.apply(this,arguments)}}()),T(G(n),"onFilePaste",function(){var s=pe(K().mark(function c(u){var d,p;return K().wrap(function(g){for(;;)switch(g.prev=g.next){case 0:if(d=n.props.pastable,d){g.next=3;break}return g.abrupt("return");case 3:if(u.type!=="paste"){g.next=6;break}return p=u.clipboardData,g.abrupt("return",n.onDataTransferFiles(p,function(){u.preventDefault()}));case 6:case"end":return g.stop()}},c)}));return function(c){return s.apply(this,arguments)}}()),T(G(n),"onFileDragOver",function(s){s.preventDefault()}),T(G(n),"onFileDrop",function(){var s=pe(K().mark(function c(u){var d;return K().wrap(function(f){for(;;)switch(f.prev=f.next){case 0:if(u.preventDefault(),u.type!=="drop"){f.next=4;break}return d=u.dataTransfer,f.abrupt("return",n.onDataTransferFiles(d));case 4:case"end":return f.stop()}},c)}));return function(c){return s.apply(this,arguments)}}()),T(G(n),"uploadFiles",function(s){var c=se(s),u=c.map(function(d){return d.uid=Le(),n.processFile(d,c)});Promise.all(u).then(function(d){var p=n.props.onBatchStart;p==null||p(d.map(function(f){var g=f.origin,$=f.parsedFile;return{file:g,parsedFile:$}})),d.filter(function(f){return f.parsedFile!==null}).forEach(function(f){n.post(f)})})}),T(G(n),"processFile",function(){var s=pe(K().mark(function c(u,d){var p,f,g,$,w,v,h,b,S;return K().wrap(function(m){for(;;)switch(m.prev=m.next){case 0:if(p=n.props.beforeUpload,f=u,!p){m.next=14;break}return m.prev=3,m.next=6,p(u,d);case 6:f=m.sent,m.next=12;break;case 9:m.prev=9,m.t0=m.catch(3),f=!1;case 12:if(f!==!1){m.next=14;break}return m.abrupt("return",{origin:u,parsedFile:null,action:null,data:null});case 14:if(g=n.props.action,typeof g!="function"){m.next=21;break}return m.next=18,g(u);case 18:$=m.sent,m.next=22;break;case 21:$=g;case 22:if(w=n.props.data,typeof w!="function"){m.next=29;break}return m.next=26,w(u);case 26:v=m.sent,m.next=30;break;case 29:v=w;case 30:return h=(ve(f)==="object"||typeof f=="string")&&f?f:u,h instanceof File?b=h:b=new File([h],u.name,{type:u.type}),S=b,S.uid=u.uid,m.abrupt("return",{origin:u,data:v,parsedFile:S,action:$});case 35:case"end":return m.stop()}},c,null,[[3,9]])}));return function(c,u){return s.apply(this,arguments)}}()),T(G(n),"saveFileInput",function(s){n.fileInput=s}),n}return pt(r,[{key:"componentDidMount",value:function(){this._isMounted=!0;var o=this.props.pastable;o&&document.addEventListener("paste",this.onFilePaste)}},{key:"componentWillUnmount",value:function(){this._isMounted=!1,this.abort(),document.removeEventListener("paste",this.onFilePaste)}},{key:"componentDidUpdate",value:function(o){var a=this.props.pastable;a&&!o.pastable?document.addEventListener("paste",this.onFilePaste):!a&&o.pastable&&document.removeEventListener("paste",this.onFilePaste)}},{key:"post",value:function(o){var a=this,i=o.data,s=o.origin,c=o.action,u=o.parsedFile;if(this._isMounted){var d=this.props,p=d.onStart,f=d.customRequest,g=d.name,$=d.headers,w=d.withCredentials,v=d.method,h=s.uid,b=f||Tr,S={action:c,filename:g,data:i,file:u,headers:$,withCredentials:w,method:v||"post",onProgress:function(m){var P=a.props.onProgress;P==null||P(m,u)},onSuccess:function(m,P){var x=a.props.onSuccess;x==null||x(m,u,P),delete a.reqs[h]},onError:function(m,P){var x=a.props.onError;x==null||x(m,P,u),delete a.reqs[h]}};p(s),this.reqs[h]=b(S)}}},{key:"reset",value:function(){this.setState({uid:Le()})}},{key:"abort",value:function(o){var a=this.reqs;if(o){var i=o.uid?o.uid:o;a[i]&&a[i].abort&&a[i].abort(),delete a[i]}else Object.keys(a).forEach(function(s){a[s]&&a[s].abort&&a[s].abort(),delete a[s]})}},{key:"render",value:function(){var o=this.props,a=o.component,i=o.prefixCls,s=o.className,c=o.classNames,u=c===void 0?{}:c,d=o.disabled,p=o.id,f=o.name,g=o.style,$=o.styles,w=$===void 0?{}:$,v=o.multiple,h=o.accept,b=o.capture,S=o.children,C=o.directory,m=o.openFileDialogOnClick,P=o.onMouseEnter,x=o.onMouseLeave,D=o.hasControlInside,N=nt(o,Wr),E=_(T(T(T({},i,!0),"".concat(i,"-disabled"),d),s,s)),te=C?{directory:"directory",webkitdirectory:"webkitdirectory"}:{},re=d?{}:{onClick:m?this.onClick:function(){},onKeyDown:m?this.onKeyDown:function(){},onMouseEnter:P,onMouseLeave:x,onDrop:this.onFileDrop,onDragOver:this.onFileDragOver,tabIndex:D?void 0:"0"};return Ue.createElement(a,ue({},re,{className:E,role:D?void 0:"button",style:g}),Ue.createElement("input",ue({},Mt(N,{aria:!0,data:!0}),{id:p,name:f,disabled:d,type:"file",ref:this.saveFileInput,onClick:function(B){return B.stopPropagation()},key:this.state.uid,style:Ae({display:"none"},w.input),className:u.input,accept:h},te,{multiple:v,onChange:this.onChange},b!=null?{capture:b}:{})),S)}}]),r}(l.Component);function Ne(){}var _e=function(e){ct(r,e);var t=ut(r);function r(){var n;dt(this,r);for(var o=arguments.length,a=new Array(o),i=0;i<o;i++)a[i]=arguments[i];return n=t.call.apply(t,[this].concat(a)),T(G(n),"uploader",void 0),T(G(n),"saveUploader",function(s){n.uploader=s}),n}return pt(r,[{key:"abort",value:function(o){this.uploader.abort(o)}},{key:"render",value:function(){return Ue.createElement(Br,ue({},this.props,{ref:this.saveUploader}))}}]),r}(l.Component);T(_e,"defaultProps",{component:"span",prefixCls:"rc-upload",data:{},headers:{},name:"file",multipart:!1,onStart:Ne,onError:Ne,onSuccess:Ne,multiple:!1,beforeUpload:null,customRequest:null,withCredentials:!1,openFileDialogOnClick:!0,hasControlInside:!1});const Hr=e=>{const{componentCls:t,iconCls:r}=e;return{[`${t}-wrapper`]:{[`${t}-drag`]:{position:"relative",width:"100%",height:"100%",textAlign:"center",background:e.colorFillAlter,border:`${ee(e.lineWidth)} dashed ${e.colorBorder}`,borderRadius:e.borderRadiusLG,cursor:"pointer",transition:`border-color ${e.motionDurationSlow}`,[t]:{padding:e.padding},[`${t}-btn`]:{display:"table",width:"100%",height:"100%",outline:"none",borderRadius:e.borderRadiusLG,"&:focus-visible":{outline:`${ee(e.lineWidthFocus)} solid ${e.colorPrimaryBorder}`}},[`${t}-drag-container`]:{display:"table-cell",verticalAlign:"middle"},[`
          &:not(${t}-disabled):hover,
          &-hover:not(${t}-disabled)
        `]:{borderColor:e.colorPrimaryHover},[`p${t}-drag-icon`]:{marginBottom:e.margin,[r]:{color:e.colorPrimary,fontSize:e.uploadThumbnailSize}},[`p${t}-text`]:{margin:`0 0 ${ee(e.marginXXS)}`,color:e.colorTextHeading,fontSize:e.fontSizeLG},[`p${t}-hint`]:{color:e.colorTextDescription,fontSize:e.fontSize},[`&${t}-disabled`]:{[`p${t}-drag-icon ${r},
            p${t}-text,
            p${t}-hint
          `]:{color:e.colorTextDisabled}}}}}},Xr=e=>{const{componentCls:t,iconCls:r,fontSize:n,lineHeight:o,calc:a}=e,i=`${t}-list-item`,s=`${i}-actions`,c=`${i}-action`;return{[`${t}-wrapper`]:{[`${t}-list`]:Object.assign(Object.assign({},mt()),{lineHeight:e.lineHeight,[i]:{position:"relative",height:a(e.lineHeight).mul(n).equal(),marginTop:e.marginXS,fontSize:n,display:"flex",alignItems:"center",transition:`background-color ${e.motionDurationSlow}`,borderRadius:e.borderRadiusSM,"&:hover":{backgroundColor:e.controlItemBgHover},[`${i}-name`]:Object.assign(Object.assign({},ft),{padding:`0 ${ee(e.paddingXS)}`,lineHeight:o,flex:"auto",transition:`all ${e.motionDurationSlow}`}),[s]:{whiteSpace:"nowrap",[c]:{opacity:0},[r]:{color:e.actionsColor,transition:`all ${e.motionDurationSlow}`},[`
              ${c}:focus-visible,
              &.picture ${c}
            `]:{opacity:1}},[`${t}-icon ${r}`]:{color:e.colorIcon,fontSize:n},[`${i}-progress`]:{position:"absolute",bottom:e.calc(e.uploadProgressOffset).mul(-1).equal(),width:"100%",paddingInlineStart:a(n).add(e.paddingXS).equal(),fontSize:n,lineHeight:0,pointerEvents:"none","> div":{margin:0}}},[`${i}:hover ${c}`]:{opacity:1},[`${i}-error`]:{color:e.colorError,[`${i}-name, ${t}-icon ${r}`]:{color:e.colorError},[s]:{[`${r}, ${r}:hover`]:{color:e.colorError},[c]:{opacity:1}}},[`${t}-list-item-container`]:{transition:`opacity ${e.motionDurationSlow}, height ${e.motionDurationSlow}`,"&::before":{display:"table",width:0,height:0,content:'""'}}})}}},qr=e=>{const{componentCls:t}=e,r=new Te("uploadAnimateInlineIn",{from:{width:0,height:0,padding:0,opacity:0,margin:e.calc(e.marginXS).div(-2).equal()}}),n=new Te("uploadAnimateInlineOut",{to:{width:0,height:0,padding:0,opacity:0,margin:e.calc(e.marginXS).div(-2).equal()}}),o=`${t}-animate-inline`;return[{[`${t}-wrapper`]:{[`${o}-appear, ${o}-enter, ${o}-leave`]:{animationDuration:e.motionDurationSlow,animationTimingFunction:e.motionEaseInOutCirc,animationFillMode:"forwards"},[`${o}-appear, ${o}-enter`]:{animationName:r},[`${o}-leave`]:{animationName:n}}},{[`${t}-wrapper`]:Tt(e)},r,n]},Vr=e=>{const{componentCls:t,iconCls:r,uploadThumbnailSize:n,uploadProgressOffset:o,calc:a}=e,i=`${t}-list`,s=`${i}-item`;return{[`${t}-wrapper`]:{[`
        ${i}${i}-picture,
        ${i}${i}-picture-card,
        ${i}${i}-picture-circle
      `]:{[s]:{position:"relative",height:a(n).add(a(e.lineWidth).mul(2)).add(a(e.paddingXS).mul(2)).equal(),padding:e.paddingXS,border:`${ee(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusLG,"&:hover":{background:"transparent"},[`${s}-thumbnail`]:Object.assign(Object.assign({},ft),{width:n,height:n,lineHeight:ee(a(n).add(e.paddingSM).equal()),textAlign:"center",flex:"none",[r]:{fontSize:e.fontSizeHeading2,color:e.colorPrimary},img:{display:"block",width:"100%",height:"100%",overflow:"hidden"}}),[`${s}-progress`]:{bottom:o,width:`calc(100% - ${ee(a(e.paddingSM).mul(2).equal())})`,marginTop:0,paddingInlineStart:a(n).add(e.paddingXS).equal()}},[`${s}-error`]:{borderColor:e.colorError,[`${s}-thumbnail ${r}`]:{[`svg path[fill='${qe[0]}']`]:{fill:e.colorErrorBg},[`svg path[fill='${qe.primary}']`]:{fill:e.colorError}}},[`${s}-uploading`]:{borderStyle:"dashed",[`${s}-name`]:{marginBottom:o}}},[`${i}${i}-picture-circle ${s}`]:{[`&, &::before, ${s}-thumbnail`]:{borderRadius:"50%"}}}}},Gr=e=>{const{componentCls:t,iconCls:r,fontSizeLG:n,colorTextLightSolid:o,calc:a}=e,i=`${t}-list`,s=`${i}-item`,c=e.uploadPicCardSize;return{[`
      ${t}-wrapper${t}-picture-card-wrapper,
      ${t}-wrapper${t}-picture-circle-wrapper
    `]:Object.assign(Object.assign({},mt()),{display:"block",[`${t}${t}-select`]:{width:c,height:c,textAlign:"center",verticalAlign:"top",backgroundColor:e.colorFillAlter,border:`${ee(e.lineWidth)} dashed ${e.colorBorder}`,borderRadius:e.borderRadiusLG,cursor:"pointer",transition:`border-color ${e.motionDurationSlow}`,[`> ${t}`]:{display:"flex",alignItems:"center",justifyContent:"center",height:"100%",textAlign:"center"},[`&:not(${t}-disabled):hover`]:{borderColor:e.colorPrimary}},[`${i}${i}-picture-card, ${i}${i}-picture-circle`]:{display:"flex",flexWrap:"wrap","@supports not (gap: 1px)":{"& > *":{marginBlockEnd:e.marginXS,marginInlineEnd:e.marginXS}},"@supports (gap: 1px)":{gap:e.marginXS},[`${i}-item-container`]:{display:"inline-block",width:c,height:c,verticalAlign:"top"},"&::after":{display:"none"},"&::before":{display:"none"},[s]:{height:"100%",margin:0,"&::before":{position:"absolute",zIndex:1,width:`calc(100% - ${ee(a(e.paddingXS).mul(2).equal())})`,height:`calc(100% - ${ee(a(e.paddingXS).mul(2).equal())})`,backgroundColor:e.colorBgMask,opacity:0,transition:`all ${e.motionDurationSlow}`,content:'" "'}},[`${s}:hover`]:{[`&::before, ${s}-actions`]:{opacity:1}},[`${s}-actions`]:{position:"absolute",insetInlineStart:0,zIndex:10,width:"100%",whiteSpace:"nowrap",textAlign:"center",opacity:0,transition:`all ${e.motionDurationSlow}`,[`
            ${r}-eye,
            ${r}-download,
            ${r}-delete
          `]:{zIndex:10,width:n,margin:`0 ${ee(e.marginXXS)}`,fontSize:n,cursor:"pointer",transition:`all ${e.motionDurationSlow}`,color:o,"&:hover":{color:o},svg:{verticalAlign:"baseline"}}},[`${s}-thumbnail, ${s}-thumbnail img`]:{position:"static",display:"block",width:"100%",height:"100%",objectFit:"contain"},[`${s}-name`]:{display:"none",textAlign:"center"},[`${s}-file + ${s}-name`]:{position:"absolute",bottom:e.margin,display:"block",width:`calc(100% - ${ee(a(e.paddingXS).mul(2).equal())})`},[`${s}-uploading`]:{[`&${s}`]:{backgroundColor:e.colorFillAlter},[`&::before, ${r}-eye, ${r}-download, ${r}-delete`]:{display:"none"}},[`${s}-progress`]:{bottom:e.marginXL,width:`calc(100% - ${ee(a(e.paddingXS).mul(2).equal())})`,paddingInlineStart:0}}}),[`${t}-wrapper${t}-picture-circle-wrapper`]:{[`${t}${t}-select`]:{borderRadius:"50%"}}}},Kr=e=>{const{componentCls:t}=e;return{[`${t}-rtl`]:{direction:"rtl"}}},Jr=e=>{const{componentCls:t,colorTextDisabled:r}=e;return{[`${t}-wrapper`]:Object.assign(Object.assign({},st(e)),{[t]:{outline:0,"input[type='file']":{cursor:"pointer"}},[`${t}-select`]:{display:"inline-block"},[`${t}-hidden`]:{display:"none"},[`${t}-disabled`]:{color:r,cursor:"not-allowed"}})}},Yr=e=>({actionsColor:e.colorIcon}),Zr=ot("Upload",e=>{const{fontSizeHeading3:t,fontHeight:r,lineWidth:n,controlHeightLG:o,calc:a}=e,i=it(e,{uploadThumbnailSize:a(t).mul(2).equal(),uploadProgressOffset:a(a(r).div(2)).add(n).equal(),uploadPicCardSize:a(o).mul(2.55).equal()});return[Jr(i),Hr(i),Vr(i),Gr(i),Xr(i),qr(i),Kr(i),Ut(i)]},Yr);function Ce(e){return Object.assign(Object.assign({},e),{lastModified:e.lastModified,lastModifiedDate:e.lastModifiedDate,name:e.name,size:e.size,type:e.type,uid:e.uid,percent:0,originFileObj:e})}function Se(e,t){const r=se(t),n=r.findIndex(({uid:o})=>o===e.uid);return n===-1?r.push(e):r[n]=e,r}function ke(e,t){const r=e.uid!==void 0?"uid":"name";return t.filter(n=>n[r]===e[r])[0]}function Qr(e,t){const r=e.uid!==void 0?"uid":"name",n=t.filter(o=>o[r]!==e[r]);return n.length===t.length?null:n}const en=(e="")=>{const t=e.split("/"),n=t[t.length-1].split(/#|\?/)[0];return(/\.[^./\\]*$/.exec(n)||[""])[0]},vt=e=>e.indexOf("image/")===0,tn=e=>{if(e.type&&!e.thumbUrl)return vt(e.type);const t=e.thumbUrl||e.url||"",r=en(t);return/^data:image\//.test(t)||/(webp|svg|png|gif|jpg|jpeg|jfif|bmp|dpg|ico|heic|heif)$/i.test(r)?!0:!(/^data:/.test(t)||r)},de=200;function rn(e){return new Promise(t=>{if(!e.type||!vt(e.type)){t("");return}const r=document.createElement("canvas");r.width=de,r.height=de,r.style.cssText=`position: fixed; left: 0; top: 0; width: ${de}px; height: ${de}px; z-index: 9999; display: none;`,document.body.appendChild(r);const n=r.getContext("2d"),o=new Image;if(o.onload=()=>{const{width:a,height:i}=o;let s=de,c=de,u=0,d=0;a>i?(c=i*(de/a),d=-(c-s)/2):(s=a*(de/i),u=-(s-c)/2),n.drawImage(o,u,d,s,c);const p=r.toDataURL();document.body.removeChild(r),window.URL.revokeObjectURL(o.src),t(p)},o.crossOrigin="anonymous",e.type.startsWith("image/svg+xml")){const a=new FileReader;a.onload=()=>{a.result&&typeof a.result=="string"&&(o.src=a.result)},a.readAsDataURL(e)}else if(e.type.startsWith("image/gif")){const a=new FileReader;a.onload=()=>{a.result&&t(a.result)},a.readAsDataURL(e)}else o.src=window.URL.createObjectURL(e)})}const nn=l.forwardRef(({prefixCls:e,className:t,style:r,locale:n,listType:o,file:a,items:i,progress:s,iconRender:c,actionIconRender:u,itemRender:d,isImgUrl:p,showPreviewIcon:f,showRemoveIcon:g,showDownloadIcon:$,previewIcon:w,removeIcon:v,downloadIcon:h,extra:b,onPreview:S,onDownload:C,onClose:m},P)=>{var x,D;const{status:N}=a,[E,te]=l.useState(N);l.useEffect(()=>{N!=="removed"&&te(N)},[N]);const[re,J]=l.useState(!1);l.useEffect(()=>{const W=setTimeout(()=>{J(!0)},300);return()=>{clearTimeout(W)}},[]);const B=c(a);let Y=l.createElement("div",{className:`${e}-icon`},B);if(o==="picture"||o==="picture-card"||o==="picture-circle")if(E==="uploading"||!a.thumbUrl&&!a.url){const W=_(`${e}-list-item-thumbnail`,{[`${e}-list-item-file`]:E!=="uploading"});Y=l.createElement("div",{className:W},B)}else{const W=p!=null&&p(a)?l.createElement("img",{src:a.thumbUrl||a.url,alt:a.name,className:`${e}-list-item-image`,crossOrigin:a.crossOrigin}):B,ie=_(`${e}-list-item-thumbnail`,{[`${e}-list-item-file`]:p&&!p(a)});Y=l.createElement("a",{className:ie,onClick:M=>S(a,M),href:a.url||a.thumbUrl,target:"_blank",rel:"noopener noreferrer"},W)}const U=_(`${e}-list-item`,`${e}-list-item-${E}`),j=typeof a.linkProps=="string"?JSON.parse(a.linkProps):a.linkProps,Q=(typeof g=="function"?g(a):g)?u((typeof v=="function"?v(a):v)||l.createElement(Gt,null),()=>m(a),e,n.removeFile,!0):null,H=(typeof $=="function"?$(a):$)&&E==="done"?u((typeof h=="function"?h(a):h)||l.createElement(Kt,null),()=>C(a),e,n.downloadFile):null,z=o!=="picture-card"&&o!=="picture-circle"&&l.createElement("span",{key:"download-delete",className:_(`${e}-list-item-actions`,{picture:o==="picture"})},H,Q),A=typeof b=="function"?b(a):b,X=A&&l.createElement("span",{className:`${e}-list-item-extra`},A),y=_(`${e}-list-item-name`),R=a.url?l.createElement("a",Object.assign({key:"view",target:"_blank",rel:"noopener noreferrer",className:y,title:a.name},j,{href:a.url,onClick:W=>S(a,W)}),a.name,X):l.createElement("span",{key:"view",className:y,onClick:W=>S(a,W),title:a.name},a.name,X),q=(typeof f=="function"?f(a):f)&&(a.url||a.thumbUrl)?l.createElement("a",{href:a.url||a.thumbUrl,target:"_blank",rel:"noopener noreferrer",onClick:W=>S(a,W),title:n.previewFile},typeof w=="function"?w(a):w||l.createElement(_t,null)):null,V=(o==="picture-card"||o==="picture-circle")&&E!=="uploading"&&l.createElement("span",{className:`${e}-list-item-actions`},q,E==="done"&&H,Q),{getPrefixCls:ne}=l.useContext(Oe),ae=ne(),le=l.createElement("div",{className:U},Y,R,z,V,re&&l.createElement(gt,{motionName:`${ae}-fade`,visible:E==="uploading",motionDeadline:2e3},({className:W})=>{const ie="percent"in a?l.createElement(Sr,Object.assign({},s,{type:"line",percent:a.percent,"aria-label":a["aria-label"],"aria-labelledby":a["aria-labelledby"]})):null;return l.createElement("div",{className:_(`${e}-list-item-progress`,W)},ie)})),ce=a.response&&typeof a.response=="string"?a.response:((x=a.error)===null||x===void 0?void 0:x.statusText)||((D=a.error)===null||D===void 0?void 0:D.message)||n.uploadError,oe=E==="error"?l.createElement(at,{title:ce,getPopupContainer:W=>W.parentNode},le):le;return l.createElement("div",{className:_(`${e}-list-item-container`,t),style:r,ref:P},d?d(oe,a,i,{download:C.bind(null,a),preview:S.bind(null,a),remove:m.bind(null,a)}):oe)}),an=(e,t)=>{const{listType:r="text",previewFile:n=rn,onPreview:o,onDownload:a,onRemove:i,locale:s,iconRender:c,isImageUrl:u=tn,prefixCls:d,items:p=[],showPreviewIcon:f=!0,showRemoveIcon:g=!0,showDownloadIcon:$=!1,removeIcon:w,previewIcon:v,downloadIcon:h,extra:b,progress:S={size:[-1,2],showInfo:!1},appendAction:C,appendActionVisible:m=!0,itemRender:P,disabled:x}=e,D=zt(),[N,E]=l.useState(!1),te=["picture-card","picture-circle"].includes(r);l.useEffect(()=>{r.startsWith("picture")&&(p||[]).forEach(y=>{!(y.originFileObj instanceof File||y.originFileObj instanceof Blob)||y.thumbUrl!==void 0||(y.thumbUrl="",n==null||n(y.originFileObj).then(R=>{y.thumbUrl=R||"",D()}))})},[r,p,n]),l.useEffect(()=>{E(!0)},[]);const re=(y,R)=>{if(o)return R==null||R.preventDefault(),o(y)},J=y=>{typeof a=="function"?a(y):y.url&&window.open(y.url)},B=y=>{i==null||i(y)},Y=y=>{if(c)return c(y,r);const R=y.status==="uploading";if(r.startsWith("picture")){const q=r==="picture"?l.createElement(Ke,null):s.uploading,V=u!=null&&u(y)?l.createElement(Nr,null):l.createElement(Or,null);return R?q:V}return R?l.createElement(Ke,null):l.createElement(jr,null)},U=(y,R,q,V,ne)=>{const ae={type:"text",size:"small",title:V,onClick:le=>{var ce,oe;R(),l.isValidElement(y)&&((oe=(ce=y.props).onClick)===null||oe===void 0||oe.call(ce,le))},className:`${q}-list-item-action`,disabled:ne?x:!1};return l.isValidElement(y)?l.createElement(Ge,Object.assign({},ae,{icon:Ve(y,Object.assign(Object.assign({},y.props),{onClick:()=>{}}))})):l.createElement(Ge,Object.assign({},ae),l.createElement("span",null,y))};l.useImperativeHandle(t,()=>({handlePreview:re,handleDownload:J}));const{getPrefixCls:j}=l.useContext(Oe),Q=j("upload",d),H=j(),z=_(`${Q}-list`,`${Q}-list-${r}`),A=l.useMemo(()=>lt(Wt(H),["onAppearEnd","onEnterEnd","onLeaveEnd"]),[H]),X=Object.assign(Object.assign({},te?{}:A),{motionDeadline:2e3,motionName:`${Q}-${te?"animate-inline":"animate"}`,keys:se(p.map(y=>({key:y.uid,file:y}))),motionAppear:N});return l.createElement("div",{className:z},l.createElement(Bt,Object.assign({},X,{component:!1}),({key:y,file:R,className:q,style:V})=>l.createElement(nn,{key:y,locale:s,prefixCls:Q,className:q,style:V,file:R,items:p,progress:S,listType:r,isImgUrl:u,showPreviewIcon:f,showRemoveIcon:g,showDownloadIcon:$,removeIcon:w,previewIcon:v,downloadIcon:h,extra:b,iconRender:Y,actionIconRender:U,itemRender:P,onPreview:re,onDownload:J,onClose:B})),C&&l.createElement(gt,Object.assign({},X,{visible:m,forceRender:!0}),({className:y,style:R})=>Ve(C,q=>({className:_(q.className,y),style:Object.assign(Object.assign(Object.assign({},R),{pointerEvents:y?"none":void 0}),q.style)}))))},on=l.forwardRef(an);var sn=function(e,t,r,n){function o(a){return a instanceof r?a:new r(function(i){i(a)})}return new(r||(r=Promise))(function(a,i){function s(d){try{u(n.next(d))}catch(p){i(p)}}function c(d){try{u(n.throw(d))}catch(p){i(p)}}function u(d){d.done?a(d.value):o(d.value).then(s,c)}u((n=n.apply(e,[])).next())})};const ye=`__LIST_IGNORE_${Date.now()}__`,ln=(e,t)=>{const{fileList:r,defaultFileList:n,onRemove:o,showUploadList:a=!0,listType:i="text",onPreview:s,onDownload:c,onChange:u,onDrop:d,previewFile:p,disabled:f,locale:g,iconRender:$,isImageUrl:w,progress:v,prefixCls:h,className:b,type:S="select",children:C,style:m,itemRender:P,maxCount:x,data:D={},multiple:N=!1,hasControlInside:E=!0,action:te="",accept:re="",supportServerRender:J=!0,rootClassName:B}=e,Y=l.useContext(Ht),U=f??Y,[j,Q]=Xt(n||[],{value:r,postState:O=>O??[]}),[H,z]=l.useState("drop"),A=l.useRef(null),X=l.useRef(null);l.useMemo(()=>{const O=Date.now();(r||[]).forEach((F,k)=>{!F.uid&&!Object.isFrozen(F)&&(F.uid=`__AUTO__${O}_${k}__`)})},[r]);const y=(O,F,k)=>{let I=se(F),L=!1;x===1?I=I.slice(-1):x&&(L=I.length>x,I=I.slice(0,x)),Je.flushSync(()=>{Q(I)});const Z={file:O,fileList:I};k&&(Z.event=k),(!L||O.status==="removed"||I.some(fe=>fe.uid===O.uid))&&Je.flushSync(()=>{u==null||u(Z)})},R=(O,F)=>sn(void 0,void 0,void 0,function*(){const{beforeUpload:k,transformFile:I}=e;let L=O;if(k){const Z=yield k(O,F);if(Z===!1)return!1;if(delete O[ye],Z===ye)return Object.defineProperty(O,ye,{value:!0,configurable:!0}),!1;typeof Z=="object"&&Z&&(L=Z)}return I&&(L=yield I(L)),L}),q=O=>{const F=O.filter(L=>!L.file[ye]);if(!F.length)return;const k=F.map(L=>Ce(L.file));let I=se(j);k.forEach(L=>{I=Se(L,I)}),k.forEach((L,Z)=>{let fe=L;if(F[Z].parsedFile)L.status="uploading";else{const{originFileObj:he}=L;let ge;try{ge=new File([he],he.name,{type:he.type})}catch{ge=new Blob([he],{type:he.type}),ge.name=he.name,ge.lastModifiedDate=new Date,ge.lastModified=new Date().getTime()}ge.uid=L.uid,fe=ge}y(fe,I)})},V=(O,F,k)=>{try{typeof O=="string"&&(O=JSON.parse(O))}catch{}if(!ke(F,j))return;const I=Ce(F);I.status="done",I.percent=100,I.response=O,I.xhr=k;const L=Se(I,j);y(I,L)},ne=(O,F)=>{if(!ke(F,j))return;const k=Ce(F);k.status="uploading",k.percent=O.percent;const I=Se(k,j);y(k,I,O)},ae=(O,F,k)=>{if(!ke(k,j))return;const I=Ce(k);I.error=O,I.response=F,I.status="error";const L=Se(I,j);y(I,L)},le=O=>{let F;Promise.resolve(typeof o=="function"?o(O):o).then(k=>{var I;if(k===!1)return;const L=Qr(O,j);L&&(F=Object.assign(Object.assign({},O),{status:"removed"}),j==null||j.forEach(Z=>{const fe=F.uid!==void 0?"uid":"name";Z[fe]===F[fe]&&!Object.isFrozen(Z)&&(Z.status="removed")}),(I=A.current)===null||I===void 0||I.abort(F),y(F,L))})},ce=O=>{z(O.type),O.type==="drop"&&(d==null||d(O))};l.useImperativeHandle(t,()=>({onBatchStart:q,onSuccess:V,onProgress:ne,onError:ae,fileList:j,upload:A.current,nativeElement:X.current}));const{getPrefixCls:oe,direction:W,upload:ie}=l.useContext(Oe),M=oe("upload",h),be=Object.assign(Object.assign({onBatchStart:q,onError:ae,onProgress:ne,onSuccess:V},e),{data:D,multiple:N,action:te,accept:re,supportServerRender:J,prefixCls:M,disabled:U,beforeUpload:R,onChange:void 0,hasControlInside:E});delete be.className,delete be.style,(!C||U)&&delete be.id;const ze=`${M}-wrapper`,[Pe,We,yt]=Zr(M,ze),[wt]=qt("Upload",Vt.Upload),{showRemoveIcon:Be,showPreviewIcon:Ct,showDownloadIcon:St,removeIcon:Et,previewIcon:xt,downloadIcon:Ot,extra:It}=typeof a=="boolean"?{}:a,Pt=typeof Be>"u"?!U:Be,Fe=(O,F)=>a?l.createElement(on,{prefixCls:M,listType:i,items:j,previewFile:p,onPreview:s,onDownload:c,onRemove:le,showRemoveIcon:Pt,showPreviewIcon:Ct,showDownloadIcon:St,removeIcon:Et,previewIcon:xt,downloadIcon:Ot,iconRender:$,extra:It,locale:Object.assign(Object.assign({},wt),g),isImageUrl:w,progress:v,appendAction:O,appendActionVisible:F,itemRender:P,disabled:U}):O,De=_(ze,b,B,We,yt,ie==null?void 0:ie.className,{[`${M}-rtl`]:W==="rtl",[`${M}-picture-card-wrapper`]:i==="picture-card",[`${M}-picture-circle-wrapper`]:i==="picture-circle"}),He=Object.assign(Object.assign({},ie==null?void 0:ie.style),m);if(S==="drag"){const O=_(We,M,`${M}-drag`,{[`${M}-drag-uploading`]:j.some(F=>F.status==="uploading"),[`${M}-drag-hover`]:H==="dragover",[`${M}-disabled`]:U,[`${M}-rtl`]:W==="rtl"});return Pe(l.createElement("span",{className:De,ref:X},l.createElement("div",{className:O,style:He,onDrop:ce,onDragOver:ce,onDragLeave:ce},l.createElement(_e,Object.assign({},be,{ref:A,className:`${M}-btn`}),l.createElement("div",{className:`${M}-drag-container`},C))),Fe()))}const Ft=_(M,`${M}-select`,{[`${M}-disabled`]:U,[`${M}-hidden`]:!C}),Xe=l.createElement("div",{className:Ft,style:He},l.createElement(_e,Object.assign({},be,{ref:A})));return Pe(i==="picture-card"||i==="picture-circle"?l.createElement("span",{className:De,ref:X},Fe(Xe,!!C)):l.createElement("span",{className:De,ref:X},Xe,Fe()))},bt=l.forwardRef(ln);var cn=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const un=l.forwardRef((e,t)=>{var{style:r,height:n,hasControlInside:o=!1}=e,a=cn(e,["style","height","hasControlInside"]);return l.createElement(bt,Object.assign({ref:t,hasControlInside:o},a,{type:"drag",style:Object.assign(Object.assign({},r),{height:n})}))}),$t=bt;$t.Dragger=un;$t.LIST_IGNORE=ye;export{vn as R,$t as U,hn as a};
