import{r as l,F as o,u as w,d as ye,j as r,R as W,C as y,y as m,B as x,a4 as fr,z as hr,T as $}from"./index-De_f0oL2.js";import pr from"./index-Bssl0HaD.js";import{e as gr}from"./service-DcPXuTuP.js";import{p as A}from"./service-CEuf3VDV.js";import{R as wr,o as P,d as je}from"./down-BCLNnN1h.js";import{s as u}from"./Index.module-OEpj_sYj.js";import{p as xr}from"./service-ClCV2GnI.js";import{u as Sr}from"./debounce-D4mn-XUD.js";import{R as yr}from"./index-BmnYJy3v.js";import{f as J}from"./format-ChnqMkgG.js";import{s as d}from"./index-BcPP1N8I.js";import{D as jr}from"./index-CdSZ9YgQ.js";import{S as T}from"./index-BWJehDyc.js";import{C as Ne}from"./index-DZyVV6rP.js";import{R as Nr,a as Ir}from"./FullscreenOutlined-DzCTibKW.js";import{R as Ar,U as Tr,a as br}from"./index-BH5uxjwl.js";import{R as Cr}from"./DeleteOutlined-D-FcgX8f.js";import{M as Y}from"./index-Dck5cc4J.js";import{P as Ie}from"./index-TkzW9Zmk.js";import{F as Er}from"./Table-D-iLeFE-.js";import{R as Dr}from"./InfoCircleOutlined-DUD7mNgc.js";import"./useMultipleSelect-B0dEIXT-.js";const{Dragger:Or}=Tr,ea=()=>{var Se;const Q=[{title:"专项奖励名称",key:"rewardsName",dataIndex:"rewardsName",align:"center",fixed:"left",width:130,render:(e,t)=>r.jsx($,{title:e,children:r.jsx("div",{className:u.over_ellipsis,children:e})})},{title:"专项奖励文号",key:"rewardsNo",dataIndex:"rewardsNo",align:"center",fixed:"left",width:120,render:(e,t)=>r.jsx($,{title:e,children:r.jsx("div",{className:u.over_ellipsis,children:e})})},{title:"专项奖励金额",key:"rewardsAmount",dataIndex:"rewardsAmount",align:"center",fixed:"left",width:80,render:e=>r.jsx("span",{children:J(e)})},{title:"专项奖励层级",key:"rewardsLevel",dataIndex:"rewardsLevel",actionType:"select",actionOptionName:"levelList",align:"center",fixed:"left",width:100,render:e=>{var t;return(t=N==null?void 0:N.find(a=>a.enumId===e))==null?void 0:t.enumName}},{title:"专项奖励",children:[{title:"专业线",dataIndex:"rewardsProfLine",key:"rewardsProfLine",align:"center",width:75,actionType:"select",actionOptionName:"profLineList",render:e=>{var t;return(t=E==null?void 0:E.find(a=>a.enumId===e))==null?void 0:t.enumName}},{title:"奖项分类",dataIndex:"rewardsType",key:"rewardsType",align:"center",width:75,actionType:"select",actionOptionName:"typeList",render:e=>{var t;return(t=D==null?void 0:D.find(a=>a.enumId===e))==null?void 0:t.enumName}},{title:"组织名称",dataIndex:"rewardsOrgName",key:"rewardsOrgName",actionType:"cascader",align:"center",width:120,render:e=>{var t;return Array.isArray(e)?((t=e[e.length-1])==null?void 0:t.orgName)||"":e||""}},{title:"组织金额",dataIndex:"rewardsOrgAmount",key:"rewardsOrgAmount",align:"center",width:60,render:e=>r.jsx("span",{children:J(e)})},{title:"员工编号",dataIndex:"rewardsStaffId",key:"rewardsStaffId",align:"center",width:75},{title:"员工姓名",dataIndex:"rewardsStaffName",key:"rewardsStaffName",align:"center",width:75},{title:"员工金额",dataIndex:"rewardsStaffAmount",key:"rewardsStaffAmount",align:"center",width:60,render:e=>r.jsx("span",{children:J(e)})},{title:"备注",dataIndex:"remark",key:"remark",align:"center",width:100,render:(e,t)=>r.jsx($,{title:e,children:r.jsx("div",{className:u.over_ellipsis,children:e})})}]},{title:r.jsxs(r.Fragment,{children:[r.jsx("span",{children:"操作"}),r.jsx($,{title:"修改总金额时，同文号的总金额都会修改。",children:r.jsx(Dr,{})})]}),dataIndex:"action",key:"action",align:"center",fixed:"right",width:120,render:(e,t)=>r.jsx("div",{className:"action",children:r.jsx(Ie,{title:"修改总金额时，同文号的总金额都会修改。",onConfirm:()=>Ke(t),okText:" 确认",cancelText:"取消",children:r.jsx(x,{type:"link",children:"修改"})})})}],X=l.useRef(null),Z=l.useRef(null),ee=l.useRef(null),[j]=o.useForm(),[i]=o.useForm(),[re]=o.useForm(),[Ae,S]=l.useState(!1),[ae,Te]=l.useState([]),[be,q]=l.useState(!1),[Ce,z]=l.useState(!1),[Ee,te]=l.useState(!1),[b,se]=l.useState([]),[k,De]=l.useState([]),[N,Oe]=l.useState([]),[E,Re]=l.useState([]),[D,Fe]=l.useState([]),[v,ke]=l.useState(0),[Me,Le]=l.useState(0),[C,_e]=l.useState([]),G=l.useRef([]),[M,U]=l.useState([]),[Ve,le]=l.useState(!1),[ne,oe]=l.useState(!0),[H,L]=l.useState({}),[de,B]=l.useState(!1),[$e,O]=l.useState(!1),[_,ce]=l.useState([]),[f,ie]=l.useState({total:0,pageNum:1,pageSize:5}),[h,me]=l.useState({total:0,pageNum:1,pageSize:50}),{runAsync:K}=w(xr.getEnumType,{manual:!0}),{runAsync:Pe}=w(gr.build4LevelOrgTree,{manual:!0}),{runAsync:Ye}=w(A.getSpecialRewards,{manual:!0}),{runAsync:qe}=w(A.getEmployeeByEmpId,{manual:!0}),{runAsync:ze}=w(A.getDocNumList,{manual:!0}),{runAsync:ve}=w(A.addSpecialRewards,{manual:!0}),{runAsync:Ge}=w(A.delBatchByRewardsNo,{manual:!0}),{runAsync:Ue}=w(A.exportSpecialRewards,{manual:!0}),{runAsync:He}=w(A.uploadSpecialRewards,{manual:!0}),{runAsync:Be}=w(A.downloadSalaryTemplate,{manual:!0});l.useEffect(()=>(Je(),fe(),I(),window.addEventListener("resize",ue),()=>{window.removeEventListener("resize",ue)}),[]);const ue=()=>{R()};l.useEffect(()=>{R()},[(document.querySelector(".rewardPage_table .ant-table-header")||{}).offsetHeight]),l.useEffect(()=>{R()},[ae]),l.useEffect(()=>{v>0&&I()},[v]),l.useEffect(()=>{(h==null?void 0:h.total)>0&&I()},[h.pageNum,h.pageSize]),l.useEffect(()=>{(f==null?void 0:f.total)>0&&I()},[f.pageNum,f.pageSize]);const R=()=>{var a;const e=(document.querySelector(".rewardPage_table .ant-table-header")||{}).offsetHeight||0,t=(document.querySelector(".rewardPage_table .ant-table-pagination")||{}).offsetHeight||26;e&&t&&Le(((a=Z.current)==null?void 0:a.offsetHeight)-(ee.current.offsetHeight+e+t))},fe=()=>{const e=ye().subtract(1,"month");j.setFieldsValue({cycleId:e}),Qe()},Ke=e=>{const t={...e,rewardsOrgId:ur(k,e.rewardsOrgId)};ce([{orgId:t.rewardsOrgId[t.rewardsOrgId.length-1],orgName:t.rewardsOrgName}]),B(!0),L(t),i.setFieldsValue(t)},We=()=>{const e=_[(_==null?void 0:_.length)-1]||{};O(!0),he({...H,rewardsOrgId:e==null?void 0:e.orgId,rewardsOrgName:e==null?void 0:e.orgName},"edit")},Je=async()=>{const[[e,t],[a,s],[p,n]]=await Promise.all([K({code:"specialRewardsLevel"}),K({code:"specialRewardsProfLine"}),K({code:"specialRewardsType"})]);e||a||p||(t.STATUS==="0000"&&Oe(t.DATA),s.STATUS==="0000"&&Re(s.DATA),n.STATUS==="0000"&&Fe(n.DATA))},Qe=async()=>{const[e,t]=await Pe({monthId:ye().format("YYYYMM"),tag:"2"});if(e){d.error((t==null?void 0:t.DATA)||(t==null?void 0:t.MESSAGE)||"调用失败");return}t.STATUS==="0000"?(De(t.DATA),j.setFieldValue("orgaId","")):d.error((t==null?void 0:t.MESSAGE)||(t==null?void 0:t.DATA)||"调用失败")},I=async()=>{var s,p,n;const e=j.getFieldsValue();S(!0);const[t,a]=await Ye({...e,cycleId:(s=e==null?void 0:e.cycleId)==null?void 0:s.format("YYYYMM"),orgaId:e!=null&&e.orgaId?e==null?void 0:e.orgaId[((p=e==null?void 0:e.orgaId)==null?void 0:p.length)-1]:"",...h});if(S(!1),t){d.error((a==null?void 0:a.DATA)||(a==null?void 0:a.MESSAGE)||"调用失败");return}if(a.STATUS==="0000"){const{DATA:{data:c}}=a,g=c.map((F,V)=>({...F,key:F.id||V}));Te(g),me({...h,total:(n=a.DATA)==null?void 0:n.totalCount})}else d.error((a==null?void 0:a.MESSAGE)||(a==null?void 0:a.DATA)||"调用失败")},Xe=async()=>{var e,t;try{P("正在导出",0,"loading");const a=j.getFieldsValue(),s=await Ue({...a,cycleId:(e=a==null?void 0:a.cycleId)==null?void 0:e.format("YYYYMM"),orgaId:a!=null&&a.orgaId?a==null?void 0:a.orgaId[((t=a==null?void 0:a.orgaId)==null?void 0:t.length)-1]:""});je(s)}catch(a){P("导出失败",1,"error"),console.error("Download failed:",a)}},he=async(e,t)=>{const a=i.getFieldsValue();S(!0);const s=t==="edit"?[{...e,rewardsOrgId:e.rewardsType==="org"?e.rewardsOrgId:"",rewardsOrgName:e.rewardsType==="org"?e.rewardsOrgName:"",rewardsStaffName:a.rewardsStaffId&&a.rewardsStaffName?a.rewardsStaffName:"",rewardsStaffId:a.rewardsStaffId&&a.rewardsStaffName?a.rewardsStaffId:""}]:e==null?void 0:e.map(c=>{var g,F,V;return{...c,cycleId:(g=c==null?void 0:c.cycleId)==null?void 0:g.format("YYYYMM"),rewardsOrgId:((F=c.rewardsOrgName)==null?void 0:F.length)>0?c.rewardsOrgName[c.rewardsOrgName.length-1].orgId:"",rewardsOrgName:((V=c.rewardsOrgName)==null?void 0:V.length)>0?c.rewardsOrgName[c.rewardsOrgName.length-1].orgName:""}});if(t==="edit"&&e.realRewardsStaffAmount&&!e.rewardsStaffName){d.warning("员工金额不为空时，员工姓名和员工编码必填"),O(!1),S(!1);return}const[p,n]=await ve(s);if(p){d.error((n==null?void 0:n.DATA)||(n==null?void 0:n.MESSAGE)||"调用失败"),O(!1),S(!1);return}n.STATUS==="0000"?(d.success(n==null?void 0:n.DATA),q(!1),O(!1),B(!1),I()):(d.error((n==null?void 0:n.MESSAGE)||(n==null?void 0:n.DATA)||"调用失败"),O(!1),S(!1))},Ze=async()=>{try{P("正在导出",0,"loading");const e=await Be({templateId:"SPECIAL_REWARDS"});je(e)}catch(e){P("导出失败",1,"error"),console.error("Download failed:",e)}},er=async()=>{var s,p,n;const e=j.getFieldsValue();le(!0);const[t,a]=await ze({...e,cycleId:(s=e==null?void 0:e.cycleId)==null?void 0:s.format("YYYYMM"),orgaId:e!=null&&e.orgaId?e==null?void 0:e.orgaId[((p=e==null?void 0:e.orgaId)==null?void 0:p.length)-1]:"",...f});if(le(!1),t){d.error((a==null?void 0:a.DATA)||(a==null?void 0:a.MESSAGE)||"调用失败");return}if(a.STATUS==="0000"){const{DATA:{data:c}}=a;G.current=c,U(c),ie({...f,total:(n=a.DATA)==null?void 0:n.totalCount})}else d.error((a==null?void 0:a.MESSAGE)||(a==null?void 0:a.DATA)||"调用失败")},pe=Sr(async e=>{const[t,a]=await qe({empId:e.target.value});if(t){d.error((a==null?void 0:a.DATA)||(a==null?void 0:a.MESSAGE)||"调用失败");return}if(a.STATUS==="0000"){const{DATA:s}=a;de?(L({...H,rewardsStaffName:s==null?void 0:s.employeeName}),i.setFieldsValue({rewardsStaffName:(s==null?void 0:s.employeeName)||""})):j.setFieldsValue({empName:s==null?void 0:s.employeeName})}else d.error((a==null?void 0:a.MESSAGE)||(a==null?void 0:a.DATA)||"调用失败")},500),rr=()=>{te(!0),er()},ar=e=>{console.log("Success:",e==null?void 0:e.rewardsNo);let t=G.current;e!=null&&e.rewardsNo&&(t=M==null?void 0:M.filter(a=>{var s;return a.rewardsNo===((s=e==null?void 0:e.rewardsNo)==null?void 0:s.trim())})),U(t)},tr=e=>{console.log("Success:",e),I()},sr=()=>{const e=v+1;j.resetFields(),fe(),ke(e)},lr=e=>{const t={total:e==null?void 0:e.total,pageNum:e==null?void 0:e.current,pageSize:e==null?void 0:e.pageSize};me(t)},nr=e=>{const t={total:e==null?void 0:e.total,pageNum:e==null?void 0:e.current,pageSize:e==null?void 0:e.pageSize};ie(t)},ge=()=>{z(!1),se([])},we=()=>{te(!1),U([]),G.current=[],re.resetFields()},or=()=>{(b==null?void 0:b.length)>0?ir():d.error("请先选择文件上传")},dr=()=>{(C==null?void 0:C.length)>0?cr():d.error("请先选择文件上传")},cr=async()=>{S(!0);const[e,t]=await Ge({rewardsNo:C==null?void 0:C.map(a=>a.rewardsNo)});if(e){d.error((t==null?void 0:t.DATA)||(t==null?void 0:t.MESSAGE)||"调用失败"),S(!1);return}t.STATUS==="0000"?(d.success(t==null?void 0:t.DATA),we(),I()):(d.error((t==null?void 0:t.MESSAGE)||(t==null?void 0:t.DATA)||"调用失败"),S(!1))},ir=async()=>{const e=new FormData;b.map(s=>s==null?void 0:s.originFileObj).forEach(s=>{e.append("file",s)}),e.append("tag","1");const[t,a]=await He(e);if(t){d.error((a==null?void 0:a.DATA)||(a==null?void 0:a.MESSAGE)||"调用失败");return}a.STATUS==="0000"?(z(!1),d.success(a==null?void 0:a.DATA),I()):d.error((a==null?void 0:a.MESSAGE)||(a==null?void 0:a.DATA)||"调用失败")},mr=e=>{e.cycleId},xe=(e,t)=>t.some(a=>a.orgName.toLowerCase().indexOf(e.trim().toLowerCase())>-1);function ur(e,t){let a=[];function s(p,n,c){for(const g of p){if(g.orgId===n)return a=[...c,g.orgId],!0;if(g.children&&g.children.length>0&&s(g.children,n,[...c,g.orgId]))return!0}return!1}return s(e,t,[]),a}return r.jsxs("div",{className:`pt-[0.5rem] px-[0rem] h-full flex flex-col ${u.processManagement_page}`,children:[r.jsx("div",{ref:X,className:"bg-white pt-[0.5rem] pb-[0rem] px-[1.75rem] mb-[0rem]",children:r.jsx(o,{labelCol:{span:6},form:j,onFinish:tr,onValuesChange:mr,children:r.jsxs(W,{gutter:24,children:[r.jsx(y,{span:6,children:r.jsx(o.Item,{name:"cycleId",label:"月份",className:"mb-[0.5rem]",children:r.jsx(jr,{picker:"month",style:{width:"100%"},allowClear:!1})})}),r.jsx(y,{span:6,children:r.jsx(o.Item,{name:"rewardsNo",label:"专项奖励文号",className:"mb-[0.5rem]",children:r.jsx(m,{placeholder:"请输入专项奖励文号",allowClear:!0,style:{width:"100%"}})})}),r.jsx(y,{span:6,children:r.jsx(o.Item,{name:"rewardsLevel",label:"专项奖励层级",className:"mb-[0.5rem]",children:r.jsx(T,{placeholder:"请选择专项奖励层级",allowClear:!0,children:N==null?void 0:N.map(e=>r.jsx(T.Option,{value:e==null?void 0:e.enumId,children:e==null?void 0:e.enumName},e==null?void 0:e.enumId))})})}),r.jsx(y,{span:6,children:r.jsx(o.Item,{name:"orgaId",label:"组织",className:"mb-[0.5rem]",children:r.jsx(Ne,{allowClear:!0,changeOnSelect:!0,expandTrigger:"hover",displayRender:e=>e[e.length-1],options:k,fieldNames:{value:"orgId",label:"orgName",children:"children"},placeholder:"请选择组织",showSearch:{filter:xe},onSearch:e=>console.log(e)})})}),r.jsx(y,{span:6,children:r.jsx(o.Item,{name:"empId",label:"员工编号",className:"mb-[0.5rem]",children:r.jsx(m,{placeholder:"请输入员工编号",allowClear:!0,style:{width:"100%"},onChange:pe})})}),r.jsx(y,{span:6,children:r.jsx(o.Item,{name:"empName",label:"员工姓名",className:"mb-[0.5rem]",children:r.jsx(m,{placeholder:"请输入员工姓名",allowClear:!0,style:{width:"100%"}})})}),r.jsx(y,{span:6,children:r.jsxs("div",{className:"text-right",children:[r.jsx(x,{type:"primary",htmlType:"submit",children:"查询"}),r.jsx(x,{className:"ml-[0.4rem]",onClick:()=>sr(),children:"重置"})]})})]})})}),r.jsxs("div",{ref:Z,className:"relative bg-white px-5 pt-2.5",style:{height:`calc(100% - ${((Se=X.current)==null?void 0:Se.offsetHeight)+15}px)`},children:[r.jsxs("div",{ref:ee,className:`flex justify-between items-center overflow-hidden mb-2 ${u.animation_box} ${ne?"h-[1.6rem]":"h-0"}`,children:[r.jsxs("div",{className:"flex ",children:[ne?r.jsx(Nr,{className:`${u.shousuo_icon} text-[1rem]`,onClick:()=>{oe(!1),setTimeout(()=>{R()},200)}}):r.jsx(Ir,{className:`${u.shousuo_icon} absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10`,onClick:()=>{oe(!0),setTimeout(()=>{R()},200)}}),r.jsxs("div",{className:"font-bold text-[0.8rem] ml-[1rem]",children:["数据列表",r.jsx("span",{className:"text-[0.7rem]",style:{color:"#939393"},children:"（单位：元）"})]})]}),r.jsxs("div",{className:"flex",children:[r.jsxs("div",{className:"flex items-center gap-x-[0.25rem] ml-[0.6rem]",children:[r.jsx(fr,{name:"excel",width:20,height:20}),r.jsx("span",{className:"text-[#E60027] cursor-pointer",onClick:()=>Ze(),children:"下载导入模版"})]}),r.jsxs(x,{className:"ml-[0.4rem]",danger:!0,onClick:()=>{z(!0)},children:[r.jsx(Ar,{}),"导入"]}),r.jsxs(x,{type:"primary",className:"ml-[0.4rem]",onClick:()=>{q(!0)},children:[r.jsx(hr,{}),"新增"]}),r.jsxs(x,{type:"primary",className:"ml-[0.4rem]",onClick:()=>{rr()},children:[r.jsx(Cr,{}),"删除"]}),r.jsxs(x,{className:"ml-[0.4rem]",danger:!0,onClick:()=>Xe(),children:[r.jsx(wr,{}),"导出"]})]})]}),r.jsx(yr,{className:"rewardPage_table",rowClassName:(e,t)=>t%2===1?"customRow odd":"customRow even",columns:Q,dataSource:ae,loading:Ae,bordered:!0,scroll:{y:`calc(${Me}px - 0.625rem - 1.6rem - 0.5rem)`},onChange:lr,pagination:{...h,total:h==null?void 0:h.total,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50"]}})]}),r.jsx(Y,{title:"- 新增数据 -",destroyOnClose:!0,open:be,wrapClassName:u.add_table_modal,centered:!0,footer:null,onCancel:()=>q(!1),width:"80%",children:r.jsx(pr,{columns:Q,submitData:e=>he(e),cascaderOption:k,levelList:N,profLineList:E,typeList:D})}),r.jsx(Y,{title:"- 文件上传 -",destroyOnClose:!0,open:Ce,centered:!0,className:`${u.add_table_modal} ${u.detail_modal}`,footer:null,onCancel:ge,children:r.jsxs("div",{className:"mt-4",children:[r.jsx(W,{children:r.jsx(y,{span:22,offset:1,className:"h-[10rem]",children:r.jsxs(Or,{action:"",maxCount:1,multiple:!1,fileList:b,beforeUpload(e,t){return console.log(e,t),!1},onChange(e){const{status:t}=e.file;t!=="uploading"&&(console.log(e.file,e.fileList),se(e.fileList))},children:[r.jsx("p",{className:"ant-upload-drag-icon",children:r.jsx(br,{style:{color:"#F14846"}})}),r.jsx("p",{className:"ant-upload-text",children:"点击或将文件拖拽到这里上传"}),r.jsx("p",{className:"ant-upload-hint",children:"支持excel格式的文件。"})]})})}),r.jsxs("div",{style:{display:"flex",justifyContent:"center",marginTop:"2rem"},children:[r.jsx(x,{danger:!0,onClick:ge,children:"取消"}),r.jsx(Ie,{title:"",description:"导入文件如有重复数据，将会被覆盖，请确认是否上传。",onConfirm:or,onCancel:()=>{},okText:"确认",cancelText:"取消",children:r.jsx(x,{danger:!0,type:"primary",style:{marginLeft:"1rem"},disabled:b.length<1,children:"上传"})})]})]})}),r.jsxs(Y,{title:"- 删除 -",destroyOnClose:!0,open:Ee,wrapClassName:`${u.add_table_modal} ${u.footer_center}`,centered:!0,okText:"删除",cancelText:"返回",onOk:dr,onCancel:we,children:[r.jsx(o,{labelCol:{span:6},form:re,onFinish:ar,children:r.jsx(W,{gutter:24,children:r.jsx(y,{span:18,children:r.jsx(o.Item,{name:"rewardsNo",label:"专项奖励文号",children:r.jsx(m,{placeholder:"请输入专项奖励文号",allowClear:!0,style:{width:"100%"}})})})})}),r.jsx(Er,{rowKey:(e,t)=>(e==null?void 0:e.rewardsNo)||t,rowSelection:{type:"checkbox",onChange:(e,t)=>{console.log(`selectedRowKeys: ${e}`,"selectedRows: ",t),_e(t)},getCheckboxProps:e=>({})},columns:[{title:"专项奖励文号",key:"rewardsNo",dataIndex:"rewardsNo",fixed:"left",width:"80%"}],onChange:nr,loading:Ve,dataSource:M,pagination:{...f,size:"small",total:f==null?void 0:f.total,showSizeChanger:!1,showQuickJumper:!1,pageSizeOptions:["10","20","50"]}})]}),r.jsx(Y,{title:r.jsx("div",{style:{textAlign:"center",color:"#E60027"},children:"-修改数据-"}),destroyOnClose:!0,open:de,centered:!0,footer:null,onCancel:()=>{L({}),B(!1)},width:"30%",children:r.jsxs(o,{form:i,labelCol:{span:6},wrapperCol:{span:16},style:{maxWidth:600},initialValues:{},onValuesChange:e=>{L({...H,...i.getFieldsValue()})},onFinish:We,children:[r.jsx(o.Item,{label:"月份",name:"cycleId",rules:[{required:!1,message:"请输入月份!"}],children:r.jsx(m,{disabled:!0})}),r.jsx(o.Item,{label:"专项奖励文号",name:"rewardsNo",rules:[{required:!1,message:"请输入专项奖励文号!"}],children:r.jsx(m,{disabled:!0})}),r.jsx(o.Item,{label:"专项奖励名称",name:"rewardsName",rules:[{required:!1,message:"请输入专项奖励名称!"}],children:r.jsx(m,{})}),r.jsx(o.Item,{label:"专项奖励金额",name:"realRewardsAmount",rules:[{required:!1,message:"请输入专项奖励金额!"}],children:r.jsx(m,{})}),r.jsx(o.Item,{label:"专项奖励层级",name:"rewardsLevel",rules:[{required:!1,message:"请选择专项奖励层级!"}],children:r.jsx(T,{placeholder:"请选择专项奖励层级",allowClear:!0,children:N.map(e=>r.jsx(T.Option,{value:e==null?void 0:e.enumId,children:e==null?void 0:e.enumName},e==null?void 0:e.enumId))})}),r.jsx(o.Item,{label:"专业线",name:"rewardsProfLine",rules:[{required:!1,message:"请输入专业线!"}],children:r.jsx(T,{placeholder:"请选择专业线",allowClear:!0,children:E.map(e=>r.jsx(T.Option,{value:e==null?void 0:e.enumId,children:e==null?void 0:e.enumName},e==null?void 0:e.enumId))})}),r.jsx(o.Item,{label:"奖项分类",name:"rewardsType",rules:[{required:!1,message:"请选择奖项分类!"}],children:r.jsx(T,{placeholder:"请选择奖项分类",allowClear:!0,children:D.map(e=>r.jsx(T.Option,{value:e==null?void 0:e.enumId,children:e==null?void 0:e.enumName},e==null?void 0:e.enumId))})}),r.jsx(o.Item,{label:"组织名称",name:"rewardsOrgId",dependencies:["rewardsType"],rules:[{required:i.getFieldValue("rewardsType")==="org",message:"请选择组织名称!"}],children:r.jsx(Ne,{allowClear:!1,changeOnSelect:!0,expandTrigger:"hover",disabled:i.getFieldValue("rewardsType")==="staff",displayRender:e=>e[e.length-1],options:k||[],fieldNames:{value:"orgId",label:"orgName",children:"children"},placeholder:"请选择组织名称",showSearch:{filter:xe},onChange:(e,t)=>ce(t),onSearch:e=>console.log(e)})}),r.jsx(o.Item,{label:"组织金额",name:"realRewardsOrgAmount",dependencies:["rewardsType"],rules:[{required:i.getFieldValue("rewardsType")==="org",message:"请输入组织金额!"}],children:r.jsx(m,{disabled:i.getFieldValue("rewardsType")==="staff"})}),r.jsx(o.Item,{label:"员工编号",name:"rewardsStaffId",rules:[{required:i.getFieldValue("rewardsType")==="staff",message:"请输入员工编号!"}],children:r.jsx(m,{onChange:pe})}),r.jsx(o.Item,{label:"员工姓名",name:"rewardsStaffName",rules:[{required:i.getFieldValue("rewardsType")==="staff"||i.getFieldValue("rewardsStaffId"),message:"请输入员工姓名!"}],children:r.jsx(m,{disabled:!0})}),r.jsx(o.Item,{label:"员工金额",name:"realRewardsStaffAmount",rules:[{required:i.getFieldValue("rewardsType")==="staff",message:"请输入员工金额!"}],children:r.jsx(m,{})}),r.jsx(o.Item,{label:"备注",name:"remark",rules:[{required:!1,message:"请输入备注!"}],children:r.jsx(m,{})}),r.jsx(o.Item,{wrapperCol:{offset:10,span:12},children:r.jsx(x,{type:"primary",htmlType:"submit",loading:$e,children:"提交"})})]})})]})};export{ea as default};
