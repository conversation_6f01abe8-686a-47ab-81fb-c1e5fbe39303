import{F as c,r as p,u as V,j as d,B as C,y as O,z}from"./index-De_f0oL2.js";import{u as G}from"./debounce-D4mn-XUD.js";import{p as H}from"./service-CEuf3VDV.js";import{C as U}from"./index-DZyVV6rP.js";import{S as E}from"./index-BWJehDyc.js";import{D as J}from"./index-CdSZ9YgQ.js";import{F as K}from"./Table-D-iLeFE-.js";import{s as Q}from"./index-BcPP1N8I.js";import"./useMultipleSelect-B0dEIXT-.js";const W="_add_yohve_1",X={add:W},ea=u=>{const[j]=c.useForm(),e=p.useRef([]),[_,A]=p.useState([]),[$,R]=p.useState(u.columns),[N,b]=p.useState(!1),{runAsync:D}=V(H.getEmployeeByEmpId,{manual:!0});p.useEffect(()=>{F()},[]);const F=()=>{R([...k(u.columns),{title:"操作",dataIndex:"action",width:80,align:"center",fixed:"right",render:(l,f)=>d.jsx("div",{className:"action",children:d.jsx(C,{type:"link",onClick:()=>P(f),children:"删除"})})}])},B=async(l,f,a,g)=>{var y;const[r,n]=await D({empId:f});if(!r)if(n.STATUS==="0000"){const{DATA:t}=n,I=t==null?void 0:t.employeeName,s=t==null?void 0:t.orgaId,S=(h,w)=>{for(const o of h){if(o.orgId===w)return[o];if(o.children&&o.children.length>0){const v=S(o.children,w);if(v)return[o,...v]}}return null},T=S((u==null?void 0:u.cascaderOption)||[],s);e.current=(y=e.current)==null?void 0:y.map(h=>{const w={...h};return(h==null?void 0:h.key)===(l==null?void 0:l.key)&&(w.rewardsStaffName=I,T&&(w.rewardsOrgName=T)),w}),j.setFieldValue(g,I),T&&j.setFieldValue(`rewardsOrgName${l==null?void 0:l.key}`,T.map(h=>h.orgId)),console.log("修改：",e.current)}else Q.error(n==null?void 0:n.MESSAGE)},k=l=>{var f;return(f=l==null?void 0:l.filter(a=>(a==null?void 0:a.dataIndex)!=="action"))==null?void 0:f.map(a=>{var g;return((g=a==null?void 0:a.children)==null?void 0:g.length)>0?{...a,width:a!=null&&a.width&&a.width>100?a==null?void 0:a.width:100,children:k(a==null?void 0:a.children)}:{...a,width:a!=null&&a.width&&a.width>120?a==null?void 0:a.width:120,render:(r,n,y)=>{var t,I;return d.jsx(c.Item,{name:a.dataIndex+(n==null?void 0:n.key),rules:[{required:["rewardsOrgName","rewardsOrgAmount"].includes(a==null?void 0:a.dataIndex)&&(n==null?void 0:n.rewardsType)==="org"||["rewardsStaffId","rewardsStaffName","rewardsStaffAmount"].includes(a==null?void 0:a.dataIndex)&&(n==null?void 0:n.rewardsType)==="staff"||(a==null?void 0:a.dataIndex)==="rewardsStaffName"&&((I=(t=e.current)==null?void 0:t.find(s=>(s==null?void 0:s.key)===(n==null?void 0:n.key)))==null?void 0:I.rewardsStaffId),message:`请输入${a.title}`}],children:(a==null?void 0:a.actionType)==="cascader"?d.jsx(U,{allowClear:!0,changeOnSelect:!0,expandTrigger:"hover",disabled:["rewardsOrgName"].includes(a==null?void 0:a.dataIndex)&&(n==null?void 0:n.rewardsType)==="staff",displayRender:s=>s[s.length-1],options:(u==null?void 0:u.cascaderOption)||[],fieldNames:{value:"orgId",label:"orgName",children:"children"},placeholder:["rewardsOrgName"].includes(a==null?void 0:a.dataIndex)&&(n==null?void 0:n.rewardsType)==="staff"?"":`请选择${a==null?void 0:a.title}`,showSearch:{filter:q},onSearch:s=>console.log(s),value:Array.isArray(r)?r.map(s=>typeof s=="object"?s==null?void 0:s.orgId:s):void 0,onChange:(s,S)=>x(n,S,a)}):(a==null?void 0:a.actionType)==="select"?d.jsx(d.Fragment,{children:d.jsx(E,{placeholder:`请选择${a==null?void 0:a.title}`,allowClear:!0,value:Array.isArray(r)?void 0:r,onChange:s=>x(n,s,a),children:(u[a==null?void 0:a.actionOptionName]||[]).map(s=>d.jsx(E.Option,{value:s==null?void 0:s.enumId,children:s==null?void 0:s.enumName},s==null?void 0:s.enumId))})}):(a==null?void 0:a.actionType)==="datePicker"?d.jsx(J,{picker:"month",value:Array.isArray(r)?void 0:r,allowClear:!0,placeholder:`请选择${a.title}`,onChange:s=>x(n,s,a),style:{width:"100%"}}):(a==null?void 0:a.dataIndex)==="rewardsStaffId"?d.jsx(O,{placeholder:["rewardsOrgAmount"].includes(a==null?void 0:a.dataIndex)&&(n==null?void 0:n.rewardsType)==="staff"?"":`请输入${a.title}`,value:Array.isArray(r)?"":r,allowClear:!0,disabled:["rewardsOrgAmount"].includes(a==null?void 0:a.dataIndex)&&(n==null?void 0:n.rewardsType)==="staff",onChange:s=>x(n,s.target.value,a,`rewardsStaffName${n==null?void 0:n.key}`)}):d.jsx(O,{placeholder:["rewardsOrgAmount"].includes(a==null?void 0:a.dataIndex)&&(n==null?void 0:n.rewardsType)==="staff"||["rewardsStaffName"].includes(a==null?void 0:a.dataIndex)?"":`请输入${a.title}`,value:Array.isArray(r)?"":r,allowClear:!0,disabled:["rewardsOrgAmount"].includes(a==null?void 0:a.dataIndex)&&(n==null?void 0:n.rewardsType)==="staff"||["rewardsStaffName"].includes(a==null?void 0:a.dataIndex),onChange:s=>x(n,s.target.value,a)})},`${a.dataIndex}_${n==null?void 0:n.key}_${y}`)}}})},x=(l,f,a,g)=>{var r;e.current=(r=e.current)==null?void 0:r.map(n=>{const y={...n};return(n==null?void 0:n.key)===(l==null?void 0:l.key)&&(y[a==null?void 0:a.dataIndex]=f),y}),(a==null?void 0:a.dataIndex)==="rewardsType"?A(e.current):(a==null?void 0:a.dataIndex)==="rewardsStaffId"&&G(B(l,f,a,g),500),console.log("修改：",e.current)},P=l=>{var f;e.current=(f=e.current)==null?void 0:f.filter(a=>(a==null?void 0:a.key)!==(l==null?void 0:l.key)),A(e.current)},L=async()=>{b(!0);try{await(u==null?void 0:u.submitData(e.current))}finally{b(!1)}},M=()=>{e.current=[...e.current,{key:new Date().getTime()}],A(e.current)},q=(l,f)=>f.some(a=>a.orgName.toLowerCase().indexOf(l.trim().toLowerCase())>-1);return d.jsx("div",{className:X.add,children:d.jsxs(c,{name:"dynamic_form_nest_item",form:j,onFinish:L,style:{width:"100%",marginTop:16,maxHeight:"500px"},autoComplete:"off",children:[d.jsx(K,{style:{marginBottom:20},className:"edit-table",columns:$,dataSource:_,bordered:!0,scroll:{y:"20rem"},pagination:!1}),d.jsx(c.Item,{style:{width:"100%",display:"flex",justifyContent:"right"},children:d.jsx(C,{onClick:()=>M(),danger:!0,block:!0,icon:d.jsx(z,{}),style:{width:150},children:"新增一条数据"})}),d.jsx(c.Item,{style:{textAlign:"center",width:"100%",background:"#fff"},children:d.jsx(C,{type:"primary",htmlType:"submit",loading:N,disabled:N,style:{},children:"提交"})})]})})};export{ea as default};
