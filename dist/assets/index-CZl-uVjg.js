import{r as o,F as h,j as e,u as j,d as re,R as ce,C as b,S as de,B as S,T as r}from"./index-De_f0oL2.js";import{e as C}from"./service-DcPXuTuP.js";import{R as me,o as H,d as he}from"./down-BCLNnN1h.js";import{s as u}from"./Index.module-CQ0G-enU.js";import{R as ue}from"./index-BmnYJy3v.js";import{D as ge}from"./index-CdSZ9YgQ.js";import{C as fe}from"./index-DZyVV6rP.js";import{R as pe,a as xe}from"./FullscreenOutlined-DzCTibKW.js";import"./Table-D-iLeFE-.js";import"./index-BWJehDyc.js";import"./useMultipleSelect-B0dEIXT-.js";const Me=()=>{var A;const v=[{title:"单位",key:"orgName",dataIndex:"orgName",align:"center",fixed:"left",width:150,children:[],render:(t,n)=>e.jsx(r,{title:t,children:e.jsx("div",{className:u.over_ellipsis,children:t})})},{title:"定比上一年度末变动人数",key:"list1",dataIndex:"list1",align:"center",children:[{title:"单位变动人数-增加",key:"orgIncMomLydec",dataIndex:"orgIncMomLydec",align:"center",children:[],width:130},{title:"单位变动人数-减少",key:"orgDecMomLydec",dataIndex:"orgDecMomLydec",align:"center",children:[],width:130},{title:e.jsx(r,{title:"不含跨单位",children:e.jsx("span",{children:"岗位人数"})}),key:"postNum",dataIndex:"postNum",align:"center",children:[],width:75},{title:e.jsx(r,{title:"不含跨单位",children:e.jsx("span",{children:"岗位提升人数"})}),key:"postUpMomLydec",dataIndex:"postUpMomLydec",align:"center",children:[],width:95},{title:e.jsx(r,{title:"不含跨单位",children:e.jsx("span",{children:"岗位下降人数"})}),key:"postDownMomLydec",dataIndex:"postDownMomLydec",align:"center",children:[],width:95}]},{title:"环比上月人员变动情况",key:"list2",dataIndex:"list2",align:"center",children:[{title:"单位变动人数-增加",key:"orgIncMom",dataIndex:"orgIncMom",align:"center",children:[],width:130},{title:"单位变动人数-减少",key:"orgDecMom",dataIndex:"orgDecMom",align:"center",children:[],width:130},{title:e.jsx(r,{title:"不含跨单位",children:e.jsx("span",{children:"岗位人数"})}),key:"postNum",dataIndex:"postNum",align:"center",children:[],width:75},{title:e.jsx(r,{title:"不含跨单位",children:e.jsx("span",{children:"岗位提升人数"})}),key:"postUpMom",dataIndex:"postUpMom",align:"center",children:[],width:95},{title:e.jsx(r,{title:"不含跨单位",children:e.jsx("span",{children:"岗位下降人数"})}),key:"postDownMom",dataIndex:"postDownMom",align:"center",children:[],width:95}]}],g=o.useRef(null),N=o.useRef(null),R=o.useRef(null),[l]=h.useForm(),[V,T]=o.useState(0),[P,U]=o.useState([]),[d,q]=o.useState([]),[B,J]=o.useState(v),[c,p]=o.useState([]),[K,x]=o.useState(!1),[y,Q]=o.useState(0),[M,k]=o.useState(!0),[i,L]=o.useState({total:0,pageNum:1,pageSize:50}),{runAsync:W}=j(C.exportEmployeeChangeExcel,{manual:!0}),{runAsync:G}=j(C.getEmployeeChangePag,{manual:!0}),{runAsync:X}=j(C.build4LevelOrgTree2,{manual:!0});o.useEffect(()=>(x(!1),g.current&&T(g.current.offsetHeight),F(),f(),window.addEventListener("resize",D),()=>{window.removeEventListener("resize",D)}),[]);const D=()=>{m()};o.useEffect(()=>{y>0&&f()},[y]),o.useEffect(()=>{m()},[(document.querySelector(".statistics_table .ant-table-header")||{}).offsetHeight]),o.useEffect(()=>{(d==null?void 0:d.length)>0&&(J(v),m())},[d]),o.useEffect(()=>{(i==null?void 0:i.total)>0&&f()},[i.pageNum,i.pageSize]);const m=()=>{var a;const t=(document.querySelector(".statistics_table .ant-table-header")||{}).offsetHeight,n=(document.querySelector(".statistics_table .ant-table-pagination")||{}).offsetHeight||26;t&&n&&T(((a=N.current)==null?void 0:a.offsetHeight)-(R.current.offsetHeight+t+n))},E=async()=>{var a;const[t,n]=await X({monthId:(a=l.getFieldValue("monthId"))==null?void 0:a.format("YYYYMM"),tag:"1"});t||n.STATUS==="0000"&&(U(n.DATA),p([]),l.setFieldValue("orgId",""))},f=async t=>{var z,Y,_;x(!0);const n=i,a=l.getFieldsValue(),s=c?c[c.length-1]:{},[w,I]=await G({...n,...a,orgId:void 0,orgId4:Number(s==null?void 0:s.level)===4?s==null?void 0:s.orgId:"",orgId5:Number(s==null?void 0:s.level)===5?s==null?void 0:s.orgId:"",monthId:(Y=(z=l.getFieldsValue())==null?void 0:z.monthId)==null?void 0:Y.format("YYYYMM")});if(x(!1),!w&&I.STATUS==="0000"){const{DATA:{data:ae}}=I,le=ae.map(($,ie)=>({...$,key:$.id||ie}));q(le),L({...i,total:(_=I.DATA)==null?void 0:_.totalCount})}},F=()=>{const t=re();l.setFieldsValue({monthId:t,orgId:""}),E()},Z=t=>{t.monthId&&E()},O=()=>{f()},ee=async()=>{var t,n;try{H("正在导出",0,"loading");const a=l.getFieldsValue(),s=c?c[c.length-1]:{},w=await W({...a,orgId:void 0,orgId4:Number(s==null?void 0:s.level)===4?s==null?void 0:s.orgId:"",orgId5:Number(s==null?void 0:s.level)===5?s==null?void 0:s.orgId:"",monthId:(n=(t=l.getFieldsValue())==null?void 0:t.monthId)==null?void 0:n.format("YYYYMM")});he(w)}catch(a){H("导出失败",1,"error"),console.error("Download failed:",a)}},te=()=>{const t=y+1;l.resetFields(),p([]),F(),Q(t)},se=t=>{const n={total:t==null?void 0:t.total,pageNum:t==null?void 0:t.current,pageSize:t==null?void 0:t.pageSize};L(n)},ne=(t,n)=>n.some(a=>a.orgName.toLowerCase().indexOf(t.trim().toLowerCase())>-1),oe=(t,n)=>{p(n)};return e.jsxs("div",{className:`h-full pt-[0.5rem] flex flex-col ${u.employment_page}`,children:[e.jsx("div",{ref:g,className:"bg-white pt-[0.5rem] px-8 mb-[0.5rem]",children:e.jsx(h,{form:l,initialValues:{tag:""},onFinish:O,onValuesChange:Z,autoComplete:"off",children:e.jsxs(ce,{gutter:24,children:[e.jsx(b,{span:6,children:e.jsx(h.Item,{label:"月份",name:"monthId",wrapperCol:{span:20},children:e.jsx(ge,{className:"w-full",picker:"month"})})}),e.jsx(b,{span:6,children:e.jsx(h.Item,{label:"单位",name:"orgId",wrapperCol:{span:20},children:e.jsx(fe,{allowClear:!0,changeOnSelect:!0,expandTrigger:"hover",displayRender:t=>t[t.length-1],options:P,onChange:oe,fieldNames:{value:"orgId",label:"orgName",children:"children"},placeholder:"请选择",showSearch:{filter:ne},onSearch:t=>console.log(t)})})}),e.jsx(b,{span:6,children:e.jsx(h.Item,{labelCol:{span:0},wrapperCol:{span:24},children:e.jsxs(de,{size:"small",children:[e.jsx(S,{type:"primary",htmlType:"submit",children:"查询"}),e.jsx(S,{htmlType:"button",onClick:()=>te(),children:"重置"})]})})})]})})}),e.jsxs("div",{ref:N,className:"relative bg-white px-5 pt-2.5",style:{height:`calc(100% - ${((A=g.current)==null?void 0:A.offsetHeight)+15}px)`},children:[e.jsxs("div",{ref:R,className:`flex justify-between items-center mb-2 overflow-hidden ${u.animation_box} ${M?"h-[1.6rem]":"h-0"}`,children:[e.jsxs("div",{className:"flex ",children:[M?e.jsx(pe,{className:`${u.shousuo_icon} text-[1rem]`,onClick:()=>{k(!1),setTimeout(()=>{m()},200)}}):e.jsx(xe,{className:`${u.shousuo_icon} absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10`,onClick:()=>{k(!0),setTimeout(()=>{m()},200)}}),e.jsx("div",{className:"font-bold text-[0.8rem] ml-3",children:"数据列表"})]}),e.jsx(S,{danger:!0,ghost:!0,icon:e.jsx(me,{}),onClick:()=>ee(),children:"导出"})]}),e.jsx(ue,{className:"statistics_table",rowClassName:(t,n)=>n%2===1?"customRow odd":"customRow even",columns:B,dataSource:d,loading:K,bordered:!0,scroll:{y:`calc(${V}px - 0.625rem - 1.6rem - 0.5rem)`},onChange:se,pagination:{...i,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50"]}})]})]})};export{Me as default};
