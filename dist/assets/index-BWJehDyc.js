import{r,c as Le,V as Rt,a7 as yt,a5 as Z,K as z,a8 as W,a9 as pt,bl as nr,l as rr,ao as _t,a$ as or,al as ee,_ as Ye,aX as ar,af as lr,bm as ir,p as Ge,bn as cr,bo as ur,n as xt,w as Kt,x as On,aY as ut,an as sr,ak as dr,i as cn,ay as un,bp as fr,v as Mn,a6 as vr,t as mr,bq as Qt,s as Zt,ac as Lt,b as Dn,E as $t,k as Pn,q as Nn,b8 as sn,b9 as dn,G as Jt,bb as gr,ba as pr,bd as hr,bc as br,J as Ut,a as xe,Z as Tn,b7 as Sr,I as Bn,ad as Cr,a3 as wr,m as yr,U as Ir,bg as Er,L as Rr,aB as xr,$ as $r,M as Or,ax as Mr,a0 as Dr,bi as Pr,br as Nr,a1 as Tr,bk as Br}from"./index-De_f0oL2.js";var zt=function(t){var n=t.className,o=t.customizeIcon,a=t.customizeIconProps,l=t.children,i=t.onMouseDown,c=t.onClick,d=typeof o=="function"?o(a):o;return r.createElement("span",{className:n,onMouseDown:function(f){f.preventDefault(),i==null||i(f)},style:{userSelect:"none",WebkitUserSelect:"none"},unselectable:"on",onClick:c,"aria-hidden":!0},d!==void 0?d:r.createElement("span",{className:Le(n.split(/\s+/).map(function(s){return"".concat(s,"-icon")}))},l))},Hr=function(t,n,o,a,l){var i=arguments.length>5&&arguments[5]!==void 0?arguments[5]:!1,c=arguments.length>6?arguments[6]:void 0,d=arguments.length>7?arguments[7]:void 0,s=Rt.useMemo(function(){if(yt(a)==="object")return a.clearIcon;if(l)return l},[a,l]),f=Rt.useMemo(function(){return!!(!i&&a&&(o.length||c)&&!(d==="combobox"&&c===""))},[a,i,o.length,c,d]);return{allowClear:f,clearIcon:Rt.createElement(zt,{className:"".concat(t,"-clear"),onMouseDown:n,customizeIcon:s},"×")}},Hn=r.createContext(null);function Lr(){return r.useContext(Hn)}function _r(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:10,t=r.useState(!1),n=Z(t,2),o=n[0],a=n[1],l=r.useRef(null),i=function(){window.clearTimeout(l.current)};r.useEffect(function(){return i},[]);var c=function(s,f){i(),l.current=window.setTimeout(function(){a(s),f&&f()},e)};return[o,c,i]}function Ln(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:250,t=r.useRef(null),n=r.useRef(null);r.useEffect(function(){return function(){window.clearTimeout(n.current)}},[]);function o(a){(a||t.current===null)&&(t.current=a),window.clearTimeout(n.current),n.current=window.setTimeout(function(){t.current=null},e)}return[function(){return t.current},o]}function zr(e,t,n,o){var a=r.useRef(null);a.current={open:t,triggerOpen:n,customizedTrigger:o},r.useEffect(function(){function l(i){var c;if(!((c=a.current)!==null&&c!==void 0&&c.customizedTrigger)){var d=i.target;d.shadowRoot&&i.composed&&(d=i.composedPath()[0]||d),a.current.open&&e().filter(function(s){return s}).every(function(s){return!s.contains(d)&&s!==d})&&a.current.triggerOpen(!1)}}return window.addEventListener("mousedown",l),function(){return window.removeEventListener("mousedown",l)}},[])}function Vr(e){return e&&![z.ESC,z.SHIFT,z.BACKSPACE,z.TAB,z.WIN_KEY,z.ALT,z.META,z.WIN_KEY_RIGHT,z.CTRL,z.SEMICOLON,z.EQUALS,z.CAPS_LOCK,z.CONTEXT_MENU,z.F1,z.F2,z.F3,z.F4,z.F5,z.F6,z.F7,z.F8,z.F9,z.F10,z.F11,z.F12].includes(e)}function Fr(e,t,n){var o=W(W({},e),t);return Object.keys(t).forEach(function(a){var l=t[a];typeof l=="function"&&(o[a]=function(){for(var i,c=arguments.length,d=new Array(c),s=0;s<c;s++)d[s]=arguments[s];return l.apply(void 0,d),(i=e[a])===null||i===void 0?void 0:i.call.apply(i,[e].concat(d))})}),o}var Ar=["prefixCls","id","inputElement","autoFocus","autoComplete","editable","activeDescendantId","value","open","attrs"],jr=function(t,n){var o=t.prefixCls,a=t.id,l=t.inputElement,i=t.autoFocus,c=t.autoComplete,d=t.editable,s=t.activeDescendantId,f=t.value,v=t.open,h=t.attrs,u=pt(t,Ar),g=l||r.createElement("input",null),m=g,p=m.ref,S=m.props;return nr(!("maxLength"in g.props)),g=r.cloneElement(g,W(W(W({type:"search"},Fr(u,S)),{},{id:a,ref:rr(n,p),autoComplete:c||"off",autoFocus:i,className:Le("".concat(o,"-selection-search-input"),S==null?void 0:S.className),role:"combobox","aria-expanded":v||!1,"aria-haspopup":"listbox","aria-owns":"".concat(a,"_list"),"aria-autocomplete":"list","aria-controls":"".concat(a,"_list"),"aria-activedescendant":v?s:void 0},h),{},{value:d?f:"",readOnly:!d,unselectable:d?null:"on",style:W(W({},S.style),{},{opacity:d?null:0})})),g},_n=r.forwardRef(jr);function zn(e){return Array.isArray(e)?e:e!==void 0?[e]:[]}var Wr=typeof window<"u"&&window.document&&window.document.documentElement,Kr=Wr;function Ur(e){return e!=null}function Xr(e){return!e&&e!==0}function fn(e){return["string","number"].includes(yt(e))}function Vn(e){var t=void 0;return e&&(fn(e.title)?t=e.title.toString():fn(e.label)&&(t=e.label.toString())),t}function Gr(e,t){Kr?r.useLayoutEffect(e,t):r.useEffect(e,t)}function Yr(e){var t;return(t=e.key)!==null&&t!==void 0?t:e.value}var vn=function(t){t.preventDefault(),t.stopPropagation()},qr=function(t){var n=t.id,o=t.prefixCls,a=t.values,l=t.open,i=t.searchValue,c=t.autoClearSearchValue,d=t.inputRef,s=t.placeholder,f=t.disabled,v=t.mode,h=t.showSearch,u=t.autoFocus,g=t.autoComplete,m=t.activeDescendantId,p=t.tabIndex,S=t.removeIcon,b=t.maxTagCount,R=t.maxTagTextLength,w=t.maxTagPlaceholder,V=w===void 0?function(j){return"+ ".concat(j.length," ...")}:w,L=t.tagRender,P=t.onToggleOpen,K=t.onRemove,_=t.onInputChange,ce=t.onInputPaste,X=t.onInputKeyDown,Y=t.onInputMouseDown,oe=t.onInputCompositionStart,se=t.onInputCompositionEnd,ae=t.onInputBlur,q=r.useRef(null),de=r.useState(0),te=Z(de,2),le=te[0],H=te[1],D=r.useState(!1),Q=Z(D,2),J=Q[0],pe=Q[1],ue="".concat(o,"-selection"),Oe=l||v==="multiple"&&c===!1||v==="tags"?i:"",he=v==="tags"||v==="multiple"&&c===!1||h&&(l||J);Gr(function(){H(q.current.scrollWidth)},[Oe]);var $e=function(y,B,ne,F,k){return r.createElement("span",{title:Vn(y),className:Le("".concat(ue,"-item"),ee({},"".concat(ue,"-item-disabled"),ne))},r.createElement("span",{className:"".concat(ue,"-item-content")},B),F&&r.createElement(zt,{className:"".concat(ue,"-item-remove"),onMouseDown:vn,onClick:k,customizeIcon:S},"×"))},ie=function(y,B,ne,F,k,ve){var Be=function(De){vn(De),P(!l)};return r.createElement("span",{onMouseDown:Be},L({label:B,value:y,disabled:ne,closable:F,onClose:k,isMaxTag:!!ve}))},me=function(y){var B=y.disabled,ne=y.label,F=y.value,k=!f&&!B,ve=ne;if(typeof R=="number"&&(typeof ne=="string"||typeof ne=="number")){var Be=String(ve);Be.length>R&&(ve="".concat(Be.slice(0,R),"..."))}var Ee=function(ge){ge&&ge.stopPropagation(),K(y)};return typeof L=="function"?ie(F,ve,B,k,Ee):$e(y,ve,B,k,Ee)},O=function(y){if(!a.length)return null;var B=typeof V=="function"?V(y):V;return typeof L=="function"?ie(void 0,B,!1,!1,void 0,!0):$e({title:B},B,!1)},C=r.createElement("div",{className:"".concat(ue,"-search"),style:{width:le},onFocus:function(){pe(!0)},onBlur:function(){pe(!1)}},r.createElement(_n,{ref:d,open:l,prefixCls:o,id:n,inputElement:null,disabled:f,autoFocus:u,autoComplete:g,editable:he,activeDescendantId:m,value:Oe,onKeyDown:X,onMouseDown:Y,onChange:_,onPaste:ce,onCompositionStart:oe,onCompositionEnd:se,onBlur:ae,tabIndex:p,attrs:_t(t,!0)}),r.createElement("span",{ref:q,className:"".concat(ue,"-search-mirror"),"aria-hidden":!0},Oe," ")),x=r.createElement(or,{prefixCls:"".concat(ue,"-overflow"),data:a,renderItem:me,renderRest:O,suffix:C,itemKey:Yr,maxCount:b});return r.createElement("span",{className:"".concat(ue,"-wrap")},x,!a.length&&!Oe&&r.createElement("span",{className:"".concat(ue,"-placeholder")},s))},Qr=function(t){var n=t.inputElement,o=t.prefixCls,a=t.id,l=t.inputRef,i=t.disabled,c=t.autoFocus,d=t.autoComplete,s=t.activeDescendantId,f=t.mode,v=t.open,h=t.values,u=t.placeholder,g=t.tabIndex,m=t.showSearch,p=t.searchValue,S=t.activeValue,b=t.maxLength,R=t.onInputKeyDown,w=t.onInputMouseDown,V=t.onInputChange,L=t.onInputPaste,P=t.onInputCompositionStart,K=t.onInputCompositionEnd,_=t.onInputBlur,ce=t.title,X=r.useState(!1),Y=Z(X,2),oe=Y[0],se=Y[1],ae=f==="combobox",q=ae||m,de=h[0],te=p||"";ae&&S&&!oe&&(te=S),r.useEffect(function(){ae&&se(!1)},[ae,S]);var le=f!=="combobox"&&!v&&!m?!1:!!te,H=ce===void 0?Vn(de):ce,D=r.useMemo(function(){return de?null:r.createElement("span",{className:"".concat(o,"-selection-placeholder"),style:le?{visibility:"hidden"}:void 0},u)},[de,le,u,o]);return r.createElement("span",{className:"".concat(o,"-selection-wrap")},r.createElement("span",{className:"".concat(o,"-selection-search")},r.createElement(_n,{ref:l,prefixCls:o,id:a,open:v,inputElement:n,disabled:i,autoFocus:c,autoComplete:d,editable:q,activeDescendantId:s,value:te,onKeyDown:R,onMouseDown:w,onChange:function(J){se(!0),V(J)},onPaste:L,onCompositionStart:P,onCompositionEnd:K,onBlur:_,tabIndex:g,attrs:_t(t,!0),maxLength:ae?b:void 0})),!ae&&de?r.createElement("span",{className:"".concat(o,"-selection-item"),title:H,style:le?{visibility:"hidden"}:void 0},de.label):null,D)},Zr=function(t,n){var o=r.useRef(null),a=r.useRef(!1),l=t.prefixCls,i=t.open,c=t.mode,d=t.showSearch,s=t.tokenWithEnter,f=t.disabled,v=t.prefix,h=t.autoClearSearchValue,u=t.onSearch,g=t.onSearchSubmit,m=t.onToggleOpen,p=t.onInputKeyDown,S=t.onInputBlur,b=t.domRef;r.useImperativeHandle(n,function(){return{focus:function(H){o.current.focus(H)},blur:function(){o.current.blur()}}});var R=Ln(0),w=Z(R,2),V=w[0],L=w[1],P=function(H){var D=H.which,Q=o.current instanceof HTMLTextAreaElement;!Q&&i&&(D===z.UP||D===z.DOWN)&&H.preventDefault(),p&&p(H),D===z.ENTER&&c==="tags"&&!a.current&&!i&&(g==null||g(H.target.value)),!(Q&&!i&&~[z.UP,z.DOWN,z.LEFT,z.RIGHT].indexOf(D))&&Vr(D)&&m(!0)},K=function(){L(!0)},_=r.useRef(null),ce=function(H){u(H,!0,a.current)!==!1&&m(!0)},X=function(){a.current=!0},Y=function(H){a.current=!1,c!=="combobox"&&ce(H.target.value)},oe=function(H){var D=H.target.value;if(s&&_.current&&/[\r\n]/.test(_.current)){var Q=_.current.replace(/[\r\n]+$/,"").replace(/\r\n/g," ").replace(/[\r\n]/g," ");D=D.replace(Q,_.current)}_.current=null,ce(D)},se=function(H){var D=H.clipboardData,Q=D==null?void 0:D.getData("text");_.current=Q||""},ae=function(H){var D=H.target;if(D!==o.current){var Q=document.body.style.msTouchAction!==void 0;Q?setTimeout(function(){o.current.focus()}):o.current.focus()}},q=function(H){var D=V();H.target!==o.current&&!D&&!(c==="combobox"&&f)&&H.preventDefault(),(c!=="combobox"&&(!d||!D)||!i)&&(i&&h!==!1&&u("",!0,!1),m())},de={inputRef:o,onInputKeyDown:P,onInputMouseDown:K,onInputChange:oe,onInputPaste:se,onInputCompositionStart:X,onInputCompositionEnd:Y,onInputBlur:S},te=c==="multiple"||c==="tags"?r.createElement(qr,Ye({},t,de)):r.createElement(Qr,Ye({},t,de));return r.createElement("div",{ref:b,className:"".concat(l,"-selector"),onClick:ae,onMouseDown:q},v&&r.createElement("div",{className:"".concat(l,"-prefix")},v),te)},Jr=r.forwardRef(Zr),kr=["prefixCls","disabled","visible","children","popupElement","animation","transitionName","dropdownStyle","dropdownClassName","direction","placement","builtinPlacements","dropdownMatchSelectWidth","dropdownRender","dropdownAlign","getPopupContainer","empty","getTriggerDOMNode","onPopupVisibleChange","onPopupMouseEnter"],eo=function(t){var n=t===!0?0:1;return{bottomLeft:{points:["tl","bl"],offset:[0,4],overflow:{adjustX:n,adjustY:1},htmlRegion:"scroll"},bottomRight:{points:["tr","br"],offset:[0,4],overflow:{adjustX:n,adjustY:1},htmlRegion:"scroll"},topLeft:{points:["bl","tl"],offset:[0,-4],overflow:{adjustX:n,adjustY:1},htmlRegion:"scroll"},topRight:{points:["br","tr"],offset:[0,-4],overflow:{adjustX:n,adjustY:1},htmlRegion:"scroll"}}},to=function(t,n){var o=t.prefixCls;t.disabled;var a=t.visible,l=t.children,i=t.popupElement,c=t.animation,d=t.transitionName,s=t.dropdownStyle,f=t.dropdownClassName,v=t.direction,h=v===void 0?"ltr":v,u=t.placement,g=t.builtinPlacements,m=t.dropdownMatchSelectWidth,p=t.dropdownRender,S=t.dropdownAlign,b=t.getPopupContainer,R=t.empty,w=t.getTriggerDOMNode,V=t.onPopupVisibleChange,L=t.onPopupMouseEnter,P=pt(t,kr),K="".concat(o,"-dropdown"),_=i;p&&(_=p(i));var ce=r.useMemo(function(){return g||eo(m)},[g,m]),X=c?"".concat(K,"-").concat(c):d,Y=typeof m=="number",oe=r.useMemo(function(){return Y?null:m===!1?"minWidth":"width"},[m,Y]),se=s;Y&&(se=W(W({},se),{},{width:m}));var ae=r.useRef(null);return r.useImperativeHandle(n,function(){return{getPopupElement:function(){var de;return(de=ae.current)===null||de===void 0?void 0:de.popupElement}}}),r.createElement(ar,Ye({},P,{showAction:V?["click"]:[],hideAction:V?["click"]:[],popupPlacement:u||(h==="rtl"?"bottomRight":"bottomLeft"),builtinPlacements:ce,prefixCls:K,popupTransitionName:X,popup:r.createElement("div",{onMouseEnter:L},_),ref:ae,stretch:oe,popupAlign:S,popupVisible:a,getPopupContainer:b,popupClassName:Le(f,ee({},"".concat(K,"-empty"),R)),popupStyle:se,getTriggerDOMNode:w,onPopupVisibleChange:V}),l)},no=r.forwardRef(to);function mn(e,t){var n=e.key,o;return"value"in e&&(o=e.value),n??(o!==void 0?o:"rc-index-key-".concat(t))}function Xt(e){return typeof e<"u"&&!Number.isNaN(e)}function Fn(e,t){var n=e||{},o=n.label,a=n.value,l=n.options,i=n.groupLabel,c=o||(t?"children":"label");return{label:c,value:a||"value",options:l||"options",groupLabel:i||c}}function ro(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=t.fieldNames,o=t.childrenAsData,a=[],l=Fn(n,!1),i=l.label,c=l.value,d=l.options,s=l.groupLabel;function f(v,h){Array.isArray(v)&&v.forEach(function(u){if(h||!(d in u)){var g=u[c];a.push({key:mn(u,a.length),groupOption:h,data:u,label:u[i],value:g})}else{var m=u[s];m===void 0&&o&&(m=u.label),a.push({key:mn(u,a.length),group:!0,data:u,label:m}),f(u[d],!0)}})}return f(e,!1),a}function Gt(e){var t=W({},e);return"props"in t||Object.defineProperty(t,"props",{get:function(){return lr(!1,"Return type is option instead of Option instance. Please read value directly instead of reading from `props`."),t}}),t}var oo=function(t,n,o){if(!n||!n.length)return null;var a=!1,l=function c(d,s){var f=ir(s),v=f[0],h=f.slice(1);if(!v)return[d];var u=d.split(v);return a=a||u.length>1,u.reduce(function(g,m){return[].concat(Ge(g),Ge(c(m,h)))},[]).filter(Boolean)},i=l(t,n);return a?typeof o<"u"?i.slice(0,o):i:null},kt=r.createContext(null);function ao(e){var t=e.visible,n=e.values;if(!t)return null;var o=50;return r.createElement("span",{"aria-live":"polite",style:{width:0,height:0,position:"absolute",overflow:"hidden",opacity:0}},"".concat(n.slice(0,o).map(function(a){var l=a.label,i=a.value;return["number","string"].includes(yt(l))?l:i}).join(", ")),n.length>o?", ...":null)}var lo=["id","prefixCls","className","showSearch","tagRender","direction","omitDomProps","displayValues","onDisplayValuesChange","emptyOptions","notFoundContent","onClear","mode","disabled","loading","getInputElement","getRawInputElement","open","defaultOpen","onDropdownVisibleChange","activeValue","onActiveValueChange","activeDescendantId","searchValue","autoClearSearchValue","onSearch","onSearchSplit","tokenSeparators","allowClear","prefix","suffixIcon","clearIcon","OptionList","animation","transitionName","dropdownStyle","dropdownClassName","dropdownMatchSelectWidth","dropdownRender","dropdownAlign","placement","builtinPlacements","getPopupContainer","showAction","onFocus","onBlur","onKeyUp","onKeyDown","onMouseDown"],io=["value","onChange","removeIcon","placeholder","autoFocus","maxTagCount","maxTagTextLength","maxTagPlaceholder","choiceTransitionName","onInputKeyDown","onPopupScroll","tabIndex"],Yt=function(t){return t==="tags"||t==="multiple"},co=r.forwardRef(function(e,t){var n,o=e.id,a=e.prefixCls,l=e.className,i=e.showSearch,c=e.tagRender,d=e.direction,s=e.omitDomProps,f=e.displayValues,v=e.onDisplayValuesChange,h=e.emptyOptions,u=e.notFoundContent,g=u===void 0?"Not Found":u,m=e.onClear,p=e.mode,S=e.disabled,b=e.loading,R=e.getInputElement,w=e.getRawInputElement,V=e.open,L=e.defaultOpen,P=e.onDropdownVisibleChange,K=e.activeValue,_=e.onActiveValueChange,ce=e.activeDescendantId,X=e.searchValue,Y=e.autoClearSearchValue,oe=e.onSearch,se=e.onSearchSplit,ae=e.tokenSeparators,q=e.allowClear,de=e.prefix,te=e.suffixIcon,le=e.clearIcon,H=e.OptionList,D=e.animation,Q=e.transitionName,J=e.dropdownStyle,pe=e.dropdownClassName,ue=e.dropdownMatchSelectWidth,Oe=e.dropdownRender,he=e.dropdownAlign,$e=e.placement,ie=e.builtinPlacements,me=e.getPopupContainer,O=e.showAction,C=O===void 0?[]:O,x=e.onFocus,j=e.onBlur,y=e.onKeyUp,B=e.onKeyDown,ne=e.onMouseDown,F=pt(e,lo),k=Yt(p),ve=(i!==void 0?i:k)||p==="combobox",Be=W({},F);io.forEach(function(re){delete Be[re]}),s==null||s.forEach(function(re){delete Be[re]});var Ee=r.useState(!1),De=Z(Ee,2),ge=De[0],Fe=De[1];r.useEffect(function(){Fe(cr())},[]);var Xe=r.useRef(null),be=r.useRef(null),Re=r.useRef(null),Se=r.useRef(null),we=r.useRef(null),Pe=r.useRef(!1),rt=_r(),_e=Z(rt,3),Ae=_e[0],Ie=_e[1],ht=_e[2];r.useImperativeHandle(t,function(){var re,A;return{focus:(re=Se.current)===null||re===void 0?void 0:re.focus,blur:(A=Se.current)===null||A===void 0?void 0:A.blur,scrollTo:function(Ue){var Te;return(Te=we.current)===null||Te===void 0?void 0:Te.scrollTo(Ue)},nativeElement:Xe.current||be.current}});var ze=r.useMemo(function(){var re;if(p!=="combobox")return X;var A=(re=f[0])===null||re===void 0?void 0:re.value;return typeof A=="string"||typeof A=="number"?String(A):""},[X,p,f]),st=p==="combobox"&&typeof R=="function"&&R()||null,He=typeof w=="function"&&w(),bt=ur(be,He==null||(n=He.props)===null||n===void 0?void 0:n.ref),Je=r.useState(!1),ot=Z(Je,2),dt=ot[0],mt=ot[1];xt(function(){mt(!0)},[]);var ke=Kt(!1,{defaultValue:L,value:V}),je=Z(ke,2),at=je[0],lt=je[1],ye=dt?at:!1,it=!g&&h;(S||it&&ye&&p==="combobox")&&(ye=!1);var qe=it?!1:ye,I=r.useCallback(function(re){var A=re!==void 0?re:!ye;S||(lt(A),ye!==A&&(P==null||P(A)))},[S,ye,lt,P]),T=r.useMemo(function(){return(ae||[]).some(function(re){return[`
`,`\r
`].includes(re)})},[ae]),N=r.useContext(kt)||{},$=N.maxCount,G=N.rawValues,Ce=function(A,Ke,Ue){if(!(k&&Xt($)&&(G==null?void 0:G.size)>=$)){var Te=!0,Ve=A;_==null||_(null);var vt=oo(A,ae,Xt($)?$-G.size:void 0),ct=Ue?null:vt;return p!=="combobox"&&ct&&(Ve="",se==null||se(ct),I(!1),Te=!1),oe&&ze!==Ve&&oe(Ve,{source:Ke?"typing":"effect"}),Te}},Qe=function(A){!A||!A.trim()||oe(A,{source:"submit"})};r.useEffect(function(){!ye&&!k&&p!=="combobox"&&Ce("",!1,!1)},[ye]),r.useEffect(function(){at&&S&&lt(!1),S&&!Pe.current&&Ie(!1)},[S]);var We=Ln(),et=Z(We,2),Me=et[0],tt=et[1],gt=r.useRef(!1),Ot=function(A){var Ke=Me(),Ue=A.key,Te=Ue==="Enter";if(Te&&(p!=="combobox"&&A.preventDefault(),ye||I(!0)),tt(!!ze),Ue==="Backspace"&&!Ke&&k&&!ze&&f.length){for(var Ve=Ge(f),vt=null,ct=Ve.length-1;ct>=0;ct-=1){var St=Ve[ct];if(!St.disabled){Ve.splice(ct,1),vt=St;break}}vt&&v(Ve,{type:"remove",values:[vt]})}for(var Et=arguments.length,Ct=new Array(Et>1?Et-1:0),Bt=1;Bt<Et;Bt++)Ct[Bt-1]=arguments[Bt];if(ye&&(!Te||!gt.current)){var Ht;Te&&(gt.current=!0),(Ht=we.current)===null||Ht===void 0||Ht.onKeyDown.apply(Ht,[A].concat(Ct))}B==null||B.apply(void 0,[A].concat(Ct))},Tt=function(A){for(var Ke=arguments.length,Ue=new Array(Ke>1?Ke-1:0),Te=1;Te<Ke;Te++)Ue[Te-1]=arguments[Te];if(ye){var Ve;(Ve=we.current)===null||Ve===void 0||Ve.onKeyUp.apply(Ve,[A].concat(Ue))}A.key==="Enter"&&(gt.current=!1),y==null||y.apply(void 0,[A].concat(Ue))},nt=function(A){var Ke=f.filter(function(Ue){return Ue!==A});v(Ke,{type:"remove",values:[A]})},Ze=function(){gt.current=!1},M=r.useRef(!1),E=function(){Ie(!0),S||(x&&!M.current&&x.apply(void 0,arguments),C.includes("focus")&&I(!0)),M.current=!0},U=function(){Pe.current=!0,Ie(!1,function(){M.current=!1,Pe.current=!1,I(!1)}),!S&&(ze&&(p==="tags"?oe(ze,{source:"submit"}):p==="multiple"&&oe("",{source:"blur"})),j&&j.apply(void 0,arguments))},fe=[];r.useEffect(function(){return function(){fe.forEach(function(re){return clearTimeout(re)}),fe.splice(0,fe.length)}},[]);var Ne=function(A){var Ke,Ue=A.target,Te=(Ke=Re.current)===null||Ke===void 0?void 0:Ke.getPopupElement();if(Te&&Te.contains(Ue)){var Ve=setTimeout(function(){var Et=fe.indexOf(Ve);if(Et!==-1&&fe.splice(Et,1),ht(),!ge&&!Te.contains(document.activeElement)){var Ct;(Ct=Se.current)===null||Ct===void 0||Ct.focus()}});fe.push(Ve)}for(var vt=arguments.length,ct=new Array(vt>1?vt-1:0),St=1;St<vt;St++)ct[St-1]=arguments[St];ne==null||ne.apply(void 0,[A].concat(ct))},It=r.useState({}),ft=Z(It,2),Mt=ft[1];function Vt(){Mt({})}var Dt;He&&(Dt=function(A){I(A)}),zr(function(){var re;return[Xe.current,(re=Re.current)===null||re===void 0?void 0:re.getPopupElement()]},qe,I,!!He);var Pt=r.useMemo(function(){return W(W({},e),{},{notFoundContent:g,open:ye,triggerOpen:qe,id:o,showSearch:ve,multiple:k,toggleOpen:I})},[e,g,qe,ye,o,ve,k,I]),rn=!!te||b,on;rn&&(on=r.createElement(zt,{className:Le("".concat(a,"-arrow"),ee({},"".concat(a,"-arrow-loading"),b)),customizeIcon:te,customizeIconProps:{loading:b,searchValue:ze,open:ye,focused:Ae,showSearch:ve}}));var Zn=function(){var A;m==null||m(),(A=Se.current)===null||A===void 0||A.focus(),v([],{type:"clear",values:f}),Ce("",!1,!1)},an=Hr(a,Zn,f,q,le,S,ze,p),Jn=an.allowClear,kn=an.clearIcon,er=r.createElement(H,{ref:we}),tr=Le(a,l,ee(ee(ee(ee(ee(ee(ee(ee(ee(ee({},"".concat(a,"-focused"),Ae),"".concat(a,"-multiple"),k),"".concat(a,"-single"),!k),"".concat(a,"-allow-clear"),q),"".concat(a,"-show-arrow"),rn),"".concat(a,"-disabled"),S),"".concat(a,"-loading"),b),"".concat(a,"-open"),ye),"".concat(a,"-customize-input"),st),"".concat(a,"-show-search"),ve)),ln=r.createElement(no,{ref:Re,disabled:S,prefixCls:a,visible:qe,popupElement:er,animation:D,transitionName:Q,dropdownStyle:J,dropdownClassName:pe,direction:d,dropdownMatchSelectWidth:ue,dropdownRender:Oe,dropdownAlign:he,placement:$e,builtinPlacements:ie,getPopupContainer:me,empty:h,getTriggerDOMNode:function(A){return be.current||A},onPopupVisibleChange:Dt,onPopupMouseEnter:Vt},He?r.cloneElement(He,{ref:bt}):r.createElement(Jr,Ye({},e,{domRef:be,prefixCls:a,inputElement:st,ref:Se,id:o,prefix:de,showSearch:ve,autoClearSearchValue:Y,mode:p,activeDescendantId:ce,tagRender:c,values:f,open:ye,onToggleOpen:I,activeValue:K,searchValue:ze,onSearch:Ce,onSearchSubmit:Qe,onRemove:nt,tokenWithEnter:T,onInputBlur:Ze}))),Ft;return He?Ft=ln:Ft=r.createElement("div",Ye({className:tr},Be,{ref:Xe,onMouseDown:Ne,onKeyDown:Ot,onKeyUp:Tt,onFocus:E,onBlur:U}),r.createElement(ao,{visible:Ae&&!ye,values:f}),ln,on,Jn&&kn),r.createElement(Hn.Provider,{value:Pt},Ft)}),en=function(){return null};en.isSelectOptGroup=!0;var tn=function(){return null};tn.isSelectOption=!0;var An=r.forwardRef(function(e,t){var n=e.height,o=e.offsetY,a=e.offsetX,l=e.children,i=e.prefixCls,c=e.onInnerResize,d=e.innerProps,s=e.rtl,f=e.extra,v={},h={display:"flex",flexDirection:"column"};return o!==void 0&&(v={height:n,position:"relative",overflow:"hidden"},h=W(W({},h),{},ee(ee(ee(ee(ee({transform:"translateY(".concat(o,"px)")},s?"marginRight":"marginLeft",-a),"position","absolute"),"left",0),"right",0),"top",0))),r.createElement("div",{style:v},r.createElement(On,{onResize:function(g){var m=g.offsetHeight;m&&c&&c()}},r.createElement("div",Ye({style:h,className:Le(ee({},"".concat(i,"-holder-inner"),i)),ref:t},d),l,f)))});An.displayName="Filler";function uo(e){var t=e.children,n=e.setRef,o=r.useCallback(function(a){n(a)},[]);return r.cloneElement(t,{ref:o})}function so(e,t,n,o,a,l,i,c){var d=c.getKey;return e.slice(t,n+1).map(function(s,f){var v=t+f,h=i(s,v,{style:{width:o},offsetX:a}),u=d(s);return r.createElement(uo,{key:u,setRef:function(m){return l(s,m)}},h)})}function fo(e,t,n){var o=e.length,a=t.length,l,i;if(o===0&&a===0)return null;o<a?(l=e,i=t):(l=t,i=e);var c={__EMPTY_ITEM__:!0};function d(g){return g!==void 0?n(g):c}for(var s=null,f=Math.abs(o-a)!==1,v=0;v<i.length;v+=1){var h=d(l[v]),u=d(i[v]);if(h!==u){s=v,f=f||h!==d(i[v+1]);break}}return s===null?null:{index:s,multiple:f}}function vo(e,t,n){var o=r.useState(e),a=Z(o,2),l=a[0],i=a[1],c=r.useState(null),d=Z(c,2),s=d[0],f=d[1];return r.useEffect(function(){var v=fo(l||[],e||[],t);(v==null?void 0:v.index)!==void 0&&f(e[v.index]),i(e)},[e]),[s]}var gn=(typeof navigator>"u"?"undefined":yt(navigator))==="object"&&/Firefox/i.test(navigator.userAgent);const jn=function(e,t,n,o){var a=r.useRef(!1),l=r.useRef(null);function i(){clearTimeout(l.current),a.current=!0,l.current=setTimeout(function(){a.current=!1},50)}var c=r.useRef({top:e,bottom:t,left:n,right:o});return c.current.top=e,c.current.bottom=t,c.current.left=n,c.current.right=o,function(d,s){var f=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,v=d?s<0&&c.current.left||s>0&&c.current.right:s<0&&c.current.top||s>0&&c.current.bottom;return f&&v?(clearTimeout(l.current),a.current=!1):(!v||a.current)&&i(),!a.current&&v}};function mo(e,t,n,o,a,l,i){var c=r.useRef(0),d=r.useRef(null),s=r.useRef(null),f=r.useRef(!1),v=jn(t,n,o,a);function h(b,R){if(ut.cancel(d.current),!v(!1,R)){var w=b;if(!w._virtualHandled)w._virtualHandled=!0;else return;c.current+=R,s.current=R,gn||w.preventDefault(),d.current=ut(function(){var V=f.current?10:1;i(c.current*V,!1),c.current=0})}}function u(b,R){i(R,!0),gn||b.preventDefault()}var g=r.useRef(null),m=r.useRef(null);function p(b){if(e){ut.cancel(m.current),m.current=ut(function(){g.current=null},2);var R=b.deltaX,w=b.deltaY,V=b.shiftKey,L=R,P=w;(g.current==="sx"||!g.current&&V&&w&&!R)&&(L=w,P=0,g.current="sx");var K=Math.abs(L),_=Math.abs(P);g.current===null&&(g.current=l&&K>_?"x":"y"),g.current==="y"?h(b,P):u(b,L)}}function S(b){e&&(f.current=b.detail===s.current)}return[p,S]}function go(e,t,n,o){var a=r.useMemo(function(){return[new Map,[]]},[e,n.id,o]),l=Z(a,2),i=l[0],c=l[1],d=function(f){var v=arguments.length>1&&arguments[1]!==void 0?arguments[1]:f,h=i.get(f),u=i.get(v);if(h===void 0||u===void 0)for(var g=e.length,m=c.length;m<g;m+=1){var p,S=e[m],b=t(S);i.set(b,m);var R=(p=n.get(b))!==null&&p!==void 0?p:o;if(c[m]=(c[m-1]||0)+R,b===f&&(h=m),b===v&&(u=m),h!==void 0&&u!==void 0)break}return{top:c[h-1]||0,bottom:c[u]}};return d}var po=function(){function e(){dr(this,e),ee(this,"maps",void 0),ee(this,"id",0),ee(this,"diffRecords",new Map),this.maps=Object.create(null)}return sr(e,[{key:"set",value:function(n,o){this.diffRecords.set(n,this.maps[n]),this.maps[n]=o,this.id+=1}},{key:"get",value:function(n){return this.maps[n]}},{key:"resetRecord",value:function(){this.diffRecords.clear()}},{key:"getRecord",value:function(){return this.diffRecords}}]),e}();function pn(e){var t=parseFloat(e);return isNaN(t)?0:t}function ho(e,t,n){var o=r.useState(0),a=Z(o,2),l=a[0],i=a[1],c=r.useRef(new Map),d=r.useRef(new po),s=r.useRef(0);function f(){s.current+=1}function v(){var u=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;f();var g=function(){var S=!1;c.current.forEach(function(b,R){if(b&&b.offsetParent){var w=b.offsetHeight,V=getComputedStyle(b),L=V.marginTop,P=V.marginBottom,K=pn(L),_=pn(P),ce=w+K+_;d.current.get(R)!==ce&&(d.current.set(R,ce),S=!0)}}),S&&i(function(b){return b+1})};if(u)g();else{s.current+=1;var m=s.current;Promise.resolve().then(function(){m===s.current&&g()})}}function h(u,g){var m=e(u);c.current.get(m),g?(c.current.set(m,g),v()):c.current.delete(m)}return r.useEffect(function(){return f},[]),[h,v,d.current,l]}var hn=14/15;function bo(e,t,n){var o=r.useRef(!1),a=r.useRef(0),l=r.useRef(0),i=r.useRef(null),c=r.useRef(null),d,s=function(u){if(o.current){var g=Math.ceil(u.touches[0].pageX),m=Math.ceil(u.touches[0].pageY),p=a.current-g,S=l.current-m,b=Math.abs(p)>Math.abs(S);b?a.current=g:l.current=m;var R=n(b,b?p:S,!1,u);R&&u.preventDefault(),clearInterval(c.current),R&&(c.current=setInterval(function(){b?p*=hn:S*=hn;var w=Math.floor(b?p:S);(!n(b,w,!0)||Math.abs(w)<=.1)&&clearInterval(c.current)},16))}},f=function(){o.current=!1,d()},v=function(u){d(),u.touches.length===1&&!o.current&&(o.current=!0,a.current=Math.ceil(u.touches[0].pageX),l.current=Math.ceil(u.touches[0].pageY),i.current=u.target,i.current.addEventListener("touchmove",s,{passive:!1}),i.current.addEventListener("touchend",f,{passive:!0}))};d=function(){i.current&&(i.current.removeEventListener("touchmove",s),i.current.removeEventListener("touchend",f))},xt(function(){return e&&t.current.addEventListener("touchstart",v,{passive:!0}),function(){var h;(h=t.current)===null||h===void 0||h.removeEventListener("touchstart",v),d(),clearInterval(c.current)}},[e])}function bn(e){return Math.floor(Math.pow(e,.5))}function qt(e,t){var n="touches"in e?e.touches[0]:e;return n[t?"pageX":"pageY"]-window[t?"scrollX":"scrollY"]}function So(e,t,n){r.useEffect(function(){var o=t.current;if(e&&o){var a=!1,l,i,c=function(){ut.cancel(l)},d=function h(){c(),l=ut(function(){n(i),h()})},s=function(u){if(!(u.target.draggable||u.button!==0)){var g=u;g._virtualHandled||(g._virtualHandled=!0,a=!0)}},f=function(){a=!1,c()},v=function(u){if(a){var g=qt(u,!1),m=o.getBoundingClientRect(),p=m.top,S=m.bottom;if(g<=p){var b=p-g;i=-bn(b),d()}else if(g>=S){var R=g-S;i=bn(R),d()}else c()}};return o.addEventListener("mousedown",s),o.ownerDocument.addEventListener("mouseup",f),o.ownerDocument.addEventListener("mousemove",v),function(){o.removeEventListener("mousedown",s),o.ownerDocument.removeEventListener("mouseup",f),o.ownerDocument.removeEventListener("mousemove",v),c()}}},[e])}var Co=10;function wo(e,t,n,o,a,l,i,c){var d=r.useRef(),s=r.useState(null),f=Z(s,2),v=f[0],h=f[1];return xt(function(){if(v&&v.times<Co){if(!e.current){h(function(le){return W({},le)});return}l();var u=v.targetAlign,g=v.originAlign,m=v.index,p=v.offset,S=e.current.clientHeight,b=!1,R=u,w=null;if(S){for(var V=u||g,L=0,P=0,K=0,_=Math.min(t.length-1,m),ce=0;ce<=_;ce+=1){var X=a(t[ce]);P=L;var Y=n.get(X);K=P+(Y===void 0?o:Y),L=K}for(var oe=V==="top"?p:S-p,se=_;se>=0;se-=1){var ae=a(t[se]),q=n.get(ae);if(q===void 0){b=!0;break}if(oe-=q,oe<=0)break}switch(V){case"top":w=P-p;break;case"bottom":w=K-S+p;break;default:{var de=e.current.scrollTop,te=de+S;P<de?R="top":K>te&&(R="bottom")}}w!==null&&i(w),w!==v.lastTop&&(b=!0)}b&&h(W(W({},v),{},{times:v.times+1,targetAlign:R,lastTop:w}))}},[v,e.current]),function(u){if(u==null){c();return}if(ut.cancel(d.current),typeof u=="number")i(u);else if(u&&yt(u)==="object"){var g,m=u.align;"index"in u?g=u.index:g=t.findIndex(function(b){return a(b)===u.key});var p=u.offset,S=p===void 0?0:p;h({times:0,index:g,offset:S,originAlign:m})}}}var Sn=r.forwardRef(function(e,t){var n=e.prefixCls,o=e.rtl,a=e.scrollOffset,l=e.scrollRange,i=e.onStartMove,c=e.onStopMove,d=e.onScroll,s=e.horizontal,f=e.spinSize,v=e.containerSize,h=e.style,u=e.thumbStyle,g=e.showScrollBar,m=r.useState(!1),p=Z(m,2),S=p[0],b=p[1],R=r.useState(null),w=Z(R,2),V=w[0],L=w[1],P=r.useState(null),K=Z(P,2),_=K[0],ce=K[1],X=!o,Y=r.useRef(),oe=r.useRef(),se=r.useState(g),ae=Z(se,2),q=ae[0],de=ae[1],te=r.useRef(),le=function(){g===!0||g===!1||(clearTimeout(te.current),de(!0),te.current=setTimeout(function(){de(!1)},3e3))},H=l-v||0,D=v-f||0,Q=r.useMemo(function(){if(a===0||H===0)return 0;var O=a/H;return O*D},[a,H,D]),J=function(C){C.stopPropagation(),C.preventDefault()},pe=r.useRef({top:Q,dragging:S,pageY:V,startTop:_});pe.current={top:Q,dragging:S,pageY:V,startTop:_};var ue=function(C){b(!0),L(qt(C,s)),ce(pe.current.top),i(),C.stopPropagation(),C.preventDefault()};r.useEffect(function(){var O=function(y){y.preventDefault()},C=Y.current,x=oe.current;return C.addEventListener("touchstart",O,{passive:!1}),x.addEventListener("touchstart",ue,{passive:!1}),function(){C.removeEventListener("touchstart",O),x.removeEventListener("touchstart",ue)}},[]);var Oe=r.useRef();Oe.current=H;var he=r.useRef();he.current=D,r.useEffect(function(){if(S){var O,C=function(y){var B=pe.current,ne=B.dragging,F=B.pageY,k=B.startTop;ut.cancel(O);var ve=Y.current.getBoundingClientRect(),Be=v/(s?ve.width:ve.height);if(ne){var Ee=(qt(y,s)-F)*Be,De=k;!X&&s?De-=Ee:De+=Ee;var ge=Oe.current,Fe=he.current,Xe=Fe?De/Fe:0,be=Math.ceil(Xe*ge);be=Math.max(be,0),be=Math.min(be,ge),O=ut(function(){d(be,s)})}},x=function(){b(!1),c()};return window.addEventListener("mousemove",C,{passive:!0}),window.addEventListener("touchmove",C,{passive:!0}),window.addEventListener("mouseup",x,{passive:!0}),window.addEventListener("touchend",x,{passive:!0}),function(){window.removeEventListener("mousemove",C),window.removeEventListener("touchmove",C),window.removeEventListener("mouseup",x),window.removeEventListener("touchend",x),ut.cancel(O)}}},[S]),r.useEffect(function(){return le(),function(){clearTimeout(te.current)}},[a]),r.useImperativeHandle(t,function(){return{delayHidden:le}});var $e="".concat(n,"-scrollbar"),ie={position:"absolute",visibility:q?null:"hidden"},me={position:"absolute",background:"rgba(0, 0, 0, 0.5)",borderRadius:99,cursor:"pointer",userSelect:"none"};return s?(ie.height=8,ie.left=0,ie.right=0,ie.bottom=0,me.height="100%",me.width=f,X?me.left=Q:me.right=Q):(ie.width=8,ie.top=0,ie.bottom=0,X?ie.right=0:ie.left=0,me.width="100%",me.height=f,me.top=Q),r.createElement("div",{ref:Y,className:Le($e,ee(ee(ee({},"".concat($e,"-horizontal"),s),"".concat($e,"-vertical"),!s),"".concat($e,"-visible"),q)),style:W(W({},ie),h),onMouseDown:J,onMouseMove:le},r.createElement("div",{ref:oe,className:Le("".concat($e,"-thumb"),ee({},"".concat($e,"-thumb-moving"),S)),style:W(W({},me),u),onMouseDown:ue}))}),yo=20;function Cn(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,n=e/t*e;return isNaN(n)&&(n=0),n=Math.max(n,yo),Math.floor(n)}var Io=["prefixCls","className","height","itemHeight","fullHeight","style","data","children","itemKey","virtual","direction","scrollWidth","component","onScroll","onVirtualScroll","onVisibleChange","innerProps","extraRender","styles","showScrollBar"],Eo=[],Ro={overflowY:"auto",overflowAnchor:"none"};function xo(e,t){var n=e.prefixCls,o=n===void 0?"rc-virtual-list":n,a=e.className,l=e.height,i=e.itemHeight,c=e.fullHeight,d=c===void 0?!0:c,s=e.style,f=e.data,v=e.children,h=e.itemKey,u=e.virtual,g=e.direction,m=e.scrollWidth,p=e.component,S=p===void 0?"div":p,b=e.onScroll,R=e.onVirtualScroll,w=e.onVisibleChange,V=e.innerProps,L=e.extraRender,P=e.styles,K=e.showScrollBar,_=K===void 0?"optional":K,ce=pt(e,Io),X=r.useCallback(function(M){return typeof h=="function"?h(M):M==null?void 0:M[h]},[h]),Y=ho(X),oe=Z(Y,4),se=oe[0],ae=oe[1],q=oe[2],de=oe[3],te=!!(u!==!1&&l&&i),le=r.useMemo(function(){return Object.values(q.maps).reduce(function(M,E){return M+E},0)},[q.id,q.maps]),H=te&&f&&(Math.max(i*f.length,le)>l||!!m),D=g==="rtl",Q=Le(o,ee({},"".concat(o,"-rtl"),D),a),J=f||Eo,pe=r.useRef(),ue=r.useRef(),Oe=r.useRef(),he=r.useState(0),$e=Z(he,2),ie=$e[0],me=$e[1],O=r.useState(0),C=Z(O,2),x=C[0],j=C[1],y=r.useState(!1),B=Z(y,2),ne=B[0],F=B[1],k=function(){F(!0)},ve=function(){F(!1)},Be={getKey:X};function Ee(M){me(function(E){var U;typeof M=="function"?U=M(E):U=M;var fe=mt(U);return pe.current.scrollTop=fe,fe})}var De=r.useRef({start:0,end:J.length}),ge=r.useRef(),Fe=vo(J,X),Xe=Z(Fe,1),be=Xe[0];ge.current=be;var Re=r.useMemo(function(){if(!te)return{scrollHeight:void 0,start:0,end:J.length-1,offset:void 0};if(!H){var M;return{scrollHeight:((M=ue.current)===null||M===void 0?void 0:M.offsetHeight)||0,start:0,end:J.length-1,offset:void 0}}for(var E=0,U,fe,Ne,It=J.length,ft=0;ft<It;ft+=1){var Mt=J[ft],Vt=X(Mt),Dt=q.get(Vt),Pt=E+(Dt===void 0?i:Dt);Pt>=ie&&U===void 0&&(U=ft,fe=E),Pt>ie+l&&Ne===void 0&&(Ne=ft),E=Pt}return U===void 0&&(U=0,fe=0,Ne=Math.ceil(l/i)),Ne===void 0&&(Ne=J.length-1),Ne=Math.min(Ne+1,J.length-1),{scrollHeight:E,start:U,end:Ne,offset:fe}},[H,te,ie,J,de,l]),Se=Re.scrollHeight,we=Re.start,Pe=Re.end,rt=Re.offset;De.current.start=we,De.current.end=Pe,r.useLayoutEffect(function(){var M=q.getRecord();if(M.size===1){var E=Array.from(M.keys())[0],U=M.get(E),fe=J[we];if(fe&&U===void 0){var Ne=X(fe);if(Ne===E){var It=q.get(E),ft=It-i;Ee(function(Mt){return Mt+ft})}}}q.resetRecord()},[Se]);var _e=r.useState({width:0,height:l}),Ae=Z(_e,2),Ie=Ae[0],ht=Ae[1],ze=function(E){ht({width:E.offsetWidth,height:E.offsetHeight})},st=r.useRef(),He=r.useRef(),bt=r.useMemo(function(){return Cn(Ie.width,m)},[Ie.width,m]),Je=r.useMemo(function(){return Cn(Ie.height,Se)},[Ie.height,Se]),ot=Se-l,dt=r.useRef(ot);dt.current=ot;function mt(M){var E=M;return Number.isNaN(dt.current)||(E=Math.min(E,dt.current)),E=Math.max(E,0),E}var ke=ie<=0,je=ie>=ot,at=x<=0,lt=x>=m,ye=jn(ke,je,at,lt),it=function(){return{x:D?-x:x,y:ie}},qe=r.useRef(it()),I=cn(function(M){if(R){var E=W(W({},it()),M);(qe.current.x!==E.x||qe.current.y!==E.y)&&(R(E),qe.current=E)}});function T(M,E){var U=M;E?(un.flushSync(function(){j(U)}),I()):Ee(U)}function N(M){var E=M.currentTarget.scrollTop;E!==ie&&Ee(E),b==null||b(M),I()}var $=function(E){var U=E,fe=m?m-Ie.width:0;return U=Math.max(U,0),U=Math.min(U,fe),U},G=cn(function(M,E){E?(un.flushSync(function(){j(function(U){var fe=U+(D?-M:M);return $(fe)})}),I()):Ee(function(U){var fe=U+M;return fe})}),Ce=mo(te,ke,je,at,lt,!!m,G),Qe=Z(Ce,2),We=Qe[0],et=Qe[1];bo(te,pe,function(M,E,U,fe){var Ne=fe;return ye(M,E,U)?!1:!Ne||!Ne._virtualHandled?(Ne&&(Ne._virtualHandled=!0),We({preventDefault:function(){},deltaX:M?E:0,deltaY:M?0:E}),!0):!1}),So(H,pe,function(M){Ee(function(E){return E+M})}),xt(function(){function M(U){var fe=ke&&U.detail<0,Ne=je&&U.detail>0;te&&!fe&&!Ne&&U.preventDefault()}var E=pe.current;return E.addEventListener("wheel",We,{passive:!1}),E.addEventListener("DOMMouseScroll",et,{passive:!0}),E.addEventListener("MozMousePixelScroll",M,{passive:!1}),function(){E.removeEventListener("wheel",We),E.removeEventListener("DOMMouseScroll",et),E.removeEventListener("MozMousePixelScroll",M)}},[te,ke,je]),xt(function(){if(m){var M=$(x);j(M),I({x:M})}},[Ie.width,m]);var Me=function(){var E,U;(E=st.current)===null||E===void 0||E.delayHidden(),(U=He.current)===null||U===void 0||U.delayHidden()},tt=wo(pe,J,q,i,X,function(){return ae(!0)},Ee,Me);r.useImperativeHandle(t,function(){return{nativeElement:Oe.current,getScrollInfo:it,scrollTo:function(E){function U(fe){return fe&&yt(fe)==="object"&&("left"in fe||"top"in fe)}U(E)?(E.left!==void 0&&j($(E.left)),tt(E.top)):tt(E)}}}),xt(function(){if(w){var M=J.slice(we,Pe+1);w(M,J)}},[we,Pe,J]);var gt=go(J,X,q,i),Ot=L==null?void 0:L({start:we,end:Pe,virtual:H,offsetX:x,offsetY:rt,rtl:D,getSize:gt}),Tt=so(J,we,Pe,m,x,se,v,Be),nt=null;l&&(nt=W(ee({},d?"height":"maxHeight",l),Ro),te&&(nt.overflowY="hidden",m&&(nt.overflowX="hidden"),ne&&(nt.pointerEvents="none")));var Ze={};return D&&(Ze.dir="rtl"),r.createElement("div",Ye({ref:Oe,style:W(W({},s),{},{position:"relative"}),className:Q},Ze,ce),r.createElement(On,{onResize:ze},r.createElement(S,{className:"".concat(o,"-holder"),style:nt,ref:pe,onScroll:N,onMouseEnter:Me},r.createElement(An,{prefixCls:o,height:Se,offsetX:x,offsetY:rt,scrollWidth:m,onInnerResize:ae,ref:ue,innerProps:V,rtl:D,extra:Ot},Tt))),H&&Se>l&&r.createElement(Sn,{ref:st,prefixCls:o,scrollOffset:ie,scrollRange:Se,rtl:D,onScroll:T,onStartMove:k,onStopMove:ve,spinSize:Je,containerSize:Ie.height,style:P==null?void 0:P.verticalScrollBar,thumbStyle:P==null?void 0:P.verticalScrollBarThumb,showScrollBar:_}),H&&m>Ie.width&&r.createElement(Sn,{ref:He,prefixCls:o,scrollOffset:x,scrollRange:m,rtl:D,onScroll:T,onStartMove:k,onStopMove:ve,spinSize:bt,containerSize:Ie.width,horizontal:!0,style:P==null?void 0:P.horizontalScrollBar,thumbStyle:P==null?void 0:P.horizontalScrollBarThumb,showScrollBar:_}))}var Wn=r.forwardRef(xo);Wn.displayName="List";function $o(){return/(mac\sos|macintosh)/i.test(navigator.appVersion)}var Oo=["disabled","title","children","style","className"];function wn(e){return typeof e=="string"||typeof e=="number"}var Mo=function(t,n){var o=Lr(),a=o.prefixCls,l=o.id,i=o.open,c=o.multiple,d=o.mode,s=o.searchValue,f=o.toggleOpen,v=o.notFoundContent,h=o.onPopupScroll,u=r.useContext(kt),g=u.maxCount,m=u.flattenOptions,p=u.onActiveValue,S=u.defaultActiveFirstOption,b=u.onSelect,R=u.menuItemSelectedIcon,w=u.rawValues,V=u.fieldNames,L=u.virtual,P=u.direction,K=u.listHeight,_=u.listItemHeight,ce=u.optionRender,X="".concat(a,"-item"),Y=fr(function(){return m},[i,m],function(O,C){return C[0]&&O[1]!==C[1]}),oe=r.useRef(null),se=r.useMemo(function(){return c&&Xt(g)&&(w==null?void 0:w.size)>=g},[c,g,w==null?void 0:w.size]),ae=function(C){C.preventDefault()},q=function(C){var x;(x=oe.current)===null||x===void 0||x.scrollTo(typeof C=="number"?{index:C}:C)},de=r.useCallback(function(O){return d==="combobox"?!1:w.has(O)},[d,Ge(w).toString(),w.size]),te=function(C){for(var x=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1,j=Y.length,y=0;y<j;y+=1){var B=(C+y*x+j)%j,ne=Y[B]||{},F=ne.group,k=ne.data;if(!F&&!(k!=null&&k.disabled)&&(de(k.value)||!se))return B}return-1},le=r.useState(function(){return te(0)}),H=Z(le,2),D=H[0],Q=H[1],J=function(C){var x=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;Q(C);var j={source:x?"keyboard":"mouse"},y=Y[C];if(!y){p(null,-1,j);return}p(y.value,C,j)};r.useEffect(function(){J(S!==!1?te(0):-1)},[Y.length,s]);var pe=r.useCallback(function(O){return d==="combobox"?String(O).toLowerCase()===s.toLowerCase():w.has(O)},[d,s,Ge(w).toString(),w.size]);r.useEffect(function(){var O=setTimeout(function(){if(!c&&i&&w.size===1){var x=Array.from(w)[0],j=Y.findIndex(function(y){var B=y.data;return s?String(B.value).startsWith(s):B.value===x});j!==-1&&(J(j),q(j))}});if(i){var C;(C=oe.current)===null||C===void 0||C.scrollTo(void 0)}return function(){return clearTimeout(O)}},[i,s]);var ue=function(C){C!==void 0&&b(C,{selected:!w.has(C)}),c||f(!1)};if(r.useImperativeHandle(n,function(){return{onKeyDown:function(C){var x=C.which,j=C.ctrlKey;switch(x){case z.N:case z.P:case z.UP:case z.DOWN:{var y=0;if(x===z.UP?y=-1:x===z.DOWN?y=1:$o()&&j&&(x===z.N?y=1:x===z.P&&(y=-1)),y!==0){var B=te(D+y,y);q(B),J(B,!0)}break}case z.TAB:case z.ENTER:{var ne,F=Y[D];F&&!(F!=null&&(ne=F.data)!==null&&ne!==void 0&&ne.disabled)&&!se?ue(F.value):ue(void 0),i&&C.preventDefault();break}case z.ESC:f(!1),i&&C.stopPropagation()}},onKeyUp:function(){},scrollTo:function(C){q(C)}}}),Y.length===0)return r.createElement("div",{role:"listbox",id:"".concat(l,"_list"),className:"".concat(X,"-empty"),onMouseDown:ae},v);var Oe=Object.keys(V).map(function(O){return V[O]}),he=function(C){return C.label};function $e(O,C){var x=O.group;return{role:x?"presentation":"option",id:"".concat(l,"_list_").concat(C)}}var ie=function(C){var x=Y[C];if(!x)return null;var j=x.data||{},y=j.value,B=x.group,ne=_t(j,!0),F=he(x);return x?r.createElement("div",Ye({"aria-label":typeof F=="string"&&!B?F:null},ne,{key:C},$e(x,C),{"aria-selected":pe(y)}),y):null},me={role:"listbox",id:"".concat(l,"_list")};return r.createElement(r.Fragment,null,L&&r.createElement("div",Ye({},me,{style:{height:0,width:0,overflow:"hidden"}}),ie(D-1),ie(D),ie(D+1)),r.createElement(Wn,{itemKey:"key",ref:oe,data:Y,height:K,itemHeight:_,fullHeight:!1,onMouseDown:ae,onScroll:h,virtual:L,direction:P,innerProps:L?null:me},function(O,C){var x=O.group,j=O.groupOption,y=O.data,B=O.label,ne=O.value,F=y.key;if(x){var k,ve=(k=y.title)!==null&&k!==void 0?k:wn(B)?B.toString():void 0;return r.createElement("div",{className:Le(X,"".concat(X,"-group"),y.className),title:ve},B!==void 0?B:F)}var Be=y.disabled,Ee=y.title;y.children;var De=y.style,ge=y.className,Fe=pt(y,Oo),Xe=Mn(Fe,Oe),be=de(ne),Re=Be||!be&&se,Se="".concat(X,"-option"),we=Le(X,Se,ge,ee(ee(ee(ee({},"".concat(Se,"-grouped"),j),"".concat(Se,"-active"),D===C&&!Re),"".concat(Se,"-disabled"),Re),"".concat(Se,"-selected"),be)),Pe=he(O),rt=!R||typeof R=="function"||be,_e=typeof Pe=="number"?Pe:Pe||ne,Ae=wn(_e)?_e.toString():void 0;return Ee!==void 0&&(Ae=Ee),r.createElement("div",Ye({},_t(Xe),L?{}:$e(O,C),{"aria-selected":pe(ne),className:we,title:Ae,onMouseMove:function(){D===C||Re||J(C)},onClick:function(){Re||ue(ne)},style:De}),r.createElement("div",{className:"".concat(Se,"-content")},typeof ce=="function"?ce(O,{index:C}):_e),r.isValidElement(R)||be,rt&&r.createElement(zt,{className:"".concat(X,"-option-state"),customizeIcon:R,customizeIconProps:{value:ne,disabled:Re,isSelected:be}},be?"✓":null))}))},Do=r.forwardRef(Mo);const Po=function(e,t){var n=r.useRef({values:new Map,options:new Map}),o=r.useMemo(function(){var l=n.current,i=l.values,c=l.options,d=e.map(function(v){if(v.label===void 0){var h;return W(W({},v),{},{label:(h=i.get(v.value))===null||h===void 0?void 0:h.label})}return v}),s=new Map,f=new Map;return d.forEach(function(v){s.set(v.value,v),f.set(v.value,t.get(v.value)||c.get(v.value))}),n.current.values=s,n.current.options=f,d},[e,t]),a=r.useCallback(function(l){return t.get(l)||n.current.options.get(l)},[t]);return[o,a]};function At(e,t){return zn(e).join("").toUpperCase().includes(t)}const No=function(e,t,n,o,a){return r.useMemo(function(){if(!n||o===!1)return e;var l=t.options,i=t.label,c=t.value,d=[],s=typeof o=="function",f=n.toUpperCase(),v=s?o:function(u,g){return a?At(g[a],f):g[l]?At(g[i!=="children"?i:"label"],f):At(g[c],f)},h=s?function(u){return Gt(u)}:function(u){return u};return e.forEach(function(u){if(u[l]){var g=v(n,h(u));if(g)d.push(u);else{var m=u[l].filter(function(p){return v(n,h(p))});m.length&&d.push(W(W({},u),{},ee({},l,m)))}return}v(n,h(u))&&d.push(u)}),d},[e,o,a,n,t])};var yn=0,To=vr();function Bo(){var e;return To?(e=yn,yn+=1):e="TEST_OR_SSR",e}function Ho(e){var t=r.useState(),n=Z(t,2),o=n[0],a=n[1];return r.useEffect(function(){a("rc_select_".concat(Bo()))},[]),e||o}var Lo=["children","value"],_o=["children"];function zo(e){var t=e,n=t.key,o=t.props,a=o.children,l=o.value,i=pt(o,Lo);return W({key:n,value:l!==void 0?l:n,children:a},i)}function Kn(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;return mr(e).map(function(n,o){if(!r.isValidElement(n)||!n.type)return null;var a=n,l=a.type.isSelectOptGroup,i=a.key,c=a.props,d=c.children,s=pt(c,_o);return t||!l?zo(n):W(W({key:"__RC_SELECT_GRP__".concat(i===null?o:i,"__"),label:i},s),{},{options:Kn(d)})}).filter(function(n){return n})}var Vo=function(t,n,o,a,l){return r.useMemo(function(){var i=t,c=!t;c&&(i=Kn(n));var d=new Map,s=new Map,f=function(u,g,m){m&&typeof m=="string"&&u.set(g[m],g)},v=function h(u){for(var g=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,m=0;m<u.length;m+=1){var p=u[m];!p[o.options]||g?(d.set(p[o.value],p),f(s,p,o.label),f(s,p,a),f(s,p,l)):h(p[o.options],!0)}};return v(i),{options:i,valueOptions:d,labelOptions:s}},[t,n,o,a,l])};function In(e){var t=r.useRef();t.current=e;var n=r.useCallback(function(){return t.current.apply(t,arguments)},[]);return n}var Fo=["id","mode","prefixCls","backfill","fieldNames","inputValue","searchValue","onSearch","autoClearSearchValue","onSelect","onDeselect","dropdownMatchSelectWidth","filterOption","filterSort","optionFilterProp","optionLabelProp","options","optionRender","children","defaultActiveFirstOption","menuItemSelectedIcon","virtual","direction","listHeight","listItemHeight","labelRender","value","defaultValue","labelInValue","onChange","maxCount"],Ao=["inputValue"];function jo(e){return!e||yt(e)!=="object"}var Wo=r.forwardRef(function(e,t){var n=e.id,o=e.mode,a=e.prefixCls,l=a===void 0?"rc-select":a,i=e.backfill,c=e.fieldNames,d=e.inputValue,s=e.searchValue,f=e.onSearch,v=e.autoClearSearchValue,h=v===void 0?!0:v,u=e.onSelect,g=e.onDeselect,m=e.dropdownMatchSelectWidth,p=m===void 0?!0:m,S=e.filterOption,b=e.filterSort,R=e.optionFilterProp,w=e.optionLabelProp,V=e.options,L=e.optionRender,P=e.children,K=e.defaultActiveFirstOption,_=e.menuItemSelectedIcon,ce=e.virtual,X=e.direction,Y=e.listHeight,oe=Y===void 0?200:Y,se=e.listItemHeight,ae=se===void 0?20:se,q=e.labelRender,de=e.value,te=e.defaultValue,le=e.labelInValue,H=e.onChange,D=e.maxCount,Q=pt(e,Fo),J=Ho(n),pe=Yt(o),ue=!!(!V&&P),Oe=r.useMemo(function(){return S===void 0&&o==="combobox"?!1:S},[S,o]),he=r.useMemo(function(){return Fn(c,ue)},[JSON.stringify(c),ue]),$e=Kt("",{value:s!==void 0?s:d,postState:function(T){return T||""}}),ie=Z($e,2),me=ie[0],O=ie[1],C=Vo(V,P,he,R,w),x=C.valueOptions,j=C.labelOptions,y=C.options,B=r.useCallback(function(I){var T=zn(I);return T.map(function(N){var $,G,Ce,Qe,We;if(jo(N))$=N;else{var et;Ce=N.key,G=N.label,$=(et=N.value)!==null&&et!==void 0?et:Ce}var Me=x.get($);if(Me){var tt;G===void 0&&(G=Me==null?void 0:Me[w||he.label]),Ce===void 0&&(Ce=(tt=Me==null?void 0:Me.key)!==null&&tt!==void 0?tt:$),Qe=Me==null?void 0:Me.disabled,We=Me==null?void 0:Me.title}return{label:G,value:$,key:Ce,disabled:Qe,title:We}})},[he,w,x]),ne=Kt(te,{value:de}),F=Z(ne,2),k=F[0],ve=F[1],Be=r.useMemo(function(){var I,T=pe&&k===null?[]:k,N=B(T);return o==="combobox"&&Xr((I=N[0])===null||I===void 0?void 0:I.value)?[]:N},[k,B,o,pe]),Ee=Po(Be,x),De=Z(Ee,2),ge=De[0],Fe=De[1],Xe=r.useMemo(function(){if(!o&&ge.length===1){var I=ge[0];if(I.value===null&&(I.label===null||I.label===void 0))return[]}return ge.map(function(T){var N;return W(W({},T),{},{label:(N=typeof q=="function"?q(T):T.label)!==null&&N!==void 0?N:T.value})})},[o,ge,q]),be=r.useMemo(function(){return new Set(ge.map(function(I){return I.value}))},[ge]);r.useEffect(function(){if(o==="combobox"){var I,T=(I=ge[0])===null||I===void 0?void 0:I.value;O(Ur(T)?String(T):"")}},[ge]);var Re=In(function(I,T){var N=T??I;return ee(ee({},he.value,I),he.label,N)}),Se=r.useMemo(function(){if(o!=="tags")return y;var I=Ge(y),T=function($){return x.has($)};return Ge(ge).sort(function(N,$){return N.value<$.value?-1:1}).forEach(function(N){var $=N.value;T($)||I.push(Re($,N.label))}),I},[Re,y,x,ge,o]),we=No(Se,he,me,Oe,R),Pe=r.useMemo(function(){return o!=="tags"||!me||we.some(function(I){return I[R||"value"]===me})||we.some(function(I){return I[he.value]===me})?we:[Re(me)].concat(Ge(we))},[Re,R,o,we,me,he]),rt=function I(T){var N=Ge(T).sort(function($,G){return b($,G,{searchValue:me})});return N.map(function($){return Array.isArray($.options)?W(W({},$),{},{options:$.options.length>0?I($.options):$.options}):$})},_e=r.useMemo(function(){return b?rt(Pe):Pe},[Pe,b,me]),Ae=r.useMemo(function(){return ro(_e,{fieldNames:he,childrenAsData:ue})},[_e,he,ue]),Ie=function(T){var N=B(T);if(ve(N),H&&(N.length!==ge.length||N.some(function(Ce,Qe){var We;return((We=ge[Qe])===null||We===void 0?void 0:We.value)!==(Ce==null?void 0:Ce.value)}))){var $=le?N:N.map(function(Ce){return Ce.value}),G=N.map(function(Ce){return Gt(Fe(Ce.value))});H(pe?$:$[0],pe?G:G[0])}},ht=r.useState(null),ze=Z(ht,2),st=ze[0],He=ze[1],bt=r.useState(0),Je=Z(bt,2),ot=Je[0],dt=Je[1],mt=K!==void 0?K:o!=="combobox",ke=r.useCallback(function(I,T){var N=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},$=N.source,G=$===void 0?"keyboard":$;dt(T),i&&o==="combobox"&&I!==null&&G==="keyboard"&&He(String(I))},[i,o]),je=function(T,N,$){var G=function(){var nt,Ze=Fe(T);return[le?{label:Ze==null?void 0:Ze[he.label],value:T,key:(nt=Ze==null?void 0:Ze.key)!==null&&nt!==void 0?nt:T}:T,Gt(Ze)]};if(N&&u){var Ce=G(),Qe=Z(Ce,2),We=Qe[0],et=Qe[1];u(We,et)}else if(!N&&g&&$!=="clear"){var Me=G(),tt=Z(Me,2),gt=tt[0],Ot=tt[1];g(gt,Ot)}},at=In(function(I,T){var N,$=pe?T.selected:!0;$?N=pe?[].concat(Ge(ge),[I]):[I]:N=ge.filter(function(G){return G.value!==I}),Ie(N),je(I,$),o==="combobox"?He(""):(!Yt||h)&&(O(""),He(""))}),lt=function(T,N){Ie(T);var $=N.type,G=N.values;($==="remove"||$==="clear")&&G.forEach(function(Ce){je(Ce.value,!1,$)})},ye=function(T,N){if(O(T),He(null),N.source==="submit"){var $=(T||"").trim();if($){var G=Array.from(new Set([].concat(Ge(be),[$])));Ie(G),je($,!0),O("")}return}N.source!=="blur"&&(o==="combobox"&&Ie(T),f==null||f(T))},it=function(T){var N=T;o!=="tags"&&(N=T.map(function(G){var Ce=j.get(G);return Ce==null?void 0:Ce.value}).filter(function(G){return G!==void 0}));var $=Array.from(new Set([].concat(Ge(be),Ge(N))));Ie($),$.forEach(function(G){je(G,!0)})},qe=r.useMemo(function(){var I=ce!==!1&&p!==!1;return W(W({},C),{},{flattenOptions:Ae,onActiveValue:ke,defaultActiveFirstOption:mt,onSelect:at,menuItemSelectedIcon:_,rawValues:be,fieldNames:he,virtual:I,direction:X,listHeight:oe,listItemHeight:ae,childrenAsData:ue,maxCount:D,optionRender:L})},[D,C,Ae,ke,mt,at,_,be,he,ce,p,X,oe,ae,ue,L]);return r.createElement(kt.Provider,{value:qe},r.createElement(co,Ye({},Q,{id:J,prefixCls:l,ref:t,omitDomProps:Ao,mode:o,displayValues:Xe,onDisplayValuesChange:lt,direction:X,searchValue:me,onSearch:ye,autoClearSearchValue:h,onSearchSplit:it,dropdownMatchSelectWidth:p,OptionList:Do,emptyOptions:!Ae.length,activeValue:st,activeDescendantId:"".concat(J,"_list_").concat(ot)})))}),nn=Wo;nn.Option=tn;nn.OptGroup=en;const Ko=()=>{const[,e]=Qt(),[t]=Zt("Empty"),o=new Lt(e.colorBgBase).toHsl().l<.5?{opacity:.65}:{};return r.createElement("svg",{style:o,width:"184",height:"152",viewBox:"0 0 184 152",xmlns:"http://www.w3.org/2000/svg"},r.createElement("title",null,(t==null?void 0:t.description)||"Empty"),r.createElement("g",{fill:"none",fillRule:"evenodd"},r.createElement("g",{transform:"translate(24 31.67)"},r.createElement("ellipse",{fillOpacity:".8",fill:"#F5F5F7",cx:"67.797",cy:"106.89",rx:"67.797",ry:"12.668"}),r.createElement("path",{d:"M122.034 69.674L98.109 40.229c-1.148-1.386-2.826-2.225-4.593-2.225h-51.44c-1.766 0-3.444.839-4.592 2.225L13.56 69.674v15.383h108.475V69.674z",fill:"#AEB8C2"}),r.createElement("path",{d:"M101.537 86.214L80.63 61.102c-1.001-1.207-2.507-1.867-4.048-1.867H31.724c-1.54 0-3.047.66-4.048 1.867L6.769 86.214v13.792h94.768V86.214z",fill:"url(#linearGradient-1)",transform:"translate(13.56)"}),r.createElement("path",{d:"M33.83 0h67.933a4 4 0 0 1 4 4v93.344a4 4 0 0 1-4 4H33.83a4 4 0 0 1-4-4V4a4 4 0 0 1 4-4z",fill:"#F5F5F7"}),r.createElement("path",{d:"M42.678 9.953h50.237a2 2 0 0 1 2 2V36.91a2 2 0 0 1-2 2H42.678a2 2 0 0 1-2-2V11.953a2 2 0 0 1 2-2zM42.94 49.767h49.713a2.262 2.262 0 1 1 0 4.524H42.94a2.262 2.262 0 0 1 0-4.524zM42.94 61.53h49.713a2.262 2.262 0 1 1 0 4.525H42.94a2.262 2.262 0 0 1 0-4.525zM121.813 105.032c-.775 3.071-3.497 5.36-6.735 5.36H20.515c-3.238 0-5.96-2.29-6.734-5.36a7.309 7.309 0 0 1-.222-1.79V69.675h26.318c2.907 0 5.25 2.448 5.25 5.42v.04c0 2.971 2.37 5.37 5.277 5.37h34.785c2.907 0 5.277-2.421 5.277-5.393V75.1c0-2.972 2.343-5.426 5.25-5.426h26.318v33.569c0 .617-.077 1.216-.221 1.789z",fill:"#DCE0E6"})),r.createElement("path",{d:"M149.121 33.292l-6.83 2.65a1 1 0 0 1-1.317-1.23l1.937-6.207c-2.589-2.944-4.109-6.534-4.109-10.408C138.802 8.102 148.92 0 161.402 0 173.881 0 184 8.102 184 18.097c0 9.995-10.118 18.097-22.599 18.097-4.528 0-8.744-1.066-12.28-2.902z",fill:"#DCE0E6"}),r.createElement("g",{transform:"translate(149.65 15.383)",fill:"#FFF"},r.createElement("ellipse",{cx:"20.654",cy:"3.167",rx:"2.849",ry:"2.815"}),r.createElement("path",{d:"M5.698 5.63H0L2.898.704zM9.259.704h4.985V5.63H9.259z"}))))},Uo=()=>{const[,e]=Qt(),[t]=Zt("Empty"),{colorFill:n,colorFillTertiary:o,colorFillQuaternary:a,colorBgContainer:l}=e,{borderColor:i,shadowColor:c,contentColor:d}=r.useMemo(()=>({borderColor:new Lt(n).onBackground(l).toHexString(),shadowColor:new Lt(o).onBackground(l).toHexString(),contentColor:new Lt(a).onBackground(l).toHexString()}),[n,o,a,l]);return r.createElement("svg",{width:"64",height:"41",viewBox:"0 0 64 41",xmlns:"http://www.w3.org/2000/svg"},r.createElement("title",null,(t==null?void 0:t.description)||"Empty"),r.createElement("g",{transform:"translate(0 1)",fill:"none",fillRule:"evenodd"},r.createElement("ellipse",{fill:c,cx:"32",cy:"33",rx:"32",ry:"7"}),r.createElement("g",{fillRule:"nonzero",stroke:i},r.createElement("path",{d:"M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"}),r.createElement("path",{d:"M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z",fill:d}))))},Xo=e=>{const{componentCls:t,margin:n,marginXS:o,marginXL:a,fontSize:l,lineHeight:i}=e;return{[t]:{marginInline:o,fontSize:l,lineHeight:i,textAlign:"center",[`${t}-image`]:{height:e.emptyImgHeight,marginBottom:o,opacity:e.opacityImage,img:{height:"100%"},svg:{maxWidth:"100%",height:"100%",margin:"auto"}},[`${t}-description`]:{color:e.colorTextDescription},[`${t}-footer`]:{marginTop:n},"&-normal":{marginBlock:a,color:e.colorTextDescription,[`${t}-description`]:{color:e.colorTextDescription},[`${t}-image`]:{height:e.emptyImgHeightMD}},"&-small":{marginBlock:o,color:e.colorTextDescription,[`${t}-image`]:{height:e.emptyImgHeightSM}}}}},Go=Dn("Empty",e=>{const{componentCls:t,controlHeightLG:n,calc:o}=e,a=$t(e,{emptyImgCls:`${t}-img`,emptyImgHeight:o(n).mul(2.5).equal(),emptyImgHeightMD:n,emptyImgHeightSM:o(n).mul(.875).equal()});return[Xo(a)]});var Yo=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,o=Object.getOwnPropertySymbols(e);a<o.length;a++)t.indexOf(o[a])<0&&Object.prototype.propertyIsEnumerable.call(e,o[a])&&(n[o[a]]=e[o[a]]);return n};const Un=r.createElement(Ko,null),Xn=r.createElement(Uo,null),wt=e=>{const{className:t,rootClassName:n,prefixCls:o,image:a=Un,description:l,children:i,imageStyle:c,style:d,classNames:s,styles:f}=e,v=Yo(e,["className","rootClassName","prefixCls","image","description","children","imageStyle","style","classNames","styles"]),{getPrefixCls:h,direction:u,className:g,style:m,classNames:p,styles:S}=Pn("empty"),b=h("empty",o),[R,w,V]=Go(b),[L]=Zt("Empty"),P=typeof l<"u"?l:L==null?void 0:L.description,K=typeof P=="string"?P:"empty";let _=null;return typeof a=="string"?_=r.createElement("img",{alt:K,src:a}):_=a,R(r.createElement("div",Object.assign({className:Le(w,V,b,g,{[`${b}-normal`]:a===Xn,[`${b}-rtl`]:u==="rtl"},t,n,p.root,s==null?void 0:s.root),style:Object.assign(Object.assign(Object.assign(Object.assign({},S.root),m),f==null?void 0:f.root),d)},v),r.createElement("div",{className:Le(`${b}-image`,p.image,s==null?void 0:s.image),style:Object.assign(Object.assign(Object.assign({},c),S.image),f==null?void 0:f.image)},_),P&&r.createElement("div",{className:Le(`${b}-description`,p.description,s==null?void 0:s.description),style:Object.assign(Object.assign({},S.description),f==null?void 0:f.description)},P),i&&r.createElement("div",{className:Le(`${b}-footer`,p.footer,s==null?void 0:s.footer),style:Object.assign(Object.assign({},S.footer),f==null?void 0:f.footer)},i)))};wt.PRESENTED_IMAGE_DEFAULT=Un;wt.PRESENTED_IMAGE_SIMPLE=Xn;const qo=e=>{const{componentName:t}=e,{getPrefixCls:n}=r.useContext(Nn),o=n("empty");switch(t){case"Table":case"List":return Rt.createElement(wt,{image:wt.PRESENTED_IMAGE_SIMPLE});case"Select":case"TreeSelect":case"Cascader":case"Transfer":case"Mentions":return Rt.createElement(wt,{image:wt.PRESENTED_IMAGE_SIMPLE,className:`${o}-small`});case"Table.filter":return null;default:return Rt.createElement(wt,null)}},Qo=e=>{const n={overflow:{adjustX:!0,adjustY:!0,shiftY:!0},htmlRegion:e==="scroll"?"scroll":"visible",dynamicInset:!0};return{bottomLeft:Object.assign(Object.assign({},n),{points:["tl","bl"],offset:[0,4]}),bottomRight:Object.assign(Object.assign({},n),{points:["tr","br"],offset:[0,4]}),topLeft:Object.assign(Object.assign({},n),{points:["bl","tl"],offset:[0,-4]}),topRight:Object.assign(Object.assign({},n),{points:["br","tr"],offset:[0,-4]})}};function Zo(e,t){return e||Qo(t)}const En=e=>{const{optionHeight:t,optionFontSize:n,optionLineHeight:o,optionPadding:a}=e;return{position:"relative",display:"block",minHeight:t,padding:a,color:e.colorText,fontWeight:"normal",fontSize:n,lineHeight:o,boxSizing:"border-box"}},Jo=e=>{const{antCls:t,componentCls:n}=e,o=`${n}-item`,a=`&${t}-slide-up-enter${t}-slide-up-enter-active`,l=`&${t}-slide-up-appear${t}-slide-up-appear-active`,i=`&${t}-slide-up-leave${t}-slide-up-leave-active`,c=`${n}-dropdown-placement-`,d=`${o}-option-selected`;return[{[`${n}-dropdown`]:Object.assign(Object.assign({},Jt(e)),{position:"absolute",top:-9999,zIndex:e.zIndexPopup,boxSizing:"border-box",padding:e.paddingXXS,overflow:"hidden",fontSize:e.fontSize,fontVariant:"initial",backgroundColor:e.colorBgElevated,borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary,[`
          ${a}${c}bottomLeft,
          ${l}${c}bottomLeft
        `]:{animationName:br},[`
          ${a}${c}topLeft,
          ${l}${c}topLeft,
          ${a}${c}topRight,
          ${l}${c}topRight
        `]:{animationName:hr},[`${i}${c}bottomLeft`]:{animationName:pr},[`
          ${i}${c}topLeft,
          ${i}${c}topRight
        `]:{animationName:gr},"&-hidden":{display:"none"},[o]:Object.assign(Object.assign({},En(e)),{cursor:"pointer",transition:`background ${e.motionDurationSlow} ease`,borderRadius:e.borderRadiusSM,"&-group":{color:e.colorTextDescription,fontSize:e.fontSizeSM,cursor:"default"},"&-option":{display:"flex","&-content":Object.assign({flex:"auto"},Ut),"&-state":{flex:"none",display:"flex",alignItems:"center"},[`&-active:not(${o}-option-disabled)`]:{backgroundColor:e.optionActiveBg},[`&-selected:not(${o}-option-disabled)`]:{color:e.optionSelectedColor,fontWeight:e.optionSelectedFontWeight,backgroundColor:e.optionSelectedBg,[`${o}-option-state`]:{color:e.colorPrimary}},"&-disabled":{[`&${o}-option-selected`]:{backgroundColor:e.colorBgContainerDisabled},color:e.colorTextDisabled,cursor:"not-allowed"},"&-grouped":{paddingInlineStart:e.calc(e.controlPaddingHorizontal).mul(2).equal()}},"&-empty":Object.assign(Object.assign({},En(e)),{color:e.colorTextDisabled})}),[`${d}:has(+ ${d})`]:{borderEndStartRadius:0,borderEndEndRadius:0,[`& + ${d}`]:{borderStartStartRadius:0,borderStartEndRadius:0}},"&-rtl":{direction:"rtl"}})},sn(e,"slide-up"),sn(e,"slide-down"),dn(e,"move-up"),dn(e,"move-down")]},ko=e=>{const{multipleSelectItemHeight:t,paddingXXS:n,lineWidth:o,INTERNAL_FIXED_ITEM_MARGIN:a}=e,l=e.max(e.calc(n).sub(o).equal(),0),i=e.max(e.calc(l).sub(a).equal(),0);return{basePadding:l,containerPadding:i,itemHeight:xe(t),itemLineHeight:xe(e.calc(t).sub(e.calc(e.lineWidth).mul(2)).equal())}},ea=e=>{const{multipleSelectItemHeight:t,selectHeight:n,lineWidth:o}=e;return e.calc(n).sub(t).div(2).sub(o).equal()},ta=e=>{const{componentCls:t,iconCls:n,borderRadiusSM:o,motionDurationSlow:a,paddingXS:l,multipleItemColorDisabled:i,multipleItemBorderColorDisabled:c,colorIcon:d,colorIconHover:s,INTERNAL_FIXED_ITEM_MARGIN:f}=e;return{[`${t}-selection-overflow`]:{position:"relative",display:"flex",flex:"auto",flexWrap:"wrap",maxWidth:"100%","&-item":{flex:"none",alignSelf:"center",maxWidth:"100%",display:"inline-flex"},[`${t}-selection-item`]:{display:"flex",alignSelf:"center",flex:"none",boxSizing:"border-box",maxWidth:"100%",marginBlock:f,borderRadius:o,cursor:"default",transition:`font-size ${a}, line-height ${a}, height ${a}`,marginInlineEnd:e.calc(f).mul(2).equal(),paddingInlineStart:l,paddingInlineEnd:e.calc(l).div(2).equal(),[`${t}-disabled&`]:{color:i,borderColor:c,cursor:"not-allowed"},"&-content":{display:"inline-block",marginInlineEnd:e.calc(l).div(2).equal(),overflow:"hidden",whiteSpace:"pre",textOverflow:"ellipsis"},"&-remove":Object.assign(Object.assign({},Tn()),{display:"inline-flex",alignItems:"center",color:d,fontWeight:"bold",fontSize:10,lineHeight:"inherit",cursor:"pointer",[`> ${n}`]:{verticalAlign:"-0.2em"},"&:hover":{color:s}})}}}},na=(e,t)=>{const{componentCls:n,INTERNAL_FIXED_ITEM_MARGIN:o}=e,a=`${n}-selection-overflow`,l=e.multipleSelectItemHeight,i=ea(e),c=t?`${n}-${t}`:"",d=ko(e);return{[`${n}-multiple${c}`]:Object.assign(Object.assign({},ta(e)),{[`${n}-selector`]:{display:"flex",alignItems:"center",width:"100%",height:"100%",paddingInline:d.basePadding,paddingBlock:d.containerPadding,borderRadius:e.borderRadius,[`${n}-disabled&`]:{background:e.multipleSelectorBgDisabled,cursor:"not-allowed"},"&:after":{display:"inline-block",width:0,margin:`${xe(o)} 0`,lineHeight:xe(l),visibility:"hidden",content:'"\\a0"'}},[`${n}-selection-item`]:{height:d.itemHeight,lineHeight:xe(d.itemLineHeight)},[`${n}-selection-wrap`]:{alignSelf:"flex-start","&:after":{lineHeight:xe(l),marginBlock:o}},[`${n}-prefix`]:{marginInlineStart:e.calc(e.inputPaddingHorizontalBase).sub(d.basePadding).equal()},[`${a}-item + ${a}-item,
        ${n}-prefix + ${n}-selection-wrap
      `]:{[`${n}-selection-search`]:{marginInlineStart:0},[`${n}-selection-placeholder`]:{insetInlineStart:0}},[`${a}-item-suffix`]:{minHeight:d.itemHeight,marginBlock:o},[`${n}-selection-search`]:{display:"inline-flex",position:"relative",maxWidth:"100%",marginInlineStart:e.calc(e.inputPaddingHorizontalBase).sub(i).equal(),"\n          &-input,\n          &-mirror\n        ":{height:l,fontFamily:e.fontFamily,lineHeight:xe(l),transition:`all ${e.motionDurationSlow}`},"&-input":{width:"100%",minWidth:4.1},"&-mirror":{position:"absolute",top:0,insetInlineStart:0,insetInlineEnd:"auto",zIndex:999,whiteSpace:"pre",visibility:"hidden"}},[`${n}-selection-placeholder`]:{position:"absolute",top:"50%",insetInlineStart:e.calc(e.inputPaddingHorizontalBase).sub(d.basePadding).equal(),insetInlineEnd:e.inputPaddingHorizontalBase,transform:"translateY(-50%)",transition:`all ${e.motionDurationSlow}`}})}};function jt(e,t){const{componentCls:n}=e,o=t?`${n}-${t}`:"",a={[`${n}-multiple${o}`]:{fontSize:e.fontSize,[`${n}-selector`]:{[`${n}-show-search&`]:{cursor:"text"}},[`
        &${n}-show-arrow ${n}-selector,
        &${n}-allow-clear ${n}-selector
      `]:{paddingInlineEnd:e.calc(e.fontSizeIcon).add(e.controlPaddingHorizontal).equal()}}};return[na(e,t),a]}const ra=e=>{const{componentCls:t}=e,n=$t(e,{selectHeight:e.controlHeightSM,multipleSelectItemHeight:e.multipleItemHeightSM,borderRadius:e.borderRadiusSM,borderRadiusSM:e.borderRadiusXS}),o=$t(e,{fontSize:e.fontSizeLG,selectHeight:e.controlHeightLG,multipleSelectItemHeight:e.multipleItemHeightLG,borderRadius:e.borderRadiusLG,borderRadiusSM:e.borderRadius});return[jt(e),jt(n,"sm"),{[`${t}-multiple${t}-sm`]:{[`${t}-selection-placeholder`]:{insetInline:e.calc(e.controlPaddingHorizontalSM).sub(e.lineWidth).equal()},[`${t}-selection-search`]:{marginInlineStart:2}}},jt(o,"lg")]};function Wt(e,t){const{componentCls:n,inputPaddingHorizontalBase:o,borderRadius:a}=e,l=e.calc(e.controlHeight).sub(e.calc(e.lineWidth).mul(2)).equal(),i=t?`${n}-${t}`:"";return{[`${n}-single${i}`]:{fontSize:e.fontSize,height:e.controlHeight,[`${n}-selector`]:Object.assign(Object.assign({},Jt(e,!0)),{display:"flex",borderRadius:a,flex:"1 1 auto",[`${n}-selection-wrap:after`]:{lineHeight:xe(l)},[`${n}-selection-search`]:{position:"absolute",inset:0,width:"100%","&-input":{width:"100%",WebkitAppearance:"textfield"}},[`
          ${n}-selection-item,
          ${n}-selection-placeholder
        `]:{display:"block",padding:0,lineHeight:xe(l),transition:`all ${e.motionDurationSlow}, visibility 0s`,alignSelf:"center"},[`${n}-selection-placeholder`]:{transition:"none",pointerEvents:"none"},[["&:after",`${n}-selection-item:empty:after`,`${n}-selection-placeholder:empty:after`].join(",")]:{display:"inline-block",width:0,visibility:"hidden",content:'"\\a0"'}}),[`
        &${n}-show-arrow ${n}-selection-item,
        &${n}-show-arrow ${n}-selection-search,
        &${n}-show-arrow ${n}-selection-placeholder
      `]:{paddingInlineEnd:e.showArrowPaddingInlineEnd},[`&${n}-open ${n}-selection-item`]:{color:e.colorTextPlaceholder},[`&:not(${n}-customize-input)`]:{[`${n}-selector`]:{width:"100%",height:"100%",alignItems:"center",padding:`0 ${xe(o)}`,[`${n}-selection-search-input`]:{height:l,fontSize:e.fontSize},"&:after":{lineHeight:xe(l)}}},[`&${n}-customize-input`]:{[`${n}-selector`]:{"&:after":{display:"none"},[`${n}-selection-search`]:{position:"static",width:"100%"},[`${n}-selection-placeholder`]:{position:"absolute",insetInlineStart:0,insetInlineEnd:0,padding:`0 ${xe(o)}`,"&:after":{display:"none"}}}}}}}function oa(e){const{componentCls:t}=e,n=e.calc(e.controlPaddingHorizontalSM).sub(e.lineWidth).equal();return[Wt(e),Wt($t(e,{controlHeight:e.controlHeightSM,borderRadius:e.borderRadiusSM}),"sm"),{[`${t}-single${t}-sm`]:{[`&:not(${t}-customize-input)`]:{[`${t}-selector`]:{padding:`0 ${xe(n)}`},[`&${t}-show-arrow ${t}-selection-search`]:{insetInlineEnd:e.calc(n).add(e.calc(e.fontSize).mul(1.5)).equal()},[`
            &${t}-show-arrow ${t}-selection-item,
            &${t}-show-arrow ${t}-selection-placeholder
          `]:{paddingInlineEnd:e.calc(e.fontSize).mul(1.5).equal()}}}},Wt($t(e,{controlHeight:e.singleItemHeightLG,fontSize:e.fontSizeLG,borderRadius:e.borderRadiusLG}),"lg")]}const aa=e=>{const{fontSize:t,lineHeight:n,lineWidth:o,controlHeight:a,controlHeightSM:l,controlHeightLG:i,paddingXXS:c,controlPaddingHorizontal:d,zIndexPopupBase:s,colorText:f,fontWeightStrong:v,controlItemBgActive:h,controlItemBgHover:u,colorBgContainer:g,colorFillSecondary:m,colorBgContainerDisabled:p,colorTextDisabled:S,colorPrimaryHover:b,colorPrimary:R,controlOutline:w}=e,V=c*2,L=o*2,P=Math.min(a-V,a-L),K=Math.min(l-V,l-L),_=Math.min(i-V,i-L);return{INTERNAL_FIXED_ITEM_MARGIN:Math.floor(c/2),zIndexPopup:s+50,optionSelectedColor:f,optionSelectedFontWeight:v,optionSelectedBg:h,optionActiveBg:u,optionPadding:`${(a-t*n)/2}px ${d}px`,optionFontSize:t,optionLineHeight:n,optionHeight:a,selectorBg:g,clearBg:g,singleItemHeightLG:i,multipleItemBg:m,multipleItemBorderColor:"transparent",multipleItemHeight:P,multipleItemHeightSM:K,multipleItemHeightLG:_,multipleSelectorBgDisabled:p,multipleItemColorDisabled:S,multipleItemBorderColorDisabled:"transparent",showArrowPaddingInlineEnd:Math.ceil(e.fontSize*1.25),hoverBorderColor:b,activeBorderColor:R,activeOutlineColor:w,selectAffixPadding:c}},Gn=(e,t)=>{const{componentCls:n,antCls:o,controlOutlineWidth:a}=e;return{[`&:not(${n}-customize-input) ${n}-selector`]:{border:`${xe(e.lineWidth)} ${e.lineType} ${t.borderColor}`,background:e.selectorBg},[`&:not(${n}-disabled):not(${n}-customize-input):not(${o}-pagination-size-changer)`]:{[`&:hover ${n}-selector`]:{borderColor:t.hoverBorderHover},[`${n}-focused& ${n}-selector`]:{borderColor:t.activeBorderColor,boxShadow:`0 0 0 ${xe(a)} ${t.activeOutlineColor}`,outline:0},[`${n}-prefix`]:{color:t.color}}}},Rn=(e,t)=>({[`&${e.componentCls}-status-${t.status}`]:Object.assign({},Gn(e,t))}),la=e=>({"&-outlined":Object.assign(Object.assign(Object.assign(Object.assign({},Gn(e,{borderColor:e.colorBorder,hoverBorderHover:e.hoverBorderColor,activeBorderColor:e.activeBorderColor,activeOutlineColor:e.activeOutlineColor,color:e.colorText})),Rn(e,{status:"error",borderColor:e.colorError,hoverBorderHover:e.colorErrorHover,activeBorderColor:e.colorError,activeOutlineColor:e.colorErrorOutline,color:e.colorError})),Rn(e,{status:"warning",borderColor:e.colorWarning,hoverBorderHover:e.colorWarningHover,activeBorderColor:e.colorWarning,activeOutlineColor:e.colorWarningOutline,color:e.colorWarning})),{[`&${e.componentCls}-disabled`]:{[`&:not(${e.componentCls}-customize-input) ${e.componentCls}-selector`]:{background:e.colorBgContainerDisabled,color:e.colorTextDisabled}},[`&${e.componentCls}-multiple ${e.componentCls}-selection-item`]:{background:e.multipleItemBg,border:`${xe(e.lineWidth)} ${e.lineType} ${e.multipleItemBorderColor}`}})}),Yn=(e,t)=>{const{componentCls:n,antCls:o}=e;return{[`&:not(${n}-customize-input) ${n}-selector`]:{background:t.bg,border:`${xe(e.lineWidth)} ${e.lineType} transparent`,color:t.color},[`&:not(${n}-disabled):not(${n}-customize-input):not(${o}-pagination-size-changer)`]:{[`&:hover ${n}-selector`]:{background:t.hoverBg},[`${n}-focused& ${n}-selector`]:{background:e.selectorBg,borderColor:t.activeBorderColor,outline:0}}}},xn=(e,t)=>({[`&${e.componentCls}-status-${t.status}`]:Object.assign({},Yn(e,t))}),ia=e=>({"&-filled":Object.assign(Object.assign(Object.assign(Object.assign({},Yn(e,{bg:e.colorFillTertiary,hoverBg:e.colorFillSecondary,activeBorderColor:e.activeBorderColor,color:e.colorText})),xn(e,{status:"error",bg:e.colorErrorBg,hoverBg:e.colorErrorBgHover,activeBorderColor:e.colorError,color:e.colorError})),xn(e,{status:"warning",bg:e.colorWarningBg,hoverBg:e.colorWarningBgHover,activeBorderColor:e.colorWarning,color:e.colorWarning})),{[`&${e.componentCls}-disabled`]:{[`&:not(${e.componentCls}-customize-input) ${e.componentCls}-selector`]:{borderColor:e.colorBorder,background:e.colorBgContainerDisabled,color:e.colorTextDisabled}},[`&${e.componentCls}-multiple ${e.componentCls}-selection-item`]:{background:e.colorBgContainer,border:`${xe(e.lineWidth)} ${e.lineType} ${e.colorSplit}`}})}),ca=e=>({"&-borderless":{[`${e.componentCls}-selector`]:{background:"transparent",border:`${xe(e.lineWidth)} ${e.lineType} transparent`},[`&${e.componentCls}-disabled`]:{[`&:not(${e.componentCls}-customize-input) ${e.componentCls}-selector`]:{color:e.colorTextDisabled}},[`&${e.componentCls}-multiple ${e.componentCls}-selection-item`]:{background:e.multipleItemBg,border:`${xe(e.lineWidth)} ${e.lineType} ${e.multipleItemBorderColor}`},[`&${e.componentCls}-status-error`]:{[`${e.componentCls}-prefix, ${e.componentCls}-selection-item`]:{color:e.colorError}},[`&${e.componentCls}-status-warning`]:{[`${e.componentCls}-prefix, ${e.componentCls}-selection-item`]:{color:e.colorWarning}}}}),qn=(e,t)=>{const{componentCls:n,antCls:o}=e;return{[`&:not(${n}-customize-input) ${n}-selector`]:{borderWidth:`0 0 ${xe(e.lineWidth)} 0`,borderStyle:`none none ${e.lineType} none`,borderColor:t.borderColor,background:e.selectorBg,borderRadius:0},[`&:not(${n}-disabled):not(${n}-customize-input):not(${o}-pagination-size-changer)`]:{[`&:hover ${n}-selector`]:{borderColor:t.hoverBorderHover},[`${n}-focused& ${n}-selector`]:{borderColor:t.activeBorderColor,outline:0},[`${n}-prefix`]:{color:t.color}}}},$n=(e,t)=>({[`&${e.componentCls}-status-${t.status}`]:Object.assign({},qn(e,t))}),ua=e=>({"&-underlined":Object.assign(Object.assign(Object.assign(Object.assign({},qn(e,{borderColor:e.colorBorder,hoverBorderHover:e.hoverBorderColor,activeBorderColor:e.activeBorderColor,activeOutlineColor:e.activeOutlineColor,color:e.colorText})),$n(e,{status:"error",borderColor:e.colorError,hoverBorderHover:e.colorErrorHover,activeBorderColor:e.colorError,activeOutlineColor:e.colorErrorOutline,color:e.colorError})),$n(e,{status:"warning",borderColor:e.colorWarning,hoverBorderHover:e.colorWarningHover,activeBorderColor:e.colorWarning,activeOutlineColor:e.colorWarningOutline,color:e.colorWarning})),{[`&${e.componentCls}-disabled`]:{[`&:not(${e.componentCls}-customize-input) ${e.componentCls}-selector`]:{color:e.colorTextDisabled}},[`&${e.componentCls}-multiple ${e.componentCls}-selection-item`]:{background:e.multipleItemBg,border:`${xe(e.lineWidth)} ${e.lineType} ${e.multipleItemBorderColor}`}})}),sa=e=>({[e.componentCls]:Object.assign(Object.assign(Object.assign(Object.assign({},la(e)),ia(e)),ca(e)),ua(e))}),da=e=>{const{componentCls:t}=e;return{position:"relative",transition:`all ${e.motionDurationMid} ${e.motionEaseInOut}`,input:{cursor:"pointer"},[`${t}-show-search&`]:{cursor:"text",input:{cursor:"auto",color:"inherit",height:"100%"}},[`${t}-disabled&`]:{cursor:"not-allowed",input:{cursor:"not-allowed"}}}},fa=e=>{const{componentCls:t}=e;return{[`${t}-selection-search-input`]:{margin:0,padding:0,background:"transparent",border:"none",outline:"none",appearance:"none",fontFamily:"inherit","&::-webkit-search-cancel-button":{display:"none",appearance:"none"}}}},va=e=>{const{antCls:t,componentCls:n,inputPaddingHorizontalBase:o,iconCls:a}=e,l={[`${n}-clear`]:{opacity:1,background:e.colorBgBase,borderRadius:"50%"}};return{[n]:Object.assign(Object.assign({},Jt(e)),{position:"relative",display:"inline-flex",cursor:"pointer",[`&:not(${n}-customize-input) ${n}-selector`]:Object.assign(Object.assign({},da(e)),fa(e)),[`${n}-selection-item`]:Object.assign(Object.assign({flex:1,fontWeight:"normal",position:"relative",userSelect:"none"},Ut),{[`> ${t}-typography`]:{display:"inline"}}),[`${n}-selection-placeholder`]:Object.assign(Object.assign({},Ut),{flex:1,color:e.colorTextPlaceholder,pointerEvents:"none"}),[`${n}-arrow`]:Object.assign(Object.assign({},Tn()),{position:"absolute",top:"50%",insetInlineStart:"auto",insetInlineEnd:o,height:e.fontSizeIcon,marginTop:e.calc(e.fontSizeIcon).mul(-1).div(2).equal(),color:e.colorTextQuaternary,fontSize:e.fontSizeIcon,lineHeight:1,textAlign:"center",pointerEvents:"none",display:"flex",alignItems:"center",transition:`opacity ${e.motionDurationSlow} ease`,[a]:{verticalAlign:"top",transition:`transform ${e.motionDurationSlow}`,"> svg":{verticalAlign:"top"},[`&:not(${n}-suffix)`]:{pointerEvents:"auto"}},[`${n}-disabled &`]:{cursor:"not-allowed"},"> *:not(:last-child)":{marginInlineEnd:8}}),[`${n}-selection-wrap`]:{display:"flex",width:"100%",position:"relative",minWidth:0,"&:after":{content:'"\\a0"',width:0,overflow:"hidden"}},[`${n}-prefix`]:{flex:"none",marginInlineEnd:e.selectAffixPadding},[`${n}-clear`]:{position:"absolute",top:"50%",insetInlineStart:"auto",insetInlineEnd:o,zIndex:1,display:"inline-block",width:e.fontSizeIcon,height:e.fontSizeIcon,marginTop:e.calc(e.fontSizeIcon).mul(-1).div(2).equal(),color:e.colorTextQuaternary,fontSize:e.fontSizeIcon,fontStyle:"normal",lineHeight:1,textAlign:"center",textTransform:"none",cursor:"pointer",opacity:0,transition:`color ${e.motionDurationMid} ease, opacity ${e.motionDurationSlow} ease`,textRendering:"auto","&:before":{display:"block"},"&:hover":{color:e.colorIcon}},"@media(hover:none)":l,"&:hover":l}),[`${n}-status`]:{"&-error, &-warning, &-success, &-validating":{[`&${n}-has-feedback`]:{[`${n}-clear`]:{insetInlineEnd:e.calc(o).add(e.fontSize).add(e.paddingXS).equal()}}}}}},ma=e=>{const{componentCls:t}=e;return[{[t]:{[`&${t}-in-form-item`]:{width:"100%"}}},va(e),oa(e),ra(e),Jo(e),{[`${t}-rtl`]:{direction:"rtl"}},Sr(e,{borderElCls:`${t}-selector`,focusElCls:`${t}-focused`})]},ga=Dn("Select",(e,{rootPrefixCls:t})=>{const n=$t(e,{rootPrefixCls:t,inputPaddingHorizontalBase:e.calc(e.paddingSM).sub(1).equal(),multipleSelectItemHeight:e.multipleItemHeight,selectHeight:e.controlHeight});return[ma(n),sa(n)]},aa,{unitless:{optionLineHeight:!0,optionSelectedFontWeight:!0}});var pa={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"}}]},name:"check",theme:"outlined"},ha=function(t,n){return r.createElement(Bn,Ye({},t,{ref:n,icon:pa}))},ba=r.forwardRef(ha),Sa={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"}}]},name:"down",theme:"outlined"},Ca=function(t,n){return r.createElement(Bn,Ye({},t,{ref:n,icon:Sa}))},wa=r.forwardRef(Ca);function ya({suffixIcon:e,clearIcon:t,menuItemSelectedIcon:n,removeIcon:o,loading:a,multiple:l,hasFeedback:i,prefixCls:c,showSuffixIcon:d,feedbackIcon:s,showArrow:f,componentName:v}){const h=t??r.createElement(Cr,null),u=S=>e===null&&!i&&!f?null:r.createElement(r.Fragment,null,d!==!1&&S,i&&s);let g=null;if(e!==void 0)g=u(e);else if(a)g=u(r.createElement(yr,{spin:!0}));else{const S=`${c}-suffix`;g=({open:b,showSearch:R})=>u(b&&R?r.createElement(Ir,{className:S}):r.createElement(wa,{className:S}))}let m=null;n!==void 0?m=n:l?m=r.createElement(ba,null):m=null;let p=null;return o!==void 0?p=o:p=r.createElement(wr,null),{clearIcon:h,suffixIcon:g,itemIcon:m,removeIcon:p}}function Ia(e,t){return t!==void 0?t:e!==null}var Ea=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,o=Object.getOwnPropertySymbols(e);a<o.length;a++)t.indexOf(o[a])<0&&Object.prototype.propertyIsEnumerable.call(e,o[a])&&(n[o[a]]=e[o[a]]);return n};const Qn="SECRET_COMBOBOX_MODE_DO_NOT_USE",Ra=(e,t)=>{var n,o,a,l,i;const{prefixCls:c,bordered:d,className:s,rootClassName:f,getPopupContainer:v,popupClassName:h,dropdownClassName:u,listHeight:g=256,placement:m,listItemHeight:p,size:S,disabled:b,notFoundContent:R,status:w,builtinPlacements:V,dropdownMatchSelectWidth:L,popupMatchSelectWidth:P,direction:K,style:_,allowClear:ce,variant:X,dropdownStyle:Y,transitionName:oe,tagRender:se,maxCount:ae,prefix:q,dropdownRender:de,popupRender:te,onDropdownVisibleChange:le,onOpenChange:H,styles:D,classNames:Q}=e,J=Ea(e,["prefixCls","bordered","className","rootClassName","getPopupContainer","popupClassName","dropdownClassName","listHeight","placement","listItemHeight","size","disabled","notFoundContent","status","builtinPlacements","dropdownMatchSelectWidth","popupMatchSelectWidth","direction","style","allowClear","variant","dropdownStyle","transitionName","tagRender","maxCount","prefix","dropdownRender","popupRender","onDropdownVisibleChange","onOpenChange","styles","classNames"]),{getPopupContainer:pe,getPrefixCls:ue,renderEmpty:Oe,direction:he,virtual:$e,popupMatchSelectWidth:ie,popupOverflow:me}=r.useContext(Nn),{showSearch:O,style:C,styles:x,className:j,classNames:y}=Pn("select"),[,B]=Qt(),ne=p??(B==null?void 0:B.controlHeight),F=ue("select",c),k=ue(),ve=K??he,{compactSize:Be,compactItemClassnames:Ee}=Er(F,ve),[De,ge]=Rr("select",X,d),Fe=xr(F),[Xe,be,Re]=ga(F,Fe),Se=r.useMemo(()=>{const{mode:$}=e;if($!=="combobox")return $===Qn?"combobox":$},[e.mode]),we=Se==="multiple"||Se==="tags",Pe=Ia(e.suffixIcon,e.showArrow),rt=(n=P??L)!==null&&n!==void 0?n:ie,_e=((o=D==null?void 0:D.popup)===null||o===void 0?void 0:o.root)||((a=x.popup)===null||a===void 0?void 0:a.root)||Y,Ae=te||de,Ie=H||le,{status:ht,hasFeedback:ze,isFormItemInput:st,feedbackIcon:He}=r.useContext($r),bt=Tr(ht,w);let Je;R!==void 0?Je=R:Se==="combobox"?Je=null:Je=(Oe==null?void 0:Oe("Select"))||r.createElement(qo,{componentName:"Select"});const{suffixIcon:ot,itemIcon:dt,removeIcon:mt,clearIcon:ke}=ya(Object.assign(Object.assign({},J),{multiple:we,hasFeedback:ze,feedbackIcon:He,showSuffixIcon:Pe,prefixCls:F,componentName:"Select"})),je=ce===!0?{clearIcon:ke}:ce,at=Mn(J,["suffixIcon","itemIcon"]),lt=Le(((l=Q==null?void 0:Q.popup)===null||l===void 0?void 0:l.root)||((i=y==null?void 0:y.popup)===null||i===void 0?void 0:i.root)||h||u,{[`${F}-dropdown-${ve}`]:ve==="rtl"},f,y.root,Q==null?void 0:Q.root,Re,Fe,be),ye=Or($=>{var G;return(G=S??Be)!==null&&G!==void 0?G:$}),it=r.useContext(Mr),qe=b??it,I=Le({[`${F}-lg`]:ye==="large",[`${F}-sm`]:ye==="small",[`${F}-rtl`]:ve==="rtl",[`${F}-${De}`]:ge,[`${F}-in-form-item`]:st},Dr(F,bt,ze),Ee,j,s,y.root,Q==null?void 0:Q.root,f,Re,Fe,be),T=r.useMemo(()=>m!==void 0?m:ve==="rtl"?"bottomRight":"bottomLeft",[m,ve]),[N]=Pr("SelectLike",_e==null?void 0:_e.zIndex);return Xe(r.createElement(nn,Object.assign({ref:t,virtual:$e,showSearch:O},at,{style:Object.assign(Object.assign(Object.assign(Object.assign({},x.root),D==null?void 0:D.root),C),_),dropdownMatchSelectWidth:rt,transitionName:Nr(k,"slide-up",oe),builtinPlacements:Zo(V,me),listHeight:g,listItemHeight:ne,mode:Se,prefixCls:F,placement:T,direction:ve,prefix:q,suffixIcon:ot,menuItemSelectedIcon:dt,removeIcon:mt,allowClear:je,notFoundContent:Je,className:I,getPopupContainer:v||pe,dropdownClassName:lt,disabled:qe,dropdownStyle:Object.assign(Object.assign({},_e),{zIndex:N}),maxCount:we?ae:void 0,tagRender:we?se:void 0,dropdownRender:Ae,onDropdownVisibleChange:Ie})))},Nt=r.forwardRef(Ra),xa=Br(Nt,"dropdownAlign");Nt.SECRET_COMBOBOX_MODE_DO_NOT_USE=Qn;Nt.Option=tn;Nt.OptGroup=en;Nt._InternalPanelDoNotUseOrYouWillBeFired=xa;export{co as B,qo as D,wt as E,Wn as L,ba as R,Nt as S,wa as a,ko as b,Lr as c,Ho as d,ga as e,Ia as f,ta as g,Zo as m,ya as u};
