import{bV as F,r as i,q as I,aB as S,bW as j,bX as $,bY as v,bZ as E,c as _,b_ as M,b$ as N,c0 as V,c1 as c,c2 as W,c3 as A,c4 as B,c5 as Y,c6 as p,c7 as q,c8 as z}from"./index-De_f0oL2.js";var D=function(e,n){var l={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(l[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)n.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(l[r[o]]=e[r[o]]);return l};const G=e=>{const{prefixCls:n,className:l,closeIcon:r,closable:o,type:a,title:x,children:d,footer:h}=e,y=D(e,["prefixCls","className","closeIcon","closable","type","title","children","footer"]),{getPrefixCls:m}=i.useContext(I),C=m(),s=n||m("modal"),b=S(C),[w,O,g]=j(s,b),f=`${s}-confirm`;let u={};return a?u={closable:o??!1,title:"",footer:"",children:i.createElement($,Object.assign({},e,{prefixCls:s,confirmPrefixCls:f,rootPrefixCls:C,content:d}))}:u={closable:o??!0,title:x,footer:h!==null&&i.createElement(v,Object.assign({},e)),children:d},w(i.createElement(E,Object.assign({prefixCls:s,className:_(O,`${s}-pure-panel`,a&&f,a&&`${f}-${a}`,l,g,b)},y,{closeIcon:M(s,r),closable:o},u)))},R=F(G);function P(e){return c(z(e))}const t=N;t.useModal=V;t.info=function(n){return c(W(n))};t.success=function(n){return c(A(n))};t.error=function(n){return c(B(n))};t.warning=P;t.warn=P;t.confirm=function(n){return c(Y(n))};t.destroyAll=function(){for(;p.length;){const n=p.pop();n&&n()}};t.config=q;t._InternalPanelDoNotUseOrYouWillBeFired=R;export{t as M};
