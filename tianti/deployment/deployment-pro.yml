apiVersion: apps/v1
kind: Deployment
metadata:
  name: zxl-pc-newui
  namespace: tjzxl
  labels:
    app: zxl
spec:
  selector:
    matchLabels:
      app: zxl-pc-newui
  replicas: 1
  template:
    metadata:
      labels:
        app: zxl-pc-newui
    spec:
      containers:
        - name: zxl-pc-newui
          image: harbor.dcos.xixian.unicom.local/syyc/zxl/front-pc:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 80
          env:
            - name: FORWARD_SERVER_HOST
              value: "zxl-backend.tjzxl"
            - name: FORWARD_SERVER_PORT
              value: "18083"
            - name: API_PREFIX
              value: "zhyy"
            - name: FORWARD_SERVER_HOST2
              value: "zxl-schedule.tjzxl"
            - name: FORWARD_SERVER_PORT2
              value: "18081"
            - name: API_PREFIX2
              value: "schedule"
            - name: CROSS_ORIGIN
              value: "wildcard"
