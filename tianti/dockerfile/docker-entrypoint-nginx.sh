#!/bin/sh
#set -eux
set -ex

if [ ! -z "${DIST_DIR}" ] && [ ! -z "${APP_NAME}" ] && [ "${DIST_DIR}" != "${APP_NAME}" ]
then
    mv ${DIST_DIR} ${APP_NAME}
fi

export NGINX_PORT=${NGINX_PORT:-80}
export NGINX_PORT_HTTPS=${NGINX_PORT_HTTPS:-443}
export CROSS_ORIGIN=${CROSS_ORIGIN:-''}
export CROSS_ORIGIN_PROCESS=$(echo ${CROSS_ORIGIN}|sed  's/\$//g')
envsubst '${DIST_DIR} \
          ${APP_NAME} \
          ${NGINX_PORT} \
          ${NGINX_PORT_HTTPS} \
          ${FORWARD_SERVER_HOST} \
          ${FORWARD_SERVER_PORT} \
          ${API_PREFIX} \
	        ${FORWARD_SERVER_HOST2} \
          ${FORWARD_SERVER_PORT2} \
          ${API_PREFIX2} \
          ${FORWARD_SERVER_HOST3} \
          ${FORWARD_SERVER_PORT3} \
          ${API_PREFIX3} \
	        ${CROSS_ORIGIN} \
          ${CROSS_ORIGIN_PROCESS} \
          '\
            < /etc/nginx/nginx.conf.template > /etc/nginx/nginx.conf
exec "$@"

