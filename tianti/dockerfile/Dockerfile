FROM harbor.dcos.xixian.unicom.local/syyc/nginx-arm:1.24
ARG ARG_DIST_DIR=dist
ARG ARG_APP_NAME=efop
ARG ARG_NGINX_PORT=80
ENV FORWARD_SERVER_HOST=127.0.0.1 \
    FORWARD_SERVER_PORT=8083 \
    API_PREFIX=api \
    NGINX_PORT=${ARG_NGINX_PORT} \
    DIST_DIR=${ARG_DIST_DIR} \
    APP_NAME=${ARG_APP_NAME}
WORKDIR /usr/share/nginx/html
ADD ${ARG_DIST_DIR} /usr/share/nginx/html/${ARG_DIST_DIR}
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime &&\
    echo "Asia/Shanghai" > /etc/timezone
COPY tianti/dockerfile/nginx.conf.template /etc/nginx/nginx.conf.template
COPY tianti/dockerfile/docker-entrypoint-nginx.sh /usr/local/bin/
COPY tianti/dockerfile/ssl /usr/share/nginx/ssl/
RUN  chmod +x /usr/local/bin/docker-entrypoint-nginx.sh
ENTRYPOINT ["docker-entrypoint-nginx.sh"]
EXPOSE ${ARG_NGINX_PORT}
CMD nginx -g "daemon off;"
