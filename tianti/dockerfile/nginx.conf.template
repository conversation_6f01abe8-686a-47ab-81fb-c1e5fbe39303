
#user  nobody;
worker_processes  1;

#error_log  logs/error.log;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;

#pid        logs/nginx.pid;


events {
    worker_connections  1024;
}


http {



    server_tokens off;

    # 解决上传大文件 报413 Request Entity Too Large 的问题
    client_max_body_size 100m;

    include       mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr:$server_port - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent"  dq_ip:["$http_x_forwarded_for"] '
                      '$upstream_addr - $upstream_response_time - $remote_addr - zj_ip:[$proxy_add_x_forwarded_for]' ;

    access_log  /var/log/nginx/access.log  main;

    sendfile        on;
    #tcp_nopush     on;

    #keepalive_timeout  0;
    keepalive_timeout  65;

    #gzip  on;
    upstream forward-server {
        server ${FORWARD_SERVER_HOST}:${FORWARD_SERVER_PORT};
    }
    upstream forward-server2 {
        server ${FORWARD_SERVER_HOST2}:${FORWARD_SERVER_PORT2};
    }


    server {

        listen       ${NGINX_PORT} ssl;
        server_name  localhost;

        #SSL配置
        ssl_certificate      /usr/share/nginx/ssl/ssl.crt;
        ssl_certificate_key  /usr/share/nginx/ssl/ssl_nopass.key;
        ssl_protocols        TLSv1 TLSv1.1 TLSv1.2;
        ssl_ciphers          ECDHE-RSA-AES128-GCM-SHA256:HIGH:!aNULL:!MD5:!RC4:!DHE;
        ssl_prefer_server_ciphers  on;
        ssl_session_cache    shared:SSL:10m;
        ssl_session_timeout  10m;

        gzip on;
        gzip_static on;
        gzip_min_length 1k;
        gzip_comp_level 9;
        gzip_types text/plain application/javascript application/x-javascript text/css application/xml text/javascript application/x-httpd-php image/jpeg image/gif image/png;
        gzip_vary on;
        gzip_disable "MSIE [1-6]\.";

        #charset koi8-r;

        #access_log  logs/host.access.log  main;

        # cross origin
        set $cors_origin '';
        if ($http_origin ~* '^(${CROSS_ORIGIN})') {
            set $cors_origin $http_origin;
        }
        set $cors_origin_process '${CROSS_ORIGIN_PROCESS}';
        if ($cors_origin_process = 'wildcard') {
            set $cors_origin '*';
        }
        if ($cors_origin_process = '') {
            set $cors_origin '';
        }

        add_header Access-Control-Allow-Origin '$cors_origin';
        add_header Access-Control-Allow-Methods 'GET,POST,OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
        if ($request_method = 'OPTIONS') {
            return 204;
        }


        location / {
            #root   html;
            root   /usr/share/nginx/html;
            index  index.html index.htm;
            # fix react cra 重复刷新页面报404问题
            try_files $uri /${APP_NAME}/index.html;
            # fix 304
            expires -1;
            if_modified_since off;
            add_header Last-Modified "";
            add_header Cache-Control no-cache;
            etag off;
        }


        location ^~/${API_PREFIX}/ {
            proxy_pass http://forward-server/${API_PREFIX}/;

            # fix 426 Upgrade Required
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header REMOTE-HOST $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

        location ^~/${API_PREFIX2}/ {
            proxy_pass http://forward-server2/${API_PREFIX2}/;
            proxy_set_header Host $host;

            # fix 426 Upgrade Required
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            # 添加 X-Forwarded-Proto 头部以指示后端程序 使用的协议为 HTTP
            proxy_set_header X-Forwarded-Proto http;
        }



        #error_page  404              /404.html;

        # redirect server error pages to the static page /50x.html
        #
        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }

        # proxy the PHP scripts to Apache listening on 127.0.0.1:80
        #
        #location ~ \.php$ {
        #    proxy_pass   http://127.0.0.1;
        #}

        # pass the PHP scripts to FastCGI server listening on 127.0.0.1:9000
        #
        #location ~ \.php$ {
        #    root           html;
        #    fastcgi_pass   127.0.0.1:9000;
        #    fastcgi_index  index.php;
        #    fastcgi_param  SCRIPT_FILENAME  /scripts$fastcgi_script_name;
        #    include        fastcgi_params;
        #}

        # deny access to .htaccess files, if Apache's document root
        # concurs with nginx's one
        #
        #location ~ /\.ht {
        #    deny  all;
        #}
    }


    # another virtual host using mix of IP-, name-, and port-based configuration
    #
    #server {
    #    listen       8000;
    #    listen       somename:8080;
    #    server_name  somename  alias  another.alias;

    #    location / {
    #        root   html;
    #        index  index.html index.htm;
    #    }
    #}


    # HTTPS server
    #
    #server {
    #    listen       443 ssl;
    #    server_name  localhost;

    #    ssl_certificate      cert.pem;
    #    ssl_certificate_key  cert.key;

    #    ssl_session_cache    shared:SSL:1m;
    #    ssl_session_timeout  5m;

    #    ssl_ciphers  HIGH:!aNULL:!MD5;
    #    ssl_prefer_server_ciphers  on;

    #    location / {
    #        root   html;
    #        index  index.html index.htm;
    #    }
    #}

}
