export const modules = import.meta.glob('../pages/**/*/index.{jsx,tsx}')

export const componentPaths = Object.keys(modules).map((path: string) =>
  path.replace('../pages', '')
)

export const components = Object.keys(modules).reduce<Record<string, () => Promise<any>>>(
  (prev, path: string) => {
    // console.log('path', path)
    const formatPath = path.replace('../pages', '')
    prev[formatPath] = async () => {
      // 这里其实就是动态加载js，如果报错了说明js资源不存在
      return (await modules[path]()) as any
    }
    // console.log('prev', prev)
    return prev
  },
  {}
)
