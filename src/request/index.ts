import axios from 'axios'

import type {
  AxiosInstance,
  AxiosRequestConfig,
  InternalAxiosRequestConfig,
  AxiosResponse,
  CreateAxiosDefaults
} from 'axios'

import { antdUtils } from '@/utils'

export type Response<T> = Promise<[boolean, T, AxiosResponse<T>]>

class Request {
  axiosInstance: AxiosInstance

  public constructor(config?: CreateAxiosDefaults) {
    // 实例化axios
    this.axiosInstance = axios.create(config)

    // 请求拦截器
    this.axiosInstance.interceptors.request.use((axiosConfig: InternalAxiosRequestConfig) =>
      this.requestInterceptor(axiosConfig)
    )

    // 响应拦截器
    this.axiosInstance.interceptors.response.use(
      (response: AxiosResponse<unknown, unknown>) => this.responseSuccessInterceptor(response),
      (error: any) => this.responseErrorInterceptor(error)
    )
  }

  private async requestInterceptor(axiosConfig: InternalAxiosRequestConfig): Promise<any> {
    const Info = JSON.parse(localStorage.getItem('userStore'))
    const InfoTk = JSON.parse(localStorage.getItem('globalStore'))
    if (Info?.state?.currentUser?.orgInfo) {
      axiosConfig.headers['OrgId5'] = `${Info.state.currentUser.orgInfo.orgId5}`
      axiosConfig.headers['OrgName5'] = encodeURIComponent(
        `${Info.state.currentUser.orgInfo.orgName5}`
      )
      axiosConfig.headers['Manager-Auth-Token'] = InfoTk.state.token || ''
      axiosConfig.headers['OrgId6'] = `${Info.state.currentUser.orgInfo.orgId6}`
      axiosConfig.headers['OrgName6'] = encodeURIComponent(
        `${Info.state.currentUser.orgInfo.orgName6}`
      )
    }
    return Promise.resolve(axiosConfig)
  }

  private async responseSuccessInterceptor(response: AxiosResponse<any, any>): Promise<any> {
    return Promise.resolve([false, response.data, response])
  }

  private async responseErrorInterceptor(error: any): Promise<any> {
    const { status } = error?.response || {}
    if (status === 401) {
      // 如果接口401，退出登录
      antdUtils.message?.warning('登录信息已过期，将于3s后退出登录页面，请重新登录！')
      return Promise.resolve([true, error?.response?.data])
    } else {
      antdUtils.notification?.error({
        message: '出错了',
        description: error?.response?.data?.message
      })
      return Promise.resolve([true, error?.response?.data])
    }
  }

  request<T, D = any>(config: AxiosRequestConfig<D>): Response<T> {
    return this.axiosInstance(config)
  }

  get<T, D = any>(url: string, config?: AxiosRequestConfig<D>): Response<T> {
    return this.axiosInstance.get(url, config)
  }

  post<T, D = any>(url: string, data?: D, config?: AxiosRequestConfig<D>): Response<T> {
    return this.axiosInstance.post(url, data, config)
  }

  put<T, D = any>(url: string, data?: D, config?: AxiosRequestConfig<D>): Response<T> {
    return this.axiosInstance.put(url, data, config)
  }

  delete<T, D = any>(url: string, config?: AxiosRequestConfig<D>): Response<T> {
    return this.axiosInstance.delete(url, config)
  }
}

const request = new Request({ timeout: 120000 }) // 超时设置为2分钟

export default request
