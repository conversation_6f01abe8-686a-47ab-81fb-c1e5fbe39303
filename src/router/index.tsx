import { useEffect } from 'react'
import { createBrowserRouter, Navigate, RouteObject, RouterProvider } from 'react-router-dom'
import { App } from 'antd'
import Layout from '../layout'
import Login from '@/pages/login'
import RouterErrorElement from './router-error-element'
import { antdUtils } from '@/utils/antd'

export const router = createBrowserRouter(
  [
    {
      path: '/',
      element: <Navigate to='/dashboard' />
    },
    {
      path: '/login',
      Component: Login
    },
    {
      path: '*',
      Component: Layout,
      children: [],
      errorElement: <RouterErrorElement />
    }
  ],
  { basename: '/efop' }
)

function findNodeByPath(routes: RouteObject[], path: string) {
  for (let i = 0; i < routes.length; i += 1) {
    const element = routes[i]

    if (element.path === path) return element

    findNodeByPath(element.children || [], path)
  }
}

export const addRoutes = (parentPath: string, routes: RouteObject[]) => {
  if (!parentPath) {
    router.routes.push(...(routes as any))
    return
  }

  const curNode = findNodeByPath(router.routes, parentPath)

  if (curNode?.children) {
    curNode?.children.push(...routes)
  } else if (curNode) {
    curNode.children = routes
  }
}

export const replaceRoutes = (parentPath: string, routes: RouteObject[]) => {
  if (!parentPath) {
    router.routes.push(...(routes as any))
    return
  }

  const curNode = findNodeByPath(router.routes, parentPath)

  if (curNode) {
    curNode.children = routes
  }
}

const Router = () => {
  const { notification, message, modal } = App.useApp()
  useEffect(() => {
    antdUtils.setMessageInstance(message)
    antdUtils.setNotificationInstance(notification)
    antdUtils.setModalInstance(modal)
  }, [notification, message, modal])

  return <RouterProvider router={router} />
}

export default Router
