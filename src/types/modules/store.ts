export interface Menu {
  id: string
  parentCode?: string
  menuCode?: string
  menuName?: string
  iconClass?: string
  tag?: string
  type?: number
  route?: string
  filePath?: string
  orderNumber?: number
  url?: string
  show?: boolean
  showType: string
  children?: Menu[]
  path: string
  Component?: any
  parentPaths?: string[]
  authCode?: string
  curVersion?: string
}

export interface User {
  userId: number
  userName: string
}

export interface Role {
  roleCode: string[]
}

export interface Account {
  userInfo: User
  currentMenuCode: string
  menus: Menu[]
  flatMenus: Menu[]
  orgInfo?: any
  roleInfo: Role
}
