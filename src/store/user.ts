import { create } from 'zustand'
import { devtools, persist, createJSONStorage } from 'zustand/middleware'

import { Account } from '@/types'

interface State {
  currentUser: Account | null
  tabUrl: string | null
}

interface Action {
  setCurrentUser: (currentUser: State['currentUser']) => void
  setTabUrl: (currentUser: State['tabUrl']) => void
  reset: () => void
}

const initialState: State = {
  currentUser: null,
  tabUrl: null
}

export const useUserStore = create<State & Action>()(
  devtools(
    persist(
      set => {
        return {
          ...initialState,
          setCurrentUser: (currentUser: State['currentUser']) => set({ currentUser }),
          setTabUrl: (tabUrl: State['tabUrl']) => set({ tabUrl }),
          reset: () => {
            set(initialState)
          }
        }
      },
      { name: 'userStore', storage: createJSONStorage(() => localStorage) }
    ),
    { name: 'userStore' }
  )
)
