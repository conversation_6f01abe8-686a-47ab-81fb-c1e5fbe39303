import { create } from 'zustand'

import { devtools, persist, createJSONStorage } from 'zustand/middleware'

type State = {
  token: string
  collapsed: boolean
  darkMode: boolean
  menuMode: 'vertical' | 'inline'
}

type Action = {
  setToken: (token: State['token']) => void
  setCollapsed: (collapsed: State['collapsed']) => void
  setDarkMode: (darkMode: State['darkMode']) => void
  setMenuMode: (menuMode: State['menuMode']) => void
  reset: () => void
}

const initialState: State = {
  token: '',
  collapsed: false,
  darkMode: false,
  menuMode: 'inline'
}

export const useGlobalStore = create<State & Action>()(
  devtools(
    persist(
      set => ({
        ...initialState,
        setToken: (token: State['token']) => {
          set({ token })
        },
        setCollapsed: (collapsed: State['collapsed']) => {
          set({ collapsed })
        },
        setDarkMode: (darkMode: State['darkMode']) => {
          set({ darkMode })
        },
        setMenuMode: (menuMode: State['menuMode']) => {
          set({ menuMode })
        },
        reset: () => {
          set(initialState)
        }
      }),
      { name: 'globalStore', storage: createJSONStorage(() => localStorage) }
    ),
    { name: 'globalStore' }
  )
)
