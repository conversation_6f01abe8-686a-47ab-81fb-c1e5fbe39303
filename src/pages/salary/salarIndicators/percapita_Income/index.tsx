import {
  But<PERSON>,
  Col,
  DatePicker,
  Form,
  FormProps,
  message,
  Row,
  Select,
  Space,
  Tooltip
} from 'antd'
import dayjs from 'dayjs'
import {
  DownloadOutlined,
  FullscreenExitOutlined,
  FullscreenOutlined,
  InfoCircleOutlined
} from '@ant-design/icons'
import { useEffect, useRef, useState } from 'react'
import { useRequest } from '@/hooks'
import indicatorsService from '@/pages/salary/salarIndicators/service.ts'
import { downFile, openNotification } from '@/utils/down.tsx'
import styles from '../Index.module.scss'
import { orgOption } from '@/pages/effect/employment/assest.ts'
import ResizableTable from '@/components/resizeTable/index.jsx'
import { formatMoney } from '@/hooks/format'

type FieldType = {
  cycleId?: string
  unit?: string
  curPost?: string
}

const ReportPer: React.FC = () => {
  const columns = [
    // {
    //   title: '月份',
    //   key: 'cycleId',
    //   dataIndex: 'cycleId',
    //   align: 'center',
    //   fixed: 'left',
    //   width: 50,
    //   children: []
    // },
    {
      title: '单位',
      key: 'cityName',
      dataIndex: 'cityName',
      align: 'center',
      fixed: 'left',
      width: 120,
      children: [],
      render: (text: string, _record: orgOption) => (
        <Tooltip title={text}>
          <div className={styles.over_ellipsis}>{text}</div>
        </Tooltip>
      )
    },
    {
      title: '主营业务收入',
      key: 'incomeMainBusi',
      dataIndex: 'incomeMainBusi',
      align: 'center',
      width: 70,
      children: [],
      render: text => <span>{formatMoney(text)}</span>
    },
    {
      title: '经营利润',
      key: 'profitBusi',
      dataIndex: 'profitBusi',
      align: 'center',
      width: 70,
      children: [],
      render: text => <span>{formatMoney(text)}</span>
    },
    {
      title: '全量人均创收',
      key: 'incomeTotalPer',
      dataIndex: 'incomeTotalPer',
      align: 'center',
      width: 70,
      children: [],
      render: text => <span>{formatMoney(text)}</span>
    },
    {
      title: '全口径人均创收',
      key: 'incomeTotalCaliber',
      dataIndex: 'incomeTotalCaliber',
      align: 'center',
      width: 70,
      children: [],
      render: text => <span>{formatMoney(text)}</span>
    },
    {
      title: '人均创收（从业人员）',
      key: 'incomePer',
      dataIndex: 'incomePer',
      align: 'center',
      children: [],
      width: 70,
      render: text => <span>{formatMoney(text)}</span>
    },
    {
      title: '全量人均创利',
      key: 'profitTotalPer',
      dataIndex: 'profitTotalPer',
      align: 'center',
      children: [],
      width: 70,
      render: text => <span>{formatMoney(text)}</span>
    },
    {
      title: '全口径人均创利',
      key: 'profitTotalCaliber',
      dataIndex: 'profitTotalCaliber',
      align: 'center',
      children: [],
      width: 70,
      render: text => <span>{formatMoney(text)}</span>
    },
    {
      title: '从业人员人均创利',
      key: 'level',
      dataIndex: 'level',
      align: 'center',
      width: 70,
      children: [
        {
          title: '人均创利',
          key: 'profitPer',
          dataIndex: 'profitPer',
          align: 'center',
          children: [],
          width: 70,
          render: text => <span>{formatMoney(text)}</span>
        },
        {
          title: '预算值',
          key: 'budget',
          dataIndex: 'budget',
          align: 'center',
          children: [],
          width: 70,
          render: text => <span>{formatMoney(text)}</span>
        },
        {
          title: '完成率',
          key: 'completeRate',
          dataIndex: 'completeRate',
          align: 'center',
          children: [],
          width: 70
        }
      ]
    },
    {
      title: (
        <>
          <span>人员统计</span>
          <Tooltip title='不含跨单位'>
            <InfoCircleOutlined />
          </Tooltip>
        </>
      ),
      key: 'level',
      dataIndex: 'level',
      align: 'center',
      width: 50,
      children: [
        {
          title: '人数合计',
          key: 'totalPerson',
          dataIndex: 'totalPerson',
          align: 'center',
          children: [],
          width: 70
        },
        {
          title: '合同制',
          key: 'totalPersonContract',
          dataIndex: 'totalPersonContract',
          align: 'center',
          children: [],
          width: 70
        },
        {
          title: '劳务派遣',
          key: 'totalPersonLabor',
          dataIndex: 'totalPersonLabor',
          align: 'center',
          children: [],
          width: 70
        },
        {
          title: '紧密型外包',
          key: 'totalPersonTos',
          dataIndex: 'totalPersonTos',
          align: 'center',
          width: 70
        },
        {
          title: '其他外包',
          key: 'totalPersonOos',
          dataIndex: 'totalPersonOos',
          align: 'center',
          width: 70
        },
        {
          title: '调整人数',
          key: 'totalPersonAdjust',
          dataIndex: 'totalPersonAdjust',
          align: 'center',
          width: 70
        }
      ]
    }
  ]

  const topRef = useRef(null)
  const contentRef = useRef(null)
  const tableTopRef = useRef(null)
  const [formRef] = Form.useForm()
  // const [cascaderSelected, setCascaderSelected] = useState<any>([])
  const [height, setHeight] = useState(0)
  const [tableData, setTableData] = useState<any>([])
  const [tableLoading, setTableLoading] = useState<boolean>(false)
  const [tableColumns, setTableColumns] = useState<any>(columns)
  const [isReset, setIsReset] = useState<number>(0)
  const [orgList, setOrgList] = useState<any>([])
  const [showTitle, setShowTitle] = useState<boolean>(true)
  const [pagination, setPagination] = useState({
    total: 0,
    pageNum: 1,
    pageSize: 50
  })

  const { runAsync: getEnumType } = useRequest(indicatorsService.getEnumType, {
    manual: true
  })
  const { runAsync: getPerIncomeProfit } = useRequest(indicatorsService.getPerIncomeProfit, {
    manual: true
  })
  const { runAsync: exportPerIncomeProfit } = useRequest(indicatorsService.exportPerIncomeProfit, {
    manual: true
  })

  useEffect(() => {
    setTableColumns(columns)
    getOrgList()
    initDate()
    queryTableData()
    // 添加 resize 事件监听器
    window.addEventListener('resize', handleResize)
    // 清除事件监听器，防止内存泄漏
    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])
  const handleResize = () => {
    initHeight()
  }

  useEffect(() => {
    initHeight()
  }, [(document.querySelector('.percapita_Income_table .ant-table-header') || {})['offsetHeight']])

  useEffect(() => {
    initHeight()
  }, [tableData])

  useEffect(() => {
    if (isReset > 0) {
      queryTableData()
    }
  }, [isReset])

  useEffect(() => {
    if (pagination?.total > 0) {
      queryTableData()
    }
  }, [pagination.pageNum, pagination.pageSize])

  const initHeight = () => {
    const headerHeight =
      (document.querySelector('.percapita_Income_table .ant-table-header') || {})['offsetHeight'] ||
      0
    const pageHeight =
      (document.querySelector('.percapita_Income_table .ant-table-pagination') || {})[
        'offsetHeight'
      ] || 26
    if (headerHeight && pageHeight) {
      setHeight(
        contentRef.current?.offsetHeight -
          (tableTopRef.current.offsetHeight + headerHeight + pageHeight)
      )
    }
  }

  const getOrgList = async () => {
    const [error, res] = await getEnumType({ code: '1010', tag: 1 })
    if (error) {
      message.error(res?.DATA || res?.MESSAGE || '调用失败')
      return
    }
    if (res.STATUS === '0000') {
      setOrgList(res.DATA)
    } else {
      message.error(res?.MESSAGE || res?.DATA || '调用失败')
    }
  }

  // 查询表格数据
  const queryTableData = async () => {
    const values = formRef.getFieldsValue()
    setTableLoading(true)
    const [error, res] = await getPerIncomeProfit({
      ...values,
      cycleId: values?.cycleId?.format('YYYYMM'),
      ...pagination
    })
    setTableLoading(false)
    if (error) {
      message.error(res?.DATA || res?.MESSAGE || '调用失败')
      return
    }
    if (res.STATUS === '0000') {
      const {
        DATA: { data }
      } = res
      const dataWithKeys = data.map((item, index) => ({
        ...item,
        key: item.id || index // 使用数据中的唯一字段作为 key，或回退到索引
      }))
      setTableData(dataWithKeys)
      setPagination({
        ...pagination,
        total: res.DATA?.totalCount
      })
    } else {
      message.error(res?.MESSAGE || res?.DATA || '调用失败')
    }
  }

  // 初始化查询条件
  const initDate = () => {
    const date = dayjs().subtract(1, 'month')
    formRef.setFieldsValue({
      cycleId: date
    })
  }

  // 点击查询
  const onFormFinish: FormProps<FieldType>['onFinish'] = values => {
    console.log('Success:', values)
    queryTableData()
  }
  // 点击导出
  const exportToExcelFun = async () => {
    try {
      // 调用接口获取 Blob 文件数据
      openNotification('正在导出', 0, 'loading')
      const values = formRef.getFieldsValue()
      const response = await exportPerIncomeProfit({
        ...values,
        cycleId: values?.cycleId?.format('YYYYMM'),
        orgaId: values?.cityId
        // ...pagination
      })
      downFile(response)
    } catch (error) {
      openNotification('导出失败', 1, 'error')
      console.error('Download failed:', error)
    }

    // fetch('/efop/file/人员统计报表（个人）.xls', {
    //   method: 'GET',
    //   headers: {
    //     'Content-Type': 'application/vnd.ms-excel' // MIME 类型
    //   },
    //   credentials: 'include' // 如果需要发送 cookie
    // })
    //   .then(response => {
    //     if (!response.ok) {
    //       throw new Error('Network response was not ok')
    //     }
    //     return response.blob() // 将响应转换为 Blob
    //   })
    //   .then(blob => {
    //     const link = document.createElement('a')
    //     link.href = window.URL.createObjectURL(blob) // 创建 Blob URL
    //     link.download = '人员统计报表（个人）.xls' // 下载文件时显示的文件名
    //     link.click() // 模拟点击下载
    //   })
    //   .catch(error => {
    //     console.error('Download error:', error)
    //   })
  }
  // 查询重置
  const onReset = () => {
    const newReset = isReset + 1
    formRef.resetFields()
    initDate()
    setIsReset(newReset)
  }

  // 切换分页
  const onChangePage = page => {
    const newPage = {
      total: page?.total,
      pageNum: page?.current,
      pageSize: page?.pageSize
    }
    setPagination(newPage)
  }

  const filter = (input, option) =>
    (option?.enumName ?? '').toLowerCase().includes(input.toLowerCase()?.trim())

  return (
    <div className={`${'pt-[0.5rem] px-[0rem] h-full flex flex-col'} ${styles.employment_page}`}>
      <div ref={topRef} className={'bg-white pt-2 px-8 mb-2'}>
        <Form
          form={formRef}
          initialValues={{
            tag: ''
          }}
          onFinish={onFormFinish}
          // onFinishFailed={onFinishFailed}
          autoComplete='off'
        >
          <Row
            gutter={24}
            // justify="end"
          >
            <Col span={6}>
              <Form.Item
                label='月份'
                name='cycleId'
                wrapperCol={{ span: 20 }}
                className='mb-[0.5rem]'
              >
                <DatePicker className='w-full' allowClear={false} picker='month' />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                label='单位'
                name='cityId'
                wrapperCol={{ span: 20 }}
                className='mb-[0.5rem]'
              >
                <Select
                  placeholder={'请选择'}
                  className='w-full'
                  allowClear={true}
                  showSearch
                  filterOption={filter}
                  options={orgList}
                  fieldNames={{
                    value: 'enumId',
                    label: 'enumName'
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item labelCol={{ span: 0 }} wrapperCol={{ span: 24 }} className='mb-[0.5rem]'>
                <Space size='small'>
                  <Button type='primary' htmlType='submit'>
                    查询
                  </Button>
                  <Button htmlType='button' onClick={() => onReset()}>
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>
      <div
        ref={contentRef}
        className='relative bg-white px-5 pt-2.5'
        style={{ height: `calc(100% - ${topRef.current?.offsetHeight + 15}px)` }}
      >
        <div
          ref={tableTopRef}
          className={`${'flex justify-between items-center overflow-hidden mb-2'} ${styles.animation_box} ${showTitle ? 'h-[1.8rem]' : 'h-0'}`}
        >
          <div className={'flex '}>
            {showTitle ? (
              <FullscreenExitOutlined
                className={`${styles.shousuo_icon} text-[1rem]`}
                onClick={() => {
                  setShowTitle(false)
                  setTimeout(() => {
                    initHeight()
                  }, 200)
                }}
              />
            ) : (
              <FullscreenOutlined
                className={`${styles.shousuo_icon} ${'absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10'}`}
                onClick={() => {
                  setShowTitle(true)
                  setTimeout(() => {
                    initHeight()
                  }, 200)
                }}
              />
            )}
            <div className='font-bold text-[0.8rem] ml-3'>
              数据列表
              <span className='text-[0.7rem]' style={{ color: '#939393' }}>
                （单位：万元）
              </span>
            </div>
          </div>
          <Button danger ghost icon={<DownloadOutlined />} onClick={() => exportToExcelFun()}>
            导出
          </Button>
        </div>
        <ResizableTable
          className='percapita_Income_table'
          rowClassName={(_, i) => (i % 2 === 1 ? `customRow odd` : `customRow even`)}
          columns={tableColumns}
          dataSource={tableData}
          loading={tableLoading}
          bordered
          scroll={{
            // x: 'max-content',
            y: `calc(${height}px - 0.625rem - 1.6rem - 0.5rem)`
          }}
          onChange={onChangePage}
          pagination={{
            ...pagination,
            total: pagination?.total,
            showSizeChanger: true,
            showQuickJumper: true,
            pageSizeOptions: ['10', '20', '50']
          }}
        />
      </div>
    </div>
  )
}
export default ReportPer
