import {
  <PERSON><PERSON>,
  Col,
  DatePicker,
  DatePickerProps,
  Form,
  FormProps,
  message,
  Row,
  Space,
  Tooltip,
  Cascader
} from 'antd'
import dayjs from 'dayjs'
import { DownloadOutlined, FullscreenExitOutlined, FullscreenOutlined } from '@ant-design/icons'
import { useEffect, useRef, useState } from 'react'
import { useRequest } from '@/hooks'
import { downFile, openNotification } from '@/utils/down.tsx'
import styles from '../Index.module.scss'
import indicatorsService from '@/pages/salary/salarIndicators/service.ts'
import { orgOption } from '@/pages/effect/employment/assest.ts'
import ResizableTable from '@/components/resizeTable/index.jsx'

type FieldType = {
  month?: string
  unit?: string
  curPost?: string
}

const ReportPer: React.FC = () => {
  const columns = [
    {
      title: '单位',
      key: 'cityName',
      dataIndex: 'cityName',
      align: 'center',
      fixed: 'left',
      width: 130,
      children: [],
      render: (text: string, _record: orgOption) => (
        <Tooltip title={text}>
          <div className={styles.over_ellipsis}>{text}</div>
        </Tooltip>
      )
    },
    {
      title: '部室/营服中心',
      key: 'orgName',
      dataIndex: 'orgName',
      align: 'center',
      fixed: 'left',
      width: 160,
      children: [],
      render: (text: string, _record: orgOption) => (
        <Tooltip title={text}>
          <div className={styles.over_ellipsis}>{text}</div>
        </Tooltip>
      )
    },
    {
      title: '员工编号',
      key: 'staffId',
      dataIndex: 'staffId',
      align: 'center',
      fixed: 'left',
      width: 100,
      children: []
    },
    {
      title: '员工姓名',
      key: 'staffName',
      dataIndex: 'staffName',
      align: 'center',
      fixed: 'left',
      width: 100,
      children: []
    },
    {
      title: '单元名称',
      key: 'unitName',
      dataIndex: 'unitName',
      align: 'center',
      fixed: 'left',
      width: 100,
      children: [],
      render: (text: string, _record: orgOption) => (
        <Tooltip title={text}>
          <div className={styles.over_ellipsis}>{text}</div>
        </Tooltip>
      )
    },
    {
      title: '年度考评结果',
      key: 'name',
      dataIndex: 'name',
      align: 'center',
      children: [
        {
          title: '考评价结果',
          key: 'yearEvalVal2023',
          dataIndex: 'yearEvalVal2023',
          align: 'center',
          width: 100,
          children: []
        },
        {
          title: 'KPI得分',
          key: 'yearKpi2023',
          dataIndex: 'yearKpi2023',
          align: 'center',
          width: 100,
          children: []
        },
        {
          title: '单元名称',
          key: 'yearUnit2023',
          dataIndex: 'yearUnit2023',
          align: 'center',
          width: 100,
          children: [],
          render: (text: string, _record: orgOption) => (
            <Tooltip title={text}>
              <div className={styles.over_ellipsis}>{text}</div>
            </Tooltip>
          )
        },
        {
          title: '单位',
          key: 'yearOrgName2023',
          dataIndex: 'yearOrgName2023',
          align: 'center',
          width: 100,
          children: [],
          render: (text: string, _record: orgOption) => (
            <Tooltip title={text}>
              <div className={styles.over_ellipsis}>{text}</div>
            </Tooltip>
          )
        },
        {
          title: '年考评价结果',
          key: 'yearEvalVal2024',
          dataIndex: 'yearEvalVal2024',
          align: 'center',
          width: 100,
          children: []
        },
        {
          title: 'KPI得分',
          key: 'yearKpi2024',
          dataIndex: 'yearKpi2024',
          align: 'center',
          width: 100,
          children: []
        },
        {
          title: '单元名称',
          key: 'yearUnit2024',
          dataIndex: 'yearUnit2024',
          align: 'center',
          width: 100,
          children: [],
          render: (text: string, _record: orgOption) => (
            <Tooltip title={text}>
              <div className={styles.over_ellipsis}>{text}</div>
            </Tooltip>
          )
        },
        {
          title: '单位',
          key: 'yearOrgName2024',
          dataIndex: 'yearOrgName2024',
          align: 'center',
          width: 100,
          children: [],
          render: (text: string, _record: orgOption) => (
            <Tooltip title={text}>
              <div className={styles.over_ellipsis}>{text}</div>
            </Tooltip>
          )
        }
      ]
    }
  ]

  const topRef = useRef(null)
  const contentRef = useRef(null)
  const tableTopRef = useRef(null)
  const [formRef] = Form.useForm()
  // const [cascaderSelected, setCascaderSelected] = useState<any>([])
  const [showTitle, setShowTitle] = useState<boolean>(true)
  const [height, setHeight] = useState(0)
  const [monthDate, setMonthDate] = useState<any>('')
  const [tableData, setTableData] = useState<any>([])
  const [tableLoading, setTableLoading] = useState<boolean>(false)
  const [tableColumns, setTableColumns] = useState<any>(columns)
  const [isReset, setIsReset] = useState<number>(0)
  const [orgList, setOrgList] = useState<any>([])
  const [pagination, setPagination] = useState({
    total: 0,
    pageNum: 1,
    pageSize: 50
  })
  const { runAsync: getStatEvalYearMonth } = useRequest(indicatorsService.getStatEvalYearMonth, {
    manual: true
  })
  const { runAsync: exportStatEvalYearMonth } = useRequest(
    indicatorsService.exportStatEvalYearMonth,
    {
      manual: true
    }
  )
  const { runAsync: build4LevelOrgTree2 } = useRequest(indicatorsService.build4LevelOrgTree2, {
    manual: true
  })

  useEffect(() => {
    // getEnumTypes()
    initDate()
    getOrgList()
    queryTableData()
    // 添加 resize 事件监听器
    window.addEventListener('resize', handleResize)
    // 清除事件监听器，防止内存泄漏
    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])
  const handleResize = () => {
    initHeight()
  }

  useEffect(() => {
    if (monthDate) {
      initColumns()
    }
  }, [monthDate])

  useEffect(() => {
    initHeight()
  }, [(document.querySelector('.assessmentResults_table .ant-table-header') || {})['offsetHeight']])

  useEffect(() => {
    initHeight()
  }, [tableData])

  useEffect(() => {
    if (isReset > 0) {
      queryTableData()
    }
  }, [isReset])

  useEffect(() => {
    if (pagination?.total > 0) {
      queryTableData()
    }
  }, [pagination.pageNum, pagination.pageSize])

  const initHeight = () => {
    const headerHeight =
      (document.querySelector('.assessmentResults_table .ant-table-header') || {})[
        'offsetHeight'
      ] || 0
    const pageHeight =
      (document.querySelector('.assessmentResults_table .ant-table-pagination') || {})[
        'offsetHeight'
      ] || 26
    if (headerHeight && pageHeight) {
      setHeight(
        contentRef.current?.offsetHeight -
          (tableTopRef.current.offsetHeight + headerHeight + pageHeight)
      )
    }
  }

  const getOrgList = async () => {
    const [error, res] = await build4LevelOrgTree2({
      monthId: dayjs().format('YYYYMM'),
      tag: '1'
    })
    if (error) {
      message.error(res?.DATA || res?.MESSAGE || '调用失败')
      return
    }
    if (res.STATUS === '0000') {
      setOrgList(res.DATA)
    } else {
      message.error(res?.MESSAGE || res?.DATA || '调用失败')
    }
  }

  // 查询表格数据
  const queryTableData = async () => {
    const values = formRef.getFieldsValue()
    const [cityId] = values.cityId ? values.cityId.slice(-1) : [undefined]
    setTableLoading(true)
    const [error, res] = await getStatEvalYearMonth({
      // ...values,
      cycleId: values?.cycleId?.format('YYYY'),
      cityId,
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize
      // ...pagination
    })
    setTableLoading(false)
    if (error) {
      message.error(res?.DATA || res?.MESSAGE || '调用失败')
      return
    }
    if (res.STATUS === '0000') {
      const {
        DATA: { data }
      } = res
      const dataWithKeys = data.map((item, index) => ({
        ...item,
        key: item.id || index // 使用数据中的唯一字段作为 key，或回退到索引
      }))
      setTableData(dataWithKeys)
      setPagination({
        ...pagination,
        total: res.DATA?.totalCount
      })
    } else {
      message.error(res?.MESSAGE || res?.DATA || '调用失败')
    }
  }

  // 初始化查询条件
  const initDate = () => {
    const date = dayjs()
    formRef.setFieldsValue({
      cycleId: date
    })
    setMonthDate(date)
  }

  const initColumns = () => {
    const date = dayjs(monthDate)
    // const month = date.month();
    const year = date.year()
    columns[columns.findIndex(item => item?.title === '年度考评结果')].children[0].title =
      `${year - 1}年考评价结果`
    columns[columns.findIndex(item => item?.title === '年度考评结果')].children[4].title =
      `${year}年考评价结果`
    const monthColumn = {
      title: '月度考评结果',
      key: 'name',
      dataIndex: 'name',
      align: 'center',
      children: []
    }
    for (let i = 12; i >= 0; i--) {
      const name = `${date.endOf('year').subtract(i, 'month').format('YYYYMM')}`
      monthColumn.children.push(
        {
          title: name,
          key: `monthEvalVal${i === 12 ? '2023' : '2024'}${name?.slice(4)}`,
          dataIndex: `monthEvalVal${i === 12 ? '2023' : '2024'}${name?.slice(4)}`,
          align: 'center',
          width: 100,
          children: []
        },
        {
          title: 'KPI得分',
          key: `monthKpi${i === 12 ? '2023' : '2024'}${name?.slice(4)}`,
          dataIndex: `monthKpi${i === 12 ? '2023' : '2024'}${name?.slice(4)}`,
          align: 'center',
          width: 100,
          children: []
        }
      )
    }
    setTableColumns([...columns, monthColumn])
  }

  // 点击查询
  const onFormFinish: FormProps<FieldType>['onFinish'] = values => {
    console.log('Success:', values)
    queryTableData()
  }
  // 点击导出
  const exportToExcelFun = async () => {
    try {
      // 调用接口获取 Blob 文件数据
      openNotification('正在导出', 0, 'loading')
      const values = formRef.getFieldsValue()
      const [cityId] = values.cityId ? values.cityId.slice(-1) : [undefined]
      const response = await exportStatEvalYearMonth({
        // ...values,
        cycleId: values?.cycleId?.format('YYYY'),
        orgaId: cityId
      })
      downFile(response)
    } catch (error) {
      openNotification('导出失败', 1, 'error')
      console.error('Download failed:', error)
    }
  }
  // 查询重置
  const onReset = () => {
    const newReset = isReset + 1
    formRef.resetFields()
    initDate()
    setIsReset(newReset)
  }

  // 切换分页
  const onChangePage = page => {
    const newPage = {
      total: page?.total,
      pageNum: page?.current,
      pageSize: page?.pageSize
    }
    setPagination(newPage)
  }

  // 切换月份选择
  const onMonthChange: DatePickerProps['onChange'] = (date, dateString) => {
    console.log(date, dateString)
    setMonthDate(date)
  }

  return (
    <div className={`${'pt-[0.5rem] px-[0rem] h-full flex flex-col'} ${styles.employment_page}`}>
      <div ref={topRef} className={'bg-white pt-2 px-8 mb-2'}>
        <Form
          form={formRef}
          initialValues={{
            tag: ''
          }}
          onFinish={onFormFinish}
          // onFinishFailed={onFinishFailed}
          autoComplete='off'
        >
          <Row
            gutter={24}
            // justify="end"
          >
            <Col span={6}>
              <Form.Item
                label='年份'
                name='cycleId'
                wrapperCol={{ span: 20 }}
                className='mb-[0.5rem]'
              >
                <DatePicker
                  className='w-full'
                  allowClear={false}
                  onChange={onMonthChange}
                  picker='year'
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                label='单位'
                name='cityId'
                wrapperCol={{ span: 20 }}
                className='mb-[0.5rem]'
              >
                {/* <Select
                  placeholder={'请选择'}
                  className='w-full'
                  allowClear={true}
                  showSearch
                  filterOption={filter}
                  options={orgList}
                  fieldNames={{
                    value: 'enumId',
                    label: 'enumName'
                  }}
                /> */}
                <Cascader
                  style={{ width: '75%' }}
                  allowClear={true}
                  changeOnSelect
                  expandTrigger='hover'
                  displayRender={labels => labels[labels.length - 1]}
                  options={orgList}
                  fieldNames={{
                    value: 'orgId',
                    label: 'orgName',
                    children: 'children'
                  }}
                  placeholder='请选择'
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item labelCol={{ span: 0 }} wrapperCol={{ span: 24 }} className='mb-[0.5rem]'>
                <Space size='small'>
                  <Button type='primary' htmlType='submit'>
                    查询
                  </Button>
                  <Button htmlType='button' onClick={() => onReset()}>
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>
      <div
        ref={contentRef}
        className='relative bg-white px-5 pt-2.5'
        style={{ height: `calc(100% - ${topRef.current?.offsetHeight + 15}px)` }}
      >
        <div
          ref={tableTopRef}
          className={`${'flex justify-between items-center overflow-hidden mb-2'} ${styles.animation_box} ${showTitle ? 'h-[1.6rem]' : 'h-0'}`}
        >
          <div className={'flex '}>
            {showTitle ? (
              <FullscreenExitOutlined
                className={`${styles.shousuo_icon} text-[1rem]`}
                onClick={() => {
                  setShowTitle(false)
                  setTimeout(() => {
                    initHeight()
                  }, 200)
                }}
              />
            ) : (
              <FullscreenOutlined
                className={`${styles.shousuo_icon} ${'absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10'}`}
                onClick={() => {
                  setShowTitle(true)
                  setTimeout(() => {
                    initHeight()
                  }, 200)
                }}
              />
            )}
            <div className='font-bold text-[0.8rem] ml-3'>数据列表</div>
          </div>
          <Button danger ghost icon={<DownloadOutlined />} onClick={() => exportToExcelFun()}>
            导出
          </Button>
        </div>
        <ResizableTable
          className='assessmentResults_table'
          rowClassName={(_, i) => (i % 2 === 1 ? `customRow odd` : `customRow even`)}
          columns={tableColumns}
          dataSource={tableData}
          loading={tableLoading}
          bordered
          scroll={{
            // x: 'max-content',
            y: `calc(${height}px - 0.625rem - 1.6rem - 0.5rem)`
          }}
          onChange={onChangePage}
          pagination={{
            ...pagination,
            total: pagination?.total,
            showSizeChanger: true,
            showQuickJumper: true,
            pageSizeOptions: ['10', '20', '50']
          }}
        />
      </div>
    </div>
  )
}
export default ReportPer
