import {
  But<PERSON>,
  Col,
  DatePicker,
  Form,
  FormProps,
  message,
  Row,
  Select,
  Space,
  Tooltip
} from 'antd'
import dayjs from 'dayjs'
import { DownloadOutlined, FullscreenExitOutlined, FullscreenOutlined } from '@ant-design/icons'
import { useEffect, useRef, useState } from 'react'
import { useRequest } from '@/hooks'
import { Enum } from '@/pages/position/people/interface.ts'
import { downFile, openNotification } from '@/utils/down.tsx'
import styles from '../Index.module.scss'
import indicatorsService from '@/pages/salary/salarIndicators/service.ts'
import { orgOption } from '@/pages/effect/employment/assest.ts'
import ResizableTable from '@/components/resizeTable/index.jsx'
import { formatMoney } from '@/hooks/format'

type FieldType = {
  month?: string
  unit?: string
  curPost?: string
}

const ReportPer: React.FC = () => {
  const columns = [
    {
      title: '单位',
      key: 'cityName',
      dataIndex: 'cityName',
      align: 'center',
      fixed: 'left',
      width: 120,
      children: [],
      render: (text: string, _record: orgOption) => (
        <Tooltip title={text}>
          <div className={styles.over_ellipsis}>{text}</div>
        </Tooltip>
      )
    },
    {
      title: '部室/营服中心',
      key: 'orgName',
      dataIndex: 'orgName',
      align: 'center',
      fixed: 'left',
      width: 160,
      children: [],
      render: (text: string, _record: orgOption) => (
        <Tooltip title={text}>
          <div className={styles.over_ellipsis}>{text}</div>
        </Tooltip>
      )
    },
    // {
    //   title: '月份',
    //   key: 'cycleId',
    //   dataIndex: 'cycleId',
    //   align: 'center',
    //   fixed: 'left',
    //   width: 50,
    //   children: []
    // },
    {
      title: '岗位',
      key: 'position',
      dataIndex: 'position',
      align: 'center',
      fixed: 'left',
      width: 75,
      children: []
    },
    {
      title: '当月',
      key: 'level',
      dataIndex: 'level',
      align: 'center',
      width: 115,
      children: [
        {
          title: '薪酬最高',
          key: 'monthSalMax',
          dataIndex: 'monthSalMax',
          align: 'center',
          children: [],
          width: 70,
          render: text => <span>{formatMoney(text)}</span>
        },
        {
          title: '薪酬最低',
          key: 'monthSalMin',
          dataIndex: 'monthSalMin',
          align: 'center',
          children: [],
          width: 70,
          render: text => <span>{formatMoney(text)}</span>
        },
        {
          title: '平均薪酬',
          key: 'monthSalAval',
          dataIndex: 'monthSalAval',
          align: 'center',
          children: [],
          width: 70,
          render: text => <span>{formatMoney(text)}</span>
        },
        {
          title: '平均绩效',
          key: 'monthAchieveAval',
          dataIndex: 'monthAchieveAval',
          align: 'center',
          children: [],
          width: 70,
          render: text => <span>{formatMoney(text)}</span>
        }
      ]
    },
    {
      title: '全年累计',
      key: 'level',
      dataIndex: 'level',
      align: 'center',
      width: 115,
      children: [
        {
          title: '考评优秀平均绩效',
          key: 'yearLv1AchieveAval',
          dataIndex: 'yearLv1AchieveAval',
          align: 'center',
          children: [],
          width: 70,
          render: text => <span>{formatMoney(text)}</span>
        },
        {
          title: '考评良好平均绩效',
          key: 'yearLv2AchieveAval',
          dataIndex: 'yearLv2AchieveAval',
          align: 'center',
          children: [],
          width: 70,
          render: text => <span>{formatMoney(text)}</span>
        },
        {
          title: '考评称职平均绩效',
          key: 'yearLv3AchieveAval',
          dataIndex: 'yearLv3AchieveAval',
          align: 'center',
          children: [],
          width: 70,
          render: text => <span>{formatMoney(text)}</span>
        },
        {
          title: '最高',
          key: 'yearMax',
          dataIndex: 'yearMax',
          align: 'center',
          children: [],
          width: 70,
          render: text => <span>{formatMoney(text)}</span>
        },
        {
          title: '最低',
          key: 'yearMin',
          dataIndex: 'yearMin',
          align: 'center',
          children: [],
          width: 70,
          render: text => <span>{formatMoney(text)}</span>
        },
        {
          title: '绩优绩平',
          key: 'yearGoodRate',
          dataIndex: 'yearGoodRate',
          align: 'center',
          children: [],
          width: 70,
          render: (text, _) => {
            return <span style={{ color: text > 1.4 ? '#ff4d4f' : 'inherit' }}>{text}</span>
          }
        },
        {
          title: '月人均绩效',
          key: 'yearAchieveAval',
          dataIndex: 'yearAchieveAval',
          align: 'center',
          children: [],
          width: 70,
          render: text => <span>{formatMoney(text)}</span>
        }
      ]
    }
  ]

  const topRef = useRef(null)
  const contentRef = useRef(null)
  const tableTopRef = useRef(null)
  const [formRef] = Form.useForm()
  // const [cascaderSelected, setCascaderSelected] = useState<any>([])
  const [height, setHeight] = useState(0)
  const [tableData, setTableData] = useState<any>([])
  const [tableLoading, setTableLoading] = useState<boolean>(false)
  const [tableColumns, setTableColumns] = useState<any>(columns)
  const [postList, setPostList] = useState<Enum[]>([])
  const [isReset, setIsReset] = useState<number>(0)
  const [orgList, setOrgList] = useState<any>([])
  const [showTitle, setShowTitle] = useState<boolean>(true)
  const [pagination, setPagination] = useState({
    total: 0,
    pageNum: 1,
    pageSize: 50
  })

  const { runAsync: getEnumType } = useRequest(indicatorsService.getEnumType, {
    manual: true
  })
  const { runAsync: getEmployeePerPag } = useRequest(indicatorsService.getIdxPosSalAnalysis, {
    manual: true
  })
  const { runAsync: exportEmployeePerExcel } = useRequest(
    indicatorsService.exportIdxPosSalAnalysis,
    {
      manual: true
    }
  )

  useEffect(() => {
    setTableColumns(columns)
    getEnumTypes()
    initDate()
    queryTableData()
    // 添加 resize 事件监听器
    window.addEventListener('resize', handleResize)
    // 清除事件监听器，防止内存泄漏
    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])

  const handleResize = () => {
    initHeight()
  }

  useEffect(() => {
    initHeight()
  }, [
    (document.querySelector('.compensation_analysis_table .ant-table-header') || {})['offsetHeight']
  ])

  useEffect(() => {
    initHeight()
  }, [tableData])

  useEffect(() => {
    if (isReset > 0) {
      queryTableData()
    }
  }, [isReset])

  useEffect(() => {
    if (pagination?.total > 0) {
      queryTableData()
    }
  }, [pagination.pageNum, pagination.pageSize])

  const initHeight = () => {
    const headerHeight =
      (document.querySelector('.compensation_analysis_table .ant-table-header') || {})[
        'offsetHeight'
      ] || 0
    const pageHeight =
      (document.querySelector('.compensation_analysis_table .ant-table-pagination') || {})[
        'offsetHeight'
      ] || 26
    if (headerHeight && pageHeight) {
      setHeight(
        contentRef.current?.offsetHeight -
          (tableTopRef.current.offsetHeight + headerHeight + pageHeight)
      )
    }
  }

  const getEnumTypes = async () => {
    const [[orgError, orgData], [postError, postData]] = await Promise.all([
      getEnumType({ code: '1010', tag: 1 }),
      getEnumType({ code: 'IDX_POS_SAL_ANALYSIS_POSITION' })
    ])
    if (orgError || postError) {
      return
    }
    if (orgData.STATUS === '0000') {
      setOrgList(orgData.DATA)
    }
    if (postData.STATUS === '0000') {
      setPostList(postData.DATA)
    }
  }

  // 查询表格数据
  const queryTableData = async () => {
    const values = formRef.getFieldsValue()
    setTableLoading(true)
    const [error, res] = await getEmployeePerPag({
      ...values,
      cycleId: values?.cycleId?.format('YYYYMM'),
      ...pagination
    })
    setTableLoading(false)
    if (error) {
      message.error(res?.DATA || res?.MESSAGE || '调用失败')
      return
    }
    if (res.STATUS === '0000') {
      const {
        DATA: { data }
      } = res
      const dataWithKeys = data.map((item, index) => ({
        ...item,
        key: item.id || index // 使用数据中的唯一字段作为 key，或回退到索引
      }))
      setTableData(dataWithKeys)
      setPagination({
        ...pagination,
        total: res.DATA?.totalCount
      })
    } else {
      message.error(res?.MESSAGE || res?.DATA || '调用失败')
    }
  }

  // 初始化查询条件
  const initDate = () => {
    const date = dayjs().subtract(1, 'month')
    formRef.setFieldsValue({
      cycleId: date
    })
  }

  // 点击查询
  const onFormFinish: FormProps<FieldType>['onFinish'] = values => {
    console.log('Success:', values)
    queryTableData()
  }
  // 点击导出
  const exportToExcelFun = async () => {
    try {
      // 调用接口获取 Blob 文件数据
      openNotification('正在导出', 0, 'loading')
      const values = formRef.getFieldsValue()
      const response = await exportEmployeePerExcel({
        ...values,
        cycleId: values?.cycleId?.format('YYYYMM'),
        orgaId: values?.cityId
        // ...pagination
      })
      downFile(response)
    } catch (error) {
      openNotification('导出失败', 1, 'error')
      console.error('Download failed:', error)
    }
  }
  // 查询重置
  const onReset = () => {
    const newReset = isReset + 1
    formRef.resetFields()
    initDate()
    setIsReset(newReset)
  }

  // 切换分页
  const onChangePage = page => {
    const newPage = {
      total: page?.total,
      pageNum: page?.current,
      pageSize: page?.pageSize
    }
    setPagination(newPage)
  }

  const onMonthChange = () => {}

  // 搜索过滤空格
  const filter = (input, option) =>
    (option?.enumName ?? '').toLowerCase().includes(input.toLowerCase()?.trim())
  const filter2 = (input, option) =>
    (option?.value ?? '').toLowerCase().includes(input.toLowerCase()?.trim())

  return (
    <div className={`${'pt-[0.5rem] px-[0rem] h-full flex flex-col'} ${styles.employment_page}`}>
      <div ref={topRef} className={'bg-white pt-2 px-8 mb-2'}>
        <Form
          form={formRef}
          initialValues={{
            tag: ''
          }}
          onFinish={onFormFinish}
          // onFinishFailed={onFinishFailed}
          autoComplete='off'
        >
          <Row
            gutter={24}
            // justify="end"
          >
            <Col span={6}>
              <Form.Item
                label='月份'
                name='cycleId'
                wrapperCol={{ span: 20 }}
                className='mb-[0.5rem]'
              >
                <DatePicker
                  className='w-full'
                  allowClear={false}
                  onChange={onMonthChange}
                  picker='month'
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                label='单位'
                name='cityId'
                wrapperCol={{ span: 20 }}
                className='mb-[0.5rem]'
              >
                <Select
                  placeholder={'请选择'}
                  className='w-full'
                  allowClear={true}
                  showSearch
                  filterOption={filter}
                  options={orgList}
                  fieldNames={{
                    value: 'enumId',
                    label: 'enumName'
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                label='岗位'
                name='position'
                wrapperCol={{ span: 20 }}
                className='mb-[0.5rem]'
              >
                {
                  // orgValue && orgValue[0] === '49757' ?
                  <Select
                    placeholder={'请选择'}
                    className='w-full'
                    allowClear={true}
                    showSearch
                    filterOption={filter2}
                  >
                    {
                      // prettier-ignore
                      postList.map(unit => {
                          const {enumName} = unit;
                          return <Select.Option key={enumName}
                                                value={enumName}>{enumName}</Select.Option>
                        })
                    }
                  </Select>
                  // <Input placeholder={'请输入'}/>
                }
              </Form.Item>
            </Col>

            <Col span={6}>
              <Form.Item labelCol={{ span: 0 }} wrapperCol={{ span: 24 }} className='mb-[0.5rem]'>
                <Space size='small'>
                  <Button type='primary' htmlType='submit'>
                    查询
                  </Button>
                  <Button htmlType='button' onClick={() => onReset()}>
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>
      <div
        ref={contentRef}
        className='relative bg-white px-5 pt-2.5'
        style={{ height: `calc(100% - ${topRef.current?.offsetHeight + 15}px)` }}
      >
        <div
          ref={tableTopRef}
          className={`${'flex justify-between items-center overflow-hidden mb-2'} ${styles.animation_box} ${showTitle ? 'h-[1.6rem]' : 'h-0'}`}
        >
          <div className={'flex '}>
            {showTitle ? (
              <FullscreenExitOutlined
                className={`${styles.shousuo_icon} text-[1rem]`}
                onClick={() => {
                  setShowTitle(false)
                  setTimeout(() => {
                    initHeight()
                  }, 200)
                }}
              />
            ) : (
              <FullscreenOutlined
                className={`${styles.shousuo_icon} ${'absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10'}`}
                onClick={() => {
                  setShowTitle(true)
                  setTimeout(() => {
                    initHeight()
                  }, 200)
                }}
              />
            )}
            <div className='font-bold text-[0.8rem] ml-3'>
              数据列表
              <span className='text-[0.7rem]' style={{ color: '#939393' }}>
                （单位：万元）
              </span>
            </div>
          </div>
          <Button danger ghost icon={<DownloadOutlined />} onClick={() => exportToExcelFun()}>
            导出
          </Button>
        </div>
        <ResizableTable
          className='compensation_analysis_table'
          rowClassName={(_, i) => (i % 2 === 1 ? `customRow odd` : `customRow even`)}
          columns={tableColumns}
          dataSource={tableData}
          loading={tableLoading}
          bordered
          scroll={{
            // x: 'max-content',
            y: `calc(${height}px - 0.625rem - 1.6rem - 0.5rem)`
          }}
          onChange={onChangePage}
          pagination={{
            ...pagination,
            total: pagination?.total,
            showSizeChanger: true,
            showQuickJumper: true,
            pageSizeOptions: ['10', '20', '50']
          }}
        />
      </div>
    </div>
  )
}
export default ReportPer
