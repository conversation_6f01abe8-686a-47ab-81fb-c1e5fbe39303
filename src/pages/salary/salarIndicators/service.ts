import request from '@/request'

const indicatorsService = {
  // 查询枚举
  getEnumType: params => {
    return request.post<any>('/zhyy/employee/getEnumType', params)
  },

  // 查询
  getPerIncomeProfit: params => {
    return request.get<any>('/zhyy/salary/perIncomeProfit/list', { params })
  },

  // 导出
  exportPerIncomeProfit: params => {
    return request.get<any>('/zhyy/salary/perIncomeProfit/exportExcel', {
      params,
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      },
      responseType: 'blob'
    })
  },

  // 查询
  getMarketExitRate: params => {
    return request.get<any>('/zhyy/salary/marketExitRate/list', { params })
  },

  // 导出
  exportMarketExitRate: params => {
    return request.get<any>('/zhyy/salary/marketExitRate/exportExcel', {
      params,
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      },
      responseType: 'blob'
    })
  },

  // 查询
  getIdxPosSalAnalysis: params => {
    return request.get<any>('/zhyy/salary/idxPosSalAnalysis/list', { params })
  },

  // 导出
  exportIdxPosSalAnalysis: params => {
    return request.get<any>('/zhyy/salary/idxPosSalAnalysis/exportExcel', {
      params,
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      },
      responseType: 'blob'
    })
  },

  // 查询
  getStatEvalYearMonth: params => {
    return request.get<any>('/zhyy/salary/statEvalYearMonth/list', { params })
  },

  // 导出
  exportStatEvalYearMonth: params => {
    return request.get<any>('/zhyy/salary/statEvalYearMonth/exportExcel', {
      params,
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      },
      responseType: 'blob'
    })
  },
  // 查询组织级联-2层
  build4LevelOrgTree2: params => {
    return request.post<any>('/zhyy/employee/org/build4LevelOrgTree2', params)
  }
}

export default indicatorsService
