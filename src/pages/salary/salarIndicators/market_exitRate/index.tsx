import { But<PERSON>, Col, DatePicker, Form, message, Row, Select, Space } from 'antd'
import dayjs from 'dayjs'
import { DownloadOutlined, FullscreenExitOutlined, FullscreenOutlined } from '@ant-design/icons'
import { useEffect, useRef, useState } from 'react'
import { useRequest } from '@/hooks'
import { downFile, openNotification } from '@/utils/down.tsx'
import styles from '../Index.module.scss'
import indicatorsService from '@/pages/salary/salarIndicators/service.ts'
import ResizableTable from '@/components/resizeTable/index.jsx'

const ReportPer: React.FC = () => {
  const columns = [
    // {
    //   title: '月份',
    //   key: 'cycleId',
    //   dataIndex: 'cycleId',
    //   align: 'center',
    //   fixed: 'left',
    //   width: 100,
    //   children: []
    // },
    {
      title: '单位',
      key: 'cityName',
      dataIndex: 'cityName',
      align: 'center',
      fixed: 'left',
      width: 300,
      children: []
      // onCell: (record:orgOption,rowIndex:string) => mergeCells(record?.unit,tableData,'unit',rowIndex),
    },
    {
      title: '市场化退出率',
      key: 'marketExitRate',
      dataIndex: 'marketExitRate',
      align: 'center',
      width: 150,
      children: []
    },
    {
      title: '市场化退出人数',
      key: 'marketExitPerson',
      dataIndex: 'marketExitPerson',
      align: 'center',
      width: 150,
      children: []
    },
    {
      title: '合同制人数',
      key: 'contractPerson',
      dataIndex: 'contractPerson',
      align: 'center',
      width: 150,
      children: []
    }
  ]

  const topRef = useRef(null)
  const contentRef = useRef(null)
  const tableTopRef = useRef(null)
  const [formRef] = Form.useForm()
  // const [cascaderSelected, setCascaderSelected] = useState<any>([])
  const [height, setHeight] = useState(0)
  const [tableData, setTableData] = useState<any>([])
  const [tableLoading, setTableLoading] = useState<boolean>(false)
  const [tableColumns, setTableColumns] = useState<any>(columns)
  const [isReset, setIsReset] = useState<number>(0)
  const [orgList, setOrgList] = useState<any>([])
  const [showTitle, setShowTitle] = useState<boolean>(true)
  const [pagination, setPagination] = useState({
    total: 0,
    pageNum: 1,
    pageSize: 50
  })

  const { runAsync: getEnumType } = useRequest(indicatorsService.getEnumType, {
    manual: true
  })
  const { runAsync: getMarketExitRate } = useRequest(indicatorsService.getMarketExitRate, {
    manual: true
  })
  const { runAsync: exportMarketExitRate } = useRequest(indicatorsService.exportMarketExitRate, {
    manual: true
  })

  useEffect(() => {
    setTableColumns(columns)
    getOrgList()
    initDate()
    queryTableData()
    // 添加 resize 事件监听器
    window.addEventListener('resize', handleResize)
    // 清除事件监听器，防止内存泄漏
    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])
  const handleResize = () => {
    initHeight()
  }

  useEffect(() => {
    initHeight()
  }, [(document.querySelector('.market_exitRate_table .ant-table-header') || {})['offsetHeight']])

  useEffect(() => {
    initHeight()
  }, [tableData])

  useEffect(() => {
    if (isReset > 0) {
      queryTableData()
    }
  }, [isReset])

  useEffect(() => {
    if (pagination?.total > 0) {
      queryTableData()
    }
  }, [pagination.pageNum, pagination.pageSize])

  const initHeight = () => {
    const headerHeight =
      (document.querySelector('.market_exitRate_table .ant-table-header') || {})['offsetHeight'] ||
      0
    const pageHeight =
      (document.querySelector('.market_exitRate_table .ant-table-pagination') || {})[
        'offsetHeight'
      ] || 26
    if (headerHeight && pageHeight) {
      setHeight(
        contentRef.current?.offsetHeight -
          (tableTopRef.current.offsetHeight + headerHeight + pageHeight)
      )
    }
  }

  const getOrgList = async () => {
    const [error, res] = await getEnumType({ code: '1010', tag: 1 })
    if (error) {
      message.error(res?.DATA || res?.MESSAGE || '调用失败')
      return
    }
    if (res.STATUS === '0000') {
      setOrgList(res.DATA)
    } else {
      message.error(res?.MESSAGE || res?.DATA || '调用失败')
    }
  }

  // 查询表格数据
  const queryTableData = async () => {
    const values = formRef.getFieldsValue()
    setTableLoading(true)
    const [error, res] = await getMarketExitRate({
      ...values,
      cycleId: values?.cycleId?.format('YYYYMM'),
      ...pagination
    })
    setTableLoading(false)
    if (error) {
      message.error(res?.DATA || res?.MESSAGE || '调用失败')
      return
    }
    if (res.STATUS === '0000') {
      const {
        DATA: { data }
      } = res
      const dataWithKeys = data.map((item, index) => ({
        ...item,
        key: item.id || index // 使用数据中的唯一字段作为 key，或回退到索引
      }))
      setTableData(dataWithKeys)
      setPagination({
        ...pagination,
        total: res.DATA?.totalCount
      })
    } else {
      message.error(res?.MESSAGE || res?.DATA || '调用失败')
    }
  }

  // 初始化查询条件
  const initDate = () => {
    const date = dayjs().subtract(1, 'month')
    formRef.setFieldsValue({
      cycleId: date
    })
  }

  // 点击查询
  const onFormFinish = values => {
    console.log('Success:', values)
    queryTableData()
  }
  // 点击导出
  const exportToExcelFun = async () => {
    try {
      // 调用接口获取 Blob 文件数据
      openNotification('正在导出', 0, 'loading')
      const values = formRef.getFieldsValue()
      const response = await exportMarketExitRate({
        ...values,
        cycleId: values?.cycleId?.format('YYYYMM'),
        orgaId: values?.cityId
      })
      downFile(response)
    } catch (error) {
      openNotification('导出失败', 1, 'error')
      console.error('Download failed:', error)
    }
  }
  // 查询重置
  const onReset = () => {
    const newReset = isReset + 1
    formRef.resetFields()
    initDate()
    setIsReset(newReset)
  }

  const filter = (input, option) =>
    (option?.enumName ?? '').toLowerCase().includes(input.toLowerCase()?.trim())

  // 切换分页
  const onChangePage = page => {
    const newPage = {
      total: page?.total,
      pageNum: page?.current,
      pageSize: page?.pageSize
    }
    setPagination(newPage)
  }

  const onMonthChange = () => {}

  return (
    <div className={`${'pt-[0.5rem] px-[0rem] h-full flex flex-col'} ${styles.employment_page}`}>
      <div ref={topRef} className={'bg-white pt-2 px-8 mb-2'}>
        <Form
          form={formRef}
          initialValues={{
            tag: ''
          }}
          onFinish={onFormFinish}
          // onFinishFailed={onFinishFailed}
          autoComplete='off'
        >
          <Row
            gutter={24}
            // justify="end"
          >
            <Col span={6}>
              <Form.Item
                label='月份'
                name='cycleId'
                wrapperCol={{ span: 20 }}
                className='mb-[0.5rem]'
              >
                <DatePicker
                  className='w-full'
                  allowClear={false}
                  onChange={onMonthChange}
                  picker='month'
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                label='单位'
                name='cityId'
                wrapperCol={{ span: 20 }}
                className='mb-[0.5rem]'
              >
                <Select
                  placeholder={'请选择'}
                  className='w-full'
                  allowClear={true}
                  showSearch
                  filterOption={filter}
                  options={orgList}
                  fieldNames={{
                    value: 'enumId',
                    label: 'enumName'
                  }}
                />
              </Form.Item>
            </Col>

            <Col span={6}>
              <Form.Item labelCol={{ span: 0 }} wrapperCol={{ span: 24 }} className='mb-[0.5rem]'>
                <Space size='small'>
                  <Button type='primary' htmlType='submit'>
                    查询
                  </Button>
                  <Button htmlType='button' onClick={() => onReset()}>
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>
      <div
        ref={contentRef}
        className='relative bg-white px-5 pt-2.5'
        style={{ height: `calc(100% - ${topRef.current?.offsetHeight + 15}px)` }}
      >
        <div
          ref={tableTopRef}
          className={`${'flex justify-between items-center overflow-hidden mb-2'} ${styles.animation_box} ${showTitle ? 'h-[1.6rem]' : 'h-0'}`}
        >
          <div className={'flex '}>
            {showTitle ? (
              <FullscreenExitOutlined
                className={`${styles.shousuo_icon} text-[1rem]`}
                onClick={() => {
                  setShowTitle(false)
                  setTimeout(() => {
                    initHeight()
                  }, 200)
                }}
              />
            ) : (
              <FullscreenOutlined
                className={`${styles.shousuo_icon} ${'absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10'}`}
                onClick={() => {
                  setShowTitle(true)
                  setTimeout(() => {
                    initHeight()
                  }, 200)
                }}
              />
            )}
            <div className='font-bold text-[0.8rem] ml-3'>数据列表</div>
          </div>
          <Button danger ghost icon={<DownloadOutlined />} onClick={() => exportToExcelFun()}>
            导出
          </Button>
        </div>
        <ResizableTable
          className='market_exitRate_table'
          rowClassName={(_, i) => (i % 2 === 1 ? `customRow odd` : `customRow even`)}
          columns={tableColumns}
          dataSource={tableData}
          loading={tableLoading}
          bordered
          scroll={{
            // x: 'max-content',
            y: `calc(${height}px - 0.625rem - 1.6rem - 0.5rem)`
          }}
          onChange={onChangePage}
          pagination={{
            ...pagination,
            total: pagination?.total,
            showSizeChanger: true,
            showQuickJumper: true,
            pageSizeOptions: ['10', '20', '50']
          }}
        />
      </div>
    </div>
  )
}
export default ReportPer
