import { useEffect, useRef, useState } from 'react'
import { Form, Row, Col, Button, FormProps, Modal, Upload, message, Space, Popconfirm, Cascader, DatePicker} from 'antd'
import { UploadOutlined, DownloadOutlined, InboxOutlined, FullscreenExitOutlined, FullscreenOutlined} from '@ant-design/icons'
import SvgIcon from '@/components/SvgIcons'
import performanceService from '../service.ts'
import { useRequest } from '@/hooks'
import styles from './Index.module.scss'
import type { TableColumnsType } from 'antd'
import { FieldType } from '../interface'
import { downFile, openNotification } from '@/utils/down.tsx'
const { Dragger } = Upload
import dayjs from 'dayjs'
import ResizableTable from '@/components/resizeTable/index.jsx'
import { useUserStore } from '@/store/user.ts'

interface DataType {
  key: React.Key
  name: string
  age: number
  address: string
}

const data: DataType[] = []
for (let i = 0; i < 100; i++) {
  data.push({
    key: i,
    name: `<PERSON> ${i}`,
    age: 32,
    address: `London Park no. ${i}`
  })
}

const People: React.FC = () => {
  const [form] = Form.useForm()
  const topRef = useRef(null)
  const contentRef = useRef(null)
  const tableTopRef = useRef(null)
  const [height, setHeight] = useState(0)
  const [showTitle, setShowTitle] = useState<boolean>(true)
  const [tableLoading, setTableLoading] = useState<boolean>(false)
  const [upModalOpen, setOpModalOpen] = useState<boolean>(false)
  const [fileList, setFileList] = useState([])
  const [unitList, setUnitList] = useState<any[]>([])
  //const [typeList, setTypeList] = useState<Enum[]>([])
  const [tableData, setTableData] = useState<DataType[]>([])
  const { currentUser } = useUserStore()
  const [pagination, setPagination] = useState({
    total: 0,
    pageNum: 1,
    pageSize: 50
  })

  const { runAsync: getEnumType } = useRequest(performanceService.getEnumType, { manual: true })
  const { runAsync: getPointSalaryImport } = useRequest(performanceService.getPointSalaryImport, {
    manual: true
  })
  const { runAsync: downloadTemplate } = useRequest(
    performanceService.downloadTemplate,
    { manual: true }
  )

  const { runAsync: exportPointSalaryImport } = useRequest(performanceService.exportPointSalaryImport, {
    manual: true
  })
  const { runAsync: importPointSalaryImport } = useRequest(performanceService.importPointSalaryImport, {
    manual: true
  })
  const columns: TableColumnsType<any> = [
    {
      title: '业务账期',
      dataIndex: 'cycleId',
      key: 'cycleId',
      align: 'center',
      width: 100,
      fixed: 'left'
    },
    {
      title: '单位',
      dataIndex: 'cityName',
      key: 'cityName',
      align: 'center',
      width: 160,
    },
    {
      title: '岗位',
      dataIndex: 'position',
      key: 'position',
      align: 'center',
      width: 130,
    },
    {
      title: '积分',
      key: 'level',
      dataIndex: 'level',
      align: 'center',
      children: [
        {
          title: '服务积分单价',
          dataIndex: 'servicePointsPrice',
          key: 'servicePointsPrice',
          align: 'center',
          width: 130
        },
        {
          title: '动作积分单价',
          dataIndex: 'actionPointsPrice',
          key: 'actionPointsPrice',
          align: 'center',
          width: 130
        },
        {
          title: '价值积分单价-营销',
          dataIndex: 'valuePointsPrice',
          key: 'valuePointsPrice',
          align: 'center',
          width: 130
        },
        {
          title: '价值积分单价-交付',
          dataIndex: 'valuePointsPriceDeliver',
          key: 'valuePointsPriceDeliver',
          align: 'center',
          width: 130
        },
        {
          title: '价值积分单价-奖惩',
          dataIndex: 'valuePointsPriceRap',
          key: 'valuePointsPriceRap',
          align: 'center',
          width: 130
        },
        {
          title: '自定义积分单价',
          dataIndex: 'customPointsPrice',
          key: 'customPointsPrice',
          align: 'center',
          width: 130
        }
      ]
    }
  ]

  useEffect(() => { // 
    getEnumTypes()
    initDate()
    // 添加 resize 事件监听器
    window.addEventListener('resize', handleResize)
    // 清除事件监听器，防止内存泄漏
    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])
  const handleResize = () => {
    initHeight()
  }

  useEffect(() => {
    initHeight()
  }, [(document.querySelector('.pointSalaryImport_table .ant-table-header') || {})['offsetHeight']])

  const initHeight = () => {
    const headerHeight =
      (document.querySelector('.pointSalaryImport_table .ant-table-header') || {})['offsetHeight'] ||
      0
    const pageHeight =
      (document.querySelector('.pointSalaryImport_table .ant-table-pagination') || {})[
        'offsetHeight'
      ] || 26
    if (headerHeight && pageHeight) {
      setHeight(
        contentRef.current?.offsetHeight -
          (tableTopRef.current.offsetHeight + headerHeight + pageHeight)
      )
    }
  }

  const handleDownload = async (type: number) => {
    try {
      // 调用接口获取 Blob 文件数据
      openNotification('正在导出', 0, 'loading')
      let response = null
      if (type === 1) {
        response = await downloadTemplate({ templateId: 'SALARY_POINTS_AND_UNIT_PRICE' })
      } else if (type === 2) {
        const { loginDate, cityId, category, cycleId, empId } = form.getFieldsValue()
        const params = {
          beginTime: loginDate?.length > 0 ? dayjs(loginDate[0]).format('YYYY-MM-DD') : null,
          endTime: loginDate?.length > 0 ? dayjs(loginDate[1]).format('YYYY-MM-DD') : null,
          cycleId: cycleId ? dayjs(cycleId)?.format('YYYYMM') : '',
          cityId: cityId ? cityId[cityId?.length - 1] : null,
          category,
          empId
        }
        response = await exportPointSalaryImport(params)
      }
      downFile(response)
    } catch (error) {
      openNotification('导出失败', 1, 'error')
      console.error('Download failed:', error)
    }
  }

  // 点击查询
  const onFormFinish: FormProps<FieldType>['onFinish'] = values => {
    queryTableData({
      ...values
    })
  }

  const getEnumTypes = async () => {
    const [[unitError, unitData], [typeError]] = await Promise.all([
      getEnumType({ code: '1010', tag: 1 }),
      getEnumType({ code: 'EMPLOYEE_TRANSFER_CATEGORY' })
    ])
    if (unitError || typeError) {
      return
    }
    if (unitData.STATUS === '0000') {
      setUnitList(unitData.DATA?.filter(item => item?.orgId !== '49757'))
    }
    //if (typeData.STATUS === '0000') {
    //  setTypeList(typeData.DATA)
    //}
    const params = {
      ...form.getFieldsValue()
    }
    queryTableData(params)
  }

  const queryTableData = async params => {
    setTableLoading(true)
    const { loginDate, cityId, category, cycleId, empId } = params
    const param = {
      ...pagination,
      beginTime: loginDate?.length > 0 ? dayjs(loginDate[0]).format('YYYY-MM-DD') : null,
      endTime: loginDate?.length > 0 ? dayjs(loginDate[1]).format('YYYY-MM-DD') : null,
      cycleId: cycleId ? dayjs(cycleId)?.format('YYYYMM') : '',
      cityId: cityId ? cityId[cityId?.length - 1] : null,
      category,
      empId
    }
    const [error, res] = await getPointSalaryImport(param)
    setTableLoading(false)
    if (error) {
      message.error(res?.DATA || res?.MESSAGE || '调用失败')
      return
    }
    if (res.STATUS === '0000') {
      const {
        DATA: { data }
      } = res
      //  const data = res.data
      const dataWithKeys = data.map((item, index) => ({
        ...item,
        key: item.id || index // 使用数据中的唯一字段作为 key，或回退到索引
      }))
      setTableData(dataWithKeys)
      setPagination({
        ...pagination,
        total: res.DATA?.totalCount
      })
    } else {
      message.error(res?.MESSAGE || res?.DATA || '调用失败')
    }
  }

  const uploadFileFun = async () => {
    const formData = new FormData()
    fileList
      .map(file => file?.originFileObj)
      .forEach(file => {
        formData.append('file', file)
      })
    // formData.append('tag', '1')
    const [error, res] = await importPointSalaryImport(formData)
    if (error) {
      message.error(res?.DATA || res?.MESSAGE || '调用失败')
      return
    }
    if (res.STATUS === '0000') {
      const { loginDate, yearVal, cityId, category } = form.getFieldsValue()
      const params = {
        beginTime: loginDate.length > 0 ? dayjs(loginDate[0]).format('YYYY-MM-DD') : null,
        endTime: loginDate.length > 0 ? dayjs(loginDate[1]).format('YYYY-MM-DD') : null,
        yearVal: yearVal ? dayjs(yearVal).format('YYYY') : '',
        cityId: cityId[cityId?.length - 1],
        category
      }
      initHeight()
      queryTableData(params)
      setOpModalOpen(false)
      message.success(res?.DATA)
    } else {
      message.error(res?.MESSAGE || res?.DATA || '调用失败')
    }
  }

  // 上传
  const handleOk = () => {
    if (fileList?.length > 0) {
      uploadFileFun()
    } else {
      message.error('请先选择文件上传')
    }
  }

  const handleCancel = () => {
    setOpModalOpen(false)
  }

  // 查询重置
  const onReset = () => {
    form.resetFields()
    initDate()
  }

  // 切换分页
  const onChangePage = page => {
    const newPage = {
      total: page?.total,
      pageNum: page?.current,
      pageSize: page?.pageSize
    }
    setPagination(newPage)
  }

  useEffect(() => {
    if (pagination?.total > 0) {
      const params = {
        ...form.getFieldsValue()
      }
      queryTableData(params)
    }
  }, [pagination.pageNum, pagination.pageSize])

  // 初始化查询条件
  const initDate = () => {
    const date = dayjs().subtract(1, 'month')
    form.setFieldsValue({
      cycleId: date
    })
  }

  // 搜索过滤空格
  const filter = (input, option) =>
    (option[0]?.enumName ?? '').toLowerCase().includes(input.toLowerCase()?.trim())

  return (
    <div
      className={`${'pt-[0.5rem] px-[0rem] h-full flex flex-col'} ${styles.pointSalaryImport_page}`}
    >
      <>
        <div ref={topRef} className='bg-white pt-[0.5rem] px-[1.75rem] mb-[0.5rem]'>
          <Form
            form={form}
            labelCol={{ span: 6 }}
            onFinish={onFormFinish}
            initialValues={{
              loginDate: '',
              category: 'KOU_JING_TIAO_ZHENG',
              // cycleId: '',
              cityId: '',
              empId: ''
            }}
          >
            <Row gutter={24}>
              <Col span={6}>
                <Form.Item name='cycleId' label='账期' className='mb-[0.5rem]'>
                  <DatePicker className='w-full' picker='month' />
                </Form.Item>
              </Col>


              <Col span={5}>
                <Form.Item name='cityId' label='单位' className='mb-[0.5rem]'>
                  <Cascader
                    allowClear
                    changeOnSelect
                    expandTrigger='hover'
                    displayRender={labels => labels[labels.length - 1]}
                    options={unitList}
                    fieldNames={{
                      // value: 'orgId',
                      value: 'enumId',
                      label: 'enumName',
                      children: 'children'
                    }}
                    // showCheckedStrategy={Cascader.SHOW_CHILD}
                    // onChange={(value, selectedOptions) => {
                    //   console.log(value, selectedOptions)
                    //   setCascaderSelected(selectedOptions)
                    // }}
                    placeholder='请选择单位'
                    showSearch={{ filter }}
                    onSearch={value => console.log(value)}
                  />
                </Form.Item>
              </Col>
              
              <Col span={4}>
                <div className='text-right'>
                  <Space>
                    <Button type='primary' htmlType='submit'>
                      查询
                    </Button>
                    <Button onClick={() => onReset()}>重置</Button>
                  </Space>
                </div>
              </Col>
            </Row>
          </Form>
        </div>
        <div
          ref={contentRef}
          className='relative bg-white px-5 pt-2.5'
          style={{ height: `calc(100% - ${topRef.current?.offsetHeight + 15}px)` }}
        >
          <div
            ref={tableTopRef}
            className={`${'flex justify-between items-center overflow-hidden mb-[0.1rem]'} ${styles.animation_box} ${showTitle ? 'h-[1.8rem]' : 'h-0'}`}
          >
            <div className={'flex '}>
              {showTitle ? (
                <FullscreenExitOutlined
                  className={`${styles.shousuo_icon} text-[1rem]`}
                  onClick={() => {
                    setShowTitle(false)
                    setTimeout(() => {
                      initHeight()
                    }, 200)
                  }}
                />
              ) : (
                <FullscreenOutlined
                  className={`${styles.shousuo_icon} ${'absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10'}`}
                  onClick={() => {
                    setShowTitle(true)
                    setTimeout(() => {
                      initHeight()
                    }, 200)
                  }}
                />
              )}
              <div className='font-bold text-[0.8rem] ml-3'>数据列表</div>
            </div>
            <div className='flex gap-x-[2.5rem]'>
              <div className='flex gap-x-[0.75rem]'>
                { !currentUser.roleInfo?.roleCode?.includes('ATJ0001') && 
                  (
                    <div className='flex items-center gap-x-[0.25rem]'>
                      <SvgIcon name='excel' width={20} height={20} />
                      <span className='text-[#E60027] cursor-pointer' onClick={() => handleDownload(1)}>
                        下载导入模版
                      </span>
                    </div>
                  )
                }
                { !currentUser.roleInfo?.roleCode?.includes('ATJ0001') && 
                  (
                    <Button danger ghost icon={<UploadOutlined />} onClick={() => setOpModalOpen(true)}>
                      导入
                    </Button>
                  )
                }
                <Button danger ghost icon={<DownloadOutlined />} onClick={() => handleDownload(2)}>
                  导出
                </Button>
              </div>
            </div>
          </div>
          <ResizableTable
            className='pointSalaryImport_table'
            rowClassName={(_, i) => (i % 2 === 1 ? `customRow odd` : `customRow even`)}
            columns={columns}
            dataSource={tableData}
            bordered
            scroll={{
              // x: 'max-content',
              y: `calc(${height}px - 0.625rem - 1.6rem - 0.5rem)`
            }}
            loading={tableLoading}
            onChange={onChangePage}
            pagination={{
              ...pagination,
              showSizeChanger: true,
              showQuickJumper: true,
              pageSizeOptions: ['10', '20', '50']
            }}
          />
        </div>
      </>

      <Modal
        title='文件上传'
        destroyOnClose={true}
        open={upModalOpen}
        centered
        className={styles.detail_modal}
        // okText={ }
        footer={null}
        // onOk={handleOk}
        onCancel={handleCancel}
      >
        <div className='mt-4 mb-8' style={{ marginBottom: '0px' }}>
          <Row>
            <Col span={22} offset={1} className='h-[10rem]'>
              <Dragger
                {...{
                  action: '',
                  maxCount: 1,
                  multiple: false,
                  fileList,
                  accept: '.xls,.xlsx', //限制文件类型
                  beforeUpload(file, fileList) {
                    console.log(file, fileList)
                    return false
                    // let isError = true;
                    // form.validateFields((err, fieldsValue) => {
                    //   isError = !err;
                    // })
                    // return isError;
                  },
                  onChange(info) {
                    const { status } = info.file
                    if (status !== 'uploading') {
                      console.log(info.file, info.fileList)
                      setFileList(info.fileList)
                    }
                    // if (status === 'done') {
                    //     message.success(`${info.file.name} file uploaded successfully.`);
                    // } else if (status === 'error') {
                    //     message.error(`${info.file.name} file upload failed.`);
                    // }
                  }
                }}
              >
                <p className='ant-upload-drag-icon'>
                  <InboxOutlined style={{ color: '#F14846' }} />
                </p>
                <p className='ant-upload-text'>点击或将文件拖拽到这里上传</p>
                <p className='ant-upload-hint'>支持excel格式的文件。</p>
              </Dragger>
            </Col>
          </Row>

          <div style={{ display: 'flex', justifyContent: 'center', marginTop: '2rem' }}>
            <Button danger onClick={handleCancel}>
              取消
            </Button>

            <Popconfirm
              title=''
              description='导入文件如有重复数据，将会被覆盖，请确认是否上传。'
              onConfirm={handleOk}
              // onCancel={cancel}
              okText='确认'
              cancelText='取消'
            >
              <Button
                danger
                type='primary'
                style={{ marginLeft: '1rem' }}
                disabled={fileList.length < 1}
              >
                上传
              </Button>
            </Popconfirm>
          </div>
        </div>
      </Modal>
    </div>
  )
}
export default People
