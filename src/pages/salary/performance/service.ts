import request from '@/request'

const performanceService = {
  // 查询枚举
  getEnumType: params => {
    return request.post<any>('/zhyy/employee/getEnumType', params)
  },

  // // 人员岗位信息-列表查询
  // getEmployeePostPag: params => {
  //   return request.get<any>('/zhyy/salary/yearlyBaseData/list', { params })
  // },

  // // 人员岗位信息-下载模板
  // downloadEmployeeTemplate: params => {
  //   return request.get<any>('/zhyy/salary/common/downloadTemplate', {
  //     params,
  //     responseType: 'blob'
  //   })
  // },

  // // 人员岗位信息-下载对照表
  // downloadEmployeeCompare: params => {
  //   return request.post<any>('/zhyy/employee/downloadEmployeeCompare', params, {
  //     responseType: 'blob'
  //   })
  // },

  // // 人员岗位信息-导出模板
  // exportEmployeeExcel: params => {
  //   return request.get<any>('/zhyy/salary/yearlyBaseData/exportExcel', {
  //     params,
  //     responseType: 'blob'
  //   })
  // },

  // // 人员岗位信息-导入
  // uploadEmployeeExcel: params => {
  //   return request.post<any>('/zhyy/salary/yearlyBaseData/uploadExcel', params)
  // },

  // // 人员岗位信息-修改
  // updateEmployeePost: params => {
  //   return request.post<any>('/zhyy/employee/updateEmployeePost', params)
  // },
  // // 人员调整查询
  // updateEmployeeTransfer: params => {
  //   return request.get<any>('/zhyy/salary/employeeTransfer/list', { params })
  // },

  // importEmployeeTransfer: params => {
  //   return request.post<any>('/zhyy/salary/employeeTransfer/uploadExcel', params, {
  //     // responseType: 'blob'
  //   })
  // },

  // emportEmployeeTransfer: params => {
  //   return request.get<any>('/zhyy/salary/employeeTransfer/exportExcel', {
  //     params,
  //     responseType: 'blob'
  //   })
  // },
  // 其他查询
  // updateOther: params => {
  //   return request.get<any>('/zhyy/salary/additionalBudget/list', { params })
  // },
  // //其他导入
  // importOther: params => {
  //   return request.post<any>('/zhyy/salary/additionalBudget/uploadExcel', params, {
  //     // responseType: 'blob'
  //   })
  // },
  //其他导出
  // emportOther: params => {
  //   return request.get<any>('/zhyy/salary/additionalBudget/exportExcel', {
  //     params,
  //     responseType: 'blob'
  //   })
  // },

  // addSaveBatch: params => {
  //   return request.post<any>('/zhyy/salary/yearlyBaseData/saveBatch', params)
  // },
  // addEmployeeSaveBatch: params => {
  //   return request.post<any>('/zhyy/salary/employeeTransfer/saveBatch', params)
  // },

  // additionalBudget: params => {
  //   return request.post<any>('/zhyy/salary/additionalBudget/saveBatch', params)
  // },

  // 通用根据标识下载导入模板
  downloadTemplate: params => {
    return request.get<any>('/zhyy/salary/common/downloadTemplate', {
      params,
      responseType: 'blob'
    })
  },

  // 1 生产现业岗（积分绩效）薪酬计算表，查询
  getPointSalaryCalc: params => {
    return request.get<any>('/zhyy/salary/pointSalaryCalc/list', { params })
  },
  // 导出
  exportPointSalaryCalc: params => {
    return request.get<any>('/zhyy/salary/pointSalaryCalc/exportExcel', {
      params,
      responseType: 'blob'
    })
  },

  // 2 生产现业岗（积分绩效）薪酬-积分及单价导入功能，查询
  getPointSalaryImport: params => {
    return request.get<any>('/zhyy/salary/pointSalaryImport/list', { params })
  },
  // 导入
  importPointSalaryImport: params => {
    return request.post<any>('/zhyy/salary/pointSalaryImport/uploadExcel', params, {})
  },
  // 导出
  exportPointSalaryImport: params => {
    return request.get<any>('/zhyy/salary/pointSalaryImport/exportExcel', {
      params,
      responseType: 'blob'
    })
  },
  
  // 3 分公司薪酬总量预算分解，查询
  getSalaryBreakDown: params => {
    return request.get<any>('/zhyy/salary/salaryBreakDown/list', { params })
  },
  // 导入
  importSalaryBreakDown: params => {
    return request.post<any>('/zhyy/salary/salaryBreakDown/uploadExcel', params, {})
  },
  // 导出
  exportSalaryBreakDown: params => {
    return request.get<any>('/zhyy/salary/salaryBreakDown/exportExcel', {
      params,
      responseType: 'blob'
    })
  },

  // 4 分公司月度薪酬使用情况，查询
  getSalaryUsage: params => {
    return request.get<any>('/zhyy/salary/salaryUsage/list', { params })
  },
  // 导入
  importSalaryUsage: params => {
    return request.post<any>('/zhyy/salary/salaryUsage/uploadExcel', params, {})
  },
  // 导出
  exportSalaryUsage: params => {
    return request.get<any>('/zhyy/salary/salaryUsage/exportExcel', {
      params,
      responseType: 'blob'
    })
  },

  // 5 分公司薪酬结构对比，查询
  getSalaryStructureCompare: params => {
    return request.get<any>('/zhyy/salary/salaryStructureCompare/list', { params })
  },
  // 导出
  exportSalaryStructureCompare: params => {
    return request.get<any>('/zhyy/salary/salaryStructureCompare/exportExcel', {
      params,
      responseType: 'blob'
    })
  },

  // 6 增量收益分享月度数据，查询
  getIncrementIncome: params => {
    return request.get<any>('/zhyy/salary/incrementIncome/list', { params })
  },
  // 导入
  importIncrementIncome: params => {
    return request.post<any>('/zhyy/salary/incrementIncome/uploadExcel', params, {})
  },
  // 导出
  exportIncrementIncome: params => {
    return request.get<any>('/zhyy/salary/incrementIncome/exportExcel', {
      params,
      responseType: 'blob'
    })
  }

}

export default performanceService
