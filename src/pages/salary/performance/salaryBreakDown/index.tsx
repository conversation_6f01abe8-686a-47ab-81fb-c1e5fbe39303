import { useEffect, useRef, useState } from 'react'
import { Form, Row, Col, Button, FormProps, Modal, Upload, message, Space, Popconfirm, Cascader, DatePicker, Tooltip} from 'antd'
import { UploadOutlined, DownloadOutlined, InboxOutlined, FullscreenExitOutlined, FullscreenOutlined, InfoCircleOutlined} from '@ant-design/icons'
import SvgIcon from '@/components/SvgIcons'
import performanceService from '../service.ts'
import { useRequest } from '@/hooks'
import styles from './Index.module.scss'
import type { TableColumnsType } from 'antd'
import { FieldType } from '../interface'
import { downFile, openNotification } from '@/utils/down.tsx'
const { Dragger } = Upload
import dayjs from 'dayjs'
import ResizableTable from '@/components/resizeTable/index.jsx'
import { useUserStore } from '@/store/user.ts'

interface DataType {
  key: React.Key
  name: string
  age: number
  address: string
}

const data: DataType[] = []
for (let i = 0; i < 100; i++) {
  data.push({
    key: i,
    name: `Edward ${i}`,
    age: 32,
    address: `London Park no. ${i}`
  })
}

const People: React.FC = () => {
  const [form] = Form.useForm()
  const topRef = useRef(null)
  const contentRef = useRef(null)
  const tableTopRef = useRef(null)
  const [height, setHeight] = useState(0)
  const [showTitle, setShowTitle] = useState<boolean>(true)
  const [tableLoading, setTableLoading] = useState<boolean>(false)
  const [upModalOpen, setOpModalOpen] = useState<boolean>(false)
  const [fileList, setFileList] = useState([])
  const [unitList, setUnitList] = useState<any[]>([])
  //const [typeList, setTypeList] = useState<Enum[]>([])
  const [tableData, setTableData] = useState<DataType[]>([])
  const { currentUser } = useUserStore()
  const [pagination, setPagination] = useState({
    total: 0,
    pageNum: 1,
    pageSize: 50
  })

  const { runAsync: getEnumType } = useRequest(performanceService.getEnumType, { manual: true })
  const { runAsync: getSalaryBreakDown } = useRequest(performanceService.getSalaryBreakDown, {
    manual: true
  })
  const { runAsync: downloadTemplate } = useRequest(
    performanceService.downloadTemplate,
    { manual: true }
  )

  const { runAsync: exportSalaryBreakDown } = useRequest(performanceService.exportSalaryBreakDown, {
    manual: true
  })
  const { runAsync: importSalaryBreakDown } = useRequest(performanceService.importSalaryBreakDown, {
    manual: true
  })
  const columns: TableColumnsType<any> = [
    //{
    //  title: '序号',
    //  dataIndex: 'key',
    //  key: 'key',
    //  align: 'center',
    //  width: 70,
    //  fixed: 'left',
    //  render: (text) => {
    //    // key是从0开始，此处+1
    //    return text + 1;
    //  }
    //},
    {
      title: '单位',
      dataIndex: 'cityName',
      key: 'cityName',
      align: 'center',
      width: 160,
      fixed: 'left'
    },
    {
      title: (
        <>
          <span>
            2025年薪酬<br/>预算合计（年）
          </span>
          <Tooltip title='口径解释：填写占比、调整时填报'>
            <InfoCircleOutlined />
          </Tooltip>
        </>
      ),
      dataIndex: 'annualSalaryBudget',
      key: 'annualSalaryBudget',
      align: 'center',
      width: 180
    },
    {
      title: (
        <>
          <span>基本工资 </span>
          <Tooltip title='过节费、交通费、高温津贴、取暖补贴、信访岗位津贴、技能津贴、租房补贴、特殊补贴、加班费、夜班费等'>
            <InfoCircleOutlined />
          </Tooltip>
        </>
      ),
      dataIndex: 'basicSalary',
      key: 'basicSalary',
      align: 'center',
      width: 120
    },
    {
      title: (
        <>
          <span>津补贴 </span>
          <Tooltip title='过节费、交通费、高温津贴、取暖补贴、信访岗位津贴、技能津贴、租房补贴、特殊补贴、加班费、夜班费等'>
            <InfoCircleOutlined />
          </Tooltip>
        </>
      ),
      dataIndex: 'allowances',
      key: 'allowances',
      align: 'center',
      width: 120
    },
    {
      title: (
        <>
          <span>薪酬绩效 </span>
          <Tooltip title='不含增量收益分享、专项奖励、业绩奖励部分'>
            <InfoCircleOutlined />
          </Tooltip>
        </>
      ),
      key: 'level',
      dataIndex: 'level',
      align: 'center',
      children: [
        {
          title: (
            <>
              <span>组织支撑岗 </span>
              <Tooltip title='岗位绩效'>
                <InfoCircleOutlined />
              </Tooltip>
            </>
          ),
          dataIndex: 'orgSupportPosPerf',
          key: 'orgSupportPosPerf',
          align: 'center',
          width: 130
        },
        {
          title: (
            <>
              <span>生产现业岗 </span>
              <Tooltip title='积分绩效'>
                <InfoCircleOutlined />
              </Tooltip>
            </>
          ),
          dataIndex: 'prodOperPosPoints',
          key: 'prodOperPosPoints',
          align: 'center',
          width: 130
        },
        {
          title: (
            <>
              <span>其他人员 </span>
              <Tooltip title='四级经理、营服中心、各部室下设基层责任单元负责人、高级经理、高级总监、24年新入职大学生、待岗及长病人员等'>
                <InfoCircleOutlined />
              </Tooltip>
            </>
          ),
          dataIndex: 'otherPersonnel',
          key: 'otherPersonnel',
          align: 'center',
          width: 130
        }
      ]
    },
    {
      title: '增量收益分享',
      dataIndex: 'incrementalSharing',
      key: 'incrementalSharing',
      align: 'center',
      width: 130,
    },
    {
      title: '专项奖励',
      key: 'level',
      dataIndex: 'level',
      align: 'center',
      children: [
        {
          title: '本单位业绩相关',
          dataIndex: 'unitPerformance',
          key: 'unitPerformance',
          align: 'center',
          width: 150
        },
        {
          title: '荣誉/集团级',
          dataIndex: 'honorGroupLevel',
          key: 'honorGroupLevel',
          align: 'center',
          width: 150
        }
      ]
    },
    {
      title: '业绩奖励',
      dataIndex: 'performanceAward',
      key: 'performanceAward',
      align: 'center',
      width: 130
    },
    {
      title: '其他：可补充',
      key: 'level',
      dataIndex: 'level',
      align: 'center',
      children: [
        {
          title: '占比',
          dataIndex: 'proportion',
          key: 'proportion',
          align: 'center',
          width: 120,
        },
        {
          title: '事项说明',
          dataIndex: 'description',
          key: 'description',
          align: 'center',
          width: 120,
        }
      ]
    }
  ]

  useEffect(() => { // 
    getEnumTypes()
    initDate()
    // 添加 resize 事件监听器
    window.addEventListener('resize', handleResize)
    // 清除事件监听器，防止内存泄漏
    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])
  const handleResize = () => {
    initHeight()
  }

  useEffect(() => {
    initHeight()
  }, [(document.querySelector('.salaryBreakDown_table .ant-table-header') || {})['offsetHeight']])

  const initHeight = () => {
    const headerHeight =
      (document.querySelector('.salaryBreakDown_table .ant-table-header') || {})['offsetHeight'] ||
      0
    const pageHeight =
      (document.querySelector('.salaryBreakDown_table .ant-table-pagination') || {})[
        'offsetHeight'
      ] || 26
    if (headerHeight && pageHeight) {
      setHeight(
        contentRef.current?.offsetHeight -
          (tableTopRef.current.offsetHeight + headerHeight + pageHeight)
      )
    }
  }

  const handleDownload = async (type: number) => {
    try {
      // 调用接口获取 Blob 文件数据
      openNotification('正在导出', 0, 'loading')
      let response = null
      if (type === 1) {
        response = await downloadTemplate({ templateId: 'SUB_COMP_SAL_BUDGET_BKDN' })
      } else if (type === 2) {
        const { loginDate, cityId, category, cycleId, empId } = form.getFieldsValue()
        const params = {
          beginTime: loginDate?.length > 0 ? dayjs(loginDate[0]).format('YYYY-MM-DD') : null,
          endTime: loginDate?.length > 0 ? dayjs(loginDate[1]).format('YYYY-MM-DD') : null,
          cycleId: cycleId ? dayjs(cycleId)?.format('YYYY') : '',
          cityId: cityId ? cityId[cityId?.length - 1] : null,
          category,
          empId
        }
        response = await exportSalaryBreakDown(params)
      }
      downFile(response)
    } catch (error) {
      openNotification('导出失败', 1, 'error')
      console.error('Download failed:', error)
    }
  }

  // 点击查询
  const onFormFinish: FormProps<FieldType>['onFinish'] = values => {
    queryTableData({
      ...values
    })
  }

  const getEnumTypes = async () => {
    const [[unitError, unitData], [typeError]] = await Promise.all([
      getEnumType({ code: '1010', tag: 1 }),
      getEnumType({ code: 'EMPLOYEE_TRANSFER_CATEGORY' })
    ])
    if (unitError || typeError) {
      return
    }
    if (unitData.STATUS === '0000') {
      setUnitList(unitData.DATA?.filter(item => item?.orgId !== '49757'))
    }
    //if (typeData.STATUS === '0000') {
    //  setTypeList(typeData.DATA)
    //}
    const params = {
      ...form.getFieldsValue()
    }
    queryTableData(params)
  }

  const queryTableData = async params => {
    setTableLoading(true)
    const { loginDate, cityId, category, cycleId, empId } = params
    const param = {
      ...pagination,
      beginTime: loginDate?.length > 0 ? dayjs(loginDate[0]).format('YYYY-MM-DD') : null,
      endTime: loginDate?.length > 0 ? dayjs(loginDate[1]).format('YYYY-MM-DD') : null,
      cycleId: cycleId ? dayjs(cycleId)?.format('YYYY') : '',
      cityId: cityId ? cityId[cityId?.length - 1] : null,
      category,
      empId
    }
    const [error, res] = await getSalaryBreakDown(param)
    setTableLoading(false)
    if (error) {
      message.error(res?.DATA || res?.MESSAGE || '调用失败')
      return
    }
    if (res.STATUS === '0000') {
      const {
        DATA: { data }
      } = res
      //  const data = res.data
      const dataWithKeys = data.map((item, index) => ({
        ...item,
        key: item.id || index // 使用数据中的唯一字段作为 key，或回退到索引
      }))
      setTableData(dataWithKeys)
      setPagination({
        ...pagination,
        total: res.DATA?.totalCount
      })
    } else {
      message.error(res?.MESSAGE || res?.DATA || '调用失败')
    }
  }

  const uploadFileFun = async () => {
    const formData = new FormData()
    fileList
      .map(file => file?.originFileObj)
      .forEach(file => {
        formData.append('file', file)
      })
    // formData.append('tag', '1')
    const [error, res] = await importSalaryBreakDown(formData)
    if (error) {
      message.error(res?.DATA || res?.MESSAGE || '调用失败')
      return
    }
    if (res.STATUS === '0000') {
      const { loginDate, yearVal, cityId, category } = form.getFieldsValue()
      const params = {
        beginTime: loginDate.length > 0 ? dayjs(loginDate[0]).format('YYYY-MM-DD') : null,
        endTime: loginDate.length > 0 ? dayjs(loginDate[1]).format('YYYY-MM-DD') : null,
        yearVal: yearVal ? dayjs(yearVal).format('YYYY') : '',
        cityId: cityId[cityId?.length - 1],
        category
      }
      initHeight()
      queryTableData(params)
      setOpModalOpen(false)
      message.success(res?.DATA)
    } else {
      message.error(res?.MESSAGE || res?.DATA || '调用失败')
    }
  }

  // 上传
  const handleOk = () => {
    if (fileList?.length > 0) {
      uploadFileFun()
    } else {
      message.error('请先选择文件上传')
    }
  }

  const handleCancel = () => {
    setOpModalOpen(false)
  }

  // 查询重置
  const onReset = () => {
    form.resetFields()
    initDate()
  }

  // 切换分页
  const onChangePage = page => {
    const newPage = {
      total: page?.total,
      pageNum: page?.current,
      pageSize: page?.pageSize
    }
    setPagination(newPage)
  }

  useEffect(() => {
    if (pagination?.total > 0) {
      const params = {
        ...form.getFieldsValue()
      }
      queryTableData(params)
    }
  }, [pagination.pageNum, pagination.pageSize])

  // 初始化查询条件
  const initDate = () => {
    //const date = dayjs().subtract(1, 'month') // 获取上一个月份
    const date = dayjs() // 获取当前时间
    form.setFieldsValue({
      cycleId: date
    })
  }

  // 搜索过滤空格
  const filter = (input, option) =>
    (option[0]?.enumName ?? '').toLowerCase().includes(input.toLowerCase()?.trim())

  return (
    <div
      className={`${'pt-[0.5rem] px-[0rem] h-full flex flex-col'} ${styles.salaryBreakDown_page}`}
    >
      <>
        <div ref={topRef} className='bg-white pt-[0.5rem] px-[1.75rem] mb-[0.5rem]'>
          <Form
            form={form}
            labelCol={{ span: 6 }}
            onFinish={onFormFinish}
            initialValues={{
              loginDate: '',
              category: 'KOU_JING_TIAO_ZHENG',
              // cycleId: '',
              cityId: '',
              empId: ''
            }}
          >
            <Row gutter={24}>
              <Col span={6}>
                <Form.Item name='cycleId' label='账期' className='mb-[0.5rem]'>
                  <DatePicker className='w-full' picker='year' />
                </Form.Item>
              </Col>


              <Col span={5}>
                <Form.Item name='cityId' label='单位' className='mb-[0.5rem]'>
                  <Cascader
                    allowClear
                    changeOnSelect
                    expandTrigger='hover'
                    displayRender={labels => labels[labels.length - 1]}
                    options={unitList}
                    fieldNames={{
                      // value: 'orgId',
                      value: 'enumId',
                      label: 'enumName',
                      children: 'children'
                    }}
                    // showCheckedStrategy={Cascader.SHOW_CHILD}
                    // onChange={(value, selectedOptions) => {
                    //   console.log(value, selectedOptions)
                    //   setCascaderSelected(selectedOptions)
                    // }}
                    placeholder='请选择单位'
                    showSearch={{ filter }}
                    onSearch={value => console.log(value)}
                  />
                </Form.Item>
              </Col>
              
              <Col span={4}>
                <div className='text-right'>
                  <Space>
                    <Button type='primary' htmlType='submit'>
                      查询
                    </Button>
                    <Button onClick={() => onReset()}>重置</Button>
                  </Space>
                </div>
              </Col>
            </Row>
          </Form>
        </div>
        <div
          ref={contentRef}
          className='relative bg-white px-5 pt-2.5'
          style={{ height: `calc(100% - ${topRef.current?.offsetHeight + 15}px)` }}
        >
          <div
            ref={tableTopRef}
            className={`${'flex justify-between items-center overflow-hidden mb-[0.1rem]'} ${styles.animation_box} ${showTitle ? 'h-[1.8rem]' : 'h-0'}`}
          >
            <div className={'flex '}>
              {showTitle ? (
                <FullscreenExitOutlined
                  className={`${styles.shousuo_icon} text-[1rem]`}
                  onClick={() => {
                    setShowTitle(false)
                    setTimeout(() => {
                      initHeight()
                    }, 200)
                  }}
                />
              ) : (
                <FullscreenOutlined
                  className={`${styles.shousuo_icon} ${'absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10'}`}
                  onClick={() => {
                    setShowTitle(true)
                    setTimeout(() => {
                      initHeight()
                    }, 200)
                  }}
                />
              )}
              <div className='font-bold text-[0.8rem] ml-3'>数据列表</div>
            </div>
            <div className='flex gap-x-[2.5rem]'>
              <div className='flex gap-x-[0.75rem]'>
                { !currentUser.roleInfo?.roleCode?.includes('ATJ0001') && 
                  (
                    <div className='flex items-center gap-x-[0.25rem]'>
                      <SvgIcon name='excel' width={20} height={20} />
                      <span className='text-[#E60027] cursor-pointer' onClick={() => handleDownload(1)}>
                        下载导入模版
                      </span>
                    </div>
                  )
                }
                { !currentUser.roleInfo?.roleCode?.includes('ATJ0001') && 
                  (
                    <Button danger ghost icon={<UploadOutlined />} onClick={() => setOpModalOpen(true)}>
                      导入
                    </Button>
                  )
                }
                <Button danger ghost icon={<DownloadOutlined />} onClick={() => handleDownload(2)}>
                  导出
                </Button>
              </div>
            </div>
          </div>
          <ResizableTable
            className='salaryBreakDown_table'
            rowClassName={(_, i) => (i % 2 === 1 ? `customRow odd` : `customRow even`)}
            columns={columns}
            dataSource={tableData}
            bordered
            scroll={{
              // x: 'max-content',
              y: `calc(${height}px - 0.625rem - 1.6rem - 0.5rem)`
            }}
            loading={tableLoading}
            onChange={onChangePage}
            pagination={{
              ...pagination,
              showSizeChanger: true,
              showQuickJumper: true,
              pageSizeOptions: ['10', '20', '50']
            }}
          />
        </div>
      </>

      <Modal
        title='文件上传'
        destroyOnClose={true}
        open={upModalOpen}
        centered
        className={styles.detail_modal}
        // okText={ }
        footer={null}
        // onOk={handleOk}
        onCancel={handleCancel}
      >
        <div className='mt-4 mb-8' style={{ marginBottom: '0px' }}>
          <Row>
            <Col span={22} offset={1} className='h-[10rem]'>
              <Dragger
                {...{
                  action: '',
                  maxCount: 1,
                  multiple: false,
                  fileList,
                  accept: '.xls,.xlsx', //限制文件类型
                  beforeUpload(file, fileList) {
                    console.log(file, fileList)
                    return false
                    // let isError = true;
                    // form.validateFields((err, fieldsValue) => {
                    //   isError = !err;
                    // })
                    // return isError;
                  },
                  onChange(info) {
                    const { status } = info.file
                    if (status !== 'uploading') {
                      console.log(info.file, info.fileList)
                      setFileList(info.fileList)
                    }
                    // if (status === 'done') {
                    //     message.success(`${info.file.name} file uploaded successfully.`);
                    // } else if (status === 'error') {
                    //     message.error(`${info.file.name} file upload failed.`);
                    // }
                  }
                }}
              >
                <p className='ant-upload-drag-icon'>
                  <InboxOutlined style={{ color: '#F14846' }} />
                </p>
                <p className='ant-upload-text'>点击或将文件拖拽到这里上传</p>
                <p className='ant-upload-hint'>支持excel格式的文件。</p>
              </Dragger>
            </Col>
          </Row>

          <div style={{ display: 'flex', justifyContent: 'center', marginTop: '2rem' }}>
            <Button danger onClick={handleCancel}>
              取消
            </Button>

            <Popconfirm
              title=''
              description='导入文件如有重复数据，将会被覆盖，请确认是否上传。'
              onConfirm={handleOk}
              // onCancel={cancel}
              okText='确认'
              cancelText='取消'
            >
              <Button
                danger
                type='primary'
                style={{ marginLeft: '1rem' }}
                disabled={fileList.length < 1}
              >
                上传
              </Button>
            </Popconfirm>
          </div>
        </div>
      </Modal>
    </div>
  )
}
export default People
