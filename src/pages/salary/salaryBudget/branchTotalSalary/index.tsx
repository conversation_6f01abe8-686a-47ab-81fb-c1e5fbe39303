import {
  <PERSON><PERSON>,
  <PERSON>r,
  Col,
  DatePicker,
  Form,
  FormProps,
  Row,
  Space,
  message,
  Select
} from 'antd'
import { DownloadOutlined, FullscreenExitOutlined, FullscreenOutlined } from '@ant-design/icons'
import { useEffect, useRef, useState } from 'react'
import dayjs from 'dayjs'
import { useRequest } from '@/hooks'
import effectService from '@/pages/salary/service.ts'
import { downFile, openNotification } from '@/utils/down.tsx'
import styles from '../Index.module.scss'
import ResizableTable from '@/components/resizeTable/index.jsx'
import { formatMoney } from '@/hooks/format'
import ColumnData from './columnData.tsx'

type FieldType = {
  month?: string
  unit?: string
  perClass?: string
  cycleId?: null
}

const Statistics: React.FC = () => {
  const topRef = useRef(null)
  const contentRef = useRef(null)
  const tableTopRef = useRef(null)
  const [formRef] = Form.useForm()
  const [year, setYear] = useState<any>(dayjs().format('YYYY'))
  const [height, setHeight] = useState(0)
  const [columnTableHeight, setColumnTableHeight] = useState(0)
  const [orgList, setOrgList] = useState<any>([])
  const [tableData, setTableData] = useState<any>([])
  const [selectValue, setSelectValue] = useState<any>(['list1', 'list9'])
  const [columnList, setColumnList] = useState([])
  const [tableLoading, setTableLoading] = useState<boolean>(false)
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false)
  const [isReset, setIsReset] = useState<number>(0)
  const [showTitle, setShowTitle] = useState<boolean>(true)
  const [pagination, setPagination] = useState({
    total: 0,
    pageNum: 1,
    pageSize: 50
  })
  const [calculateLoading, setCalculateLoading] = useState<boolean>(false)
  const { runAsync: exportBranchTotalSalary } = useRequest(effectService.exportBranchTotalSalary, {
    manual: true
  })
  const { runAsync: getEnumType } = useRequest(effectService.getEnumType, { manual: true })
  const { runAsync: getBranchTotalSalary } = useRequest(effectService.getBranchTotalSalary, {
    manual: true
  })
  const { runAsync: getBranchTotalSalaryCalculate } = useRequest(
    effectService.getBranchTotalSalaryCalculate,
    {
      manual: true
    }
  )
  const { runAsync: getBranchTotalSalaryConfirm } = useRequest(
    effectService.getBranchTotalSalaryConfirm,
    {
      manual: true
    }
  )
  const allColumns = [
    // {
    //   title: '月份',
    //   key: 'cycleId',
    //   dataIndex: 'cycleId',
    //   align: 'center',
    //   fixed: 'left',
    //   width: 60,
    //   children: []
    //   // onCell: (record:orgOption,rowIndex:string) => mergeCells(record?.unit,tableData,'unit',rowIndex),
    // },
    {
      title: '单位',
      key: 'cityName',
      dataIndex: 'cityName',
      align: 'center',
      fixed: 'left',
      width: 130,
      children: []
      // onCell: (record:orgOption,rowIndex:string) => mergeCells(record?.unit,tableData,'unit',rowIndex),
    },
    {
      title: `${year}年业绩完成预算`,
      key: 'list1',
      dataIndex: 'list1',
      align: 'center',
      children: [
        {
          title: '主营收入',
          key: 'orgIncMomLydec',
          dataIndex: 'orgIncMomLydec',
          align: 'center',
          children: [
            {
              title: '预算',
              key: 'mrBudget',
              dataIndex: 'mrBudget',
              align: 'center',
              children: [],
              width: 100,
              render: text => <span>{formatMoney(text)}</span>
            },
            {
              title: '完成情况',
              key: 'mrComplete',
              dataIndex: 'mrComplete',
              align: 'center',
              children: [],
              width: 100,
              render: text => <span>{formatMoney(text)}</span>
            },
            {
              title: '完成率',
              key: 'mrCompleteRate',
              dataIndex: 'mrCompleteRate',
              align: 'center',
              children: [],
              width: 100
            }
          ]
          // width: 130
        },
        {
          title: '经营利润',
          key: 'orgDecMomLydec',
          dataIndex: '',
          align: 'center',
          children: [
            {
              title: '预算',
              key: 'profitBudget',
              dataIndex: 'profitBudget',
              align: 'center',
              children: [],
              width: 100,
              render: text => <span>{formatMoney(text)}</span>
            },
            {
              title: '完成情况',
              key: 'profitComplete',
              dataIndex: 'profitComplete',
              align: 'center',
              children: [],
              width: 100,
              render: text => <span>{formatMoney(text)}</span>
            },
            {
              title: '完成率',
              key: 'profitCompleteRate',
              dataIndex: 'profitCompleteRate',
              align: 'center',
              children: [],
              width: 100
            }
          ]
        },
        {
          title: '终端收入',
          key: 'postNum',
          dataIndex: 'postNum',
          align: 'center',
          children: [
            {
              title: '预算',
              key: 'ctBudget',
              dataIndex: 'ctBudget',
              align: 'center',
              children: [],
              width: 100,
              render: text => <span>{formatMoney(text)}</span>
            },
            {
              title: '完成情况',
              key: 'ctComplete',
              dataIndex: 'ctComplete',
              align: 'center',
              children: [],
              width: 100,
              render: text => <span>{formatMoney(text)}</span>
            },
            {
              title: '完成率',
              key: 'ctCompleteRate',
              dataIndex: 'ctCompleteRate',
              align: 'center',
              children: [],
              width: 100
            }
          ]
        }
      ]
    },
    {
      title: `${year}年当月薪酬预算合计`,
      key: 'list2',
      dataIndex: 'salBudgetTotal',
      align: 'center',
      children: [],
      width: 100,
      render: text => <span>{formatMoney(text)}</span>
    },
    {
      title: '基本保障薪酬（65%）',
      key: 'list3',
      dataIndex: 'basicSal',
      align: 'center',
      children: [],
      width: 100,
      render: text => <span>{formatMoney(text)}</span>
    },
    {
      title: '政策提低保障薪酬',
      key: 'list4',
      dataIndex: 'salTd',
      align: 'center',
      children: [],
      width: 100,
      render: text => <span>{formatMoney(text)}</span>
    },
    {
      title: '人才结构保障薪酬',
      key: 'list5',
      dataIndex: 'list5',
      align: 'center',
      children: [
        {
          title: '挂钩人均创利薪酬（3%）',
          key: '',
          dataIndex: '',
          align: 'center',
          children: [
            {
              title: '金额',
              key: 'abilityPerAmount',
              dataIndex: 'abilityPerAmount',
              align: 'center',
              children: [],
              width: 100,
              render: text => <span>{formatMoney(text)}</span>
            },
            {
              title: '人均创利完成系数',
              key: 'abilityPerPercent',
              dataIndex: 'abilityPerPercent',
              align: 'center',
              children: [],
              width: 100
            },
            {
              title: '人均创利预算完成率',
              key: 'abilityPerRate',
              dataIndex: 'abilityPerRate',
              align: 'center',
              children: [],
              width: 100
            }
          ]
        },
        {
          title: '挂钩市场化退出率薪酬(2%)',
          key: 'orgDecMom',
          dataIndex: 'orgDecMom',
          align: 'center',
          children: [
            {
              title: '金额',
              key: 'abilityQuitAmount',
              dataIndex: 'abilityQuitAmount',
              align: 'center',
              children: [],
              width: 100,
              render: text => <span>{formatMoney(text)}</span>
            },
            {
              title: '市场化退出率系数',
              key: 'abilityQuitPercent',
              dataIndex: 'abilityQuitPercent',
              align: 'center',
              children: [],
              width: 100
            },
            {
              title: '市场化退出率',
              key: 'abilityQuitRate',
              dataIndex: 'abilityQuitRate',
              align: 'center',
              children: [],
              width: 100
            }
          ]
        }
      ]
    },
    {
      title: '挂钩营业现金比率薪酬',
      key: 'list6',
      dataIndex: 'list6',
      align: 'center',
      children: [
        {
          title: '挂钩营业现金比率(5%)',
          key: 'orgIncMom',
          dataIndex: 'orgIncMom',
          align: 'center',
          children: [
            {
              title: '金额',
              key: 'cashSalAmount',
              dataIndex: 'cashSalAmount',
              align: 'center',
              children: [],
              width: 100,
              render: text => <span>{formatMoney(text)}</span>
            },
            {
              title: '系数',
              key: 'cashSalPercent',
              dataIndex: 'cashSalPercent',
              align: 'center',
              children: [],
              width: 100
            },
            {
              title: '公众新增应收账款占收比完成率',
              key: 'cashSalPubAddRcvRate',
              dataIndex: 'cashSalPubAddRcvRate',
              align: 'center',
              children: [],
              width: 100
            },
            {
              title: '公众存量应收账款回款率完成率',
              key: 'cashSalPubStockRcvRate',
              dataIndex: 'cashSalPubStockRcvRate',
              align: 'center',
              children: [],
              width: 100
            },
            {
              title: '政企新增应收账款占收比完成率',
              key: 'cashSalZqAddRcvRate',
              dataIndex: 'cashSalZqAddRcvRate',
              align: 'center',
              children: [],
              width: 100
            },
            {
              title: '政企存量应收账款回款率完成率',
              key: 'cashSalZqStockRcvRate',
              dataIndex: 'cashSalZqStockRcvRate',
              align: 'center',
              children: [],
              width: 100
            },
            {
              title: '净预收账款占收比完成率',
              key: 'cashSalAdvanceRcvRate',
              dataIndex: 'cashSalAdvanceRcvRate',
              align: 'center',
              children: [],
              width: 100
            }
          ]
        }
      ]
    },
    {
      title: '挂钩业绩',
      key: 'list7',
      dataIndex: 'list7',
      align: 'center',
      children: [
        {
          title: '挂钩业绩预算(10%)',
          key: 'orgIncMom',
          dataIndex: 'orgIncMom',
          align: 'center',
          children: [
            {
              title: '金额',
              key: 'achieveBudgetAmount',
              dataIndex: 'achieveBudgetAmount',
              align: 'center',
              children: [],
              width: 100,
              render: text => <span>{formatMoney(text)}</span>
            },
            {
              title: '系数',
              key: 'achieveBudgetPercent',
              dataIndex: 'achieveBudgetPercent',
              align: 'center',
              children: [],
              width: 100
            },
            {
              title: '联网通信收入完成率',
              key: 'achieveBudgetRcvRate',
              dataIndex: 'achieveBudgetRcvRate',
              align: 'center',
              children: [],
              width: 100
            },
            {
              title: '算网数智收入完成率',
              key: 'achieveBudgetCiRcvRate',
              dataIndex: 'achieveBudgetCiRcvRate',
              align: 'center',
              children: [],
              width: 100
            },
            {
              title: '经营利润完成率',
              key: 'achieveBudgetProfitRate',
              dataIndex: 'achieveBudgetProfitRate',
              align: 'center',
              children: [],
              width: 100
            }
          ]
        },
        {
          title: '主营收入增量受益分享',
          key: 'orgIncMom',
          dataIndex: 'orgIncMom',
          align: 'center',
          children: [
            {
              title: '金额',
              key: 'achieveAddShareAmount',
              dataIndex: 'achieveAddShareAmount',
              align: 'center',
              children: [],
              width: 100,
              render: text => <span>{formatMoney(text)}</span>
            },
            {
              title: '市场份额系数Q',
              key: 'achieveAddSharePercent',
              dataIndex: 'achieveAddSharePercent',
              align: 'center',
              children: [],
              width: 100
            },
            {
              title: `${year - 1}年市场份额`,
              key: 'achieveAddShareLastMarketAmount',
              dataIndex: 'achieveAddShareLastMarketAmount',
              align: 'center',
              children: [],
              width: 100
            },
            {
              title: `${year}年市场份额`,
              key: 'achieveAddShareMarketAmount',
              dataIndex: 'achieveAddShareMarketAmount',
              align: 'center',
              children: [],
              width: 100
            }
          ]
        },
        {
          title: '终端收入增量收益分享',
          key: 'orgIncMom',
          dataIndex: '',
          align: 'center',
          children: [
            {
              title: '金额',
              key: 'achieveTermAddShareAmount',
              dataIndex: 'achieveTermAddShareAmount',
              align: 'center',
              children: [],
              width: 100,
              render: text => <span>{formatMoney(text)}</span>
            }
          ]
        },
        {
          title: '利润增量受益分享',
          key: 'orgIncMom',
          dataIndex: '',
          align: 'center',
          children: [
            {
              title: '金额',
              key: 'achievePorfitAddShareAmount',
              dataIndex: 'achievePorfitAddShareAmount',
              align: 'center',
              children: [],
              width: 100,
              render: text => <span>{formatMoney(text)}</span>
            }
          ]
        }
      ]
    },
    {
      title: `${year}年当月其他追加预算`,
      key: 'list8',
      dataIndex: 'list8',
      align: 'center',
      children: [
        {
          title: '合计',
          key: 'otherBudgetTotal',
          dataIndex: 'otherBudgetTotal',
          align: 'center',
          width: 100,
          render: text => <span>{formatMoney(text)}</span>
        },
        {
          title: '专项奖励',
          key: 'otherBudgetSpecialReward',
          dataIndex: 'otherBudgetSpecialReward',
          align: 'center',
          width: 100,
          render: text => <span>{formatMoney(text)}</span>
        },
        {
          title: '交通补助',
          key: 'otherBudgetTransport',
          dataIndex: 'otherBudgetTransport',
          align: 'center',
          width: 100,
          render: text => <span>{formatMoney(text)}</span>
        },
        {
          title: '加班费核增（全年）',
          key: 'otherBudgetOvertime',
          dataIndex: 'otherBudgetOvertime',
          align: 'center',
          width: 100,
          render: text => <span>{formatMoney(text)}</span>
        },
        {
          title: '过节费',
          key: 'otherBudgetHoliday',
          dataIndex: 'otherBudgetHoliday',
          align: 'center',
          width: 100,
          render: text => <span>{formatMoney(text)}</span>
        },
        {
          title: '大学生租房补贴',
          key: 'otherBudgetCollegeHouse',
          dataIndex: 'otherBudgetCollegeHouse',
          align: 'center',
          width: 100,
          render: text => <span>{formatMoney(text)}</span>
        },
        {
          title: '退职',
          key: 'otherBudgetDismiss',
          dataIndex: 'otherBudgetDismiss',
          align: 'center',
          width: 100,
          render: text => <span>{formatMoney(text)}</span>
        },
        {
          title: '墩苗、下沉、值守人员（全年）',
          key: 'otherBudgetDuty',
          dataIndex: 'otherBudgetDuty',
          align: 'center',
          width: 100,
          render: text => <span>{formatMoney(text)}</span>
        },
        {
          title: `${year}年大学生`,
          key: 'otherBudgetGraduate',
          dataIndex: 'otherBudgetGraduate',
          align: 'center',
          width: 100,
          render: text => <span>{formatMoney(text)}</span>
        },
        {
          title: '融通外包费用',
          key: 'otherBudgetOs',
          dataIndex: 'otherBudgetOs',
          align: 'center',
          width: 100,
          render: text => <span>{formatMoney(text)}</span>
        },
        {
          title: '离职补偿金',
          key: 'otherBudgetQuit',
          dataIndex: 'otherBudgetQuit',
          align: 'center',
          width: 100,
          render: text => <span>{formatMoney(text)}</span>
        },
        {
          title: '其他',
          key: 'otherBudgetOther',
          dataIndex: 'otherBudgetOther',
          align: 'center',
          width: 100,
          render: text => <span>{formatMoney(text)}</span>
        },
        {
          title: '转递薪酬',
          key: 'otherBudgetTrans',
          dataIndex: 'otherBudgetTrans',
          align: 'center',
          width: 100,
          render: text => <span>{formatMoney(text)}</span>
        },
        {
          title: '取暖补贴',
          key: 'otherBudgetWarm',
          dataIndex: 'otherBudgetWarm',
          align: 'center',
          width: 100,
          render: text => <span>{formatMoney(text)}</span>
        }
      ]
    },
    {
      title: `${year}年当月薪酬总量兑现情况`,
      key: 'list9',
      dataIndex: 'list9',
      align: 'center',
      children: [
        {
          title: `${year}年当月薪酬预算`,
          key: 'salTotalDeliverBudget',
          dataIndex: 'salTotalDeliverBudget',
          align: 'center',
          width: 100,
          render: text => <span>{formatMoney(text)}</span>
        },
        {
          title: `${year}年当月实际兑现`,
          key: 'salTotalDeliverReal',
          dataIndex: 'salTotalDeliverReal',
          align: 'center',
          width: 100,
          render: text => <span>{formatMoney(text)}</span>
        },
        {
          title: '结余',
          key: 'salTotalDeliverBalance',
          dataIndex: 'salTotalDeliverBalance',
          align: 'center',
          width: 100,
          render: text => <span>{formatMoney(text)}</span>
        }
      ]
    }
  ]
  const [tableColumns, setTableColumns] = useState<any>(
    allColumns.filter(col => ['cityName', 'list1', 'list2'].includes(col.key))
  )

  useEffect(() => {
    setTableLoading(false)
    // setTableData(staList)
    if (topRef.current) {
      setHeight(topRef.current.offsetHeight)
    }
    getEnumTypes()
    // get4LevelOrgTree()
    // initDate()
    queryTableData()
    // 添加 resize 事件监听器
    window.addEventListener('resize', handleResize)
    // 清除事件监听器，防止内存泄漏
    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])
  const handleResize = () => {
    initHeight()
  }

  useEffect(() => {
    if (isReset > 0) {
      queryTableData()
    }
  }, [isReset])

  useEffect(() => {
    setTableColumns(allColumns.filter(col => ['cityName', 'list1', 'list2'].includes(col.key)))
    changeSelectColumn(selectValue)
  }, [year])

  useEffect(() => {
    setTimeout(() => {
      initHeight()
    }, 100)
  }, [tableColumns])

  useEffect(() => {
    if (tableData?.length > 0) {
      // setTableColumns(columns)
      initHeight()
    }
  }, [tableData])

  useEffect(() => {
    if (pagination?.total > 0) {
      queryTableData()
    }
  }, [pagination.pageNum, pagination.pageSize])

  const initHeight = () => {
    const headerHeight =
      (document.querySelector('.salaryBudget_table .ant-table-header') || {})['offsetHeight'] || 0
    const pageHeight =
      (document.querySelector('.salaryBudget_table .ant-table-pagination') || {})['offsetHeight'] ||
      26
    if (headerHeight && pageHeight) {
      setHeight(
        contentRef.current?.offsetHeight -
          (tableTopRef.current.offsetHeight + headerHeight + pageHeight)
      )
    }
    setColumnTableHeight(contentRef.current?.offsetHeight - tableTopRef.current.offsetHeight)
  }

  const getEnumTypes = async () => {
    const [[error, res], [selectError, selectData]] = await Promise.all([
      getEnumType({ code: '1010', tag: 1 }),
      getEnumType({ code: 'salaryBudgetMainList' })
    ])
    if (error || selectError) {
      return
    }
    if (res.STATUS === '0000') {
      setOrgList(res.DATA)
    }
    if (selectData.STATUS === '0000') {
      setColumnList(selectData.DATA)
    }
  }

  // const getEnumTypes = async () => {
  //   const [error, res] = await getEnumType({ code: '1010', tag: 1 })
  //   if (error) {
  //     return
  //   }
  //   if (res.STATUS === '0000') {
  //     setOrgList(res.DATA)
  //   }
  // }

  // 查询表格数据
  const queryTableData = async (page?: any) => {
    setTableLoading(true)
    const newPage = page || pagination
    const values = formRef.getFieldsValue()
    const orgIdArr = values?.orgaId
    const curOrg = orgIdArr?.length > 0 ? orgIdArr[orgIdArr?.length - 1] : ''
    const [error, res] = await getBranchTotalSalary({
      ...newPage,
      orgaId: curOrg,
      cycleId: values?.cycleId?.format('YYYYMM')
    })
    setTableLoading(false)
    if (error) {
      message.error(res?.DATA || res?.MESSAGE || '调用失败')
      return
    }
    if (res.STATUS === '0000') {
      const {
        DATA: { data }
      } = res
      const dataWithKeys = data.map((item, index) => ({
        ...item,
        key: item.id || index // 使用数据中的唯一字段作为 key，或回退到索引
      }))
      setTableData(dataWithKeys)
      setPagination({
        ...pagination,
        total: res.DATA?.totalCount
      })
    } else {
      message.error(res?.MESSAGE || res?.DATA || '调用失败')
    }
  }

  //计算
  const getBranchCalculate = async () => {
    const values = formRef.getFieldsValue()
    if (values.cycleId) {
      setCalculateLoading(true)
      const [error, res] = await getBranchTotalSalaryCalculate({
        cycleId: values?.cycleId?.format('YYYYMM') //账期
      })
      setCalculateLoading(false)
      if (error) {
        message.error(res?.DATA || res?.MESSAGE || '调用失败')
        return
      }
      if (res.STATUS === '0000') {
        message.success(res.DATA)
        queryTableData()
      } else {
        message.error(res?.MESSAGE || res?.DATA || '调用失败')
      }
    } else {
      message.warning('请选择账期')
    }
  }

  //确认
  const getConfirm = async () => {
    const values = formRef.getFieldsValue()
    if (values.cycleId) {
      setConfirmLoading(true)
      const [error, res] = await getBranchTotalSalaryConfirm({
        cycleId: values?.cycleId?.format('YYYYMM') //账期
      })
      setConfirmLoading(false)
      if (error) {
        message.error(res?.DATA || res?.MESSAGE || '调用失败')
        return
      }
      if (res.STATUS === '0000') {
        message.success(res.DATA)
        queryTableData()
      } else {
        message.error(res?.MESSAGE || res?.DATA || '调用失败')
      }
    } else {
      message.warning('请选择账期')
    }
  }
  // 初始化查询条件
  const initDate = () => {
    const date = dayjs().subtract(1, 'month')
    formRef.setFieldsValue({
      monthId: date,
      org: ''
    })
    // queryTableData()
  }

  // 点击查询
  const onFormFinish: FormProps<FieldType>['onFinish'] = value => {
    console.log(value)
    setYear(dayjs(value?.cycleId).format('YYYY'))
    queryTableData()
  }

  const changeSelectColumn = e => {
    const arr3 = allColumns.filter(item => e.some(y => y === item.key))
    const columns = [
      // {
      //   title: '月份',
      //   key: 'cycleId',
      //   dataIndex: 'cycleId',
      //   align: 'center',
      //   fixed: 'left',
      //   width: 80
      //   // onCell: (record:orgOption,rowIndex:string) => mergeCells(record?.unit,tableData,'unit',rowIndex),
      // },
      {
        title: '单位',
        key: 'cityName',
        dataIndex: 'cityName',
        align: 'center',
        fixed: 'left',
        width: 130
        // onCell: (record:orgOption,rowIndex:string) => mergeCells(record?.unit,tableData,'unit',rowIndex),
      },
      ...arr3
    ]
    setTableColumns(columns)
    setSelectValue(e)
  }
  // 点击导出
  const exportToExcelFun = async () => {
    try {
      // 调用接口获取 Blob 文件数据
      openNotification('正在导出', 0, 'loading')
      const { orgaId, cycleId } = formRef.getFieldsValue()
      console.log('orgId', orgaId)
      // const curOrg = orgValueArr[orgValueArr.length - 1] || ''
      const response = await exportBranchTotalSalary({
        orgaId: orgaId?.length > 0 ? orgaId[orgaId?.length - 1] : '',
        cycleId: cycleId?.format('YYYYMM')
      })
      downFile(response)
    } catch (error) {
      openNotification('导出失败', 1, 'error')
      console.error('Download failed:', error)
    }
  }
  // 查询重置
  const onReset = () => {
    const newReset = isReset + 1
    formRef.resetFields()
    // setOrgValueArr([])
    initDate()
    setIsReset(newReset)
  }

  // 切换分页
  const onChangePage = page => {
    const newPage = {
      total: page?.total,
      pageNum: page?.current,
      pageSize: page?.pageSize
    }
    setPagination(newPage)
  }

  // 搜索过滤空格
  const filter = (input, option) =>
    (option[0]?.enumName ?? '').toLowerCase().includes(input.toLowerCase()?.trim())

  return (
    <div className={`${'pt-[0.5rem] px-[0rem] h-full flex flex-col'} ${styles.employment_page}`}>
      <div ref={topRef} className={'bg-white pt-2 px-8 mb-2 h-[2.6rem]'}>
        <Form
          form={formRef}
          initialValues={{
            tag: '',
            cycleId: dayjs()
          }}
          onFinish={onFormFinish}
          // onFinishFailed={onFinishFailed}
          autoComplete='off'
        >
          <Row
            gutter={24}
            // justify="end"
          >
            <Col span={6}>
              <Form.Item label='月份' name='cycleId' wrapperCol={{ span: 20 }}>
                <DatePicker
                  className='w-full'
                  picker='month'
                  allowClear={false}
                  // onChange={e => setYear(dayjs(e).format('YYYY'))}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label='单位' name='orgaId' wrapperCol={{ span: 20 }}>
                <Cascader
                  allowClear={true}
                  changeOnSelect
                  expandTrigger='hover'
                  displayRender={labels => labels[labels.length - 1]}
                  options={orgList}
                  // onChange={handleOrgChange}
                  fieldNames={{
                    value: 'enumId',
                    label: 'enumName',
                    children: 'children'
                  }}
                  // showCheckedStrategy={Cascader.SHOW_CHILD}
                  // onChange={(value, selectedOptions) => {
                  //   console.log(value, selectedOptions)
                  //   setCascaderSelected(selectedOptions)
                  // }}
                  placeholder='请选择单位'
                  showSearch={{ filter }}
                  onSearch={value => console.log(value)}
                />
              </Form.Item>
            </Col>
            {/*<Col span={6}>*/}
            {/*  <Form.Item label="人员分类" name="perClass"*/}
            {/*             wrapperCol={{span: 20}}>*/}
            {/*    <Select className="w-full" options={perClassOptions}/>*/}
            {/*  </Form.Item>*/}
            {/*</Col>*/}

            <Col span={12}>
              <Form.Item labelCol={{ span: 0 }} wrapperCol={{ span: 24 }}>
                <Space size='small'>
                  <Button type='primary' htmlType='submit'>
                    查询
                  </Button>
                  <Button htmlType='button' onClick={() => onReset()}>
                    重置
                  </Button>
                  <Button
                    danger
                    ghost
                    icon={<DownloadOutlined />}
                    onClick={() => exportToExcelFun()}
                  >
                    导出
                  </Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>
      <div
        ref={contentRef}
        className='relative bg-white px-5 pt-2.5'
        style={{ height: `calc(100% - ${topRef.current?.offsetHeight + 15}px)` }}
      >
        <div
          ref={tableTopRef}
          className={`${'flex justify-between items-center overflow-hidden mb-2'} ${styles.animation_box} ${showTitle ? 'h-[1.6rem]' : 'h-0'}`}
        >
          <div className={'flex '}>
            {showTitle ? (
              <FullscreenExitOutlined
                className={`${styles.shousuo_icon} text-[1rem]`}
                onClick={() => {
                  setShowTitle(false)
                  setTimeout(() => {
                    initHeight()
                  }, 200)
                }}
              />
            ) : (
              <FullscreenOutlined
                className={`${styles.shousuo_icon} ${'absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10'}`}
                onClick={() => {
                  setShowTitle(true)
                  setTimeout(() => {
                    initHeight()
                  }, 200)
                }}
              />
            )}
            <div className='font-bold text-[0.8rem] ml-3'>
              数据列表
              <span className='text-[0.7rem]' style={{ color: '#939393' }}>
                （单位：万元）
              </span>
            </div>
          </div>
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <Select
              placeholder={'请选择'}
              className='w-full'
              allowClear={false}
              showSearch
              value={selectValue}
              mode='multiple'
              style={{ marginRight: '0.4rem', width: '15rem' }}
              maxTagCount={1}
              onChange={changeSelectColumn}
              // filterOption={handleSearchFilter}
            >
              {
                // prettier-ignore
                columnList.map(unit => {
                  const { enumId, enumName } = unit;
                  return <Select.Option key={enumId} value={enumId}>{enumName}</Select.Option>
                })
              }
            </Select>
            <Button
              danger
              ghost
              onClick={() => getBranchCalculate()}
              loading={calculateLoading}
              style={{ marginRight: '0.4rem' }}
            >
              计算
            </Button>
            <Button
              danger
              type='primary'
              onClick={() => {
                getConfirm()
              }}
              style={{ marginLeft: '0.4rem' }}
              loading={confirmLoading}
            >
              确认
            </Button>
          </div>
        </div>
        {tableData.length === 1 ? (
          <ColumnData
            tableData={tableData}
            tableHeight={columnTableHeight}
            columns={tableColumns}
            year={year}
          ></ColumnData>
        ) : (
          <ResizableTable
            className='salaryBudget_table'
            rowClassName={(_, i) => (i % 2 === 1 ? `customRow odd` : `customRow even`)}
            columns={tableColumns}
            dataSource={tableData}
            loading={tableLoading}
            bordered
            scroll={{
              y: `calc(${height}px - 0.625rem - 1.6rem - 0.5rem)`
            }}
            onChange={onChangePage}
            pagination={{
              ...pagination,
              showSizeChanger: true,
              showQuickJumper: true,
              pageSizeOptions: ['10', '20', '50']
            }}
          />
        )}
      </div>
    </div>
  )
}
export default Statistics
