import { useEffect, useState } from 'react'
import style from '../Index.module.scss'
import { formatMoney } from '@/hooks/format'
type BudgetData = {
  cycleId: string
  cityCode: string
  cityName: string
  mrBudget: string
  mrComplete: string
  mrCompleteRate: string
  profitBudget: string
  profitComplete: string
  profitCompleteRate: string
  ctBudget: string
  ctComplete: string
  ctCompleteRate: string
  salBudgetTotal: string
  basicSal: string
  salTd: string
  abilityPerAmount: string
  abilityPerPercent: string
  abilityPerRate: string
  abilityQuitAmount: string
  abilityQuitPercent: string
  abilityQuitRate: string
  cashSalAmount: string
  cashSalPercent: string
  cashSalPubAddRcvRate: string
  cashSalPubStockRcvRate: string
  cashSalZqAddRcvRate: string
  cashSalZqStockRcvRate: string
  cashSalAdvanceRcvRate: string
  achieveBudgetAmount: string
  achieveBudgetPercent: string
  achieveBudgetRcvRate: string
  achieveBudgetCiRcvRate: string
  achieveBudgetProfitRate: string
  achieveAddShareAmount: string
  achieveAddSharePercent: string
  achieveAddShareLastMarketAmount: string
  achieveAddShareMarketAmount: string
  achieveTermAddShareAmount: string
  achievePorfitAddShareAmount: string
  otherBudgetTotal: string
  otherBudgetSpecialReward: string
  otherBudgetTransport: string
  otherBudgetOvertime: string
  otherBudgetHoliday: string
  otherBudgetCollegeHouse: string
  otherBudgetDismiss: string
  otherBudgetDuty: string
  otherBudgetGraduate: string
  otherBudgetOs: string
  otherBudgetQuit: string
  otherBudgetOther: string
  otherBudgetTrans: string
  otherBudgetWarm: string
  salTotalDeliverBudget: string
  salTotalDeliverReal: string
  salTotalDeliverBalance: string
  key: number
}

const ColumnData: React.FC<any> = (props: any) => {
  const { tableData = [], tableHeight = 0, columns = [], year = 1000 } = props
  const [data, setData] = useState<BudgetData>({} as BudgetData)
  useEffect(() => {
    setData(tableData[0] || {})
  }, [tableData])
  const hasTargetTitle = (key: string) => {
    return columns.some(item => item.key === key)
  }
  return (
    <div
      className='overflow-auto'
      style={{
        height: `calc(${tableHeight}px - 0.625rem - 0.5rem - 10px)`
      }}
    >
      <div className={`${style.columnTable} flex flex-col w-fit overflow-auto mb-5`}>
        <div className='row'>
          <div className='area'>{data.cityName}</div>
          {/* <div className='value'>{data.cityName}</div> */}
        </div>
        {hasTargetTitle('list1') && (
          <div className='flex'>
            <div className='column col1'>{year}年业绩完成预算</div>
            <div>
              <div className='flex'>
                <div className='column col2'>主要营收</div>
                <div>
                  <div className='flex'>
                    <div className='column col3'>预算</div>
                    <div className='value'>{formatMoney(data.mrBudget)}</div>
                  </div>
                  <div className='flex'>
                    <div className='column col3'>完成情况</div>
                    <div className='value'>{formatMoney(data.mrComplete)}</div>
                  </div>
                  <div className='flex'>
                    <div className='column col3'>完成率</div>
                    <div className='value'>{data.mrCompleteRate}</div>
                  </div>
                </div>
              </div>
              <div className='flex'>
                <div className='column col2'>经营利润</div>
                <div>
                  <div className='flex'>
                    <div className='column col3'>预算</div>
                    <div className='value'>{formatMoney(data.profitBudget)}</div>
                  </div>
                  <div className='flex'>
                    <div className='column col3'>完成情况</div>
                    <div className='value'>{formatMoney(data.profitComplete)}</div>
                  </div>
                  <div className='flex'>
                    <div className='column col3'>完成率</div>
                    <div className='value'>{data.profitCompleteRate}</div>
                  </div>
                </div>
              </div>
              <div className='flex'>
                <div className='column col2'>终端收入</div>
                <div>
                  <div className='flex'>
                    <div className='column col3'>预算</div>
                    <div className='value'>{formatMoney(data.ctBudget)}</div>
                  </div>
                  <div className='flex'>
                    <div className='column col3'>完成情况</div>
                    <div className='value'>{formatMoney(data.ctComplete)}</div>
                  </div>
                  <div className='flex'>
                    <div className='column col3'>完成率</div>
                    <div className='value'>{data.ctCompleteRate}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
        {hasTargetTitle('list2') && (
          <div className='row'>
            <div className='label'>{year}年当月薪酬预算合计</div>
            <div className='value'>{formatMoney(data.salBudgetTotal)}</div>
          </div>
        )}
        {hasTargetTitle('list3') && (
          <div className='row'>
            <div className='label'>基本保障薪酬(65%)</div>
            <div className='value'>{formatMoney(data.basicSal)}</div>
          </div>
        )}
        {hasTargetTitle('list4') && (
          <div className='row'>
            <div className='label'>政策提低保障薪酬</div>
            <div className='value'>{formatMoney(data.salTd)}</div>
          </div>
        )}
        {hasTargetTitle('list5') && (
          <div className='flex'>
            <div className='column col1'>人才结构保障薪酬</div>
            <div>
              <div className='flex'>
                <div className='column col2'>挂钩人均创利薪酬(3%)</div>
                <div>
                  <div className='flex'>
                    <div className='column col3'>金额</div>
                    <div className='value'>{formatMoney(data.abilityPerAmount)}</div>
                  </div>
                  <div className='flex'>
                    <div className='column col3'>人均创利完成系数</div>
                    <div className='value'>{data.abilityPerPercent}</div>
                  </div>
                  <div className='flex'>
                    <div className='column col3'>人均创利预算完成率</div>
                    <div className='value'>{data.abilityPerRate}</div>
                  </div>
                </div>
              </div>
              <div className='flex'>
                <div className='column col2'>挂钩市场化退出率薪酬(2%)</div>
                <div>
                  <div className='flex'>
                    <div className='column col3'>金额</div>
                    <div className='value'>{formatMoney(data.abilityQuitAmount)}</div>
                  </div>
                  <div className='flex'>
                    <div className='column col3'>市场化退出率系数</div>
                    <div className='value'>{data.abilityQuitPercent}</div>
                  </div>
                  <div className='flex'>
                    <div className='column col3'>市场化退出率</div>
                    <div className='value'>{data.abilityQuitRate}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
        {hasTargetTitle('list6') && (
          <div className='flex'>
            <div className='column col1'>挂钩营业现金比率薪酬</div>
            <div>
              <div className='flex'>
                <div className='column col2'>挂钩营业现金比率(5%)</div>
                <div>
                  <div className='flex'>
                    <div className='column col3'>金额</div>
                    <div className='value'>{formatMoney(data.cashSalAmount)}</div>
                  </div>
                  <div className='flex'>
                    <div className='column col3'>系数</div>
                    <div className='value'>{data.cashSalPercent}</div>
                  </div>
                  <div className='flex'>
                    <div className='column col3'>公众新增应收账款占收比完成率</div>
                    <div className='value'>{data.cashSalPubAddRcvRate}</div>
                  </div>
                  <div className='flex'>
                    <div className='column col3'>公众存量应收账款回款率完成率</div>
                    <div className='value'>{data.cashSalPubStockRcvRate}</div>
                  </div>
                  <div className='flex'>
                    <div className='column col3'>政企新增应收账款占收比完成率</div>
                    <div className='value'>{data.cashSalZqAddRcvRate}</div>
                  </div>
                  <div className='flex'>
                    <div className='column col3'>政企存量应收账款回款率完成率</div>
                    <div className='value'>{data.cashSalZqStockRcvRate}</div>
                  </div>
                  <div className='flex'>
                    <div className='column col3'>净预收账款占收比完成率</div>
                    <div className='value'>{data.cashSalAdvanceRcvRate}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
        {hasTargetTitle('list7') && (
          <div className='flex'>
            <div className='column col1'>挂钩业绩</div>
            <div>
              <div className='flex'>
                <div className='column col2'>挂钩业绩预算(10%)</div>
                <div>
                  <div className='flex'>
                    <div className='column col3'>金额</div>
                    <div className='value'>{formatMoney(data.achieveBudgetAmount)}</div>
                  </div>
                  <div className='flex'>
                    <div className='column col3'>系数</div>
                    <div className='value'>{data.achieveBudgetPercent}</div>
                  </div>
                  <div className='flex'>
                    <div className='column col3'>联网通信收入完成率</div>
                    <div className='value'>{data.achieveBudgetRcvRate}</div>
                  </div>
                  <div className='flex'>
                    <div className='column col3'>算网数智收入完成率</div>
                    <div className='value'>{data.achieveBudgetCiRcvRate}</div>
                  </div>
                  <div className='flex'>
                    <div className='column col3'>经营利润完成率</div>
                    <div className='value'>{data.achieveBudgetProfitRate}</div>
                  </div>
                </div>
              </div>
              <div className='flex'>
                <div className='column col2'>主营收入增量受益分享</div>
                <div>
                  <div className='flex'>
                    <div className='column col3'>金额</div>
                    <div className='value'>{formatMoney(data.achieveAddShareAmount)}</div>
                  </div>
                  <div className='flex'>
                    <div className='column col3'>市场份额系数Q</div>
                    <div className='value'>{data.achieveAddSharePercent}</div>
                  </div>
                  <div className='flex'>
                    <div className='column col3'>{year - 1}年市场份额</div>
                    <div className='value'>{data.achieveAddShareLastMarketAmount}</div>
                  </div>
                  <div className='flex'>
                    <div className='column col3'>{year}年市场份额</div>
                    <div className='value'>{data.achieveAddShareMarketAmount}</div>
                  </div>
                </div>
              </div>
              <div className='flex'>
                <div className='column col2'>终端收入增量收益分享</div>
                <div>
                  <div className='flex'>
                    <div className='column col3'>金额</div>
                    <div className='value'>{formatMoney(data.achieveTermAddShareAmount)}</div>
                  </div>
                </div>
              </div>
              <div className='flex'>
                <div className='column col2'>利润增量受益分享</div>
                <div>
                  <div className='flex'>
                    <div className='column col3'>金额</div>
                    <div className='value'>{formatMoney(data.achievePorfitAddShareAmount)}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
        {hasTargetTitle('list8') && (
          <div className='flex'>
            <div className='column col1'>{year}年当月其他追加预算</div>
            <div>
              <div className='flex'>
                <div className='column col4'>合计</div>
                <div className='value'>{formatMoney(data.otherBudgetTotal)}</div>
              </div>
              <div className='flex'>
                <div className='column col4'>专项奖励</div>
                <div className='value'>{formatMoney(data.otherBudgetSpecialReward)}</div>
              </div>
              <div className='flex'>
                <div className='column col4'>交通补助</div>
                <div className='value'>{formatMoney(data.otherBudgetTransport)}</div>
              </div>
              <div className='flex'>
                <div className='column col4'>加班费核增（全年）</div>
                <div className='value'>{formatMoney(data.otherBudgetOvertime)}</div>
              </div>
              <div className='flex'>
                <div className='column col4'>过节费</div>
                <div className='value'>{formatMoney(data.otherBudgetHoliday)}</div>
              </div>
              <div className='flex'>
                <div className='column col4'>大学生租房补贴</div>
                <div className='value'>{formatMoney(data.otherBudgetCollegeHouse)}</div>
              </div>
              <div className='flex'>
                <div className='column col4'>退职</div>
                <div className='value'>{formatMoney(data.otherBudgetDismiss)}</div>
              </div>
              <div className='flex'>
                <div className='column col4'>墩苗、下沉、值守人员（全年）</div>
                <div className='value'>{formatMoney(data.otherBudgetDuty)}</div>
              </div>
              <div className='flex'>
                <div className='column col4'>{year}年大学生</div>
                <div className='value'>{formatMoney(data.otherBudgetGraduate)}</div>
              </div>
              <div className='flex'>
                <div className='column col4'>融通外包费用</div>
                <div className='value'>{formatMoney(data.otherBudgetOs)}</div>
              </div>
              <div className='flex'>
                <div className='column col4'>离职补偿金</div>
                <div className='value'>{formatMoney(data.otherBudgetQuit)}</div>
              </div>
              <div className='flex'>
                <div className='column col4'>其他</div>
                <div className='value'>{formatMoney(data.otherBudgetOther)}</div>
              </div>
              <div className='flex'>
                <div className='column col4'>转递薪酬</div>
                <div className='value'>{formatMoney(data.otherBudgetTrans)}</div>
              </div>
              <div className='flex'>
                <div className='column col4'>取暖补贴</div>
                <div className='value'>{formatMoney(data.otherBudgetWarm)}</div>
              </div>
            </div>
          </div>
        )}
        {hasTargetTitle('list9') && (
          <div className='flex'>
            <div className='column col1 last'>{year}当月薪酬总量兑现情况</div>
            <div>
              <div className='flex'>
                <div className='column col4'>{year}年当月薪酬预算</div>
                <div className='value'>{formatMoney(data.salTotalDeliverBudget)}</div>
              </div>
              <div className='flex'>
                <div className='column col4'>{year}年当月实际兑现</div>
                <div className='value'>{formatMoney(data.salTotalDeliverReal)}</div>
              </div>
              <div className='flex'>
                <div className='column col4 last'>结余</div>
                <div className='value last'>{formatMoney(data.salTotalDeliverBalance)}</div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default ColumnData
