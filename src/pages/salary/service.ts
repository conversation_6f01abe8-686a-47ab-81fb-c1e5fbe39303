import request from '@/request'

const effectService = {
  // 查询枚举
  getEnumType: params => {
    return request.post<any>('/zhyy/employee/getEnumType', params)
  },

  // 用工变动统计
  getEmployeeChangePag: params => {
    return request.post<any>('/zhyy/employee/getEmployeeChangePag', params)
  },

  // 人员岗位信息-导出模板
  exportEmployeeExcel: params => {
    return request.post<any>('/zhyy/employee/exportEmployeeExcel', params, {
      responseType: 'blob'
    })
  },

  // 人员统计-个人
  getEmployeePerPag: params => {
    return request.post<any>('/zhyy/employee/getEmployeePerPag', params)
  },

  // 人员岗位信息-导出模板
  exportEmployeePerExcel: params => {
    return request.post<any>('/zhyy/employee/exportEmployeePerExcel', params, {
      responseType: 'blob'
    })
  },

  // 用工变动-导出模板
  exportEmployeeChangeExcel: params => {
    return request.post<any>('/zhyy/employee/exportEmployeeChangeExcel', params, {
      responseType: 'blob'
    })
  },

  // 人员组织-导出模板
  exportEmployeeOrgExcel: params => {
    return request.post<any>('/zhyy/employee/exportEmployeeOrgExcel', params, {
      responseType: 'blob'
    })
  },

  // 查询组织级联-5级
  build5LevelOrgTree: params => {
    return request.post<any>('/zhyy/employee/org/build5LevelOrgTree', params)
  },

  // 查询组织级联-2层
  build4LevelOrgTree2: params => {
    return request.post<any>('/zhyy/employee/org/build4LevelOrgTree2', params)
  },

  // 查询组织级联-4级
  build4LevelOrgTree: params => {
    return request.post<any>('/zhyy/employee/org/build4LevelOrgTree', params)
  },

  // 查询人员统计表-组织
  getEmployeeOrgPag: params => {
    return request.post<any>('/zhyy/employee/getEmployeeOrgPag', params)
  },
  // 分公司工资总额预算表格查询
  getBranchTotalSalary: params => {
    return request.get<any>('/zhyy/salary/salaryBudgetMain/list', { params })
  },
  //分公司工资总额预算导出
  exportBranchTotalSalary: params => {
    return request.get<any>('/zhyy/salary/salaryBudgetMain/exportExcel', {
      params,
      responseType: 'blob'
    })
  },
  getBranchTotalSalaryCalculate: params => {
    return request.post<any>('/zhyy/salary/salaryBudgetMain/calculate', params)
  },
  getBranchTotalSalaryConfirm: params => {
    return request.post<any>('/zhyy/salary/salaryBudgetMain/confirm', params)
  },

  getSalaryForwardList: params => {
    return request.get<any>('/zhyy/salary/salaryForward/list', { params })
  },

  exportSalaryForward: params => {
    return request.get<any>('/zhyy/salary/salaryForward/exportExcel', {
      params,
      responseType: 'blob'
    })
  },
  getSalaryForwardSaveBatch: params => {
    return request.post<any>('/zhyy/salary/salaryForward/saveBatch', params)
  },
  getFlowComment: params => {
    return request.get<any>('/zhyy/salary/salaryForward/getFlowComment', { params })
  },
  getCompleteTask: params => {
    return request.post<any>('/zhyy/salary/salaryForward/completeTask', params)
  },
  getTerminateTask: params => {
    return request.post<any>('/zhyy/salary/salaryForward/terminateTask', params)
  },
  getRejectTask: params => {
    return request.post<any>('/zhyy/salary/salaryForward/rejectTask', params)
  },
  saveEditBatch: params => {
    return request.post<any>('/zhyy/salary/salaryForward/saveBatch', params)
  },

}

export default effectService
