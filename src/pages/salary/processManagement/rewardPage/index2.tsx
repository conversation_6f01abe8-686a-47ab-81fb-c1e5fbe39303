import {useState, useEffect, useRef} from 'react'
import {
    Table,
    Button,
    Input,
    Modal,
    Select,
    Form,
    Row,
    Col,
    GetProp,
    DatePicker, message, Upload, Cascader, CascaderProps, Popconfirm, Tooltip,
} from 'antd'
import {
    PlusOutlined,
    DownloadOutlined,
    UploadOutlined,
    InboxOutlined,
    FullscreenExitOutlined,
    FullscreenOutlined,
    DeleteOutlined, InfoCircleOutlined
} from '@ant-design/icons';
import EditTable from './components/edit-table/index'
import SvgIcon from '@/components/SvgIcons'
import effectService from '@/pages/effect/employment/service.ts'
import {useRequest} from '@/hooks'
import proManageService from "@/pages/salary/processManagement/service.ts";
import dayjs from "dayjs";
import {downFile, openNotification} from "@/utils/down.tsx";
// @ts-ignore
import styles from '../Index.module.scss'
import peopleService from "@/pages/position/people/service.ts";
import {useDebounce} from "@/utils/debounce.ts";

const {Dragger} = Upload
type DefaultOptionType = GetProp<CascaderProps, 'options'>[number]

const Empty = () => {
    // 表格数据
    const columns = [
        {
            title: '月份',
            key: 'cycleId',
            dataIndex: 'cycleId',
            actionType: 'datePicker',
            align: 'center',
            fixed: 'left',
            width: 50,
        },
        {
            title: '专项奖励名称',
            key: 'rewardsName',
            dataIndex: 'rewardsName',
            align: 'center',
            fixed: 'left',
            width: 100,
        },
        {
            title: '专项奖励文号',
            key: 'rewardsNo',
            dataIndex: 'rewardsNo',
            align: 'center',
            fixed: 'left',
            width: 100,
        },
        {
            title: '专项奖励金额',
            key: 'rewardsAmount',
            dataIndex: 'rewardsAmount',
            align: 'center',
            fixed: 'left',
            width: 100,
        },
        {
            title: '专项奖励层级',
            key: 'rewardsLevel',
            dataIndex: 'rewardsLevel',
            actionType: 'select',
            actionOptionName: 'levelList',
            align: 'center',
            fixed: 'left',
            width: 100,
            // render: (text) => {
            //     return levelOption?.find(item => item.value === text)?.label || ''
            // },
        },
        {
            title: '专项奖励',
            children: [
                {
                    title: '专业线',
                    dataIndex: 'rewardsProfLine',
                    key: 'rewardsProfLine',
                    align: 'center',
                    width: 75,
                    actionType: 'select',
                    actionOptionName: 'profLineList',
                },
                {
                    title: '奖项分类',
                    dataIndex: 'rewardsType',
                    key: 'rewardsType',
                    align: 'center',
                    width: 75,
                    actionType: 'select',
                    actionOptionName: 'typeList',
                },
                {
                    title: '组织名称',
                    dataIndex: 'rewardsOrgName',
                    key: 'rewardsOrgName',
                    actionType: 'cascader',
                    align: 'center',
                    width: 120,
                },
                {
                    title: '组织金额',
                    dataIndex: 'rewardsOrgAmount',
                    key: 'rewardsOrgAmount',
                    align: 'center',
                    width: 75,
                },
                {
                    title: '员工编号',
                    dataIndex: 'rewardsStaffId',
                    key: 'rewardsStaffId',
                    align: 'center',
                    width: 75,
                },
                {
                    title: '员工姓名',
                    dataIndex: 'rewardsStaffName',
                    key: 'rewardsStaffName',
                    align: 'center',
                    width: 75,
                },
                {
                    title: '员工金额',
                    dataIndex: 'rewardsStaffAmount',
                    key: 'rewardsStaffAmount',
                    align: 'center',
                    width: 75,
                },
                {
                    title: '备注',
                    dataIndex: 'remark',
                    key: 'remark',
                    align: 'center',
                    width: 120,
                },
            ],
        },
        {
            title: (
                <>
                    <span>操作</span>
                    <Tooltip title='修改总金额时，同文号的总金额都会修改。'>
                        <InfoCircleOutlined />
                    </Tooltip>
                </>
            ),
            dataIndex: 'action',
            key: 'action',
            align: 'center',
            fixed: 'right',
            width: 120,
            render: (_text, record) => (
                <div className='action'>
                    <Popconfirm
                        title='修改总金额时，同文号的总金额都会修改。'
                        onConfirm={() => handleeditRecord(record)}
                        okText=' 确认'
                        cancelText='取消'
                        //disabled={!flag}
                    >
                        <Button type='link'>修改</Button>
                    </Popconfirm>
                </div>
            )
        }
    ]
    const topRef = useRef(null)
    const contentRef = useRef(null)
    const tableTopRef = useRef(null)
    const [formRef] = Form.useForm()
    const [formEditRef] = Form.useForm()
    const [formDelRef] = Form.useForm()
    const [tableColumns, setTableColumns] = useState<any>(columns)
    const [tableLoading, setTableLoading] = useState(false)
    const [tableData, setTableData] = useState([])
    const [addModalOpen, setAddModalOpen] = useState(false)
    const [upModalOpen, setOpModalOpen] = useState(false)
    const [deleteModalOpen, setDeleteModalOpen] = useState(false)
    const [fileList, setFileList] = useState([])
    const [orgList, setOrgList] = useState<any>([])
    const [levelList, setLevelList] = useState<any>([])
    const [profLineList, setProfLineList] = useState<any>([])
    const [typeList, setTypeList] = useState<any>([])
    const [isReset, setIsReset] = useState<number>(0)
    const [height, setHeight] = useState(0)
    const [selectedRows, setSelectedRows] = useState([])
    const delTableListRef = useRef([])
    const [delTableList, setDelTableList] = useState([])
    const [delTableLoading, setDelTableLoading] = useState(false)
    const [showTitle, setShowTitle] = useState<boolean>(true)
    const [editRecord, setEditRecord] = useState<any>({})
    const [editModalOpen, setEditModalOpen] = useState(false)
    const [editLoading, setEditLoading] = useState(false)
    const [editDisabled, setEditDisabled] = useState(false);
    const [delPagination, setDelPagination] = useState({
        total: 0,
        pageNum: 1,
        pageSize: 5
    })
    const [pagination, setPagination] = useState({
        total: 0,
        pageNum: 1,
        pageSize: 50
    })
    const { runAsync: getEnumType } = useRequest(peopleService.getEnumType, { manual: true })
    const {runAsync: build4LevelOrgTree} = useRequest(effectService.build4LevelOrgTree, {
        manual: true
    })
    const {runAsync: getSpecialRewards} = useRequest(proManageService.getSpecialRewards, {
        manual: true
    })
    const {runAsync: getEmployeeByEmpId} = useRequest(proManageService.getEmployeeByEmpId, {
        manual: true
    })
    const {runAsync: getDocNumList} = useRequest(proManageService.getDocNumList, {
        manual: true
    })
    const {runAsync: addSpecialRewards} = useRequest(proManageService.addSpecialRewards, {
        manual: true
    })
    const {runAsync: delBatchByRewardsNo} = useRequest(proManageService.delBatchByRewardsNo, {
        manual: true
    })
    const {runAsync: exportSpecialRewards} = useRequest(proManageService.exportSpecialRewards, {
        manual: true
    })
    const {runAsync: uploadSpecialRewards} = useRequest(proManageService.uploadSpecialRewards, {
        manual: true
    })
    const {runAsync: downloadSalaryTemplate} = useRequest(proManageService.downloadSalaryTemplate, {
        manual: true
    })

    useEffect(() => {
        setTableColumns(columns)
        getEnumTypes()
        initDate();
        queryTableData()
    }, [])

    useEffect(() => {
        initHeight()
    }, [(document.querySelector('.rewardPage_table .ant-table-header') || {})['offsetHeight']])

    useEffect(() => {
        initHeight()
    }, [tableData])

    useEffect(() => {
        if (isReset > 0) {
            queryTableData()
        }
    }, [isReset])

    useEffect(() => {
        if (pagination?.total > 0) {
            queryTableData()
        }
    }, [pagination.pageNum, pagination.pageSize])

    useEffect(() => {
        if (delPagination?.total > 0) {
            queryTableData()
        }
    }, [delPagination.pageNum, delPagination.pageSize])

    useEffect(() => {
        setEditDisabled(formEditRef.getFieldValue('rewardsType') === 'staff')
    }, [formEditRef])

    const initHeight = () => {
        const headerHeight = (document.querySelector('.rewardPage_table .ant-table-header') || {})[
            'offsetHeight'
            ]
        const pageHeight = (document.querySelector('.rewardPage_table .ant-table-pagination') ||
            {})['offsetHeight'] || 26
        if (headerHeight) {
            setHeight(tableTopRef.current.offsetHeight + headerHeight + pageHeight)
        }
    }

    // 初始化查询条件
    const initDate = () => {
        const date = dayjs().subtract(1, 'month')
        formRef.setFieldsValue({
            cycleId: date,
        })
    }

    const handleeditRecord = (record) => {
        setEditModalOpen(record)
        setEditRecord(record);
    }

    const commitEditData = () => {
        console.log('提交修改数据：', formEditRef.getFieldsValue())
        setEditLoading(false);
    }

    const getEnumTypes = async () => {
        const [
            [orgError, orgData],
            [levelError, levelData],
            [profLineError, profLineData],
            [typeError, typeData]
        ] = await Promise.all([
            build4LevelOrgTree(),
            getEnumType({ code: 'specialRewardsLevel' }),
            getEnumType({ code: 'specialRewardsProfLine' }),
            getEnumType({ code: 'specialRewardsType' })
        ])
        if (orgError || levelError || profLineError || typeError) {
            return
        }
        if (orgData.STATUS === '0000') {
            setOrgList(orgData.DATA)
        }
        if (levelData.STATUS === '0000') {
            setLevelList(levelData.DATA)
        }
        if (profLineData.STATUS === '0000') {
            setProfLineList(profLineData.DATA)
        }
        if (typeData.STATUS === '0000') {
            setTypeList(typeData.DATA)
        }
    }

    // 查询表格数据
    const queryTableData = async () => {
        const values = formRef.getFieldsValue()
        setTableLoading(true)
        const [error, res] = await getSpecialRewards({
            ...values,
            cycleId: values?.cycleId?.format('YYYYMM'),
            orgaId: values?.orgaId ? values?.orgaId[values?.orgaId?.length - 1] : '',
            ...pagination
        })
        setTableLoading(false)
        if (error) {
            return
        }
        if (res.STATUS === '0000') {
            const {
                DATA: {data}
            } = res
            setTableData(data)
            setPagination({
                ...pagination,
                total: res.DATA?.totalCount
            })
        } else {
            message.error(res?.MESSAGE)
        }
    }

    // 点击导出
    const exportToExcelFun = async () => {
        try {
            // 调用接口获取 Blob 文件数据
            openNotification('正在导出', 0, 'loading')
            const values = formRef.getFieldsValue();
            const response = await exportSpecialRewards({
                ...values,
                cycleId: values?.cycleId?.format('YYYYMM'),
                orgaId: values?.orgaId ? values?.orgaId[values?.orgaId?.length - 1] : '',
            })
            downFile(response)
        } catch (error) {
            openNotification('导出失败', 1, 'error')
            console.error('Download failed:', error)
        }
    }

    const handleAddData = async (data) => {
        setTableLoading(true)
        const [error, res] = await addSpecialRewards(data?.map(item => ({
            ...item,
            cycleId: item?.cycleId?.format('YYYYMM'),
            rewardsOrgId: item.rewardsOrgName?.length > 0 ? item.rewardsOrgName[item.rewardsOrgName.length-1].orgId : '',
            rewardsOrgName: item.rewardsOrgName?.length > 0 ? item.rewardsOrgName[item.rewardsOrgName.length-1].orgName : '',
        })))
        // setTableLoading(false)
        if (error) {
            message.error('插入失败')
            setTableLoading(false)
            return
        }
        if (res.STATUS === '0000') {
            message.success(res?.DATA)
            setAddModalOpen(false)
            queryTableData();
        } else {
            message.error(res?.MESSAGE)
        }
    }

    const handleDownload = async () => {
        try {
            // 调用接口获取 Blob 文件数据
            openNotification('正在导出', 0, 'loading')
            const response = await downloadSalaryTemplate({
                templateId: 'SPECIAL_REWARDS'
            })
            downFile(response)
        } catch (error) {
            openNotification('导出失败', 1, 'error')
            console.error('Download failed:', error)
        }
    }


    const queryDelTableList = async () => {
        const values = formRef.getFieldsValue()
        setDelTableLoading(true)
        const [error, res] = await getDocNumList({
            ...values,
            cycleId: values?.cycleId?.format('YYYYMM'),
            orgaId: values?.orgaId ? values?.orgaId[values?.orgaId?.length - 1] : '',
            ...delPagination
        })
        setDelTableLoading(false)
        if (error) {
            return
        }
        if (res.STATUS === '0000') {
            const {
                DATA: {data}
            } = res
            delTableListRef.current = data;
            setDelTableList(data)
            setDelPagination({
                ...delPagination,
                total: res.DATA?.totalCount
            })
        } else {
            message.error(res?.MESSAGE)
        }
    }

    const handleSearchName = async (e) => {
        const [error, res] = await getEmployeeByEmpId({
            empId: e.target.value,
        })
        if (error) {
            return
        }
        if (res.STATUS === '0000') {
            const {
                DATA: userInfo
            } = res
            formRef.setFieldsValue({empName: userInfo?.employeeName})
        } else {
            message.error(res?.MESSAGE)
        }
    }

    const debouncedChange = useDebounce(handleSearchName, 500);

    const showDelModal = () => {
        setDeleteModalOpen(true);
        queryDelTableList();
    }

    // 文号查询
    const onDelFormFinish = values => {
        console.log('Success:', values?.rewardsNo);
        let newList = delTableListRef.current;
        if (values?.rewardsNo) {
            newList = delTableList?.filter(item => item.rewardsNo === values?.rewardsNo?.trim());
        }
        setDelTableList(newList);
    }

    // 点击查询
    const onFormFinish = values => {
        console.log('Success:', values)
        queryTableData()
    }

    // 查询重置
    const onReset = () => {
        const newReset = isReset + 1;
        formRef.resetFields();
        initDate();
        setIsReset(newReset)
    }

    // 切换分页
    const onChangePage = page => {
        const newPage = {
            total: page?.total,
            pageNum: page?.current,
            pageSize: page?.pageSize
        }
        setPagination(newPage)
    }

    const onChangeDelPage = page => {
        const newPage = {
            total: page?.total,
            pageNum: page?.current,
            pageSize: page?.pageSize
        }
        setDelPagination(newPage)
    }

    const handleCancel = () => {
        setOpModalOpen(false)
        setFileList([])
    }

    const handleDelCancel = () => {
        setDeleteModalOpen(false);
        setDelTableList([]);
        delTableListRef.current = [];
        formDelRef.resetFields();
    }

    //上传
    const handleOk = () => {
        if (fileList?.length > 0) {
            uploadFileFun()
        } else {
            message.error('请先选择文件上传')
        }
    }

    //删除
    const handleDelOk = () => {
        if (selectedRows?.length > 0) {
            handleDelData()
        } else {
            message.error('请先选择文件上传')
        }
    }

    const handleDelData = async () => {
        setTableLoading(true)
        const [error, res] = await delBatchByRewardsNo({
            rewardsNo: selectedRows?.map(item => item.rewardsNo)
        })
        // setTableLoading(false)
        if (error) {
            message.error('删除失败')
            setTableLoading(false)
            return
        }
        if (res.STATUS === '0000') {
            message.success(res?.DATA)
            handleDelCancel()
            queryTableData();
        } else {
            message.error(res?.MESSAGE)
        }
    }

    const uploadFileFun = async () => {
        const formData = new FormData()
        fileList
            .map(file => file?.originFileObj)
            .forEach(file => {
                formData.append('file', file)
            })
        formData.append('tag', '1')
        const [error, res] = await uploadSpecialRewards(formData)
        if (error) {
            return
        }
        if (res.STATUS === '0000') {
            setOpModalOpen(false)
            message.success(res?.DATA)
            queryTableData();
        } else {
            message.error(res?.MESSAGE)
        }
    }

    const filter = (inputValue: string, path: DefaultOptionType[]) =>
        path.some(
            option =>
                (option.orgName as string).toLowerCase().indexOf(inputValue.trim().toLowerCase()) > -1
        )

    return (
        <div className={`${'h-full pt-[0.8rem] px-[0.8rem]'} ${styles.processManagement_page} ${styles.rewardPage}`}>
            <div ref={topRef} className='bg-white pt-[0.75rem] pb-[0.75rem] px-[1.75rem] mb-[0.8rem]'>
                <Form labelCol={{span: 6}}
                      form={formRef}
                      onFinish={onFormFinish}
                >
                    <Row gutter={24}>
                        <Col span={6}>
                            <Form.Item name='cycleId' label='月份'>
                                <DatePicker picker='month' style={{width: '100%'}}/>
                            </Form.Item>
                        </Col>
                        <Col span={6}>
                            <Form.Item name='rewardsNo' label='专项奖励文号'>
                                <Input
                                    placeholder='请输入专项奖励文号'
                                    allowClear
                                    style={{width: '100%'}}
                                />
                            </Form.Item>
                        </Col>
                        <Col span={6}>
                            <Form.Item name='rewardsLevel' label='专项奖励层级'>
                                <Select placeholder='请选择专项奖励层级' allowClear>
                                    {
                                        levelList?.map(item => (
                                            <Select.Option key={item?.enumId}
                                                           value={item?.enumId}>{item?.enumName}</Select.Option>
                                        ))
                                    }
                                    {/*<Select.Option key='grp' value='grp'>集团级</Select.Option>*/}
                                    {/*<Select.Option key='com' value='com'>公司级</Select.Option>*/}
                                    {/*<Select.Option key='sub' value='sub'>各单位级</Select.Option>*/}
                                </Select>
                            </Form.Item>
                        </Col>
                        <Col span={6}>
                            <Form.Item name='orgaId' label='组织'>
                                <Cascader
                                    allowClear={false}
                                    changeOnSelect
                                    expandTrigger='hover'
                                    displayRender={labels => labels[labels.length - 1]}
                                    options={orgList}
                                    fieldNames={{
                                        value: 'orgId',
                                        label: 'orgName',
                                        children: 'children'
                                    }}
                                    placeholder='请选择组织'
                                    showSearch={{filter}}
                                    onSearch={value => console.log(value)}
                                />
                            </Form.Item>
                        </Col>
                        <Col span={6}>
                            <Form.Item name='empId' label='员工编号'>
                                <Input
                                    placeholder='请输入员工编号'
                                    allowClear
                                    style={{width: '100%'}}
                                    onChange={debouncedChange}
                                />
                            </Form.Item>
                        </Col>
                        <Col span={6}>
                            <Form.Item name='empName' label='员工姓名'>
                                <Input
                                    placeholder='请输入员工姓名'
                                    allowClear
                                    style={{width: '100%'}}
                                />
                            </Form.Item>
                        </Col>
                        <Col span={6}>
                            <div className='text-right'>
                                <Button type='primary' htmlType='submit'>查询</Button>
                                <Button className='ml-[0.4rem]' onClick={() => onReset()}>
                                    重置
                                </Button>
                            </div>
                        </Col>
                    </Row>
                </Form>
            </div>
            <div
                ref={contentRef}
                className='relative pb-[0.5rem] pt-[0.1rem] px-8 mt-[1rem] bg-white'
                style={{height: `calc(100% - ${topRef.current?.offsetHeight + 17}px)`}}
            >
                <div ref={tableTopRef}
                     className={`${'flex justify-between items-center overflow-hidden mb-[0.1rem]'} ${styles.animation_box} ${showTitle ? 'h-[2rem]' : 'h-0'}`}>
                    <>
                        {
                            showTitle ?
                                <FullscreenExitOutlined
                                    className={`${styles.shousuo_icon} ${'absolute top-[0.55rem] left-[0.3rem] text-[1rem] z-10'}`}
                                    onClick={() => {
                                        setShowTitle(false);
                                        setTimeout(() => {
                                            initHeight();
                                        }, 300)
                                    }}/> :
                                <FullscreenOutlined
                                    className={`${styles.shousuo_icon} ${'absolute top-[0.55rem] left-[0.3rem] text-[1rem] z-10'}`}
                                    onClick={() => {
                                        setShowTitle(true);
                                        setTimeout(() => {
                                            initHeight();
                                        }, 300)
                                    }}/>
                        }
                    </>
                    <div>数据列表</div>
                    <div className='flex'>
                        <div className='flex items-center gap-x-[0.25rem] ml-[0.6rem]'>
                            <SvgIcon name='excel' width={20} height={20}/>
                            <span
                                className='text-[#E60027] cursor-pointer'
                                onClick={() => handleDownload()}
                            >
                              下载导入模版
                            </span>
                        </div>
                        <Button className='ml-[0.4rem]' danger onClick={() => {
                            setOpModalOpen(true)
                        }}><UploadOutlined/>导入</Button>
                        <Button type='primary' className='ml-[0.4rem]' onClick={() => {
                            setAddModalOpen(true)
                        }}><PlusOutlined/>新增</Button>
                        <Button type="primary" className='ml-[0.4rem]' onClick={() => {
                            showDelModal()
                        }}><DeleteOutlined />删除</Button>
                        <Button className='ml-[0.4rem]' danger onClick={() => exportToExcelFun()}><DownloadOutlined/>导出</Button>
                    </div>
                </div>
                <Table
                    className='rewardPage_table'
                    columns={tableColumns}
                    dataSource={tableData}
                    loading={tableLoading}
                    bordered
                    scroll={{
                        // x: 'max-content',
                        y: `calc(${contentRef.current?.offsetHeight - height}px - 1.4rem)`
                    }}
                    onChange={onChangePage}
                    pagination={{
                        ...pagination,
                        total: pagination?.total,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        pageSizeOptions: ['10', '20', '50']
                    }}
                />
            </div>
            <Modal
                title='- 新增数据 -'
                destroyOnClose={true}
                open={addModalOpen}
                // @ts-ignore
                wrapClassName={styles.add_table_modal}
                centered
                footer={null}
                onCancel={() => setAddModalOpen(false)}
                width={'80%'}
            >
                <EditTable columns={columns} submitData={(value) => handleAddData(value)} cascaderOption={orgList}
                           levelList={levelList} profLineList={profLineList} typeList={typeList}/>
            </Modal>

            <Modal
                title='- 文件上传 -'
                destroyOnClose={true}
                open={upModalOpen}
                centered
                className={`${styles.add_table_modal} ${styles.detail_modal}`}
                footer={null}
                // okText='上传'
                // onOk={handleOk}
                onCancel={handleCancel}
            >
                <div className='mt-4'>
                    <Row>
                        <Col span={22} offset={1} className='h-[10rem]'>
                            <Dragger
                                {...{
                                    // name: 'file',
                                    // data: {
                                    //     tag: "1"
                                    // },
                                    // action: `/zhyy/employee/uploadEmployeeExcel`,
                                    action: '',
                                    maxCount: 1,
                                    multiple: false,
                                    fileList,
                                    beforeUpload(file, fileList) {
                                        console.log(file, fileList)
                                        return false
                                    },
                                    onChange(info) {
                                        const {status} = info.file
                                        if (status !== 'uploading') {
                                            console.log(info.file, info.fileList)
                                            setFileList(info.fileList)
                                        }
                                    }
                                }}
                            >
                                <p className='ant-upload-drag-icon'>
                                    <InboxOutlined style={{color: '#F14846'}}/>
                                </p>
                                <p className='ant-upload-text'>点击或将文件拖拽到这里上传</p>
                                <p className='ant-upload-hint'>支持excel格式的文件。</p>
                            </Dragger>
                        </Col>
                    </Row>
                    <div style={{display: 'flex', justifyContent: 'center', marginTop: '2rem'}}>
                        <Button danger onClick={handleCancel}>
                            取消
                        </Button>

                        <Popconfirm
                            title=''
                            description='导入文件如有重复数据，将会被覆盖，请确认是否上传。'
                            onConfirm={handleOk}
                            onCancel={() => {}}
                            okText='确认'
                            cancelText='取消'
                        >
                            <Button
                                danger
                                type='primary'
                                style={{marginLeft: '1rem'}}
                                disabled={fileList.length < 1}
                            >
                                上传
                            </Button>
                        </Popconfirm>
                    </div>
                </div>
            </Modal>

            <Modal
                title='- 删除 -'
                destroyOnClose={true}
                open={deleteModalOpen}
                wrapClassName={`${styles.add_table_modal} ${styles.footer_center}`}
                centered
                okText='删除'
                cancelText='返回'
                onOk={handleDelOk}
                onCancel={handleDelCancel}
            >
                <Form labelCol={{span: 6}}
                      form={formDelRef}
                      onFinish={onDelFormFinish}
                >
                    <Row gutter={24}>
                        <Col span={18}>
                            <Form.Item name='rewardsNo' label='专项奖励文号'>
                                <Input
                                    placeholder='请输入专项奖励文号'
                                    allowClear
                                    style={{width: '100%'}}
                                />
                            </Form.Item>
                        </Col>
                    </Row>
                </Form>
                <Table
                    rowKey={(r, i) => r?.rewardsNo || i}
                    rowSelection={{
                        type: 'checkbox',
                        onChange: (selectedRowKeys, selectedRows) => {
                            console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
                            setSelectedRows(selectedRows)
                        },
                        getCheckboxProps: (_record) => ({
                            // disabled: record.name === 'Disabled User',
                        }),
                    }}
                    columns={[
                        {
                            title: '专项奖励文号',
                            key: 'rewardsNo',
                            dataIndex: 'rewardsNo',
                            // align: 'center',
                            fixed: 'left',
                            width: '80%',
                        }
                    ]}
                    onChange={onChangeDelPage}
                    loading={delTableLoading}
                    dataSource={delTableList}
                    pagination={{
                        ...delPagination,
                        size: 'small',
                        total: delPagination?.total,
                        showSizeChanger: false,
                        showQuickJumper: false,
                        pageSizeOptions: ['10', '20', '50']
                    }}
                />
            </Modal>

            <Modal
                title={<div style={{ textAlign: 'center', color: '#E60027' }}>-修改数据-</div>}
                destroyOnClose={true}
                open={editModalOpen}
                centered
                footer={null}
                onCancel={() => {
                    setEditRecord({});
                    setEditModalOpen(false);
                }}
                width={'30%'}
            >
                <Form
                    form={formEditRef}
                    labelCol={{span: 6}}
                    wrapperCol={{span: 16}}
                    style={{maxWidth: 600}}
                    initialValues={{
                        ...editRecord
                    }}
                    onValuesChange={(changedValues) => {
                        // setEditRecord({
                        //     ...editRecord,
                        //     ...formEditRef.getFieldsValue()
                        // })
                        setEditDisabled(changedValues?.rewardsType === 'staff')
                    }}
                    onFinish={commitEditData}
                >
                    <Form.Item
                        label='月份'
                        name='cycleId'
                        rules={[
                            {
                                required: false,
                                message: '请输入月份!'
                            }
                        ]}
                    >
                        <Input disabled />
                    </Form.Item>

                    <Form.Item
                        label='专项奖励文号'
                        name='rewardsNo'
                        rules={[
                            {
                                required: false,
                                message: '请输入专项奖励文号!'
                            }
                        ]}
                    >
                        <Input disabled />
                    </Form.Item>
                    <Form.Item
                        label='专项奖励名称'
                        name='rewardsName'
                        rules={[
                            {
                                required: false,
                                message: '请输入专项奖励名称!'
                            }
                        ]}
                    >
                        <Input />
                    </Form.Item>
                    <Form.Item
                        label='专项奖励金额'
                        name='realRewardsAmount'
                        rules={[
                            {
                                required: false,
                                message: '请输入专项奖励金额!'
                            }
                        ]}
                    >
                        <Input />
                    </Form.Item>
                    <Form.Item
                        label='专项奖励层级'
                        name='rewardsLevel'
                        rules={[
                            {
                                required: false,
                                message: '请选择专项奖励层级!'
                            }
                        ]}
                    >
                        <Select placeholder={`请选择专项奖励层级`} allowClear>
                            {
                                levelList.map(item => (
                                    <Select.Option key={item?.enumId} value={item?.enumId}>{item?.enumName}</Select.Option>
                                ))
                            }
                        </Select>
                    </Form.Item>
                    <Form.Item
                        label='专业线'
                        name='rewardsAmount'
                        rules={[
                            {
                                required: false,
                                message: '请输入专业线!'
                            }
                        ]}
                    >
                        <Select placeholder={`请选择专业线`} allowClear>
                            {
                                profLineList.map(item => (
                                    <Select.Option key={item?.enumId} value={item?.enumId}>{item?.enumName}</Select.Option>
                                ))
                            }
                        </Select>
                    </Form.Item>
                    <Form.Item
                        label='奖项分类'
                        name='rewardsType'
                        rules={[
                            {
                                required: false,
                                message: '请选择奖项分类!'
                            }
                        ]}
                    >
                        <Select placeholder={`请选择奖项分类`} allowClear>
                            {
                                typeList.map(item => (
                                    <Select.Option key={item?.enumId} value={item?.enumId}>{item?.enumName}</Select.Option>
                                ))
                            }
                        </Select>
                    </Form.Item>
                    <Form.Item
                        label='组织名称'
                        name='rewardsOrgName'
                        dependencies={['rewardsType']}
                        rules={[
                            {
                                required: formEditRef.getFieldValue('rewardsType') === 'org',
                                message: '请选择组织名称!'
                            }
                        ]}
                    >
                        <Cascader
                            allowClear={false}
                            changeOnSelect
                            expandTrigger='hover'
                            disabled={editDisabled}
                            displayRender={labels => labels[labels.length - 1]}
                            options={orgList || []}
                            fieldNames={{
                                value: 'orgId',
                                label: 'orgName',
                                children: 'children'
                            }}
                            placeholder={`请选择组织名称`}
                            showSearch={{filter}}
                            onSearch={value => console.log(value)}
                        />
                    </Form.Item>
                    <Form.Item
                        label='组织金额'
                        name='realRewardsOrgAmount'
                        dependencies={['rewardsType']}
                        rules={[
                            {
                                required: formEditRef.getFieldValue('rewardsType') === 'org',
                                message: '请输入组织金额!'
                            }
                        ]}
                    >
                        <Input disabled={editDisabled} />
                    </Form.Item>
                    <Form.Item
                        label='员工编号'
                        name='rewardsStaffId'
                        rules={[
                            {
                                required: editRecord.rewardsType === 'org',
                                message: '请输入员工编号!'
                            }
                        ]}
                    >
                        <Input />
                    </Form.Item>
                    <Form.Item
                        label='员工姓名'
                        name='rewardsStaffName'
                        rules={[
                            {
                                required: editRecord.rewardsType === 'org',
                                message: '请输入员工姓名!'
                            }
                        ]}
                    >
                        <Input />
                    </Form.Item>
                    <Form.Item
                        label='员工金额'
                        name='realRewardsStaffAmount'
                        rules={[
                            {
                                required: editRecord.rewardsType === 'org',
                                message: '请输入员工金额!'
                            }
                        ]}
                    >
                        <Input />
                    </Form.Item>
                    <Form.Item
                        label='备注'
                        name='remark'
                        rules={[
                            {
                                required: false,
                                message: '请输入备注!'
                            }
                        ]}
                    >
                        <Input />
                    </Form.Item>

                    <Form.Item
                        wrapperCol={{
                            offset: 10,
                            span: 12
                        }}
                    >
                        <Button type='primary' htmlType='submit' loading={editLoading}>
                            提交
                        </Button>
                    </Form.Item>
                </Form>
            </Modal>


            {/*<Modal*/}
            {/*    title='- 文件上传 -'*/}
            {/*    destroyOnClose={true}*/}
            {/*    open={upModalOpen}*/}
            {/*    wrapClassName={styles.add_table_modal}*/}
            {/*    centered*/}
            {/*    okText='上传'*/}
            {/*    onOk={handleOk}*/}
            {/*    onCancel={handleCancel}*/}
            {/*>*/}
            {/*    <div className='mt-4 mb-8'>*/}
            {/*        <Row>*/}
            {/*            <Col span={22} offset={1}>*/}
            {/*                <div*/}
            {/*                    style={{border: '1px dashed #d9d9d9', borderRadius: '4px'}}*/}
            {/*                    className='flex gap-x-[0.25rem] items-center h-[2rem] justify-end bg-[#00000005] pr-[0.6rem] mb-[0.4rem]'>*/}
            {/*                    <SvgIcon name='excel' width={20} height={20}/>*/}
            {/*                    <span className='text-[#E60027] cursor-pointer' onClick={handleDownload}>下载导入模版</span>*/}
            {/*                </div>*/}
            {/*            </Col>*/}
            {/*        </Row>*/}
            {/*        <Row>*/}
            {/*            <Col span={22} offset={1} className='h-[10rem]'>*/}
            {/*                <Dragger*/}
            {/*                    {...{*/}
            {/*                        action: '',*/}
            {/*                        maxCount: 1,*/}
            {/*                        multiple: false,*/}
            {/*                        fileList,*/}
            {/*                        beforeUpload(file, fileList) {*/}
            {/*                            console.log(file, fileList)*/}
            {/*                            return false*/}
            {/*                        },*/}
            {/*                        onChange(info) {*/}
            {/*                            const {status} = info.file*/}
            {/*                            if (status !== 'uploading') {*/}
            {/*                                setFileList(info.fileList)*/}
            {/*                            }*/}
            {/*                        }*/}
            {/*                    }}*/}
            {/*                >*/}
            {/*                    <p className='ant-upload-drag-icon'>*/}
            {/*                        <InboxOutlined style={{color: '#F14846'}}/>*/}
            {/*                    </p>*/}
            {/*                    <p className='ant-upload-text'>点击或将文件拖拽到这里上传</p>*/}
            {/*                    <p className='ant-upload-hint'>支持excel格式的文件。</p>*/}
            {/*                </Dragger>*/}
            {/*            </Col>*/}
            {/*        </Row>*/}
            {/*    </div>*/}
            {/*</Modal>*/}
        </div>
    )
}
export default Empty
