import { useState, useEffect, useRef } from 'react'
import {
  Table,
  Button,
  Input,
  Modal,
  Select,
  Form,
  Row,
  Col,
  GetProp,
  DatePicker,
  message,
  Upload,
  Cascader,
  CascaderProps,
  Popconfirm,
  Tooltip
} from 'antd'
import {
  PlusOutlined,
  DownloadOutlined,
  UploadOutlined,
  InboxOutlined,
  FullscreenExitOutlined,
  FullscreenOutlined,
  DeleteOutlined,
  InfoCircleOutlined
} from '@ant-design/icons'
import EditTable from './components/edit-table/index'
import SvgIcon from '@/components/SvgIcons'
import effectService from '@/pages/effect/employment/service.ts'
import { useRequest } from '@/hooks'
import proManageService from '@/pages/salary/processManagement/service.ts'
import dayjs from 'dayjs'
import { downFile, openNotification } from '@/utils/down.tsx'
import styles from '../Index.module.scss'
import peopleService from '@/pages/position/people/service.ts'
import { useDebounce } from '@/utils/debounce.ts'
import { orgOption } from '@/pages/effect/employment/assest.ts'
import ResizableTable from '@/components/resizeTable/index.jsx'
import { formatMoney } from '@/hooks/format'

const { Dragger } = Upload
type DefaultOptionType = GetProp<CascaderProps, 'options'>[number]

const Empty = () => {
  // 表格数据
  const columns = [
    // {
    //   title: '月份',
    //   key: 'cycleId',
    //   dataIndex: 'cycleId',
    //   actionType: 'datePicker',
    //   align: 'center',
    //   fixed: 'left',
    //   width: 50
    // },
    {
      title: '专项奖励名称',
      key: 'rewardsName',
      dataIndex: 'rewardsName',
      align: 'center',
      fixed: 'left',
      width: 130,
      render: (text: string, _record: orgOption) => (
        <Tooltip title={text}>
          <div className={styles.over_ellipsis}>{text}</div>
        </Tooltip>
      )
    },
    {
      title: '专项奖励文号',
      key: 'rewardsNo',
      dataIndex: 'rewardsNo',
      align: 'center',
      fixed: 'left',
      width: 120,
      render: (text: string, _record: orgOption) => (
        <Tooltip title={text}>
          <div className={styles.over_ellipsis}>{text}</div>
        </Tooltip>
      )
    },
    {
      title: '专项奖励金额',
      key: 'rewardsAmount',
      dataIndex: 'rewardsAmount',
      align: 'center',
      fixed: 'left',
      width: 80,
      render: text => <span>{formatMoney(text)}</span>
    },
    {
      title: '专项奖励层级',
      key: 'rewardsLevel',
      dataIndex: 'rewardsLevel',
      actionType: 'select',
      actionOptionName: 'levelList',
      align: 'center',
      fixed: 'left',
      width: 100,
      render: text => {
        return levelList?.find(item => item.enumId === text)?.enumName
      }
    },
    {
      title: '专项奖励',
      children: [
        {
          title: '专业线',
          dataIndex: 'rewardsProfLine',
          key: 'rewardsProfLine',
          align: 'center',
          width: 75,
          actionType: 'select',
          actionOptionName: 'profLineList',
          render: text => {
            return profLineList?.find(item => item.enumId === text)?.enumName
          }
        },
        {
          title: '奖项分类',
          dataIndex: 'rewardsType',
          key: 'rewardsType',
          align: 'center',
          width: 75,
          actionType: 'select',
          actionOptionName: 'typeList',
          render: text => {
            return typeList?.find(item => item.enumId === text)?.enumName
          }
        },
        {
          title: '组织名称',
          dataIndex: 'rewardsOrgName',
          key: 'rewardsOrgName',
          actionType: 'cascader',
          align: 'center',
          width: 120,
          render: text => {
            if (Array.isArray(text)) {
              // text现在是对象数组，直接取最后一个对象的orgName
              return text[text.length - 1]?.orgName || ''
            }
            return text || ''
          }
        },
        {
          title: '组织金额',
          dataIndex: 'rewardsOrgAmount',
          key: 'rewardsOrgAmount',
          align: 'center',
          width: 60,
          render: text => <span>{formatMoney(text)}</span>
        },
        {
          title: '员工编号',
          dataIndex: 'rewardsStaffId',
          key: 'rewardsStaffId',
          align: 'center',
          width: 75
        },
        {
          title: '员工姓名',
          dataIndex: 'rewardsStaffName',
          key: 'rewardsStaffName',
          align: 'center',
          width: 75
        },
        {
          title: '员工金额',
          dataIndex: 'rewardsStaffAmount',
          key: 'rewardsStaffAmount',
          align: 'center',
          width: 60,
          render: text => <span>{formatMoney(text)}</span>
        },
        {
          title: '备注',
          dataIndex: 'remark',
          key: 'remark',
          align: 'center',
          width: 100,
          render: (text: string, _record: orgOption) => (
            <Tooltip title={text}>
              <div className={styles.over_ellipsis}>{text}</div>
            </Tooltip>
          )
        }
      ]
    },
    {
      title: (
        <>
          <span>操作</span>
          <Tooltip title='修改总金额时，同文号的总金额都会修改。'>
            <InfoCircleOutlined />
          </Tooltip>
        </>
      ),
      dataIndex: 'action',
      key: 'action',
      align: 'center',
      fixed: 'right',
      width: 120,
      render: (_text, record) => (
        <div className='action'>
          <Popconfirm
            title='修改总金额时，同文号的总金额都会修改。'
            onConfirm={() => handleEditRecord(record)}
            okText=' 确认'
            cancelText='取消'
            //disabled={!flag}
          >
            <Button type='link'>修改</Button>
          </Popconfirm>
        </div>
      )
    }
  ] as any
  const topRef = useRef(null)
  const contentRef = useRef(null)
  const tableTopRef = useRef(null)
  const [formRef] = Form.useForm()
  const [formEditRef] = Form.useForm()
  const [formDelRef] = Form.useForm()
  // const [tableColumns, setTableColumns] = useState<any>(columns)
  const [tableLoading, setTableLoading] = useState(false)
  const [tableData, setTableData] = useState([])
  const [addModalOpen, setAddModalOpen] = useState(false)
  const [upModalOpen, setOpModalOpen] = useState(false)
  const [deleteModalOpen, setDeleteModalOpen] = useState(false)
  const [fileList, setFileList] = useState([])
  const [orgList, setOrgList] = useState<any>([])
  const [levelList, setLevelList] = useState<any>([])
  const [profLineList, setProfLineList] = useState<any>([])
  const [typeList, setTypeList] = useState<any>([])
  const [isReset, setIsReset] = useState<number>(0)
  const [height, setHeight] = useState(0)
  const [selectedRows, setSelectedRows] = useState([])
  const delTableListRef = useRef([])
  const [delTableList, setDelTableList] = useState([])
  const [delTableLoading, setDelTableLoading] = useState(false)
  const [showTitle, setShowTitle] = useState<boolean>(true)
  const [editRecord, setEditRecord] = useState<any>({})
  const [editModalOpen, setEditModalOpen] = useState(false)
  const [editLoading, setEditLoading] = useState(false)
  const [orgOptions, setOrgOptions] = useState([])
  // const [editDisabled, setEditDisabled] = useState(false);
  const [delPagination, setDelPagination] = useState({
    total: 0,
    pageNum: 1,
    pageSize: 5
  })
  const [pagination, setPagination] = useState({
    total: 0,
    pageNum: 1,
    pageSize: 50
  })
  const { runAsync: getEnumType } = useRequest(peopleService.getEnumType, { manual: true })
  const { runAsync: build4LevelOrgTree } = useRequest(effectService.build4LevelOrgTree, {
    manual: true
  })
  const { runAsync: getSpecialRewards } = useRequest(proManageService.getSpecialRewards, {
    manual: true
  })
  const { runAsync: getEmployeeByEmpId } = useRequest(proManageService.getEmployeeByEmpId, {
    manual: true
  })
  const { runAsync: getDocNumList } = useRequest(proManageService.getDocNumList, {
    manual: true
  })
  const { runAsync: addSpecialRewards } = useRequest(proManageService.addSpecialRewards, {
    manual: true
  })
  const { runAsync: delBatchByRewardsNo } = useRequest(proManageService.delBatchByRewardsNo, {
    manual: true
  })
  const { runAsync: exportSpecialRewards } = useRequest(proManageService.exportSpecialRewards, {
    manual: true
  })
  const { runAsync: uploadSpecialRewards } = useRequest(proManageService.uploadSpecialRewards, {
    manual: true
  })
  const { runAsync: downloadSalaryTemplate } = useRequest(proManageService.downloadSalaryTemplate, {
    manual: true
  })

  useEffect(() => {
    // setTableColumns(columns)
    getEnumTypes()
    initDate()
    queryTableData()
    // 添加 resize 事件监听器
    window.addEventListener('resize', handleResize)
    // 清除事件监听器，防止内存泄漏
    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])
  const handleResize = () => {
    initHeight()
  }

  useEffect(() => {
    initHeight()
  }, [(document.querySelector('.rewardPage_table .ant-table-header') || {})['offsetHeight']])

  useEffect(() => {
    initHeight()
  }, [tableData])

  useEffect(() => {
    if (isReset > 0) {
      queryTableData()
    }
  }, [isReset])

  useEffect(() => {
    if (pagination?.total > 0) {
      queryTableData()
    }
  }, [pagination.pageNum, pagination.pageSize])

  useEffect(() => {
    if (delPagination?.total > 0) {
      queryTableData()
    }
  }, [delPagination.pageNum, delPagination.pageSize])

  // useEffect(() => {
  //     if (editModalOpen) {
  //         const isDis = formEditRef.getFieldValue('rewardsType') === 'staff';
  //         setEditDisabled(isDis);
  //     }
  // }, [editModalOpen])

  const initHeight = () => {
    const headerHeight =
      (document.querySelector('.rewardPage_table .ant-table-header') || {})['offsetHeight'] || 0
    const pageHeight =
      (document.querySelector('.rewardPage_table .ant-table-pagination') || {})['offsetHeight'] ||
      26
    if (headerHeight && pageHeight) {
      setHeight(
        contentRef.current?.offsetHeight -
          (tableTopRef.current.offsetHeight + headerHeight + pageHeight)
      )
    }
  }

  // 初始化查询条件
  const initDate = () => {
    const date = dayjs().subtract(1, 'month')
    formRef.setFieldsValue({
      cycleId: date
    })
    getLevelOrg()
  }

  const handleEditRecord = record => {
    const newRecord = {
      ...record,
      rewardsOrgId: findParentIds(orgList, record.rewardsOrgId)
      // rewardsType: typeList?.find(item => item.enumName === record.rewardsType)?.enumId,
      // rewardsProfLine: profLineList?.find(item => item.enumName === record.rewardsProfLine)?.enumId,
      // rewardsLevel: levelList?.find(item => item.enumName === record.rewardsLevel)?.enumId,
    }
    setOrgOptions([
      {
        orgId: newRecord.rewardsOrgId[newRecord.rewardsOrgId.length - 1],
        orgName: newRecord.rewardsOrgName
      }
    ])
    setEditModalOpen(true)
    setEditRecord(newRecord)
    formEditRef.setFieldsValue(newRecord)
  }

  const commitEditData = () => {
    const orgRecord = orgOptions[orgOptions?.length - 1] || {}
    setEditLoading(true)
    handleAddData(
      {
        ...editRecord,
        rewardsOrgId: orgRecord?.orgId,
        rewardsOrgName: orgRecord?.orgName
      },
      'edit'
    )
  }

  const getEnumTypes = async () => {
    const [
      // [orgError, orgData],
      [levelError, levelData],
      [profLineError, profLineData],
      [typeError, typeData]
    ] = await Promise.all([
      getEnumType({ code: 'specialRewardsLevel' }),
      getEnumType({ code: 'specialRewardsProfLine' }),
      getEnumType({ code: 'specialRewardsType' })
    ])
    if (levelError || profLineError || typeError) {
      return
    }
    // if (orgData.STATUS === '0000') {
    //     setOrgList(orgData.DATA)
    // }
    if (levelData.STATUS === '0000') {
      setLevelList(levelData.DATA)
    }
    if (profLineData.STATUS === '0000') {
      setProfLineList(profLineData.DATA)
    }
    if (typeData.STATUS === '0000') {
      setTypeList(typeData.DATA)
    }
  }

  const getLevelOrg = async () => {
    const [error, res] = await build4LevelOrgTree({
      // monthId: formRef.getFieldValue('cycleId')?.format('YYYYMM'),
      monthId: dayjs().format('YYYYMM'),
      tag: '2'
    })
    if (error) {
      message.error(res?.DATA || res?.MESSAGE || '调用失败')
      return
    }
    if (res.STATUS === '0000') {
      setOrgList(res.DATA)
      formRef.setFieldValue('orgaId', '')
    } else {
      message.error(res?.MESSAGE || res?.DATA || '调用失败')
    }
  }

  // 查询表格数据
  const queryTableData = async () => {
    const values = formRef.getFieldsValue()
    setTableLoading(true)
    const [error, res] = await getSpecialRewards({
      ...values,
      cycleId: values?.cycleId?.format('YYYYMM'),
      orgaId: values?.orgaId ? values?.orgaId[values?.orgaId?.length - 1] : '',
      ...pagination
    })
    setTableLoading(false)
    if (error) {
      message.error(res?.DATA || res?.MESSAGE || '调用失败')
      return
    }
    if (res.STATUS === '0000') {
      const {
        DATA: { data }
      } = res
      const dataWithKeys = data.map((item, index) => ({
        ...item,
        key: item.id || index // 使用数据中的唯一字段作为 key，或回退到索引
      }))
      setTableData(dataWithKeys)
      setPagination({
        ...pagination,
        total: res.DATA?.totalCount
      })
    } else {
      message.error(res?.MESSAGE || res?.DATA || '调用失败')
    }
  }

  // 点击导出
  const exportToExcelFun = async () => {
    try {
      // 调用接口获取 Blob 文件数据
      openNotification('正在导出', 0, 'loading')
      const values = formRef.getFieldsValue()
      const response = await exportSpecialRewards({
        ...values,
        cycleId: values?.cycleId?.format('YYYYMM'),
        orgaId: values?.orgaId ? values?.orgaId[values?.orgaId?.length - 1] : ''
      })
      downFile(response)
    } catch (error) {
      openNotification('导出失败', 1, 'error')
      console.error('Download failed:', error)
    }
  }

  const handleAddData = async (data, type?) => {
    const formValue = formEditRef.getFieldsValue()
    setTableLoading(true)
    const params =
      type === 'edit'
        ? [
            {
              ...data,
              rewardsOrgId: data.rewardsType === 'org' ? data.rewardsOrgId : '',
              rewardsOrgName: data.rewardsType === 'org' ? data.rewardsOrgName : '',
              rewardsStaffName:
                formValue.rewardsStaffId && formValue.rewardsStaffName
                  ? formValue.rewardsStaffName
                  : '',
              rewardsStaffId:
                formValue.rewardsStaffId && formValue.rewardsStaffName
                  ? formValue.rewardsStaffId
                  : ''
            }
          ]
        : data?.map(item => ({
            ...item,
            cycleId: item?.cycleId?.format('YYYYMM'),
            rewardsOrgId:
              item.rewardsOrgName?.length > 0
                ? item.rewardsOrgName[item.rewardsOrgName.length - 1].orgId
                : '',
            rewardsOrgName:
              item.rewardsOrgName?.length > 0
                ? item.rewardsOrgName[item.rewardsOrgName.length - 1].orgName
                : ''
          }))
    if (type === 'edit' && data.realRewardsStaffAmount) {
      if (!data.rewardsStaffName) {
        message.warning('员工金额不为空时，员工姓名和员工编码必填')
        setEditLoading(false)
        setTableLoading(false)
        return
      }
    }
    const [error, res] = await addSpecialRewards(params)
    // setTableLoading(false)
    if (error) {
      message.error(res?.DATA || res?.MESSAGE || '调用失败')
      setEditLoading(false)
      setTableLoading(false)
      return
    }
    if (res.STATUS === '0000') {
      message.success(res?.DATA)
      setAddModalOpen(false)
      setEditLoading(false)
      setEditModalOpen(false)
      queryTableData()
    } else {
      message.error(res?.MESSAGE || res?.DATA || '调用失败')
      setEditLoading(false)
      setTableLoading(false)
    }
  }

  const handleDownload = async () => {
    try {
      // 调用接口获取 Blob 文件数据
      openNotification('正在导出', 0, 'loading')
      const response = await downloadSalaryTemplate({
        templateId: 'SPECIAL_REWARDS'
      })
      downFile(response)
    } catch (error) {
      openNotification('导出失败', 1, 'error')
      console.error('Download failed:', error)
    }
  }

  const queryDelTableList = async () => {
    const values = formRef.getFieldsValue()
    setDelTableLoading(true)
    const [error, res] = await getDocNumList({
      ...values,
      cycleId: values?.cycleId?.format('YYYYMM'),
      orgaId: values?.orgaId ? values?.orgaId[values?.orgaId?.length - 1] : '',
      ...delPagination
    })
    setDelTableLoading(false)
    if (error) {
      message.error(res?.DATA || res?.MESSAGE || '调用失败')
      return
    }
    if (res.STATUS === '0000') {
      const {
        DATA: { data }
      } = res
      delTableListRef.current = data
      setDelTableList(data)
      setDelPagination({
        ...delPagination,
        total: res.DATA?.totalCount
      })
    } else {
      message.error(res?.MESSAGE || res?.DATA || '调用失败')
    }
  }

  const handleSearchName = async e => {
    const [error, res] = await getEmployeeByEmpId({
      empId: e.target.value
    })
    if (error) {
      message.error(res?.DATA || res?.MESSAGE || '调用失败')
      return
    }
    if (res.STATUS === '0000') {
      const { DATA: userInfo } = res
      if (editModalOpen) {
        setEditRecord({
          ...editRecord,
          rewardsStaffName: userInfo?.employeeName
        })
        formEditRef.setFieldsValue({ rewardsStaffName: userInfo?.employeeName || '' })
      } else {
        formRef.setFieldsValue({ empName: userInfo?.employeeName })
      }
    } else {
      message.error(res?.MESSAGE || res?.DATA || '调用失败')
    }
  }

  const debouncedChange = useDebounce(handleSearchName, 500)

  const showDelModal = () => {
    setDeleteModalOpen(true)
    queryDelTableList()
  }

  // 文号查询
  const onDelFormFinish = values => {
    console.log('Success:', values?.rewardsNo)
    let newList = delTableListRef.current
    if (values?.rewardsNo) {
      newList = delTableList?.filter(item => item.rewardsNo === values?.rewardsNo?.trim())
    }
    setDelTableList(newList)
  }

  // 点击查询
  const onFormFinish = values => {
    console.log('Success:', values)
    queryTableData()
  }

  // 查询重置
  const onReset = () => {
    const newReset = isReset + 1
    formRef.resetFields()
    initDate()
    setIsReset(newReset)
  }

  // 切换分页
  const onChangePage = page => {
    const newPage = {
      total: page?.total,
      pageNum: page?.current,
      pageSize: page?.pageSize
    }
    setPagination(newPage)
  }

  const onChangeDelPage = page => {
    const newPage = {
      total: page?.total,
      pageNum: page?.current,
      pageSize: page?.pageSize
    }
    setDelPagination(newPage)
  }

  const handleCancel = () => {
    setOpModalOpen(false)
    setFileList([])
  }

  const handleDelCancel = () => {
    setDeleteModalOpen(false)
    setDelTableList([])
    delTableListRef.current = []
    formDelRef.resetFields()
  }

  //上传
  const handleOk = () => {
    if (fileList?.length > 0) {
      uploadFileFun()
    } else {
      message.error('请先选择文件上传')
    }
  }

  //删除
  const handleDelOk = () => {
    if (selectedRows?.length > 0) {
      handleDelData()
    } else {
      message.error('请先选择文件上传')
    }
  }

  const handleDelData = async () => {
    setTableLoading(true)
    const [error, res] = await delBatchByRewardsNo({
      rewardsNo: selectedRows?.map(item => item.rewardsNo)
    })
    // setTableLoading(false)
    if (error) {
      message.error(res?.DATA || res?.MESSAGE || '调用失败')
      setTableLoading(false)
      return
    }
    if (res.STATUS === '0000') {
      message.success(res?.DATA)
      handleDelCancel()
      queryTableData()
    } else {
      message.error(res?.MESSAGE || res?.DATA || '调用失败')
      setTableLoading(false)
    }
  }

  const uploadFileFun = async () => {
    const formData = new FormData()
    fileList
      .map(file => file?.originFileObj)
      .forEach(file => {
        formData.append('file', file)
      })
    formData.append('tag', '1')
    const [error, res] = await uploadSpecialRewards(formData)
    if (error) {
      message.error(res?.DATA || res?.MESSAGE || '调用失败')
      return
    }
    if (res.STATUS === '0000') {
      setOpModalOpen(false)
      message.success(res?.DATA)
      queryTableData()
    } else {
      message.error(res?.MESSAGE || res?.DATA || '调用失败')
    }
  }

  const onValuesChange = changedValues => {
    if (changedValues.cycleId) {
      // getLevelOrg();
    }
  }

  const filter = (inputValue: string, path: DefaultOptionType[]) =>
    path.some(
      option =>
        (option.orgName as string).toLowerCase().indexOf(inputValue.trim().toLowerCase()) > -1
    )
  //递归查找父级id
  function findParentIds(orgList, targetId) {
    let result = []

    // 辅助递归函数，用于查找目标 ID 的父级组织
    function findParentRecursive(orgList, targetId, parents) {
      for (const org of orgList) {
        // 如果找到了目标 ID，返回当前的父级组织 IDs
        if (org.orgId === targetId) {
          result = [...parents, org.orgId]
          return true
        }
        // 如果当前组织有子组织，递归查找
        if (org.children && org.children.length > 0) {
          if (findParentRecursive(org.children, targetId, [...parents, org.orgId])) {
            return true // 找到目标 ID 后结束递归
          }
        }
      }
      return false // 未找到目标 ID
    }

    // 从组织架构列表开始查找
    findParentRecursive(orgList, targetId, [])
    return result
  }

  return (
    <div
      className={`${'pt-[0.5rem] px-[0rem] h-full flex flex-col'} ${styles.processManagement_page}`}
    >
      <div ref={topRef} className='bg-white pt-[0.5rem] pb-[0rem] px-[1.75rem] mb-[0rem]'>
        <Form
          labelCol={{ span: 6 }}
          form={formRef}
          onFinish={onFormFinish}
          onValuesChange={onValuesChange}
        >
          <Row gutter={24}>
            <Col span={6}>
              <Form.Item name='cycleId' label='月份' className='mb-[0.5rem]'>
                <DatePicker picker='month' style={{ width: '100%' }} allowClear={false} />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name='rewardsNo' label='专项奖励文号' className='mb-[0.5rem]'>
                <Input placeholder='请输入专项奖励文号' allowClear style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name='rewardsLevel' label='专项奖励层级' className='mb-[0.5rem]'>
                <Select placeholder='请选择专项奖励层级' allowClear>
                  {levelList?.map(item => (
                    <Select.Option key={item?.enumId} value={item?.enumId}>
                      {item?.enumName}
                    </Select.Option>
                  ))}
                  {/*<Select.Option key='grp' value='grp'>集团级</Select.Option>*/}
                  {/*<Select.Option key='com' value='com'>公司级</Select.Option>*/}
                  {/*<Select.Option key='sub' value='sub'>各单位级</Select.Option>*/}
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name='orgaId' label='组织' className='mb-[0.5rem]'>
                <Cascader
                  allowClear={true}
                  changeOnSelect
                  expandTrigger='hover'
                  displayRender={labels => labels[labels.length - 1]}
                  options={orgList}
                  fieldNames={{
                    value: 'orgId',
                    label: 'orgName',
                    children: 'children'
                  }}
                  placeholder='请选择组织'
                  showSearch={{ filter }}
                  onSearch={value => console.log(value)}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name='empId' label='员工编号' className='mb-[0.5rem]'>
                <Input
                  placeholder='请输入员工编号'
                  allowClear
                  style={{ width: '100%' }}
                  onChange={debouncedChange}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name='empName' label='员工姓名' className='mb-[0.5rem]'>
                <Input placeholder='请输入员工姓名' allowClear style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={6}>
              <div className='text-right'>
                <Button type='primary' htmlType='submit'>
                  查询
                </Button>
                <Button className='ml-[0.4rem]' onClick={() => onReset()}>
                  重置
                </Button>
              </div>
            </Col>
          </Row>
        </Form>
      </div>
      <div
        ref={contentRef}
        className='relative bg-white px-5 pt-2.5'
        style={{ height: `calc(100% - ${topRef.current?.offsetHeight + 15}px)` }}
      >
        <div
          ref={tableTopRef}
          className={`${'flex justify-between items-center overflow-hidden mb-2'} ${styles.animation_box} ${showTitle ? 'h-[1.6rem]' : 'h-0'}`}
        >
          <div className={'flex '}>
            {showTitle ? (
              <FullscreenExitOutlined
                className={`${styles.shousuo_icon} text-[1rem]`}
                onClick={() => {
                  setShowTitle(false)
                  setTimeout(() => {
                    initHeight()
                  }, 200)
                }}
              />
            ) : (
              <FullscreenOutlined
                className={`${styles.shousuo_icon} ${'absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10'}`}
                onClick={() => {
                  setShowTitle(true)
                  setTimeout(() => {
                    initHeight()
                  }, 200)
                }}
              />
            )}
            <div className='font-bold text-[0.8rem] ml-[1rem]'>
              数据列表
              <span className='text-[0.7rem]' style={{ color: '#939393' }}>
                （单位：元）
              </span>
            </div>
          </div>

          <div className='flex'>
            <div className='flex items-center gap-x-[0.25rem] ml-[0.6rem]'>
              <SvgIcon name='excel' width={20} height={20} />
              <span className='text-[#E60027] cursor-pointer' onClick={() => handleDownload()}>
                下载导入模版
              </span>
            </div>
            <Button
              className='ml-[0.4rem]'
              danger
              onClick={() => {
                setOpModalOpen(true)
              }}
            >
              <UploadOutlined />
              导入
            </Button>
            <Button
              type='primary'
              className='ml-[0.4rem]'
              onClick={() => {
                setAddModalOpen(true)
              }}
            >
              <PlusOutlined />
              新增
            </Button>
            <Button
              type='primary'
              className='ml-[0.4rem]'
              onClick={() => {
                showDelModal()
              }}
            >
              <DeleteOutlined />
              删除
            </Button>
            <Button className='ml-[0.4rem]' danger onClick={() => exportToExcelFun()}>
              <DownloadOutlined />
              导出
            </Button>
          </div>
        </div>
        <ResizableTable
          className='rewardPage_table'
          rowClassName={(_, i) => (i % 2 === 1 ? `customRow odd` : `customRow even`)}
          columns={columns}
          dataSource={tableData}
          loading={tableLoading}
          bordered
          scroll={{
            // x: 'max-content',
            y: `calc(${height}px - 0.625rem - 1.6rem - 0.5rem)`
          }}
          onChange={onChangePage}
          pagination={{
            ...pagination,
            total: pagination?.total,
            showSizeChanger: true,
            showQuickJumper: true,
            pageSizeOptions: ['10', '20', '50']
          }}
        />
      </div>
      <Modal
        title='- 新增数据 -'
        destroyOnClose={true}
        open={addModalOpen}
        wrapClassName={styles.add_table_modal}
        centered
        footer={null}
        onCancel={() => setAddModalOpen(false)}
        width={'80%'}
      >
        <EditTable
          columns={columns}
          submitData={value => handleAddData(value)}
          cascaderOption={orgList}
          levelList={levelList}
          profLineList={profLineList}
          typeList={typeList}
        />
      </Modal>

      <Modal
        title='- 文件上传 -'
        destroyOnClose={true}
        open={upModalOpen}
        centered
        className={`${styles.add_table_modal} ${styles.detail_modal}`}
        footer={null}
        // okText='上传'
        // onOk={handleOk}
        onCancel={handleCancel}
      >
        <div className='mt-4'>
          <Row>
            <Col span={22} offset={1} className='h-[10rem]'>
              <Dragger
                {...{
                  // name: 'file',
                  // data: {
                  //     tag: "1"
                  // },
                  // action: `/zhyy/employee/uploadEmployeeExcel`,
                  action: '',
                  maxCount: 1,
                  multiple: false,
                  fileList,
                  beforeUpload(file, fileList) {
                    console.log(file, fileList)
                    return false
                  },
                  onChange(info) {
                    const { status } = info.file
                    if (status !== 'uploading') {
                      console.log(info.file, info.fileList)
                      setFileList(info.fileList)
                    }
                  }
                }}
              >
                <p className='ant-upload-drag-icon'>
                  <InboxOutlined style={{ color: '#F14846' }} />
                </p>
                <p className='ant-upload-text'>点击或将文件拖拽到这里上传</p>
                <p className='ant-upload-hint'>支持excel格式的文件。</p>
              </Dragger>
            </Col>
          </Row>
          <div style={{ display: 'flex', justifyContent: 'center', marginTop: '2rem' }}>
            <Button danger onClick={handleCancel}>
              取消
            </Button>

            <Popconfirm
              title=''
              description='导入文件如有重复数据，将会被覆盖，请确认是否上传。'
              onConfirm={handleOk}
              onCancel={() => {}}
              okText='确认'
              cancelText='取消'
            >
              <Button
                danger
                type='primary'
                style={{ marginLeft: '1rem' }}
                disabled={fileList.length < 1}
              >
                上传
              </Button>
            </Popconfirm>
          </div>
        </div>
      </Modal>

      <Modal
        title='- 删除 -'
        destroyOnClose={true}
        open={deleteModalOpen}
        wrapClassName={`${styles.add_table_modal} ${styles.footer_center}`}
        centered
        okText='删除'
        cancelText='返回'
        onOk={handleDelOk}
        onCancel={handleDelCancel}
      >
        <Form labelCol={{ span: 6 }} form={formDelRef} onFinish={onDelFormFinish}>
          <Row gutter={24}>
            <Col span={18}>
              <Form.Item name='rewardsNo' label='专项奖励文号'>
                <Input placeholder='请输入专项奖励文号' allowClear style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
        </Form>
        <Table
          rowKey={(r, i) => r?.rewardsNo || i}
          rowSelection={{
            type: 'checkbox',
            onChange: (selectedRowKeys, selectedRows) => {
              console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows)
              setSelectedRows(selectedRows)
            },
            getCheckboxProps: _record => ({
              // disabled: record.name === 'Disabled User',
            })
          }}
          columns={[
            {
              title: '专项奖励文号',
              key: 'rewardsNo',
              dataIndex: 'rewardsNo',
              // align: 'center',
              fixed: 'left',
              width: '80%'
            }
          ]}
          onChange={onChangeDelPage}
          loading={delTableLoading}
          dataSource={delTableList}
          pagination={{
            ...delPagination,
            size: 'small',
            total: delPagination?.total,
            showSizeChanger: false,
            showQuickJumper: false,
            pageSizeOptions: ['10', '20', '50']
          }}
        />
      </Modal>

      <Modal
        title={<div style={{ textAlign: 'center', color: '#E60027' }}>-修改数据-</div>}
        destroyOnClose={true}
        open={editModalOpen}
        centered
        footer={null}
        onCancel={() => {
          setEditRecord({})
          setEditModalOpen(false)
        }}
        width={'30%'}
      >
        <Form
          form={formEditRef}
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 16 }}
          style={{ maxWidth: 600 }}
          initialValues={
            {
              // ...editRecord
            }
          }
          onValuesChange={_changedValues => {
            setEditRecord({
              ...editRecord,
              ...formEditRef.getFieldsValue()
            })
            // formEditRef.setFieldsValue({
            //     ...formEditRef.getFieldsValue()
            // });
            // setEditDisabled(changedValues?.rewardsType === 'staff')
          }}
          onFinish={commitEditData}
        >
          <Form.Item
            label='月份'
            name='cycleId'
            rules={[
              {
                required: false,
                message: '请输入月份!'
              }
            ]}
          >
            <Input disabled />
          </Form.Item>

          <Form.Item
            label='专项奖励文号'
            name='rewardsNo'
            rules={[
              {
                required: false,
                message: '请输入专项奖励文号!'
              }
            ]}
          >
            <Input disabled />
          </Form.Item>
          <Form.Item
            label='专项奖励名称'
            name='rewardsName'
            rules={[
              {
                required: false,
                message: '请输入专项奖励名称!'
              }
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label='专项奖励金额'
            name='realRewardsAmount'
            rules={[
              {
                required: false,
                message: '请输入专项奖励金额!'
              }
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label='专项奖励层级'
            name='rewardsLevel'
            rules={[
              {
                required: false,
                message: '请选择专项奖励层级!'
              }
            ]}
          >
            <Select placeholder={`请选择专项奖励层级`} allowClear>
              {levelList.map(item => (
                <Select.Option key={item?.enumId} value={item?.enumId}>
                  {item?.enumName}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            label='专业线'
            name='rewardsProfLine'
            rules={[
              {
                required: false,
                message: '请输入专业线!'
              }
            ]}
          >
            <Select placeholder={`请选择专业线`} allowClear>
              {profLineList.map(item => (
                <Select.Option key={item?.enumId} value={item?.enumId}>
                  {item?.enumName}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            label='奖项分类'
            name='rewardsType'
            rules={[
              {
                required: false,
                message: '请选择奖项分类!'
              }
            ]}
          >
            <Select placeholder={`请选择奖项分类`} allowClear>
              {typeList.map(item => (
                <Select.Option key={item?.enumId} value={item?.enumId}>
                  {item?.enumName}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            label='组织名称'
            name='rewardsOrgId'
            dependencies={['rewardsType']}
            rules={[
              {
                required: formEditRef.getFieldValue('rewardsType') === 'org',
                message: '请选择组织名称!'
              }
            ]}
          >
            <Cascader
              allowClear={false}
              changeOnSelect
              expandTrigger='hover'
              disabled={formEditRef.getFieldValue('rewardsType') === 'staff'}
              displayRender={labels => labels[labels.length - 1]}
              options={orgList || []}
              fieldNames={{
                value: 'orgId',
                label: 'orgName',
                children: 'children'
              }}
              placeholder={`请选择组织名称`}
              showSearch={{ filter }}
              onChange={(_value, selectedOptions) => setOrgOptions(selectedOptions)}
              onSearch={value => console.log(value)}
            />
          </Form.Item>
          <Form.Item
            label='组织金额'
            name='realRewardsOrgAmount'
            dependencies={['rewardsType']}
            rules={[
              {
                required: formEditRef.getFieldValue('rewardsType') === 'org',
                message: '请输入组织金额!'
              }
            ]}
          >
            <Input disabled={formEditRef.getFieldValue('rewardsType') === 'staff'} />
          </Form.Item>
          <Form.Item
            label='员工编号'
            name='rewardsStaffId'
            rules={[
              {
                required: formEditRef.getFieldValue('rewardsType') === 'staff',
                message: '请输入员工编号!'
              }
            ]}
          >
            <Input onChange={debouncedChange} />
          </Form.Item>
          <Form.Item
            label='员工姓名'
            name='rewardsStaffName'
            rules={[
              {
                required:
                  formEditRef.getFieldValue('rewardsType') === 'staff' ||
                  formEditRef.getFieldValue('rewardsStaffId'),
                message: '请输入员工姓名!'
              }
            ]}
          >
            <Input disabled />
          </Form.Item>
          <Form.Item
            label='员工金额'
            name='realRewardsStaffAmount'
            rules={[
              {
                required: formEditRef.getFieldValue('rewardsType') === 'staff',
                message: '请输入员工金额!'
              }
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label='备注'
            name='remark'
            rules={[
              {
                required: false,
                message: '请输入备注!'
              }
            ]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            wrapperCol={{
              offset: 10,
              span: 12
            }}
          >
            <Button type='primary' htmlType='submit' loading={editLoading}>
              提交
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}
export default Empty
