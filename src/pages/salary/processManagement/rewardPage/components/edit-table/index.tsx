import { useEffect, useRef, useState } from 'react'
import {
  Button,
  Form,
  Input,
  Table,
  Cascader,
  GetProp,
  CascaderProps,
  Select,
  DatePicker,
  message
} from 'antd'
import { PlusOutlined } from '@ant-design/icons'
import styles from './Index.module.scss'
import { useDebounce } from '@/utils/debounce.ts'
import { useRequest } from '@/hooks'
import proManageService from '@/pages/salary/processManagement/service.ts'

type DefaultOptionType = GetProp<CascaderProps, 'options'>[number]
const EditTabel = props => {
  // const defaultColumns = props.columns

  const [formRef] = Form.useForm()
  const tableDataRef = useRef([])
  const [tableData, setTableData] = useState([])
  const [tableColumns, setTableColumns] = useState(props.columns)
  const [submitLoading, setSubmitLoading] = useState(false)

  const { runAsync: getEmployeeByEmpId } = useRequest(proManageService.getEmployeeByEmpId, {
    manual: true
  })

  useEffect(() => {
    getColumns()
  }, [])

  // useEffect(() => {
  //     setTableData(tableDataRef.current)
  // }, [JSON.stringify(tableDataRef.current)])

  const getColumns = () => {
    setTableColumns([
      ...initColumns(props.columns),
      {
        title: '操作',
        dataIndex: 'action',
        width: 80,
        align: 'center',
        fixed: 'right',
        render: (_text, record) => (
          <div className='action'>
            {/*<Button*/}
            {/*    type='dashed'*/}
            {/*    onClick={() => deleteRecord(record)}*/}
            {/*    style={{margin: 'auto', width: '50%', marginLeft: '25%'}}*/}
            {/*>*/}
            {/*    删除*/}
            {/*</Button>*/}
            <Button type='link' onClick={() => deleteRecord(record)}>
              删除
            </Button>
          </div>
        )
      }
    ])
  }

  const handleSearchName = async (record, value, _column, _formName) => {
    const [error, res] = await getEmployeeByEmpId({
      empId: value
    })
    if (error) {
      return
    }
    if (res.STATUS === '0000') {
      const { DATA: userInfo } = res
      const employeeName = userInfo?.employeeName
      const orgaId = userInfo?.orgaId

      // 根据orgaId找到组织树中的完整路径
      const findOrgPath = (orgList, targetId) => {
        for (const org of orgList) {
          if (org.orgId === targetId) {
            return [org]
          }
          if (org.children && org.children.length > 0) {
            const childPath = findOrgPath(org.children, targetId)
            if (childPath) {
              return [org, ...childPath]
            }
          }
        }
        return null
      }

      const orgPath = findOrgPath(props?.cascaderOption || [], orgaId)

      tableDataRef.current = tableDataRef.current?.map(item => {
        const data = { ...item }
        if (item?.key === record?.key) {
          data['rewardsStaffName'] = employeeName
          if (orgPath) {
            data['rewardsOrgName'] = orgPath
          }
        }
        return data
      })
      formRef.setFieldValue(_formName, employeeName)
      if (orgPath) {
        formRef.setFieldValue(
          `rewardsOrgName${record?.key}`,
          orgPath.map(org => org.orgId)
        )
      }
      console.log('修改：', tableDataRef.current)
    } else {
      message.error(res?.MESSAGE)
    }
  }

  // const debouncedChange = useDebounce(handleSearchName, 500);

  const initColumns = columns => {
    return columns
      ?.filter(column => column?.dataIndex !== 'action')
      ?.map(item => {
        if (item?.children?.length > 0) {
          return {
            ...item,
            width: item?.width && item.width > 100 ? item?.width : 100,
            children: initColumns(item?.children)
          }
        } else {
          return {
            ...item,
            width: item?.width && item.width > 120 ? item?.width : 120,
            render: (text, record, index) => (
              <Form.Item
                key={`${item.dataIndex}_${record?.key}_${index}`}
                name={item.dataIndex + record?.key}
                rules={[
                  {
                    required:
                      (['rewardsOrgName', 'rewardsOrgAmount'].includes(item?.dataIndex) &&
                        record?.rewardsType === 'org') ||
                      (['rewardsStaffId', 'rewardsStaffName', 'rewardsStaffAmount'].includes(
                        item?.dataIndex
                      ) &&
                        record?.rewardsType === 'staff') ||
                      (item?.dataIndex === 'rewardsStaffName' &&
                        tableDataRef.current?.find(item => item?.key === record?.key)
                          ?.rewardsStaffId),
                    message: `请输入${item.title}`
                  }
                ]}
              >
                {item?.actionType === 'cascader' ? (
                  <Cascader
                    allowClear={true}
                    changeOnSelect
                    expandTrigger='hover'
                    disabled={
                      ['rewardsOrgName'].includes(item?.dataIndex) &&
                      record?.rewardsType === 'staff'
                    }
                    displayRender={labels => labels[labels.length - 1]}
                    options={props?.cascaderOption || []}
                    fieldNames={{
                      value: 'orgId',
                      label: 'orgName',
                      children: 'children'
                    }}
                    placeholder={
                      ['rewardsOrgName'].includes(item?.dataIndex) &&
                      record?.rewardsType === 'staff'
                        ? ''
                        : `请选择${item?.title}`
                    }
                    showSearch={{ filter }}
                    onSearch={value => console.log(value)}
                    value={
                      Array.isArray(text)
                        ? text.map(item => (typeof item === 'object' ? item?.orgId : item))
                        : undefined
                    }
                    onChange={(_value, option) => handleEditRecord(record, option, item)}
                  />
                ) : item?.actionType === 'select' ? (
                  <>
                    <Select
                      placeholder={`请选择${item?.title}`}
                      allowClear
                      value={Array.isArray(text) ? undefined : text}
                      onChange={value => handleEditRecord(record, value, item)}
                    >
                      {(props[item?.actionOptionName] || []).map(item => (
                        <Select.Option key={item?.enumId} value={item?.enumId}>
                          {item?.enumName}
                        </Select.Option>
                      ))}
                    </Select>
                  </>
                ) : item?.actionType === 'datePicker' ? (
                  <DatePicker
                    picker='month'
                    value={Array.isArray(text) ? undefined : text}
                    allowClear
                    placeholder={`请选择${item.title}`}
                    onChange={value => handleEditRecord(record, value, item)}
                    style={{ width: '100%' }}
                  />
                ) : item?.dataIndex === 'rewardsStaffId' ? (
                  <Input
                    placeholder={
                      ['rewardsOrgAmount'].includes(item?.dataIndex) &&
                      record?.rewardsType === 'staff'
                        ? ''
                        : `请输入${item.title}`
                    }
                    value={Array.isArray(text) ? '' : text}
                    allowClear
                    disabled={
                      ['rewardsOrgAmount'].includes(item?.dataIndex) &&
                      record?.rewardsType === 'staff'
                    }
                    onChange={e =>
                      handleEditRecord(
                        record,
                        e.target.value,
                        item,
                        `rewardsStaffName${record?.key}`
                      )
                    }
                  />
                ) : (
                  <Input
                    placeholder={
                      (['rewardsOrgAmount'].includes(item?.dataIndex) &&
                        record?.rewardsType === 'staff') ||
                      ['rewardsStaffName'].includes(item?.dataIndex)
                        ? ''
                        : `请输入${item.title}`
                    }
                    value={Array.isArray(text) ? '' : text}
                    allowClear
                    disabled={
                      (['rewardsOrgAmount'].includes(item?.dataIndex) &&
                        record?.rewardsType === 'staff') ||
                      ['rewardsStaffName'].includes(item?.dataIndex)
                    }
                    onChange={e => handleEditRecord(record, e.target.value, item)}
                  />
                )}
              </Form.Item>
            )
          }
        }
      })
  }

  const handleEditRecord = (record, value, column, _formName?) => {
    tableDataRef.current = tableDataRef.current?.map(item => {
      const data = { ...item }
      if (item?.key === record?.key) {
        // if (column?.actionType === 'cascader') {
        //     data[column?.dataIndex] = value;
        // } else {
        //     data[column?.dataIndex] = value;
        // }
        data[column?.dataIndex] = value
      }
      return data
    })
    if (column?.dataIndex === 'rewardsType') {
      setTableData(tableDataRef.current)
    } else if (column?.dataIndex === 'rewardsStaffId') {
      useDebounce(handleSearchName(record, value, column, _formName), 500)
    }
    console.log('修改：', tableDataRef.current)
  }

  const deleteRecord = record => {
    tableDataRef.current = tableDataRef.current?.filter(item => item?.key !== record?.key)
    setTableData(tableDataRef.current)
  }

  const onFinish = async () => {
    setSubmitLoading(true)
    try {
      await props?.submitData(tableDataRef.current)
    } finally {
      setSubmitLoading(false)
    }
  }

  const addRecord = () => {
    tableDataRef.current = [
      ...tableDataRef.current,
      {
        key: new Date().getTime()
      }
    ]
    setTableData(tableDataRef.current)
  }

  const filter = (inputValue: string, path: DefaultOptionType[]) =>
    path.some(
      option =>
        (option.orgName as string).toLowerCase().indexOf(inputValue.trim().toLowerCase()) > -1
    )

  return (
    <div className={styles.add}>
      {/*{initHeader()}*/}
      <Form
        name='dynamic_form_nest_item'
        form={formRef}
        onFinish={onFinish}
        style={{
          width: '100%',
          marginTop: 16,
          maxHeight: '500px'
        }}
        autoComplete='off'
      >
        {/*<Form.List name='users'>*/}
        <Table
          style={{ marginBottom: 20 }}
          className='edit-table'
          columns={tableColumns}
          dataSource={tableData}
          bordered
          scroll={{
            // x: 'max-content',
            y: `20rem`
          }}
          pagination={false}
        />

        <Form.Item style={{ width: '100%', display: 'flex', justifyContent: 'right' }}>
          <Button
            // type='dashed'
            onClick={() => addRecord()}
            danger
            block
            icon={<PlusOutlined />}
            style={{ width: 150 }}
          >
            新增一条数据
          </Button>
        </Form.Item>
        {/*</Form.List>*/}

        <Form.Item
          style={{
            textAlign: 'center',
            width: '100%',
            background: '#fff'
          }}
        >
          <Button
            type='primary'
            htmlType='submit'
            loading={submitLoading}
            disabled={submitLoading}
            style={{}}
          >
            提交
          </Button>
        </Form.Item>
      </Form>
    </div>
  )
}
export default EditTabel
