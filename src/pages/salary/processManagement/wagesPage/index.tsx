import { useState, useEffect, useRef } from 'react'
import {
  message,
  Button,
  Input,
  Modal,
  Select,
  Descriptions,
  Form,
  Timeline,
  Tag,
  Row,
  Col,
  DatePicker,
  Radio,
  Tooltip,
  Cascader
} from 'antd'
import {
  PlusOutlined,
  DownloadOutlined,
  ExclamationCircleOutlined,
  FullscreenExitOutlined,
  FullscreenOutlined
} from '@ant-design/icons'
import AddModal from './components/edit-table/index'
import { useRequest } from '@/hooks'
import effectService from '../../service'
import dayjs from 'dayjs'
import styles from '../Index.module.scss'
import { downFile, openNotification } from '@/utils/down.tsx'
import { useUserStore } from '@/store/user.ts'
import ResizableTable from '@/components/resizeTable/index.tsx'
import { formatMoney } from '@/hooks/format'

const Empty = () => {
  const { currentUser } = useUserStore()
  // 表格数据
  const [formRef] = Form.useForm()
  const topRef = useRef(null)
  const contentRef = useRef(null)
  const tableTopRef = useRef(null)
  const [height, setHeight] = useState(0)
  const [showTitle, setShowTitle] = useState(true)
  const [tableLoading, setTableLoading] = useState(false)
  const [tableData, setTableData] = useState([])
  const [addModalOpen, setAddModalOpen] = useState(false)
  const [selectedRowKeys, setSelectedRowKeys] = useState([])
  const [statusList, setStatusList] = useState([])
  const [WorkList, setWorkList] = useState([])
  const [hjList, setHjList] = useState([])
  const [cycleId, setCycleId] = useState('')
  const [incomeWork, setIncomeWork] = useState(null)
  const [outWork, setOutWork] = useState(null)
  const [empId, setEmpId] = useState('')
  const [empName, setEmpName] = useState('')
  const [nodeId, setNode] = useState(null)
  const [statusType, setStatusType] = useState(null)
  const [tipContent, setTipContent] = useState('')
  const [tipType, setTipType] = useState(0)
  const [remark, setRemark] = useState('')
  const [editId, setEditId] = useState('')
  const [salaryForwardModalOpen, setSalaryForwardModalOpen] = useState(false)
  const [salaryTipModalOpen, setSalaryTipModalOpen] = useState(false)
  const [editModalOpen, setEditModalOpen] = useState(false)
  const [editLoading, setEditLoading] = useState(false)
  const [salaryForwardData, setSalaryForwardData] = useState([])
  const [pagination, setPagination] = useState({
    total: 0,
    pageNum: 1,
    pageSize: 10
  })
  const [apply, setApply] = useState(false)
  const [approve, setApprove] = useState(false)
  const { runAsync: getEnumType } = useRequest(effectService.getEnumType, { manual: true })
  const { runAsync: getSalaryForwardList } = useRequest(effectService.getSalaryForwardList, {
    manual: true
  })
  const { runAsync: exportSalaryForward } = useRequest(effectService.exportSalaryForward, {
    manual: true
  })
  const { runAsync: getSalaryForwardSaveBatch } = useRequest(
    effectService.getSalaryForwardSaveBatch,
    {
      manual: true
    }
  )
  const { runAsync: getFlowComment } = useRequest(effectService.getFlowComment, {
    manual: true
  })
  const { runAsync: getCompleteTask } = useRequest(effectService.getCompleteTask, { manual: true })
  const { runAsync: getTerminateTask } = useRequest(effectService.getTerminateTask, {
    manual: true
  })
  const { runAsync: getRejectTask } = useRequest(effectService.getRejectTask, { manual: true })
  const { runAsync: saveEditBatch } = useRequest(effectService.saveEditBatch, { manual: true })
  const { runAsync: build4LevelOrgTree2 } = useRequest(effectService.build4LevelOrgTree2, {
    manual: true
  })
  useEffect(() => {
    get4LevelOrgTree()
    getEnumTypes()
    queryTableData()
    // 添加 resize 事件监听器
    window.addEventListener('resize', handleResize)
    // 清除事件监听器，防止内存泄漏
    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])
  const handleResize = () => {
    initHeight()
  }

  useEffect(() => {
    initHeight()
  }, [(document.querySelector('.wagesPage_table .ant-table-header') || {})['offsetHeight']])

  useEffect(() => {
    if (pagination?.total > 0) {
      queryTableData()
    }
  }, [pagination.pageNum, pagination.pageSize])

  useEffect(() => {
    queryTableData()
  }, [approve])

  const initHeight = () => {
    const headerHeight =
      (document.querySelector('.wagesPage_table .ant-table-header') || {})['offsetHeight'] || 0
    const pageHeight =
      (document.querySelector('.wagesPage_table .ant-table-pagination') || {})['offsetHeight'] || 26
    if (headerHeight && pageHeight) {
      setHeight(
        contentRef.current?.offsetHeight -
          (tableTopRef.current.offsetHeight + headerHeight + pageHeight)
      )
    }
  }

  const onSelectChange = newSelectedRowKeys => {
    setSelectedRowKeys(newSelectedRowKeys)
  }
  // 查询表格数据
  const queryTableData = async () => {
    setTableLoading(true)
    const newPage = pagination
    const [lastOutWork] = outWork ? outWork.slice(-1) : [undefined]
    const [lastIncomeWork] = incomeWork ? incomeWork.slice(-1) : [undefined]
    const [error, res] = await getSalaryForwardList({
      ...newPage,
      cycleId: cycleId ? dayjs(cycleId).format('YYYYMM') : '',
      cityOutId: lastOutWork,
      cityInId: lastIncomeWork,
      empId: empId,
      approvalNode: nodeId,
      approvalState: statusType,
      operateType: approve,
      empName: empName
    })
    setTableLoading(false)
    if (error) {
      message.error(res?.DATA || res?.MESSAGE || '调用失败')
      return
    }
    if (res.STATUS === '0000') {
      const {
        DATA: { data }
      } = res
      const dataWithKeys = data.map((item, index) => ({
        ...item,
        key: item.id || index // 使用数据中的唯一字段作为 key，或回退到索引
      }))
      setTableData(dataWithKeys)
      setPagination({
        ...pagination,
        total: res.DATA?.totalCount
      })
    } else {
      message.error(res?.MESSAGE || res?.DATA || '调用失败')
    }
  }
  const getEnumTypes = async () => {
    const [[statusError, statusData], [hjError, hjData]] = await Promise.all([
      getEnumType({ code: 'salaryForwardApprovalState' }),
      getEnumType({ code: 'salaryForwardApprovalNode' })
    ])
    if (statusError || hjError) {
      return
    }
    if (statusData.STATUS === '0000') {
      setStatusList(statusData.DATA)
    }
    if (hjData.STATUS === '0000') {
      setHjList(hjData.DATA)
    }
    // const params = {
    //   ...form.getFieldsValue()
    // }
    // queryTableData(params)
  }

  const resetSearch = () => {
    setCycleId('')
    setIncomeWork(null)
    setOutWork(null)
    setEmpId('')
    setEmpName('')
    setNode('')
    setStatusType(null)
    get4LevelOrgTree()
  }

  // 点击导出
  const exportToExcelFun = async () => {
    try {
      // 调用接口获取 Blob 文件数据
      openNotification('正在导出', 0, 'loading')
      const response = await exportSalaryForward({
        // ...newPage,
        cycleId: cycleId ? dayjs(cycleId).format('YYYYMM') : '',
        cityOutId: outWork,
        cityInId: incomeWork,
        empId: empId,
        empName: empName,
        approvalNode: nodeId,
        approvalState: statusType,
        operateType: approve
      })
      downFile(response)
    } catch (error) {
      openNotification('导出失败', 1, 'error')
    }
  }
  const columns = [
    {
      title: '月份',
      key: 'cycleId',
      dataIndex: 'cycleId',
      align: 'center',
      width: 80
    },
    {
      title: '转出单位',
      key: 'cityOutName',
      dataIndex: 'cityOutName',
      align: 'center',
      width: 130,
      render: (text, _record) => (
        <Tooltip title={text}>
          <div className={styles.over_ellipsis}>{text}</div>
        </Tooltip>
      )
    },
    {
      title: '人员编号',
      key: 'empId',
      dataIndex: 'empId',
      align: 'center',
      width: 80
    },
    {
      title: '人员姓名',
      key: 'empName',
      dataIndex: 'empName',
      align: 'center',
      width: 80
    },
    {
      title: '转递金额',
      key: 'forwardAmount',
      dataIndex: 'forwardAmount',
      align: 'center',
      width: 80,
      render: text => <span>{formatMoney(text)}</span>
    },
    {
      title: '转递内容说明',
      key: 'forwardContent',
      dataIndex: 'forwardContent',
      align: 'center',
      width: 180
    },
    {
      title: '转入单位',
      key: 'cityInName',
      dataIndex: 'cityInName',
      align: 'center',
      width: 130,
      render: (text, _record) => (
        <Tooltip title={text}>
          <div className={styles.over_ellipsis}>{text}</div>
        </Tooltip>
      )
    },
    {
      title: '审批环节',
      key: 'approvalNode',
      dataIndex: 'approvalNode',
      align: 'center',
      width: 80,
      render: text => {
        const content = hjList.filter(item => item.enumId === text)[0]
        return <p>{content?.enumName}</p>
      }
    },
    {
      title: '审批状态',
      key: 'approvalState',
      dataIndex: 'approvalState',
      align: 'center',
      width: 80,
      render: text => {
        const content = statusList.filter(item => item.enumId === text)[0]
        return <p>{content?.enumName}</p>
      }
    },
    {
      title: '操作',
      align: 'center',
      width: 80,
      render: (_, record) => (
        <>
          <Button type='link' onClick={() => getDetailList(record)}>
            详情
          </Button>
          {apply &&
            currentUser?.userInfo?.userName === record.createBy &&
            currentUser?.userInfo?.userName === record.approvalUsers &&
            record.approvalState === 'TuiHui' && (
              <Button
                type='link'
                onClick={() => {
                  changeEdit(record)
                }}
              >
                修改
              </Button>
            )}
        </>
      )
    }
  ] as any

  const getDetailList = async record => {
    setSalaryForwardModalOpen(true)
    const [error, res] = await getFlowComment({
      busiId: record.id
    })
    if (error) {
      message.error(res?.DATA || res?.MESSAGE || '调用失败')
      return
    }
    if (res.STATUS === '0000') {
      setSalaryForwardData(res.DATA)
    } else {
      message.error(res?.MESSAGE || res?.DATA || '调用失败')
    }
  }

  const addColumns = [
    {
      title: '人员编号',
      key: 'empId',
      dataIndex: 'empId',
      align: 'center',
      width: 120
    },
    {
      title: '人员姓名',
      key: 'empName',
      dataIndex: 'empName',
      align: 'center',
      width: 120
    },
    {
      title: '转递金额',
      key: 'forwardAmount',
      dataIndex: 'forwardAmount',
      align: 'center',
      width: 80
    },
    {
      title: '转递内容说明',
      key: 'forwardContent',
      dataIndex: 'forwardContent',
      align: 'center',
      width: 180
    }
  ]
  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange
  }

  // 切换分页
  const onChangePage = page => {
    setPagination({
      total: page?.total,
      pageNum: page?.current,
      pageSize: page?.pageSize
    })
  }
  const title = title => {
    switch (title) {
      case '5':
        return '发起流程'
      case '0':
        return '退回'
      case '1':
        return '通过'
      case '3':
        return '待审批'
      case '6':
        return '流程结束'
      case '4':
        return '作废'
    }
  }

  const addData = async record => {
    const [error, res] = await getSalaryForwardSaveBatch(record)
    setTableLoading(false)
    if (error) {
      message.error(res?.DATA || res?.MESSAGE || '调用失败')
      return
    }
    if (res.STATUS === '0000') {
      message.success(res.DATA)
      queryTableData()
      setAddModalOpen(false)
    } else {
      message.error(res?.MESSAGE || res?.DATA || '调用失败')
    }
  }

  const openTipModal = type => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要操作的数据！')
      return
    }
    setSalaryTipModalOpen(true)
    setTipType(type)
    if (type === 1) {
      setTipContent('将提交至人力资源部审批')
    }
    if (type === 2) {
      setTipContent('将退回至上一步工位审批')
    }
    if (type === 3) {
      setTipContent('将作废此流程')
    }
  }
  const get4LevelOrgTree = async (day: any = null) => {
    // console.log('@@@a', day, dayjs(day).format('YYYYMM'), dayjs().format('YYYYMM'))
    const [error, res] = await build4LevelOrgTree2({
      monthId: day ? dayjs(day).format('YYYYMM') : dayjs().format('YYYYMM'),
      tag: '1'
    })
    if (error) {
      message.error(res?.DATA || res?.MESSAGE || '调用失败')
      return
    }
    if (res.STATUS === '0000') {
      setOutWork(null)
      setIncomeWork(null)
      console.log('@@@', res)
      setWorkList(res.DATA)
      // setOrgList(res.DATA)
      // setOrgValueArr([])
      // formRef.setFieldValue('orgId', '')
    } else {
      message.error(res?.MESSAGE || res?.DATA || '调用失败')
    }
  }

  const tipCommit = async () => {
    if (tipType === 1) {
      const [error, res] = await getCompleteTask({
        ids: selectedRowKeys,
        remark: remark
      })
      if (error) {
        message.error(res?.DATA || res?.MESSAGE || '调用失败')
        return
      }
      if (res.STATUS === '0000') {
        if (res.DATA.totalFailure !== 0) {
          if (Array.isArray(res.DATA?.auditDetailList) && res.DATA?.auditDetailList.length > 0) {
            const errMsgList = res.DATA.auditDetailList.filter(item => item.result !== 'SUCCESS')
            if (errMsgList.length > 0) {
              const messageStr = errMsgList.map(list => list.message).join('\n')
              message.success(messageStr)
              return
            }
          }
          message.success(
            `共提交${res.DATA.total}条数据，成功${res.DATA.totalSuccess}条数据，失败${res.DATA.totalFailure}条数据`
          )
          return
        }
        message.success(
          `共提交${res.DATA.total}条数据，成功${res.DATA.totalSuccess}条数据，失败${res.DATA.totalFailure}条数据`
        )
        setSalaryTipModalOpen(false)
        setRemark('')
        setSelectedRowKeys([])
        queryTableData()
      } else {
        message.error(res?.MESSAGE || res?.DATA || '调用失败')
      }
    }
    if (tipType === 2) {
      const [error, res] = await getRejectTask({
        ids: selectedRowKeys,
        remark: remark
      })
      if (error) {
        message.error(res?.DATA || res?.MESSAGE || '调用失败')
        return
      }
      if (res.STATUS === '0000') {
        if (res.DATA.totalFailure !== 0) {
          if (Array.isArray(res.DATA?.auditDetailList) && res.DATA?.auditDetailList.length > 0) {
            const errMsgList = res.DATA.auditDetailList.filter(item => item.result !== 'SUCCESS')
            if (errMsgList.length > 0) {
              const messageStr = errMsgList.map(list => list.message).join('\n')
              message.success(messageStr)
              return
            }
          }
          message.success(
            `共提交${res.DATA.total}条数据，成功${res.DATA.totalSuccess}条数据，失败${res.DATA.totalFailure}条数据`
          )
          return
        }
        message.success(
          `共提交${res.DATA.total}条数据，成功${res.DATA.totalSuccess}条数据，失败${res.DATA.totalSuccess}条数据`
        )
        setSalaryTipModalOpen(false)
        setSelectedRowKeys([])
        setRemark('')
        queryTableData()
      } else {
        message.error(res?.MESSAGE || res?.DATA || '调用失败')
      }
    }
    if (tipType === 3) {
      const [error, res] = await getTerminateTask({
        ids: selectedRowKeys,
        remark: remark
      })
      if (error) {
        message.error(res?.DATA || res?.MESSAGE || '调用失败')
        return
      }
      if (res.STATUS === '0000') {
        if (res.DATA.totalFailure !== 0) {
          if (Array.isArray(res.DATA?.auditDetailList) && res.DATA?.auditDetailList.length > 0) {
            const errMsgList = res.DATA.auditDetailList.filter(item => item.result !== 'SUCCESS')
            if (errMsgList.length > 0) {
              const messageStr = errMsgList.map(list => list.message).join('\n')
              message.success(messageStr)
              return
            }
          }
          message.success(
            `共提交${res.DATA.total}条数据，成功${res.DATA.totalSuccess}条数据，失败${res.DATA.totalFailure}条数据`
          )
          return
        }
        message.success(
          `共提交${res.DATA.total}条数据，成功${res.DATA.totalSuccess}条数据，失败${res.DATA.totalFailure}条数据`
        )
        setSalaryTipModalOpen(false)
        setSelectedRowKeys([])
        setRemark('')
        queryTableData()
      } else {
        message.error(res?.MESSAGE || res?.DATA || '调用失败')
      }
    }
  }

  const changeEdit = record => {
    const { empId, empName, realForwardAmount, forwardContent } = record
    setEditModalOpen(true)
    setEditId(record.id)
    formRef.setFieldsValue({
      empId,
      empName,
      forwardAmount: realForwardAmount,
      forwardContent
    })
  }

  const commitEditData = async values => {
    setEditLoading(true)
    const [error, res] = await saveEditBatch([
      {
        id: editId,
        ...values
      }
    ])
    setEditLoading(false)
    if (error) {
      message.error(res?.DATA || res?.MESSAGE || '调用失败')
      return
    }
    if (res.STATUS === '0000') {
      message.success(res.DATA)
      setEditModalOpen(false)
      queryTableData()
    } else {
      message.error(res?.MESSAGE || res?.DATA || '调用失败')
    }
  }

  return (
    <div
      className={`${'pt-[0.5rem] px-[0rem] h-full flex flex-col'} ${styles.processManagement_page}`}
    >
      <div ref={topRef} className='bg-white pt-[0.5rem] pb-[0.5rem] px-[1.75rem] mb-[0.5rem]'>
        <Row gutter={24}>
          <Col span={6} style={{ display: 'flex' }}>
            <div style={{ width: '25%', margin: 'auto 0' }}>月份：</div>
            {/* <Form.Item name='code1' label='月份'> */}
            <DatePicker
              picker='month'
              style={{ width: '75%' }}
              value={cycleId}
              format={'YYYY-MM'}
              placeholder='请选择月份'
              size='small'
              onChange={e => {
                setCycleId(e ? e : '')
                get4LevelOrgTree(e)
              }}
            />
            {/* </Form.Item> */}
          </Col>
          <Col span={6} style={{ display: 'flex' }}>
            <div style={{ width: '25%', margin: 'auto 0' }}>转出单位：</div>
            {/* <Select
              placeholder='请选择转出单位'
              value={outWork}
              allowClear
              onChange={e => setOutWork(e)}
              style={{ width: '75%' }}
            >
              {
                // prettier-ignore
                WorkList.map(unit => {
                  const { enumId, enumName } = unit;
                  return <Select.Option key={enumId} value={enumId}>{enumName}</Select.Option>
                })
              }
            </Select> */}
            <Cascader
              style={{ width: '75%' }}
              allowClear={true}
              changeOnSelect
              expandTrigger='hover'
              displayRender={labels => labels[labels.length - 1]}
              options={WorkList}
              value={outWork}
              onChange={e => {
                // const [lastItem] = e ? e.slice(-1) : [undefined]
                setOutWork(e)
              }}
              fieldNames={{
                value: 'orgId',
                label: 'orgName',
                children: 'children'
              }}
              placeholder='请选择转出单位'
            />
          </Col>
          <Col span={6} style={{ display: 'flex' }}>
            <div style={{ width: '25%', margin: 'auto 0' }}>转入单位：</div>

            {/* <Select
              placeholder='请选择转入单位'
              value={incomeWork}
              allowClear
              onChange={e => setIncomeWork(e)}
              style={{ width: '75%' }}
            >
              {
                // prettier-ignore
                WorkList.map(unit => {
                  const { enumId, enumName } = unit;
                  return <Select.Option key={enumId} value={enumId}>{enumName}</Select.Option>
                })
              }
            </Select> */}
            <Cascader
              style={{ width: '75%' }}
              allowClear={true}
              changeOnSelect
              expandTrigger='hover'
              displayRender={labels => labels[labels.length - 1]}
              options={WorkList}
              value={incomeWork}
              onChange={e => {
                setIncomeWork(e)
              }}
              fieldNames={{
                value: 'orgId',
                label: 'orgName',
                children: 'children'
              }}
              placeholder='请选择转入单位'
            />
          </Col>
          <Col span={6} style={{ display: 'flex' }}>
            <div style={{ width: '25%', margin: 'auto 0' }}>人员编号：</div>

            <Input
              // value={empId}
              placeholder='请输入人员编号'
              allowClear
              style={{ width: '75%' }}
              onChange={e => setEmpId(e.target.value)}
              //   onChange={
              //   debouncedChange}
            />
          </Col>
          <Col span={6} style={{ display: 'flex', marginTop: 12 }}>
            <div style={{ width: '25%', margin: 'auto 0' }}>员工姓名：</div>
            <Input
              placeholder='请输入员工姓名'
              allowClear
              style={{ width: '75%' }}
              onChange={e => setEmpName(e.target.value)}
              value={empName}
            />
          </Col>
          <Col span={6} style={{ display: 'flex', marginTop: 12 }}>
            <div style={{ width: '25%', margin: 'auto 0' }}>审批环节：</div>
            <Select
              value={nodeId}
              placeholder='请选择审批环节'
              allowClear
              onChange={e => setNode(e)}
              style={{ width: '75%' }}
            >
              {
                // prettier-ignore
                hjList.map(unit => {
                  const { enumId, enumName } = unit;
                  return <Select.Option key={enumId} value={enumId}>{enumName}</Select.Option>
                })
              }
            </Select>
          </Col>
          <Col span={4} style={{ display: 'flex', marginTop: 12 }}>
            <div style={{ width: '38%', margin: 'auto 0' }}>审批状态：</div>
            <Select
              value={statusType}
              style={{ width: '62%' }}
              placeholder='请选择审批状态'
              allowClear
              onChange={e => {
                setStatusType(e)
              }}
            >
              {
                // prettier-ignore
                statusList.map(unit => {
                  const { enumId, enumName } = unit;
                  return <Select.Option key={enumId} value={enumId}>{enumName}</Select.Option>
                })
              }
            </Select>
          </Col>
          <Col span={5} style={{ display: 'flex', marginTop: 12 }}>
            <div style={{ width: '40%', margin: 'auto 0' }}>操作类型：</div>
            <Radio
              style={{ width: '30%', margin: 'auto 0' }}
              checked={apply}
              onClick={() => {
                if (!apply && approve) {
                  setApprove(false)
                }
                setApply(!apply)
                // console.log(e)
                // apply = !apply
              }}
            >
              申请
            </Radio>
            <Radio
              style={{ width: '30%', margin: 'auto 0' }}
              checked={approve}
              onClick={() => {
                if (!approve && apply) {
                  setApply(false)
                }
                setApprove(!approve)
              }}
            >
              审批
            </Radio>
          </Col>
          <Col span={3} style={{ marginTop: 12 }}>
            <div className='text-right'>
              <Button type='primary' onClick={queryTableData}>
                {' '}
                查询{' '}
              </Button>
              <Button className='ml-[0.4rem]' onClick={resetSearch}>
                重置
              </Button>
            </div>
          </Col>
        </Row>
      </div>
      <div
        ref={contentRef}
        className='relative bg-white px-5 pt-2.5'
        style={{ height: `calc(100% - ${topRef.current?.offsetHeight + 15}px)` }}
      >
        <div
          ref={tableTopRef}
          className={`${'flex justify-between items-center overflow-hidden mb-2'} ${styles.animation_box} ${showTitle ? 'h-[1.6rem]' : 'h-0'}`}
        >
          <div className={'flex '}>
            {showTitle ? (
              <FullscreenExitOutlined
                className={`${styles.shousuo_icon} text-[1rem]`}
                onClick={() => {
                  setShowTitle(false)
                  setTimeout(() => {
                    initHeight()
                  }, 200)
                }}
              />
            ) : (
              <FullscreenOutlined
                className={`${styles.shousuo_icon} ${'absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10'}`}
                onClick={() => {
                  setShowTitle(true)
                  setTimeout(() => {
                    initHeight()
                  }, 200)
                }}
              />
            )}
            <div className='font-bold text-[0.8rem] ml-[1rem]'>
              数据列表
              <span className='text-[0.7rem]' style={{ color: '#939393' }}>
                （单位：元）
              </span>
            </div>
          </div>

          <div>
            {apply && (
              <Button
                type='primary'
                onClick={() => {
                  setAddModalOpen(true)
                }}
              >
                <PlusOutlined />
                新增
              </Button>
            )}

            <Button className='ml-[0.4rem]' danger onClick={() => exportToExcelFun()}>
              <DownloadOutlined />
              导出
            </Button>
            {approve && (
              <>
                <Button className='ml-[0.4rem]' danger onClick={() => openTipModal(1)}>
                  通过
                </Button>
                <Button className='ml-[0.4rem]' onClick={() => openTipModal(2)}>
                  退回
                </Button>
                <Button className='ml-[0.4rem]' onClick={() => openTipModal(3)}>
                  作废
                </Button>
              </>
            )}
          </div>
        </div>
        <ResizableTable
          className='wagesPage_table'
          rowClassName={(_, i) => (i % 2 === 1 ? `customRow odd` : `customRow even`)}
          loading={tableLoading}
          dataSource={tableData}
          columns={columns}
          scroll={{
            // x: 'max-content',
            y: `calc(${height}px - 0.625rem - 1.6rem - 0.5rem)`
          }}
          bordered
          rowSelection={approve ? rowSelection : null}
          onChange={onChangePage}
          pagination={{
            total: pagination?.total,
            showTotal: () => `共 ${pagination?.total} 条`,
            defaultCurrent: 1,
            defaultPageSize: 50,
            current: pagination?.pageNum,
            pageSize: 50,
            showSizeChanger: true
            // showQuickJumper: true
          }}
        />
      </div>
      <Modal
        title={<p style={{ textAlign: 'center', color: '#E60027' }}>-新增数据-</p>}
        destroyOnClose={true}
        open={addModalOpen}
        centered
        footer={null}
        onCancel={() => setAddModalOpen(false)}
        width={'60%'}
      >
        <AddModal columns={addColumns} submitData={value => addData(value)} />
      </Modal>

      <Modal
        title={<p style={{ textAlign: 'center', color: '#E60027' }}>-审批环节详情-</p>}
        destroyOnClose={true}
        open={salaryForwardModalOpen}
        centered
        footer={null}
        onCancel={() => setSalaryForwardModalOpen(false)}
        width={'50%'}
        className={styles.modal}
      >
        <Timeline style={{ marginTop: '1rem' }}>
          {salaryForwardData?.map((item, index) => {
            return (
              <Timeline.Item color={item?.status === '1' ? 'green' : '#FBCC48'} key={index}>
                <Descriptions
                  title={
                    <div style={{ display: 'flex' }}>
                      <p style={{ marginRight: '8px' }}>{item.nodeName}</p>
                      <Tag
                        color={
                          item.operation === '0'
                            ? 'red'
                            : item.operation === '3'
                              ? 'orange'
                              : item.operation === '4'
                                ? 'default'
                                : 'success'
                        }
                      >
                        {title(item.operation)}
                      </Tag>
                    </div>
                  }
                  // column={3}
                >
                  {item.operation !== '6' && item.operation !== '4' && (
                    <>
                      <Descriptions.Item label='审批人'>
                        <p>
                          {item.assigneeName}({item.assigneeOrgaName})
                        </p>
                      </Descriptions.Item>
                      <Descriptions.Item label='创建时间'>{item.createTime}</Descriptions.Item>
                      <Descriptions.Item label='审批时间'>{item.endDate}</Descriptions.Item>
                      <Descriptions.Item label='审批意见'>{item.opinion}</Descriptions.Item>
                    </>
                  )}
                </Descriptions>
              </Timeline.Item>
            )
          })}
        </Timeline>
      </Modal>

      <Modal
        title={<div style={{ textAlign: 'center', color: '#E60027' }}>-提示-</div>}
        destroyOnClose={true}
        open={salaryTipModalOpen}
        centered
        footer={
          <div style={{ display: 'flex', justifyContent: 'center' }}>
            <Button danger type='primary' style={{ marginRight: 50 }} onClick={tipCommit}>
              确认
            </Button>
            <Button
              danger
              onClick={() => {
                setSalaryTipModalOpen(false)
                setRemark('')
              }}
            >
              返回
            </Button>
          </div>
        }
        onCancel={() => {
          setSalaryTipModalOpen(false)
          setRemark('')
        }}
        width={'25%'}
      >
        <div style={{ marginTop: 12, marginBottom: 20 }}>
          <div style={{ display: 'flex', justifyContent: 'left', fontSize: 12, fontWeight: 500 }}>
            <ExclamationCircleOutlined style={{ marginRight: 12 }} />
            <div>{tipContent}</div>
          </div>

          <div style={{ display: 'flex', marginTop: 12 }}>
            <div style={{ margin: 'auto 0' }}>审批意见：</div>
            <Input
              placeholder='请输入审批意见'
              allowClear
              style={{ width: '75%' }}
              onChange={e => setRemark(e.target.value)}
              value={remark}
            />
          </div>
        </div>
      </Modal>

      <Modal
        title={<div style={{ textAlign: 'center', color: '#E60027' }}>-修改数据-</div>}
        destroyOnClose={true}
        open={editModalOpen}
        centered
        footer={null}
        onCancel={() => setEditModalOpen(false)}
        width={'30%'}
      >
        <Form
          form={formRef}
          labelCol={{
            span: 6
          }}
          wrapperCol={{
            span: 16
          }}
          style={{
            maxWidth: 600
          }}
          initialValues={{
            remember: true
          }}
          onFinish={commitEditData}
          autoComplete='off'
        >
          <Form.Item
            label='人员编号'
            name='empId'
            rules={[
              {
                required: true,
                message: '请输入人员编号!'
              }
            ]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            label='人员姓名'
            name='empName'
            rules={[
              {
                required: true,
                message: '请输入人员姓名!'
              }
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label='转递金额'
            name='forwardAmount'
            rules={[
              {
                required: true,
                message: '请输入转递金额!'
              }
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label='转递内容说明'
            name='forwardContent'
            rules={[
              {
                required: true,
                message: '请输入转递内容说明!'
              }
            ]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            wrapperCol={{
              offset: 10,
              span: 12
            }}
          >
            <Button type='primary' htmlType='submit' loading={editLoading}>
              提交
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}
export default Empty
