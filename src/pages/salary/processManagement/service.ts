import request from '@/request'

const proManageService = {
  // 查询枚举
  getEnumType: params => {
    return request.post<any>('/zhyy/employee/getEnumType', params)
  },

  // 查询
  getSpecialRewards: params => {
    return request.get<any>('/zhyy/salary/specialRewards/list', {params})
  },

  // 查询专项奖励文号
  getDocNumList: params => {
    return request.get<any>('/zhyy/salary/specialRewards/docNumList', {params})
  },

  // 根据人员编码查人员信息
  getEmployeeByEmpId: params => {
    return request.get<any>('/zhyy/employee/getEmployeeByEmpId', {params})
  },


  // 新增
  addSpecialRewards: params => {
    return request.post<any>('/zhyy/salary/specialRewards/saveBatch', params)
  },

  // 删除
  delBatchByRewardsNo: params => {
    return request.post<any>('/zhyy/salary/specialRewards/delBatchByRewardsNo', params)
  },

  // 下载导入模板
  downloadSalaryTemplate: params => {
    return request.get<any>('/zhyy/salary/common/downloadTemplate', {
      params,
      responseType: 'blob',
      headers: {
        'Manager-Operate-Flag': 1
      }
    })
  },

  // 导入
  uploadSpecialRewards: params => {
    return request.post<any>('/zhyy/salary/specialRewards/uploadExcel', params, {
      headers: {
        'Manager-Operate-Flag': 1
      }
    })
  },

  // 导出
  exportSpecialRewards: params => {
    return request.get<any>('/zhyy/salary/specialRewards/exportExcel', {
      params,
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      },
      responseType: 'blob'
    })
  },
}

export default proManageService
