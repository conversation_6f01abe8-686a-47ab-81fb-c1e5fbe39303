import { useEffect, useRef, useState } from 'react'
import {
  Form,
  Row,
  Col,
  Input,
  Select,
  Button,
  FormProps,
  Modal,
  Upload,
  message,
  Space,
  Popconfirm,
  Cascader,
  DatePicker,
  Tooltip
} from 'antd'
import {
  UploadOutlined,
  DownloadOutlined,
  InboxOutlined,
  FullscreenExitOutlined,
  FullscreenOutlined
} from '@ant-design/icons'
import SvgIcon from '@/components/SvgIcons'
import peopleService from '../service'
import { useRequest } from '@/hooks'
import styles from './Index.module.scss'
import type { TableColumnsType } from 'antd'
import { Enum, FieldType } from '../interface'
import { downFile, openNotification } from '@/utils/down.tsx'
// import effectService from '@/pages/effect/employment/service.ts'
import AddData from './addData'
const { Dragger } = Upload
const { RangePicker } = DatePicker
import dayjs from 'dayjs'
import { orgOption } from '@/pages/effect/employment/assest.ts'
import ResizableTable from '@/components/resizeTable/index.jsx'

interface DataType {
  key: React.Key
  name: string
  age: number
  address: string
}

const data: DataType[] = []
for (let i = 0; i < 100; i++) {
  data.push({
    key: i,
    name: `Edward ${i}`,
    age: 32,
    address: `London Park no. ${i}`
  })
}

const People: React.FC = () => {
  const [form] = Form.useForm()
  const topRef = useRef(null)
  const contentRef = useRef(null)
  const tableTopRef = useRef(null)
  const [height, setHeight] = useState(0)
  const [showTitle, setShowTitle] = useState<boolean>(true)
  const [tableLoading, setTableLoading] = useState<boolean>(false)
  // const [saveLoading, setSaveLoading] = useState<boolean>(false)
  const [addModalOpen, setAddModalOpen] = useState<boolean>(false)
  // const [editRecord, setEditRecord] = useState<any>(null)
  const [upModalOpen, setOpModalOpen] = useState<boolean>(false)
  const [fileList, setFileList] = useState([])
  // const [postList, setPostList] = useState<Enum[]>([])
  // const [levelList, setLevelList] = useState<Enum[]>([])
  // const [levelList2, setLevelList2] = useState<Enum[]>([])
  const [unitList, setUnitList] = useState<any[]>([])
  const [typeList, setTypeList] = useState<Enum[]>([])
  const [tableData, setTableData] = useState<DataType[]>([])
  const [pagination, setPagination] = useState({
    total: 0,
    pageNum: 1,
    pageSize: 50
  })

  const { runAsync: getEnumType } = useRequest(peopleService.getEnumType, { manual: true })
  const { runAsync: updateEmployeeTransfer } = useRequest(peopleService.updateEmployeeTransfer, {
    manual: true
  })
  const { runAsync: downloadEmployeeTemplate } = useRequest(
    peopleService.downloadEmployeeTemplate,
    { manual: true }
  )

  const { runAsync: emportEmployeeTransfer } = useRequest(peopleService.emportEmployeeTransfer, {
    manual: true
  })
  const { runAsync: importEmployeeTransfer } = useRequest(peopleService.importEmployeeTransfer, {
    manual: true
  })
  const { runAsync: addEmployeeSaveBatch } = useRequest(peopleService.addEmployeeSaveBatch, {
    manual: true
  })
  const columns: TableColumnsType<any> = [
    {
      title: '单位',
      width: 130,
      dataIndex: 'cityName',
      align: 'center',
      key: 'cityName',
      fixed: 'left',
      render: (text: string, _record: orgOption) => (
        <Tooltip title={text}>
          <div className={styles.over_ellipsis}>{text}</div>
        </Tooltip>
      )
    },
    {
      title: '分类',
      width: 100,
      dataIndex: 'category',
      align: 'center',
      key: 'category',
      fixed: 'left'
    },
    {
      title: '账期',
      dataIndex: 'cycleId',
      align: 'center',
      key: 'cycleId',
      width: 80
    },
    {
      title: '姓名',
      dataIndex: 'empName',
      align: 'center',
      key: 'empName',
      width: 80
    },
    {
      title: '员工编号',
      dataIndex: 'empId',
      align: 'center',
      key: 'empId',
      width: 80
    },
    {
      title: '导入人名称',
      dataIndex: 'createName',
      align: 'center',
      key: 'createName',
      width: 80
    },
    {
      title: '导入人编号',
      dataIndex: 'createEmpId',
      align: 'center',
      key: 'createEmpId',
      width: 80
    },
    {
      title: '导入人组织',
      dataIndex: 'createOrgaName',
      align: 'center',
      key: 'createOrgaName',
      width: 130,
      render: (text: string, _record: orgOption) => (
        <Tooltip title={text}>
          <div className={styles.over_ellipsis}>{text}</div>
        </Tooltip>
      )
    },
    {
      title: '导入时间',
      dataIndex: 'createTime',
      align: 'center',
      key: 'createTime',
      width: 100
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      align: 'center',
      width: 100,
      render: (text: string, _record: orgOption) => (
        <Tooltip title={text}>
          <div className={styles.over_ellipsis}>{text}</div>
        </Tooltip>
      )
    }
  ]

  useEffect(() => {
    // setEditRecord(null)
    getEnumTypes()
    // 添加 resize 事件监听器
    window.addEventListener('resize', handleResize)
    // 清除事件监听器，防止内存泄漏
    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])
  const handleResize = () => {
    initHeight()
  }

  useEffect(() => {
    initHeight()
  }, [(document.querySelector('.peopleAdjustment_table .ant-table-header') || {})['offsetHeight']])

  // useEffect(() => {
  //   if (!editRecord && typeList?.length > 0) {
  //     const params = {
  //       ...form.getFieldsValue()
  //     }
  //     queryTableData(params)
  //   }
  // }, [editRecord])

  const initHeight = () => {
    const headerHeight =
      (document.querySelector('.peopleAdjustment_table .ant-table-header') || {})['offsetHeight'] ||
      0
    const pageHeight =
      (document.querySelector('.peopleAdjustment_table .ant-table-pagination') || {})[
        'offsetHeight'
      ] || 26
    if (headerHeight && pageHeight) {
      setHeight(
        contentRef.current?.offsetHeight -
          (tableTopRef.current.offsetHeight + headerHeight + pageHeight)
      )
    }
  }

  const handleDownload = async (type: number) => {
    try {
      // 调用接口获取 Blob 文件数据
      openNotification('正在导出', 0, 'loading')
      let response = null
      if (type === 1) {
        response = await downloadEmployeeTemplate({ templateId: 'EMPLOYEE_TRANSFER' })
      } else if (type === 2) {
        const { loginDate, cityId, category, cycleId, empId } = form.getFieldsValue()
        const params = {
          beginTime: loginDate?.length > 0 ? dayjs(loginDate[0]).format('YYYY-MM-DD') : null,
          endTime: loginDate?.length > 0 ? dayjs(loginDate[1]).format('YYYY-MM-DD') : null,
          cycleId: cycleId ? dayjs(cycleId)?.format('YYYYMM') : '',
          cityId: cityId ? cityId[cityId?.length - 1] : null,
          category,
          empId
        }
        response = await emportEmployeeTransfer(params)
      }
      downFile(response)
    } catch (error) {
      openNotification('导出失败', 1, 'error')
      console.error('Download failed:', error)
    }
  }

  // 点击查询
  const onFormFinish: FormProps<FieldType>['onFinish'] = values => {
    queryTableData({
      ...values
    })
  }

  const getEnumTypes = async () => {
    const [[unitError, unitData], [typeError, typeData]] = await Promise.all([
      getEnumType({ code: '1010', tag: 1 }),
      getEnumType({ code: 'EMPLOYEE_TRANSFER_CATEGORY' })
    ])
    if (unitError || typeError) {
      return
    }
    if (unitData.STATUS === '0000') {
      setUnitList(unitData.DATA?.filter(item => item?.orgId !== '49757'))
    }
    if (typeData.STATUS === '0000') {
      setTypeList(typeData.DATA)
    }
    const params = {
      ...form.getFieldsValue()
    }
    queryTableData(params)
  }

  // const handleChangePost = () => {
  //   form.setFieldsValue({
  //     level: ''
  //   })
  //   getLevelData(1)
  // }

  // const handleChangePost2 = () => {
  //   editform.setFieldsValue({
  //     postLevel: ''
  //   })
  //   getLevelData(2)
  // }

  const queryTableData = async params => {
    setTableLoading(true)
    const { loginDate, cityId, category, cycleId, empId } = params
    const param = {
      ...pagination,
      beginTime: loginDate?.length > 0 ? dayjs(loginDate[0]).format('YYYY-MM-DD') : null,
      endTime: loginDate?.length > 0 ? dayjs(loginDate[1]).format('YYYY-MM-DD') : null,
      cycleId: cycleId ? dayjs(cycleId)?.format('YYYYMM') : '',
      cityId: cityId ? cityId[cityId?.length - 1] : null,
      category,
      empId
    }
    console.log(123)
    const [error, res] = await updateEmployeeTransfer(param)
    setTableLoading(false)
    if (error) {
      message.error(res?.DATA || res?.MESSAGE || '调用失败')
      return
    }
    if (res.STATUS === '0000') {
      const {
        DATA: { data }
      } = res
      const dataWithKeys = data.map((item, index) => ({
        ...item,
        key: item.id || index // 使用数据中的唯一字段作为 key，或回退到索引
      }))
      setTableData(dataWithKeys)
      setPagination({
        ...pagination,
        total: res.DATA?.totalCount
      })
    } else {
      message.error(res?.MESSAGE || res?.DATA || '调用失败')
    }
  }

  const uploadFileFun = async () => {
    const formData = new FormData()
    fileList
      .map(file => file?.originFileObj)
      .forEach(file => {
        formData.append('file', file)
      })
    // formData.append('tag', '1')
    const [error, res] = await importEmployeeTransfer(formData)
    if (error) {
      message.error(res?.DATA || res?.MESSAGE || '调用失败')
      return
    }
    if (res.STATUS === '0000') {
      const { loginDate, yearVal, cityId, category } = form.getFieldsValue()
      const params = {
        // tag: '1',
        beginTime: loginDate.length > 0 ? dayjs(loginDate[0]).format('YYYY-MM-DD') : null,
        endTime: loginDate.length > 0 ? dayjs(loginDate[1]).format('YYYY-MM-DD') : null,
        yearVal: yearVal ? dayjs(yearVal).format('YYYY') : '',
        cityId: cityId[cityId?.length - 1],
        category
      }
      initHeight()
      queryTableData(params)
      setOpModalOpen(false)
      message.success(res?.DATA)
    } else {
      message.error(res?.MESSAGE || res?.DATA || '调用失败')
    }
  }

  // const editItem = record => {
  //   setEditRecord(record)
  //   // getLevelData(2, record?.postName)
  //   editform.setFieldsValue({
  //     ...record,
  //     remark1: record?.remarkOne || record?.remark1,
  //     remark2: record?.remarkTwo || record?.remark2,
  //     remark3: record?.remarkThree || record?.remark3
  //   })
  // }

  // 上传
  const handleOk = () => {
    if (fileList?.length > 0) {
      uploadFileFun()
    } else {
      message.error('请先选择文件上传')
    }
  }

  const handleCancel = () => {
    setOpModalOpen(false)
  }

  // 查询重置
  const onReset = () => {
    form.resetFields()
  }

  // 切换分页
  const onChangePage = page => {
    const newPage = {
      total: page?.total,
      pageNum: page?.current,
      pageSize: page?.pageSize
    }
    setPagination(newPage)
  }

  useEffect(() => {
    if (pagination?.total > 0) {
      const params = {
        ...form.getFieldsValue()
      }
      queryTableData(params)
    }
  }, [pagination.pageNum, pagination.pageSize])

  // // 搜索过滤空格
  // const handleSearchFilter = (input, option) => {
  //   const cleanedInput = input.trim() // 去除输入中的空格
  //   return option.children.toLowerCase().includes(cleanedInput.toLowerCase())
  // }

  const addData = async record => {
    // const { category } = form.getFieldsValue()
    record.forEach(item => {
      // const cityName = unitList.filter(i => i.enumId === item.cityId)[0].enumName
      // item.cityName = cityName
      // item.category = category
      item.cycleId = dayjs(item.cycleId)?.format('YYYYMM')
    })
    console.log('record', record)
    const [error, res] = await addEmployeeSaveBatch(record)
    setTableLoading(false)
    if (error) {
      message.error(res?.DATA || res?.MESSAGE || '调用失败')
      return
    }
    if (res.STATUS === '0000') {
      const { loginDate, yearVal, cityId, category } = form.getFieldsValue()
      const params = {
        // tag: '1',
        beginTime: loginDate.length > 0 ? dayjs(loginDate[0]).format('YYYY-MM-DD') : null,
        endTime: loginDate.length > 0 ? dayjs(loginDate[1]).format('YYYY-MM-DD') : null,
        yearVal: yearVal ? dayjs(yearVal).format('YYYY') : '',
        cityId: cityId[cityId?.length - 1],
        category
      }
      queryTableData(params)
      setAddModalOpen(false)
    } else {
      message.error(res?.MESSAGE || res?.DATA || '调用失败')
    }
  }

  // 搜索过滤空格
  const filter = (input, option) =>
    (option[0]?.enumName ?? '').toLowerCase().includes(input.toLowerCase()?.trim())

  return (
    <div
      className={`${'pt-[0.5rem] px-[0rem] h-full flex flex-col'} ${styles.peopleAdjustment_page}`}
    >
      <>
        <div ref={topRef} className='bg-white pt-[0.5rem] px-[1.75rem] mb-[0.5rem]'>
          <Form
            form={form}
            labelCol={{ span: 6 }}
            onFinish={onFormFinish}
            initialValues={{
              loginDate: '',
              category: 'KOU_JING_TIAO_ZHENG',
              // cycleId: '',
              cityId: '',
              empId: ''
            }}
          >
            <Row gutter={24}>
              <Col span={7}>
                <Form.Item
                  label='导入时间'
                  name='loginDate'
                  wrapperCol={{ span: 24 }}
                  className='mb-[0.5rem]'
                >
                  <RangePicker
                    style={{ width: '100%' }}
                    allowClear
                    ranges={{
                      近一天: [dayjs().subtract(1, 'day'), dayjs()],
                      近三天: [dayjs().subtract(3, 'day'), dayjs()],
                      近七天: [dayjs().subtract(7, 'day'), dayjs()]
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={5}>
                <Form.Item name='category' label='分类' className='mb-[0.5rem]'>
                  <Select placeholder='请选择类型' allowClear>
                    {
                      // prettier-ignore
                      typeList.map(unit => {
                          const {enumId, enumName} = unit;
                          return <Select.Option key={enumId}
                                                value={enumId}>{enumName}</Select.Option>
                        })
                    }
                  </Select>
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item name='cycleId' label='账期' className='mb-[0.5rem]'>
                  <DatePicker className='w-full' picker='month' />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item name='cityId' label='单位' className='mb-[0.5rem]'>
                  <Cascader
                    allowClear
                    changeOnSelect
                    expandTrigger='hover'
                    displayRender={labels => labels[labels.length - 1]}
                    options={unitList}
                    fieldNames={{
                      // value: 'orgId',
                      value: 'enumId',
                      label: 'enumName',
                      children: 'children'
                    }}
                    // showCheckedStrategy={Cascader.SHOW_CHILD}
                    // onChange={(value, selectedOptions) => {
                    //   console.log(value, selectedOptions)
                    //   setCascaderSelected(selectedOptions)
                    // }}
                    placeholder='请选择'
                    showSearch={{ filter }}
                    onSearch={value => console.log(value)}
                  />
                </Form.Item>
              </Col>
              <Col span={7}>
                <Form.Item
                  name='empId'
                  label='员工编号'
                  wrapperCol={{ span: 24 }}
                  className='mb-[0.5rem]'
                >
                  <Input placeholder='请输入' allowClear />
                </Form.Item>
              </Col>
              <Col span={4}>
                <div className='text-right'>
                  <Space>
                    <Button type='primary' htmlType='submit'>
                      查询
                    </Button>
                    <Button onClick={() => onReset()}>重置</Button>
                  </Space>
                </div>
              </Col>
            </Row>
          </Form>
        </div>
        <div
          ref={contentRef}
          className='relative bg-white px-5 pt-2.5'
          style={{ height: `calc(100% - ${topRef.current?.offsetHeight + 15}px)` }}
        >
          <div
            ref={tableTopRef}
            className={`${'flex justify-between items-center overflow-hidden mb-[0.1rem]'} ${styles.animation_box} ${showTitle ? 'h-[1.8rem]' : 'h-0'}`}
          >
            <div className={'flex '}>
              {showTitle ? (
                <FullscreenExitOutlined
                  className={`${styles.shousuo_icon} text-[1rem]`}
                  onClick={() => {
                    setShowTitle(false)
                    setTimeout(() => {
                      initHeight()
                    }, 200)
                  }}
                />
              ) : (
                <FullscreenOutlined
                  className={`${styles.shousuo_icon} ${'absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10'}`}
                  onClick={() => {
                    setShowTitle(true)
                    setTimeout(() => {
                      initHeight()
                    }, 200)
                  }}
                />
              )}
              <div className='font-bold text-[0.8rem] ml-3'>数据列表</div>
            </div>
            <div className='flex gap-x-[2.5rem]'>
              <div className='flex gap-x-[0.75rem]'>
                <div className='flex items-center gap-x-[0.25rem]'>
                  <SvgIcon name='excel' width={20} height={20} />
                  <span className='text-[#E60027] cursor-pointer' onClick={() => handleDownload(1)}>
                    下载导入模版
                  </span>
                </div>
                <Button danger ghost icon={<UploadOutlined />} onClick={() => setOpModalOpen(true)}>
                  导入
                </Button>
                <Button danger ghost icon={<DownloadOutlined />} onClick={() => handleDownload(2)}>
                  导出
                </Button>
                <Button danger type='primary' onClick={() => setAddModalOpen(true)}>
                  新增
                </Button>
              </div>
            </div>
          </div>
          <ResizableTable
            className='peopleAdjustment_table'
            rowClassName={(_, i) => (i % 2 === 1 ? `customRow odd` : `customRow even`)}
            columns={columns}
            dataSource={tableData}
            scroll={{
              // x: 'max-content',
              y: `calc(${height}px - 0.625rem - 1.6rem - 0.5rem)`
            }}
            loading={tableLoading}
            onChange={onChangePage}
            pagination={{
              ...pagination,
              showSizeChanger: true,
              showQuickJumper: true,
              pageSizeOptions: ['10', '20', '50']
            }}
          />
        </div>
      </>

      <Modal
        title='新增数据'
        destroyOnClose={true}
        open={addModalOpen}
        centered
        className={styles.detail_modal}
        // okText='提交'
        // onOk={handleOk}
        footer={null}
        onCancel={() => setAddModalOpen(false)}
        width={'60%'}
      >
        <AddData typeList={typeList} addData={addData} />
      </Modal>

      <Modal
        title='文件上传'
        destroyOnClose={true}
        open={upModalOpen}
        centered
        className={styles.detail_modal}
        // okText={ }
        footer={null}
        // onOk={handleOk}
        onCancel={handleCancel}
      >
        <div className='mt-4 mb-8' style={{ marginBottom: '0px' }}>
          <Row>
            <Col span={22} offset={1} className='h-[10rem]'>
              <Dragger
                {...{
                  // name: 'file',
                  // data: {
                  //     tag: "1"
                  // },
                  // action: `/zhyy/employee/importEmployeeTransfer`,
                  action: '',
                  maxCount: 1,
                  multiple: false,
                  fileList,
                  beforeUpload(file, fileList) {
                    console.log(file, fileList)
                    return false
                    // let isError = true;
                    // form.validateFields((err, fieldsValue) => {
                    //   isError = !err;
                    // })
                    // return isError;
                  },
                  onChange(info) {
                    const { status } = info.file
                    if (status !== 'uploading') {
                      console.log(info.file, info.fileList)
                      setFileList(info.fileList)
                    }
                    // if (status === 'done') {
                    //     message.success(`${info.file.name} file uploaded successfully.`);
                    // } else if (status === 'error') {
                    //     message.error(`${info.file.name} file upload failed.`);
                    // }
                  }
                }}
              >
                <p className='ant-upload-drag-icon'>
                  <InboxOutlined style={{ color: '#F14846' }} />
                </p>
                <p className='ant-upload-text'>点击或将文件拖拽到这里上传</p>
                <p className='ant-upload-hint'>支持excel格式的文件。</p>
              </Dragger>
            </Col>
          </Row>

          <div style={{ display: 'flex', justifyContent: 'center', marginTop: '2rem' }}>
            <Button danger onClick={handleCancel}>
              取消
            </Button>

            <Popconfirm
              title=''
              description='导入文件如有重复数据，将会被覆盖，请确认是否上传。'
              onConfirm={handleOk}
              // onCancel={cancel}
              okText='确认'
              cancelText='取消'
            >
              <Button
                danger
                type='primary'
                style={{ marginLeft: '1rem' }}
                disabled={fileList.length < 1}
              >
                上传
              </Button>
            </Popconfirm>
          </div>
        </div>
      </Modal>
    </div>
  )
}
export default People
