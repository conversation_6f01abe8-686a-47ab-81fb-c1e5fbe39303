.detail_modal {
  :global {
    .ant-modal-footer {
      text-align: center;
    }
  }
}
.test_card {
  display: inline-block;
  width: 50px;
  height: 50px;
  background-color: #00acff;
}

.ant-modal-footer {
  text-align: center;
}

.peopleAdjustment_page {
  :global {
    // .ant-table-row {
    //   height: 22px !important;
    //   line-height: 22px !important;
    //   > td {
    //     padding: 0 !important;
    //   }
    // }
    // .ant-table-thead > tr > th {
    //   text-align: center !important;
    // }
    // .ant-table-thead > tr > th {
    //   padding-top: 2px !important; /* 设置表头单元格的上间距 */
    //   padding-bottom: 2px !important; /* 设置表头单元格的下间距 */
    // }
  }

  .animation_box {
    transition: height 0.2s ease;
  }

  .over_ellipsis {
    white-space: nowrap;         /* 不允许换行 */
    overflow: hidden;            /* 隐藏超出部分 */
    text-overflow: ellipsis;     /* 使用省略号显示超出文本 */
  }
}

.addData{
  :global {
    .ant-space-item {
      width: 100% !important;
    }
  }
}