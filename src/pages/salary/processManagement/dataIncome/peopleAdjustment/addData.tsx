import { Button, Form, Input, Select, Space, DatePicker } from 'antd'
import { PlusOutlined } from '@ant-design/icons'
import styles from './Index.module.scss'
import { useRequest } from '@/hooks'
import proManageService from '@/pages/salary/processManagement/service.ts'

const columns = [
  {
    title: '分类',
    dataIndex: 'category'
  },
  {
    title: '账期',
    dataIndex: 'cycleId'
  },
  {
    title: '员工编号',
    dataIndex: 'empId'
  },
  {
    title: '姓名',
    dataIndex: 'empName'
  },
  {
    title: '备注',
    dataIndex: 'remark'
  }
]

// 防抖函数
const debounce = (func, delay) => {
  let timeout
  return function (...args) {
    clearTimeout(timeout)
    timeout = setTimeout(() => {
      func(...args)
    }, delay)
  }
}

const App = props => {
  const [form] = Form.useForm()
  const { runAsync: getEmployeeByEmpId } = useRequest(proManageService.getEmployeeByEmpId, {
    manual: true
  })

  const onFinish = values => {
    props.addData(values.users)
    console.log('Received values of form:', values)
  }

  // 防抖函数用于输入框值变化时的延迟调用
  const handleEmpIdChange = debounce((empId, index, form) => {
    getEmployeeByEmpId({ empId }).then(response => {
      const res = response[1]
      if (res.STATUS === '0000') {
        const { DATA: userInfo } = res
        if (userInfo?.employeeName) {
          // 获取当前表单所有的值
          const currentValues = form.getFieldsValue()
          const updatedUsers = [...currentValues.users]
          updatedUsers[index].empName = userInfo.employeeName

          // 更新当前行的姓名字段
          form.setFieldsValue({ users: updatedUsers })
        } else {
          const currentValues = form.getFieldsValue()
          const updatedUsers = [...currentValues.users]
          updatedUsers[index].empName = ''
          form.setFieldsValue({ users: updatedUsers })
        }
      }
    })
  }, 1000)

  return (
    <div className={styles.addData}>
      <div>
        <div
          style={{
            background: '#fafafa',
            display: 'flex',
            textAlign: 'center',
            height: '3rem',
            border: '1px solid #f0f0f0',
            width: '100%'
          }}
        >
          {columns.map(item => (
            <p
              key={item.dataIndex}
              style={{ width: '25%', fontWeight: 500, fontSize: 14, margin: 'auto 3px' }}
            >
              {item.title}
            </p>
          ))}
          <p style={{ width: '25%', fontWeight: 500, fontSize: 14, margin: 'auto 1rem' }}>操作</p>
        </div>
      </div>

      <Form
        name='dynamic_form_nest_item'
        form={form}
        onFinish={onFinish}
        style={{
          width: '100%',
          marginTop: 16,
          maxHeight: '500px'
        }}
        autoComplete='off'
      >
        <Form.List name='users'>
          {(fields, { add, remove }) => (
            <div>
              <div style={{ width: '100%', maxHeight: '390px', overflowY: 'auto' }}>
                {fields.map(({ key, name }, index) => (
                  <Space
                    key={key}
                    style={{
                      marginBottom: 8,
                      width: '100%'
                    }}
                    align='baseline'
                  >
                    <Form.Item
                      key={key + 'category'}
                      name={[name, 'category']}
                      rules={[{ required: true, message: '请输入分类' }]}
                    >
                      <Select placeholder='请选择分类' allowClear>
                        {props.typeList?.map(unit => (
                          <Select.Option key={unit.enumId} value={unit.enumId}>
                            {unit.enumName}
                          </Select.Option>
                        ))}
                      </Select>
                    </Form.Item>

                    <Form.Item
                      key={key + 'cycleId'}
                      name={[name, 'cycleId']}
                      rules={[{ required: true, message: '请输入账期' }]}
                    >
                      <DatePicker className='w-full' picker='month' />
                    </Form.Item>

                    <Form.Item
                      key={key + 'empId'}
                      name={[name, 'empId']}
                      rules={[{ required: true, message: '请输入员工编号' }]}
                    >
                      <Input
                        placeholder='请输入员工编号'
                        onChange={e => {
                          const empId = e.target.value
                          handleEmpIdChange(empId, index, form)
                        }}
                      />
                    </Form.Item>

                    <Form.Item
                      key={key + 'empName'}
                      name={[name, 'empName']}
                      rules={[{ required: true, message: '请输入正确的员工编号' }]}
                    >
                      <Input placeholder='姓名' disabled />
                    </Form.Item>

                    <Form.Item key={key + 'remark'} name={[name, 'remark']}>
                      <Input placeholder='请输入备注' />
                    </Form.Item>
                    <Button
                      type='dashed'
                      onClick={() => remove(name)}
                      style={{ margin: 'auto', width: '50%', marginLeft: '25%' }}
                    >
                      删除
                    </Button>
                  </Space>
                ))}
              </div>
              <Form.Item
                style={{ width: '100%', display: 'flex', justifyContent: 'right', marginTop: 12 }}
              >
                <Button
                  onClick={() => add()}
                  danger
                  block
                  icon={<PlusOutlined />}
                  style={{ width: 150 }}
                >
                  新增一条数据
                </Button>
              </Form.Item>
            </div>
          )}
        </Form.List>
        <Form.Item
          style={{
            textAlign: 'center',
            width: '100%',
            background: '#fff'
          }}
        >
          <Button type='primary' htmlType='submit'>
            提交
          </Button>
        </Form.Item>
      </Form>
    </div>
  )
}

export default App
