import React, { useContext, useEffect, useRef, useState } from 'react'
import { Button, Form, Input, Popconfirm, Select, Space, DatePicker } from 'antd'
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons'
import styles from './Index.module.scss'
import { useDebounce } from '@/utils/debounce.ts'
import { useRequest } from '@/hooks'
import proManageService from '@/pages/salary/processManagement/service.ts'

const EditableContext = React.createContext(null)

const App = props => {
  const [form] = Form.useForm()
  const defaultColumns = props.columns
  const unitList = props.unitList
  const { runAsync: getEmployeeByEmpId } = useRequest(proManageService.getEmployeeByEmpId, {
    manual: true
  })
  const onFinish = values => {
    props.addData(values.users)
    console.log('Received values of form:', values)
  }
  const handleSearchName = (e, user, index, updatedValues) => {
    console.log(user, index, updatedValues)
    getEmployeeByEmpId({ empId: e }).then(response => {
      const res = response[1]
      if (res.STATUS === '0000') {
        const { DATA: userInfo } = res
        console.log(123)
        return 123
        // user.empName =e + '-updated'
        // updatedValues[index] = user
        // // setEmpName(userInfo?.employeeName)
        // // setMenuTreeData(res.DATA);
        // form.setFieldsValue({ users: updatedValues })
      } else {
        // message.error(res.MESSAGE);
      }
    })
  }

  const onValuesChange = (changedValues, allValues) => {
    console.log(changedValues.users, allValues)
    // 假设有两个字段 A 和 B，当 A 的值变化时希望动态更新 B 的值
    if (changedValues.users) {
      const changeItem = changedValues.users.filter(item => item)[0]
      console.log(changedValues.users.filter(item => item))
      const updatedValues = [...allValues.users]
      updatedValues.forEach((item, index) => {
        if (changeItem.empId && item.empId) {
          useDebounce(
            getEmployeeByEmpId({ empId: changeItem.empId }).then(response => {
              const res = response[1]
              if (res.STATUS === '0000') {
                const { DATA: userInfo } = res
                item.empName = userInfo?.employeeName
                updatedValues[index] = item
              }
            }),
            500
          )
        }
      })
      form.setFieldsValue({ users: updatedValues })
    }
  }
  const initHeader = () => {
    const allHeader = columns => {
      return (
        <>
          {columns.map(item => {
            if (item?.children?.length > 0) {
              return allHeader(item?.children)
            } else {
              return (
                <p
                  key={item.dataIndex}
                  style={{
                    width: item?.width || '25%',
                    fontWeight: 500,
                    fontSize: 14,
                    margin: 'auto 3px'
                  }}
                >
                  {' '}
                  {item.title}{' '}
                </p>
              )
            }
          })}
        </>
      )
    }
    return (
      <div>
        <div
          style={{
            background: '#fafafa',
            display: 'flex',
            textAlign: 'center',
            height: '3rem',
            border: '1px solid #f0f0f0',
            width: '100%'
          }}
        >
          {allHeader(defaultColumns)}
          <p style={{ width: '25%', fontWeight: 500, fontSize: 14, margin: 'auto 1rem' }}>操作</p>
        </div>
      </div>
    )
  }

  return (
    <div className={styles.add}>
      <div>
        <div
          style={{
            background: '#fafafa',
            display: 'flex',
            textAlign: 'center',
            height: '3rem',
            border: '1px solid #f0f0f0',
            width: '100%'
          }}
        >
          {defaultColumns.map(item => (
            <p
              key={item.dataIndex}
              style={{ width: '25%', fontWeight: 500, fontSize: 14, margin: 'auto 3px' }}
            >
              {' '}
              {item.title}{' '}
            </p>
          ))}
          <p style={{ width: '25%', fontWeight: 500, fontSize: 14, margin: 'auto 1rem' }}>操作</p>
        </div>
      </div>

      <Form
        name='dynamic_form_nest_item'
        form={form}
        onFinish={onFinish}
        onValuesChange={onValuesChange}
        style={{
          width: '100%',
          marginTop: 16,
          maxHeight: '500px'
        }}
        autoComplete='off'
      >
        <Form.List name='users'>
          {(fields, { add, remove }) => (
            <div>
              <div style={{ width: '100%', maxHeight: '390px', overflowY: 'auto' }}>
                {fields.map(({ key, name, ...restField }) => (
                  <Space
                    key={key}
                    style={{
                      // display: 'flex',
                      marginBottom: 8,
                      width: '100%'
                    }}
                    align='baseline'
                  >
                    {defaultColumns.map(item => (
                      <Form.Item
                        // style={{ width: item.width }}
                        key={item.dataIndex}
                        {...restField}
                        name={[name, item.dataIndex]}
                        rules={[
                          {
                            required: true,
                            message: `请输入${item.title}`
                          }
                        ]}
                      >
                        {item.select ? (
                          <Select placeholder={`请选择${item.title}`} allowClear>
                            {
                              // prettier-ignore
                              item?.children?.map(unit => {
                                const { enumId, enumName } = unit;
                                return <Select.Option key={enumId}
                                  value={enumId}>{enumName}</Select.Option>
                              })
                            }
                          </Select>
                        ) : item.date ? (
                          <DatePicker className='w-full' picker={item.date} />
                        ) : (
                          <Input placeholder={`请输入${item.title}`} />
                        )}
                      </Form.Item>
                    ))}
                    <Button
                      type='dashed'
                      onClick={() => remove(name)}
                      style={{ margin: 'auto', width: '50%', marginLeft: '25%' }}
                    >
                      删除
                    </Button>
                    {/* <MinusCircleOutlined onClick={() => remove(name)} style={{ margin: 'auto' }} /> */}
                  </Space>
                ))}
              </div>
              <Form.Item
                style={{ width: '100%', display: 'flex', justifyContent: 'right', marginTop: 12 }}
              >
                <Button
                  // type='dashed'
                  onClick={() => add()}
                  danger
                  block
                  icon={<PlusOutlined />}
                  style={{ width: 150 }}
                >
                  新增一条数据
                </Button>
              </Form.Item>
            </div>
          )}
        </Form.List>
        <Form.Item
          style={{
            textAlign: 'center',
            // position: 'fixed',
            // bottom: '18%',
            width: '100%',
            background: '#fff'
          }}
        >
          <Button type='primary' htmlType='submit' style={{}}>
            提交
          </Button>
        </Form.Item>
      </Form>
    </div>
  )
}
export default App
