import request from '@/request'

const peopleService = {
  // 查询枚举
  getEnumType: params => {
    return request.post<any>('/zhyy/employee/getEnumType', params)
  },

  // 人员岗位信息-列表查询
  getEmployeePostPag: params => {
    return request.get<any>('/zhyy/salary/yearlyBaseData/list', { params })
  },

  // 人员岗位信息-下载模板
  downloadEmployeeTemplate: params => {
    return request.get<any>('/zhyy/salary/common/downloadTemplate', {
      params,
      responseType: 'blob'
    })
  },

  // 人员岗位信息-下载对照表
  downloadEmployeeCompare: params => {
    return request.post<any>('/zhyy/employee/downloadEmployeeCompare', params, {
      responseType: 'blob'
    })
  },

  // 人员岗位信息-导出模板
  exportEmployeeExcel: params => {
    return request.get<any>('/zhyy/salary/yearlyBaseData/exportExcel', {
      params,
      responseType: 'blob'
    })
  },

  // 人员岗位信息-导入
  uploadEmployeeExcel: params => {
    return request.post<any>('/zhyy/salary/yearlyBaseData/uploadExcel', params)
  },

  // 人员岗位信息-修改
  updateEmployeePost: params => {
    return request.post<any>('/zhyy/employee/updateEmployeePost', params)
  },
  // 人员调整查询
  updateEmployeeTransfer: params => {
    return request.get<any>('/zhyy/salary/employeeTransfer/list', { params })
  },

  importEmployeeTransfer: params => {
    return request.post<any>('/zhyy/salary/employeeTransfer/uploadExcel', params, {
      // responseType: 'blob'
    })
  },

  emportEmployeeTransfer: params => {
    return request.get<any>('/zhyy/salary/employeeTransfer/exportExcel', {
      params,
      responseType: 'blob'
    })
  },
  // 其他查询
  updateOther: params => {
    return request.get<any>('/zhyy/salary/additionalBudget/list', { params })
  },
  //其他导入
  importOther: params => {
    return request.post<any>('/zhyy/salary/additionalBudget/uploadExcel', params, {
      // responseType: 'blob'
    })
  },
  //其他导出
  emportOther: params => {
    return request.get<any>('/zhyy/salary/additionalBudget/exportExcel', {
      params,
      responseType: 'blob'
    })
  },

  addSaveBatch: params => {
    return request.post<any>('/zhyy/salary/yearlyBaseData/saveBatch', params)
  },
  addEmployeeSaveBatch: params => {
    return request.post<any>('/zhyy/salary/employeeTransfer/saveBatch', params)
  },

  additionalBudget: params => {
    return request.post<any>('/zhyy/salary/additionalBudget/saveBatch', params)
  }
}

export default peopleService
