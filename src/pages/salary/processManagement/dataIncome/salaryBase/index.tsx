import { useEffect, useRef, useState } from 'react'
import {
  Form,
  Row,
  Col,
  // Input,
  Select,
  Button,
  FormProps,
  Modal,
  Upload,
  message,
  Space,
  // Spin,
  Cascader,
  DatePicker,
  Popconfirm,
  Tooltip
} from 'antd'
import {
  UploadOutlined,
  DownloadOutlined,
  InboxOutlined,
  FullscreenExitOutlined,
  FullscreenOutlined
} from '@ant-design/icons'
import SvgIcon from '@/components/SvgIcons'
import peopleService from '../service'
import { useRequest } from '@/hooks'
import styles from './Index.module.scss'
import type { TableColumnsType } from 'antd'
import { Enum, FieldType } from '../interface'
import { downFile, openNotification } from '@/utils/down.tsx'
// import effectService from '@/pages/effect/employment/service.ts'
import AddData from '../components/index'
const { Dragger } = Upload
const { RangePicker } = DatePicker
import dayjs from 'dayjs'
import { orgOption } from '@/pages/effect/employment/assest.ts'
import ResizableTable from '@/components/resizeTable/index.jsx'
import { formatMoney } from '@/hooks/format'
interface DataType {
  key: React.Key
  name: string
  age: number
  address: string
  select: boolean
}

const data: DataType[] = []
for (let i = 0; i < 100; i++) {
  data.push({
    key: i,
    name: `Edward ${i}`,
    age: 32,
    address: `London Park no. ${i}`,
    select: false
  })
}

const People: React.FC = () => {
  const [form] = Form.useForm()
  // const [editform] = Form.useForm()
  const topRef = useRef(null)
  const contentRef = useRef(null)
  const tableTopRef = useRef(null)
  const [height, setHeight] = useState(0)
  const [tableLoading, setTableLoading] = useState<boolean>(false)
  // const [saveLoading, setSaveLoading] = useState<boolean>(false)
  // const [editRecord, setEditRecord] = useState<any>(null)
  const [upModalOpen, setOpModalOpen] = useState<boolean>(false)
  const [fileList, setFileList] = useState([])
  const [addModalOpen, setAddModalOpen] = useState<boolean>(false)
  const [category, setCategory] = useState<any>('XIN_CHOU_JI_SHU')
  // const [levelList2, setLevelList2] = useState<Enum[]>([])
  const [unitList, setUnitList] = useState<any[]>([])
  const [typeList, setTypeList] = useState<Enum[]>([])
  const [tableData, setTableData] = useState<DataType[]>([])
  const [showTitle, setShowTitle] = useState<boolean>(true)
  const [pagination, setPagination] = useState({
    total: 0,
    pageNum: 1,
    pageSize: 50
  })

  const { runAsync: getEnumType } = useRequest(peopleService.getEnumType, { manual: true })
  const { runAsync: getEmployeePostPag } = useRequest(peopleService.getEmployeePostPag, {
    manual: true
  })
  const { runAsync: downloadEmployeeTemplate } = useRequest(
    peopleService.downloadEmployeeTemplate,
    { manual: true }
  )
  const { runAsync: downloadEmployeeCompare } = useRequest(peopleService.downloadEmployeeCompare, {
    manual: true
  })
  const { runAsync: exportEmployeeExcel } = useRequest(peopleService.exportEmployeeExcel, {
    manual: true
  })
  const { runAsync: uploadEmployeeExcel } = useRequest(peopleService.uploadEmployeeExcel, {
    manual: true
  })
  const { runAsync: addSaveBatch } = useRequest(peopleService.addSaveBatch, {
    manual: true
  })
  // const { runAsync: build4LevelOrgTree2 } = useRequest(effectService.build4LevelOrgTree2, {
  //   manual: true
  // })

  const columns: TableColumnsType<any> = [
    {
      title: '单位',
      width: 130,
      dataIndex: 'cityName',
      key: 'cityName',
      align: 'center',
      fixed: 'left'
    },
    {
      title: '年份',
      width: 50,
      dataIndex: 'yearVal',
      key: 'yearVal',
      align: 'center',
      fixed: 'left'
    },
    {
      title: '金额',
      dataIndex: 'amount',
      key: 'amount',
      align: 'center',
      width: 60,
      render: text => <span>{formatMoney(text)}</span>
    },
    {
      title: '类型',
      dataIndex: 'category',
      key: 'category',
      align: 'center',
      width: 80
    },
    {
      title: '导入人名称',
      dataIndex: 'createName',
      key: 'createName',
      align: 'center',
      width: 80
    },
    {
      title: '导入人编号',
      dataIndex: 'createEmpId',
      key: 'createEmpId',
      align: 'center',
      width: 80
    },
    {
      title: '导入人组织',
      dataIndex: 'createOrgaName',
      key: 'createOrgaName',
      align: 'center',
      width: 80,
      render: (text: string, _record: orgOption) => (
        <Tooltip title={text}>
          <div className={styles.over_ellipsis}>{text}</div>
        </Tooltip>
      )
    },
    {
      title: '导入时间',
      dataIndex: 'createTime',
      key: 'createTime',
      align: 'center',
      width: 100
    }
    // {
    //   title: '操作',
    //   dataIndex: 'action',
    //   key: 'action',
    //   fixed: 'right',
    //   align: 'center',
    //   width: 100,
    //   render: (_, record) => (
    //     <span
    //       className='cursor-pointer'
    //       style={{ color: '#F14846' }}
    //       onClick={() => editItem(record)}
    //     >
    //       编辑
    //     </span>
    //   )
    // }
  ]
  const addColumns = [
    {
      title: '单位',
      width: '30%',
      dataIndex: 'cityId',
      key: 'cityId',
      select: true,
      fixed: 'left',
      children: unitList
    },
    {
      title: '年份',
      width: '30%',
      dataIndex: 'yearVal',
      key: 'yearVal',
      fixed: 'left',
      date: 'year'
    },
    {
      title: '金额',
      dataIndex: 'amount',
      key: 'amount',
      width: '30%'
    }
  ]
  useEffect(() => {
    getEnumTypes()
    // 添加 resize 事件监听器
    window.addEventListener('resize', handleResize)
    // 清除事件监听器，防止内存泄漏
    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])
  const handleResize = () => {
    initHeight()
  }

  useEffect(() => {
    initHeight()
  }, [(document.querySelector('.salaryBase_table .ant-table-header') || {})['offsetHeight']])

  // useEffect(() => {
  //   initHeight()
  // }, [tableData])

  const initHeight = () => {
    const headerHeight =
      (document.querySelector('.salaryBase_table .ant-table-header') || {})['offsetHeight'] || 0
    const pageHeight =
      (document.querySelector('.salaryBase_table .ant-table-pagination') || {})['offsetHeight'] ||
      26
    if (headerHeight && pageHeight) {
      setHeight(
        contentRef.current?.offsetHeight -
          (tableTopRef.current.offsetHeight + headerHeight + pageHeight)
      )
    }
  }

  const handleDownload = async (type: number) => {
    try {
      // 调用接口获取 Blob 文件数据
      openNotification('正在导出', 0, 'loading')
      let response = null
      if (type === 1) {
        response = await downloadEmployeeTemplate({ templateId: 'YEARLY_BASE' })
      } else if (type === 2) {
        const { loginDate, yearVal, cityId, category } = form.getFieldsValue()
        const params = {
          // tag: '1',
          beginTime: loginDate.length > 0 ? dayjs(loginDate[0]).format('YYYY-MM-DD') : null,
          endTime: loginDate.length > 0 ? dayjs(loginDate[1]).format('YYYY-MM-DD') : null,
          yearVal: yearVal ? dayjs(yearVal).format('YYYY') : '',
          cityId: cityId[cityId?.length - 1],
          category
        }
        response = await exportEmployeeExcel(params)
      } else if (type === 3) {
        response = await downloadEmployeeCompare()
      }
      downFile(response)
    } catch (error) {
      openNotification('导出失败', 1, 'error')
      console.error('Download failed:', error)
    }
  }

  // 点击查询
  const onFormFinish: FormProps<FieldType>['onFinish'] = values => {
    const { loginDate, yearVal, cityId, category } = values
    const params = {
      // tag: '1',
      beginTime: loginDate.length > 0 ? dayjs(loginDate[0]).format('YYYY-MM-DD') : null,
      endTime: loginDate.length > 0 ? dayjs(loginDate[1]).format('YYYY-MM-DD') : null,
      yearVal: yearVal ? dayjs(yearVal).format('YYYY') : '',
      cityId: cityId[cityId?.length - 1],
      category
    }
    console.log('Success:', params)
    queryTableData(params)
  }

  // // 点击保存
  // const onFormFinish2: FormProps<FieldType2>['onFinish'] = async values => {
  //   console.log('Success:', values)
  //   setSaveLoading(true)
  //   const [error, res] = await addSaveBatch({
  //     // postId: values.postId.split(',')[0],
  //     tag: '1',
  //     postName: values?.postName,
  //     postLevel: values?.postLevel,
  //     remarkOne: values?.remark1,
  //     remarkTwo: values?.remark2,
  //     remarkThree: values?.remark3,
  //     employeeId: editRecord?.employeeId
  //   })
  //   setSaveLoading(false)
  //   if (error) {
  //     message.error('保存失败')
  //     return
  //   }
  //   if (res.STATUS === '0000') {
  //     message.success('保存成功')
  //     setEditRecord(null)
  //   } else {
  //     message.error(res?.MESSAGE)
  //   }
  // }

  // const getLevelData = async (type: number, postName?: string) => {
  //   let record = null
  //   if (type === 1) {
  //     const postId = form.getFieldsValue()?.postId
  //     record = postList?.find(item => `${item?.enumId},${item?.region}` === postId)
  //   } else {
  //     const postId = editform.getFieldsValue()?.postName
  //     if (postName) {
  //       record = postList?.find(item => item?.enumName === postName)
  //     } else {
  //       record = postList?.find(item => `${item?.enumId},${item?.region}` === postId)
  //     }
  //   }
  //   const [error, res] = await getEnumType({ code: '1003', region: record?.enumId })
  //   if (error) {
  //     return
  //   }
  //   if (res.STATUS === '0000') {
  //     if (type === 1) {
  //       setLevelList(res.DATA)
  //     } else {
  //       setLevelList2(res.DATA)
  //     }
  //   }
  // }

  const getEnumTypes = async () => {
    const [[unitError, unitData], [typeError, typeData]] = await Promise.all([
      getEnumType({ code: '1010', tag: 1 }),
      getEnumType({ code: 'YEARLY_BASEDATA_CATEGORY' })
    ])
    if (unitError || typeError) {
      return
    }
    if (unitData.STATUS === '0000') {
      setUnitList(unitData.DATA?.filter(item => item?.orgId !== '49757'))
    }
    if (typeData.STATUS === '0000') {
      setTypeList(typeData.DATA)
    }
    const { loginDate, yearVal, cityId, category } = form.getFieldsValue()
    const params = {
      // tag: '1',
      beginTime: loginDate.length > 0 ? dayjs(loginDate[0]).format('YYYY-MM-DD') : null,
      endTime: loginDate.length > 0 ? dayjs(loginDate[1]).format('YYYY-MM-DD') : null,
      yearVal: yearVal ? dayjs(yearVal).format('YYYY') : '',
      cityId: cityId[cityId?.length - 1],
      category
    }
    queryTableData(params)
  }

  // const handleChangePost = () => {
  //   form.setFieldsValue({
  //     level: ''
  //   })
  //   getLevelData(1)
  // }

  // const handleChangePost2 = () => {
  //   editform.setFieldsValue({
  //     postLevel: ''
  //   })
  //   getLevelData(2)
  // }

  const queryTableData = async params => {
    setTableLoading(true)
    const [error, res] = await getEmployeePostPag({
      ...params,
      ...pagination
      // code: params?.code?.trim(),
      // name: params?.name?.trim(),
      // // postId: params?.postId?.split(',')[0],
      // unit: undefined,
      // unitId: params?.unit?.length > 1 ? params?.unit[params?.unit?.length - 1] : '',
      // regionId: params?.unit?.length === 1 ? params?.unit[0] : ''
    })
    setTableLoading(false)
    if (error) {
      message.error(res?.DATA || res?.MESSAGE || '调用失败')
      return
    }
    if (res.STATUS === '0000') {
      const {
        DATA: { data }
      } = res
      const dataWithKeys = data.map((item, index) => ({
        ...item,
        key: item.id || index // 使用数据中的唯一字段作为 key，或回退到索引
      }))
      setTableData(dataWithKeys)
      setPagination({
        ...pagination,
        total: res.DATA?.totalCount
      })
    } else {
      message.error(res?.MESSAGE || res?.DATA || '调用失败')
    }
  }

  const uploadFileFun = async () => {
    const formData = new FormData()
    fileList
      .map(file => file?.originFileObj)
      .forEach(file => {
        formData.append('file', file)
      })
    formData.append('category', category)
    const [error, res] = await uploadEmployeeExcel(formData)
    console.log('error, res', error, res)
    if (error) {
      message.error(res?.DATA || res?.MESSAGE || '调用失败')
      return
    }
    if (res.STATUS === '0000') {
      setOpModalOpen(false)
      message.success(res?.DATA)
    } else {
      message.error(res?.MESSAGE || res?.DATA || '调用失败')
    }
  }

  // const editItem = record => {
  //   setEditRecord(record)
  //   // getLevelData(2, record?.postName)
  //   editform.setFieldsValue({
  //     ...record,
  //     remark1: record?.remarkOne || record?.remark1,
  //     remark2: record?.remarkTwo || record?.remark2,
  //     remark3: record?.remarkThree || record?.remark3
  //   })
  // }

  // 上传
  const handleOk = () => {
    if (fileList?.length > 0) {
      uploadFileFun()
    } else {
      message.error('请先选择文件上传')
    }
  }

  const handleCancel = () => {
    setOpModalOpen(false)
  }

  // 查询重置
  const onReset = () => {
    form.resetFields()
  }

  // 切换分页
  const onChangePage = page => {
    const newPage = {
      total: page?.total,
      pageNum: page?.current,
      pageSize: page?.pageSize
    }
    setPagination(newPage)
  }

  useEffect(() => {
    if (pagination?.total > 0) {
      const { loginDate, yearVal, cityId, category } = form.getFieldsValue()
      const params = {
        // tag: '1',
        beginTime: loginDate.length > 0 ? dayjs(loginDate[0]).format('YYYY-MM-DD') : null,
        endTime: loginDate.length > 0 ? dayjs(loginDate[1]).format('YYYY-MM-DD') : null,
        yearVal: yearVal ? dayjs(yearVal).format('YYYY') : '',
        cityId: cityId[cityId?.length - 1],
        category
      }
      queryTableData(params)
    }
  }, [pagination.pageNum, pagination.pageSize])

  // // 搜索过滤空格
  // const handleSearchFilter = (input, option) => {
  //   const cleanedInput = input.trim() // 去除输入中的空格
  //   return option.children.toLowerCase().includes(cleanedInput.toLowerCase())
  // }

  // 搜索过滤空格
  const filter = (input, option) =>
    (option[0]?.enumName ?? '').toLowerCase().includes(input.toLowerCase()?.trim())

  const addData = async record => {
    record.forEach(item => {
      const cityName = unitList.filter(i => i.enumId === item.cityId)[0].enumName
      item.cityName = cityName
      item.category = category
      item.yearVal = dayjs(item.yearVal).format('YYYY')
    })
    console.log('record', record)
    const [error, res] = await addSaveBatch(record)
    setTableLoading(false)
    if (error) {
      message.error(res?.DATA || res?.MESSAGE || '调用失败')
      return
    }
    if (res.STATUS === '0000') {
      message.success(res.DATA)
      const { loginDate, yearVal, cityId, category } = form.getFieldsValue()
      const params = {
        // tag: '1',
        beginTime: loginDate.length > 0 ? dayjs(loginDate[0]).format('YYYY-MM-DD') : null,
        endTime: loginDate.length > 0 ? dayjs(loginDate[1]).format('YYYY-MM-DD') : null,
        yearVal: yearVal ? dayjs(yearVal).format('YYYY') : '',
        cityId: cityId[cityId?.length - 1],
        category
      }
      queryTableData(params)
      setAddModalOpen(false)
    } else {
      message.error(res?.MESSAGE || res?.DATA || '调用失败')
    }
  }

  const cancel = e => {
    console.log(e)
  }

  return (
    <div
      className={`${'pt-[0.5rem] px-[0rem] h-full flex flex-col overflow-hidden'} ${styles.salaryBase_page}`}
    >
      <>
        <div ref={topRef} className='bg-white pt-[0.5rem] px-[0.5rem] mb-[0.5rem]'>
          <Form
            form={form}
            labelCol={{ span: 6 }}
            onFinish={onFormFinish}
            initialValues={{
              loginDate: [],
              yearVal: '',
              cityId: '',
              category: 'XIN_CHOU_JI_SHU'
            }}
          >
            <Row gutter={24}>
              <Col span={7}>
                <Form.Item
                  label='导入时间'
                  name='loginDate'
                  wrapperCol={{ span: 24 }}
                  className='mb-[0rem]'
                >
                  <RangePicker
                    style={{ width: '100%' }}
                    allowClear={true}
                    ranges={{
                      近一天: [dayjs().subtract(1, 'day'), dayjs()],
                      近三天: [dayjs().subtract(3, 'day'), dayjs()],
                      近七天: [dayjs().subtract(7, 'day'), dayjs()]
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={4}>
                <Form.Item name='yearVal' label='年份' className='mb-[0.5rem]'>
                  <DatePicker className='w-full' picker='year' allowClear />
                </Form.Item>
              </Col>
              <Col span={5}>
                <Form.Item name='cityId' label='单位' className='mb-[0.5rem]'>
                  <Cascader
                    allowClear={true}
                    changeOnSelect
                    expandTrigger='hover'
                    displayRender={labels => labels[labels.length - 1]}
                    options={unitList}
                    fieldNames={{
                      // value: 'orgId',
                      value: 'enumId',
                      label: 'enumName',
                      children: 'children'
                    }}
                    // showCheckedStrategy={Cascader.SHOW_CHILD}
                    // onChange={(value, selectedOptions) => {
                    //   console.log(value, selectedOptions)
                    //   setCascaderSelected(selectedOptions)
                    // }}
                    placeholder='请选择'
                    showSearch={{ filter }}
                    // onSearch={value => console.log(value)}
                  />
                </Form.Item>
              </Col>
              <Col span={4}>
                <Form.Item name='category' label='类型' className='mb-[0.5rem]'>
                  <Select placeholder='请选择类型' allowClear>
                    {
                      // prettier-ignore
                      typeList.map(unit => {
                        const {enumId, enumName} = unit;
                        return <Select.Option key={enumId}
                                              value={enumId}>{enumName}</Select.Option>
                      })
                    }
                    {/* <Select.Option key={'XIN_CHOU_JI_SHU'} value={'XIN_CHOU_JI_SHU'}>
                      薪酬基数
                    </Select.Option>
                    <Select.Option key={'YU_SUAN_ZHI'} value={'YU_SUAN_ZHI'}>
                      从业人员预算值
                    </Select.Option>
                    <Select.Option key={'JIA_BAN_FEI'} value={'JIA_BAN_FEI'}>
                      加班费
                    </Select.Option> */}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={4}>
                <div className='text-right'>
                  <Space>
                    <Button type='primary' htmlType='submit'>
                      查询
                    </Button>
                    <Button
                      danger
                      ghost
                      icon={<DownloadOutlined />}
                      onClick={() => handleDownload(2)}
                    >
                      导出
                    </Button>
                    <Button onClick={() => onReset()}>重置</Button>
                  </Space>
                </div>
              </Col>
            </Row>
          </Form>
        </div>
        <div
          ref={contentRef}
          className='relative bg-white px-5 pt-2.5'
          style={{ height: `calc(100% - ${topRef.current?.offsetHeight + 15}px)` }}
        >
          <div
            ref={tableTopRef}
            className={`${'flex justify-between items-center overflow-hidden mb-2'} ${styles.animation_box} ${showTitle ? 'h-[1.8rem]' : 'h-0'}`}
          >
            <div className={'flex '}>
              {showTitle ? (
                <FullscreenExitOutlined
                  className={`${styles.shousuo_icon} text-[1rem]`}
                  onClick={() => {
                    setShowTitle(false)
                    setTimeout(() => {
                      initHeight()
                    }, 200)
                  }}
                />
              ) : (
                <FullscreenOutlined
                  className={`${styles.shousuo_icon} ${'absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10'}`}
                  onClick={() => {
                    setShowTitle(true)
                    setTimeout(() => {
                      initHeight()
                    }, 200)
                  }}
                />
              )}
              <div className='font-bold text-[0.8rem] ml-3'>数据列表</div>
            </div>
            <div className='flex gap-x-[2.5rem]'>
              <div className='flex gap-x-[0.75rem]'>
                <div className='flex items-center gap-x-[0.25rem]'>
                  <SvgIcon name='excel' width={20} height={20} />
                  <span className='text-[#E60027] cursor-pointer' onClick={() => handleDownload(1)}>
                    下载导入模版
                  </span>
                </div>
                <Select
                  placeholder='请选择类型'
                  defaultValue={category}
                  allowClear
                  style={{ width: 150 }}
                  onChange={e => {
                    setCategory(e)
                  }}
                >
                  {
                    // prettier-ignore
                    typeList.map(unit => {
                      const {enumId, enumName} = unit;
                      return <Select.Option key={enumId}
                                            value={enumId}>{enumName}</Select.Option>
                    })
                  }
                </Select>
                <Button danger ghost icon={<UploadOutlined />} onClick={() => setOpModalOpen(true)}>
                  导入
                </Button>
                <Button danger type='primary' onClick={() => setAddModalOpen(true)}>
                  新增
                </Button>
              </div>
              {/* <div className='flex gap-x-[0.25rem]'>

                  <div className='flex items-center gap-x-[0.25rem]'>
                    <SvgIcon name='excel' width={20} height={20} />
                    <span
                      className='text-[#E60027] cursor-pointer'
                      onClick={() => handleDownload(3)}
                    >
                      下载对照表
                    </span>
                  </div>
                </div> */}
            </div>
          </div>
          <ResizableTable
            className='salaryBase_table'
            rowClassName={(_, i) => (i % 2 === 1 ? `customRow odd` : `customRow even`)}
            columns={columns}
            dataSource={tableData}
            scroll={{
              // x: 'max-content',
              y: `calc(${height}px - 0.625rem - 1.6rem - 0.5rem)`
            }}
            loading={tableLoading}
            onChange={onChangePage}
            pagination={{
              ...pagination,
              showSizeChanger: true,
              showQuickJumper: true,
              pageSizeOptions: ['10', '20', '50']
            }}
          />
        </div>
      </>
      <Modal
        title='新增数据'
        destroyOnClose={true}
        open={addModalOpen}
        centered
        className={styles.detail_modal}
        // okText='提交'
        // onOk={handleOk}
        footer={null}
        onCancel={() => setAddModalOpen(false)}
        width={'50%'}
      >
        <AddData columns={addColumns} addData={addData} unitList={unitList} />
      </Modal>
      <Modal
        title='文件上传'
        destroyOnClose={true}
        open={upModalOpen}
        centered
        className={styles.detail_modal}
        // okText={ }
        footer={null}
        // onOk={handleOk}
        onCancel={handleCancel}
      >
        <div className='mt-4 mb-8' style={{ marginBottom: '0px' }}>
          <Row>
            <Col span={22} offset={1} className='h-[10rem]'>
              <Dragger
                {...{
                  // name: 'file',
                  // data: {
                  //     tag: "1"
                  // },
                  // action: `/zhyy/employee/uploadEmployeeExcel`,
                  action: '',
                  maxCount: 1,
                  multiple: false,
                  fileList,
                  beforeUpload(file, fileList) {
                    console.log(file, fileList)
                    return false
                    // let isError = true;
                    // form.validateFields((err, fieldsValue) => {
                    //   isError = !err;
                    // })
                    // return isError;
                  },
                  onChange(info) {
                    const { status } = info.file
                    if (status !== 'uploading') {
                      console.log(info.file, info.fileList)
                      setFileList(info.fileList)
                    }
                    // if (status === 'done') {
                    //     message.success(`${info.file.name} file uploaded successfully.`);
                    // } else if (status === 'error') {
                    //     message.error(`${info.file.name} file upload failed.`);
                    // }
                  }
                }}
              >
                <p className='ant-upload-drag-icon'>
                  <InboxOutlined style={{ color: '#F14846' }} />
                </p>
                <p className='ant-upload-text'>点击或将文件拖拽到这里上传</p>
                <p className='ant-upload-hint'>支持excel格式的文件。</p>
              </Dragger>
            </Col>
          </Row>

          <div style={{ display: 'flex', justifyContent: 'center', marginTop: '2rem' }}>
            <Button danger onClick={handleCancel}>
              取消
            </Button>

            <Popconfirm
              title=''
              description='导入文件如有重复数据，将会被覆盖，请确认是否上传。'
              onConfirm={handleOk}
              onCancel={cancel}
              okText='确认'
              cancelText='取消'
            >
              <Button
                danger
                type='primary'
                style={{ marginLeft: '1rem' }}
                disabled={fileList.length < 1}
              >
                上传
              </Button>
            </Popconfirm>
          </div>
        </div>
      </Modal>
    </div>
  )
}
export default People
