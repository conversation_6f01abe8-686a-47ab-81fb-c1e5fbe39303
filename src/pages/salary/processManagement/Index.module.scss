.processManagement_page {
  :global {
    // .ant-table-row {
    //   height: 22px !important;
    //   line-height: 22px !important;
    //   > td {
    //     padding: 0 !important;
    //   }
    // }
    // .ant-table-thead > tr > th {
    //   text-align: center !important;
    // }
    // .ant-table-thead > tr > th {
    //   padding: 2px 0 !important;
    // }
  }

  .animation_box {
    transition: height 0.2s ease;
  }


}

.modal{
  :global(.ant-timeline-item-head) {
    width: 12px !important;
    height: 12px !important;
}

:global(.ant-modal-footer){
  text-align: center !important;
}
}

.detail_modal {
  :global {
    .ant-modal-footer {
      text-align: center;
    }
  }
}

.add_table_modal {
  :global {
    .ant-modal-title {
      text-align: center;
      line-height: 35px !important;
      font-size: 15px !important;
      color: #E60027 !important;
    }
  }
}

.footer_center {
  :global {
    .ant-modal-footer {
      text-align: center;
    }
  }
}

.over_ellipsis {
  white-space: nowrap;         /* 不允许换行 */
  overflow: hidden;            /* 隐藏超出部分 */
  text-overflow: ellipsis;     /* 使用省略号显示超出文本 */
}
