import { useEffect, useRef, useState } from 'react'
import {
  Button,
  Form,
  Input,
  Table,
  Cascader,
  GetProp,
  CascaderProps,
  Select,
  DatePicker,
  message
} from 'antd'
import { PlusOutlined } from '@ant-design/icons'
import styles from './Index.module.scss'
import { useDebounce } from '@/utils/debounce.ts'
import { useRequest } from '@/hooks'
import cadreManageService from '@/pages/cadreManagement/service.ts'
import dayjs from 'dayjs'

type DefaultOptionType = GetProp<CascaderProps, 'options'>[number]
const AddModal = props => {
  const [formRef] = Form.useForm()
  const tableDataRef = useRef([])
  const [tableData, setTableData] = useState([])
  const [tableColumns, setTableColumns] = useState(props.columns)

  const { runAsync: getEmployeeByEmpId } = useRequest(cadreManageService.getEmployeeByEmpId, {
    manual: true
  })

  // const { runAsync: getEnumType } = useRequest(cadreManageService.getEnumType, { manual: true })

  const debouncedSearchName = useDebounce(
    async (params: { record: any; value: string; column: any; formName: string }) => {
      const { record, value } = params
      console.log(record, value, 'record-xxx')
      const [error, res] = await getEmployeeByEmpId({
        empId: value
      })
      if (error) {
        return
      }
      if (res.STATUS === '0000') {
        const { DATA: userInfo } = res
        console.log(userInfo, 'userInfo')
        const employeeName = userInfo?.empName
        const gender = userInfo?.gender
        const ethnicity = userInfo?.nation
        const birthDate = userInfo?.birthDate
        const ageYear = userInfo?.ageYear
        const ageMonth = userInfo?.ageMonth
        const department = userInfo?.department
        tableDataRef.current = tableDataRef.current?.map(item => {
          const data = { ...item }
          if (item?.key === record?.key) {
            data['employeeName'] = employeeName
            data['gender'] = gender
            data['ethnicity'] = ethnicity
            data['birthDate'] = birthDate
            data['fulltimeEducationYear'] = ageYear
            data['fulltimeEducationMonth'] = ageMonth
            data['department'] = department
          }
          return data
        })
        setTableData([...tableDataRef.current])
        formRef.setFieldsValue({
          [`employeeName${record?.key}`]: employeeName,
          [`gender${record?.key}`]: gender,
          [`birthDate${record?.key}`]: birthDate,
          [`ethnicity${record?.key}`]: ethnicity,
          [`fulltimeEducationYear${record?.key}`]: ageYear,
          [`fulltimeEducationMonth${record?.key}`]: ageMonth,
          [`department${record?.key}`]: department
        })
      } else {
        message.error(res?.MESSAGE)
      }
    },
    500
  )

  useEffect(() => {
    getColumns()
  }, [])

  const getColumns = () => {
    setTableColumns([
      ...initColumns(props.columns),
      {
        title: '操作',
        dataIndex: 'action',
        width: 80,
        align: 'center',
        fixed: 'right',
        render: (_text, record) => (
          <div className='action'>
            <Button type='link' onClick={() => deleteRecord(record)}>
              删除
            </Button>
          </div>
        )
      }
    ])
  }

  const initColumns = columns => {
    console.log(columns, 'columns')
    return columns
      ?.filter(column => column?.dataIndex !== 'action' && column?.dataIndex !== 'exitSituation')
      ?.map(item => {
        if (item?.children?.length > 0) {
          return {
            ...item,
            width: item?.width && item.width > 100 ? item?.width : 100,
            children: initColumns(item?.children)
          }
        } else {
          return {
            ...item,
            width: item?.width && item.width > 120 ? item?.width : 120,
            render: (text, record, index) => (
              <Form.Item
                key={text + index}
                name={item.dataIndex + record?.key}
                rules={[
                  {
                    required: item?.dataIndex === 'employeeId' && record?.employeeId,
                    message: `请输入${item.title}`
                  }
                ]}
              >
                {item?.actionType === 'cascader' ? (
                  <Cascader
                    allowClear={true}
                    changeOnSelect
                    expandTrigger='hover'
                    displayRender={labels => labels[labels.length - 1]}
                    options={props?.cascaderOption || []}
                    fieldNames={{
                      value: 'orgId',
                      label: 'orgName',
                      children: 'children'
                    }}
                    placeholder={
                      ['rewardsOrgName'].includes(item?.dataIndex) &&
                      record?.rewardsType === 'staff'
                        ? ''
                        : `请选择${item?.title}`
                    }
                    showSearch={{ filter }}
                    onSearch={value => console.log(value)}
                    value={text?.map(item => item?.orgId)}
                    onChange={(_value, option) => handleEditRecord(record, option, item)}
                  />
                ) : item?.actionType === 'select' ? (
                  <>
                    <Select
                      placeholder={`请选择${item?.title}`}
                      allowClear
                      value={text}
                      onChange={value => handleEditRecord(record, value, item)}
                    >
                      {(props[item?.actionOptionName] || []).map(item => (
                        <Select.Option key={item?.enumId} value={item?.enumName}>
                          {item?.enumName}
                        </Select.Option>
                      ))}
                    </Select>
                  </>
                ) : item?.actionType === 'datePicker' ? (
                  <DatePicker
                    picker='date'
                    value={text}
                    allowClear
                    format='YYYY-MM-DD'
                    placeholder={`请选择${item.title}`}
                    onChange={value => handleEditRecord(record, value, item)}
                    style={{ width: '100%' }}
                  />
                ) : item?.dataIndex === 'employeeId' ? (
                  <Input
                    placeholder={`请输入${item.title}`}
                    value={text}
                    allowClear
                    onChange={e =>
                      handleEditRecord(record, e.target.value, item, `employeeName${record?.key}`)
                    }
                  />
                ) : (
                  <Input
                    placeholder={
                      [
                        'employeeName',
                        'birthDate',
                        'fulltimeEducationYear',
                        'fulltimeEducationMonth',
                        'ethnicity',
                        'department',
                        'gender',
                        'personnelChange'
                      ].includes(item?.dataIndex)
                        ? ''
                        : `请输入${item.title}`
                    }
                    value={item?.dataIndex === 'personnelChange' ? '新增' : text}
                    allowClear
                    disabled={[
                      'employeeName',
                      'birthDate',
                      'fulltimeEducationYear',
                      'fulltimeEducationMonth',
                      'ethnicity',
                      'department',
                      'gender',
                      'personnelChange'
                    ].includes(item?.dataIndex)}
                    onChange={e => handleEditRecord(record, e.target.value, item)}
                  />
                )}
              </Form.Item>
            )
          }
        }
      })
  }

  const handleEditRecord = (record, value, column, formName?) => {
    tableDataRef.current = tableDataRef.current?.map(item => {
      const data = { ...item }
      if (item?.key === record?.key) {
        data[column?.dataIndex] = value
      }
      return data
    })
    if (column?.dataIndex === 'employeeId') {
      debouncedSearchName({ record, value, column, formName })
    }
  }

  // 删除一条新增数据
  const deleteRecord = record => {
    tableDataRef.current = tableDataRef.current?.filter(item => item?.key !== record?.key)
    setTableData(tableDataRef.current)
  }

  // 日期字段
  const dateFields = [
    'managerLevel3StartDate',
    'currentLevelDate',
    'currentPositionDate',
    'unitManagerStartDate',
    'unitManagerPositionDate'
  ]

  const onFinish = () => {
    // 检查是否所有记录都完整填写
    const hasIncompleteRecord = tableDataRef.current.some(record => {
      const hasEmployeeId = !!record.employeeId
      const hasEmployeeName = !!record.employeeName
      return !(hasEmployeeId && hasEmployeeName)
    })

    if (hasIncompleteRecord) {
      message.error('请完整填写所有员工信息（员工编号和姓名）')
      return
    }

    // 检查是否至少有一条记录
    if (tableDataRef.current.length === 0) {
      message.error('请至少添加一条员工信息')
      return
    }

    // 获取所有可能的字段，过滤掉只有父级没有子级的字段
    const allFields = tableColumns
      .filter(col => {
        if (col.children && col.children.length > 0) {
          return true
        }
        const hasParent = tableColumns.some(
          parent =>
            parent.children && parent.children.some(child => child.dataIndex === col.dataIndex)
        )
        return !hasParent
      })
      .reduce((acc, col) => {
        if (col.children && col.children.length > 0) {
          return [...acc, ...col.children.map(child => child.dataIndex)]
        }
        return [...acc, col.dataIndex]
      }, [])
      .filter(dataIndex => dataIndex && dataIndex !== 'action')

    // 处理每条记录，确保包含所有字段
    const processedData = tableDataRef.current.map(record => {
      const newRecord = { ...record }
      allFields.forEach(field => {
        if (!(field in newRecord)) {
          newRecord[field] = ''
        }
      })
      // 处理日期字段，格式化为 YYYY-MM-DD
      dateFields.forEach(field => {
        if (newRecord[field]) {
          const date = dayjs(newRecord[field])
          if (date.isValid()) {
            newRecord[field] = date.format('YYYY-MM-DD')
          }
        }
      })

      return newRecord
    })
    console.log('processedData', processedData)
    props?.submitData(processedData)
  }

  const addRecord = () => {
    console.log(tableDataRef.current, 'tableDataRef.current1')
    const newKey = new Date().getTime()
    const newRecord = {
      key: newKey,
      personnelChange: '新增'
    }
    tableDataRef.current = [...tableDataRef.current, newRecord]
    console.log(tableDataRef.current, 'tableDataRef.current2')
    setTableData(tableDataRef.current)
    formRef.setFieldsValue({
      [`personnelChange${newKey}`]: '新增'
    })
  }

  // 级联选择器过滤
  const filter = (inputValue: string, path: DefaultOptionType[]) =>
    path.some(
      option =>
        (option.orgName as string).toLowerCase().indexOf(inputValue.trim().toLowerCase()) > -1
    )

  return (
    <div className={styles.add}>
      {/*{initHeader()}*/}
      <Form
        name='dynamic_form_nest_item'
        form={formRef}
        onFinish={onFinish}
        style={{
          width: '100%',
          marginTop: 16,
          maxHeight: '500px'
        }}
        autoComplete='off'
      >
        {/*<Form.List name='users'>*/}
        <Table
          style={{ marginBottom: 20 }}
          className='edit-table'
          columns={tableColumns}
          dataSource={tableData}
          bordered
          scroll={{
            // x: 'max-content',
            y: `20rem`
          }}
          pagination={false}
        />

        <Form.Item style={{ width: '100%', display: 'flex', justifyContent: 'right' }}>
          <Button
            // type='dashed'
            onClick={() => addRecord()}
            danger
            block
            icon={<PlusOutlined />}
            style={{ width: 150 }}
          >
            新增一条数据
          </Button>
        </Form.Item>
        {/*</Form.List>*/}

        <Form.Item
          style={{
            textAlign: 'center',
            width: '100%',
            background: '#fff'
          }}
        >
          <Button type='primary' htmlType='submit' style={{}}>
            提交
          </Button>
        </Form.Item>
      </Form>
    </div>
  )
}
export default AddModal
