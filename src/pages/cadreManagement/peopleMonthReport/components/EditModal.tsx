import { useEffect, useState } from 'react'
import { Modal, Form, Input, Select, DatePicker, message, Row, Col, Typography, Button } from 'antd'
import { useRequest } from '@/hooks'
import cadreManageService from '@/pages/cadreManagement/service.ts'
import dayjs from 'dayjs'

const { Option } = Select
const { Title } = Typography

// 日期字段
const dateFields = [
  'managerLevel3StartDate',
  'currentLevelDate',
  'currentPositionDate',
  'unitManagerStartDate',
  'unitManagerPositionDate',
  'exitDate'
]

const EditModal = ({
  open,
  onCancel,
  onOk,
  initialValues,
  unitList,
  personnelChangeList,
  managerLevelList,
  unitManagerList,
  talentList,
  exitReasonList
}) => {
  const [form] = Form.useForm()
  const [showExitFields, setShowExitFields] = useState(false)
  const [exitReason, setExitReason] = useState('')
  const [reason, setReason] = useState('')
  const { runAsync: getEmployeeByEmpId } = useRequest(cadreManageService.getEmployeeByEmpId, {
    manual: true
  })

  //const { runAsync: getEnumType } = useRequest(cadreManageService.getEnumType, { manual: true })

  useEffect(() => {
    if (open) {
      const formattedValues = { ...initialValues }
      console.log(formattedValues, 'formattedValues')
      dateFields.forEach(field => {
        if (formattedValues[field]) {
          formattedValues[field] = dayjs(formattedValues[field])
        }
      })

      const selectFields = ['isManagerLevel3', 'isUnitManager', 'talentLevel']
      selectFields.forEach(field => {
        if (formattedValues[field] === '') {
          formattedValues[field] = undefined
        }
      })

      // 处理退出原因相关字段
      if (formattedValues.backList === '其他') {
        setExitReason('其他')
        setReason(formattedValues.reason || '')
        formattedValues.exitReason = '其他'
        // formattedValues.reason = formattedValues.reason
      } else {
        setExitReason(formattedValues.backList || '')
        setReason('')
        formattedValues.exitReason = formattedValues.backList
      }

      form.setFieldsValue(formattedValues)
      // 根据formattedValues中的personnelChange来判断是否显示退出相关字段
      setShowExitFields(formattedValues.personnelChange === '退出')
    }
  }, [open, initialValues])

  // 员工编号输入后自动带出
  const handleEmpIdChange = async e => {
    const empId = e.target.value
    if (!empId) return
    const [error, res] = await getEmployeeByEmpId({ empId })
    if (!error && res.STATUS === '0000') {
      const userInfo = res.DATA
      form.setFieldsValue({
        employeeName: userInfo?.empName,
        department: userInfo?.department,
        gender: userInfo?.gender,
        ethnicity: userInfo?.nation,
        birthDate: userInfo?.birthDate
      })
    }
  }

  // 人员变化联动
  const handlePersonnelChange = value => {
    setShowExitFields(value === '退出')
    if (value !== '退出') {
      form.setFieldsValue({
        exitReason: undefined,
        exitDate: undefined,
        postExitPosition: undefined,
        customExitReason: undefined
      })
      setExitReason('')
      setReason('')
    } else {
      const { exitDate, postExitPosition, reason, backList } = initialValues
      console.log(exitDate, postExitPosition, reason, backList)
      if (backList === '其他') {
        setExitReason('其他')
        setReason(reason || '')
        form.setFieldsValue({
          exitReason: '其他',
          customExitReason: reason,
          exitDate: !exitDate ? '' : dayjs(exitDate),
          postExitPosition
        })
      } else {
        setExitReason(backList || '')
        setReason('')
        form.setFieldsValue({
          exitReason: backList,
          exitDate: !exitDate ? '' : dayjs(exitDate),
          postExitPosition
        })
      }
    }
  }

  // 退出原因联动
  const handleExitReasonChange = value => {
    setExitReason(value)
    if (value !== '其他') {
      setReason('')
      form.setFieldsValue({ reason: undefined })
    }
  }

  // 提交
  const handleOk = () => {
    form.validateFields().then(values => {
      dateFields.forEach(field => {
        if (values[field]) {
          values[field] = dayjs(values[field]).format('YYYY-MM-DD')
        }
      })
      if (showExitFields && exitReason === '其他' && !reason) {
        message.error('请填写自定义退出原因')
        return
      }
      onOk({
        ...values,
        // exitReason: exitReason === '其他' ? reason : exitReason,
        reason: exitReason === '其他' ? reason : '',
        backList: exitReason
      })
    })
  }

  return (
    <Modal
      open={open}
      title={<div style={{ textAlign: 'center', color: '#E60027' }}>-修改数据-</div>}
      centered
      onCancel={onCancel}
      onOk={handleOk}
      width={'70%'}
      destroyOnClose
      maskClosable={false}
      footer={null}
    >
      <Form form={form} layout='horizontal' initialValues={initialValues} colon={false}>
        {/* 基本信息 */}
        <Row gutter={24}>
          <Col span={8}>
            <Form.Item
              label='单位：'
              name='unit'
              rules={[{ required: true, message: '请选择单位' }]}
              labelCol={{ flex: '120px' }}
              wrapperCol={{ flex: 1 }}
              labelAlign='right'
            >
              <Select placeholder='请选择单位' allowClear>
                {unitList?.map(u => (
                  <Option key={u.enumId} value={u.enumName}>
                    {u.enumName}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label='人员变化：'
              name='personnelChange'
              rules={[{ required: true, message: '请选择人员变化' }]}
              labelCol={{ flex: '120px' }}
              wrapperCol={{ flex: 1 }}
              labelAlign='right'
            >
              <Select placeholder='请选择' onChange={handlePersonnelChange} allowClear>
                {personnelChangeList.map(opt => (
                  <Option key={opt.enumId} value={opt.enumName}>
                    {opt.enumName}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label='基层责任单元：'
              name='basicResponsibilityUnit'
              labelCol={{ flex: '120px' }}
              wrapperCol={{ flex: 1 }}
              labelAlign='right'
            >
              <Input placeholder='请填写基层责任单元' />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={24}>
          <Col span={8}>
            <Form.Item
              label='员工编号：'
              name='employeeId'
              rules={[{ required: true, message: '请输入员工编号' }]}
              labelCol={{ flex: '120px' }}
              wrapperCol={{ flex: 1 }}
              labelAlign='right'
            >
              <Input placeholder='请输入员工编号' onBlur={handleEmpIdChange} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label='员工姓名：'
              name='employeeName'
              labelCol={{ flex: '120px' }}
              wrapperCol={{ flex: 1 }}
              labelAlign='right'
            >
              <Input disabled />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label='部门：'
              name='department'
              labelCol={{ flex: '120px' }}
              wrapperCol={{ flex: 1 }}
              labelAlign='right'
            >
              <Input disabled />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={24}>
          <Col span={8}>
            <Form.Item
              label='党内职务：'
              name='partyPosition'
              labelCol={{ flex: '120px' }}
              wrapperCol={{ flex: 1 }}
              labelAlign='right'
            >
              <Input placeholder='请输入党内职务' />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label='岗位名称：'
              name='position'
              labelCol={{ flex: '120px' }}
              wrapperCol={{ flex: 1 }}
              labelAlign='right'
            >
              <Input placeholder='请输入岗位名称' />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label='岗位级别：'
              name='partyPosition'
              labelCol={{ flex: '120px' }}
              wrapperCol={{ flex: 1 }}
              labelAlign='right'
            >
              <Input placeholder='请输入岗位级别' />
            </Form.Item>
          </Col>
        </Row>

        {showExitFields && (
          <div>
            <Title level={5} style={{ fontWeight: 'bold', margin: '0 0 8px 10px' }}>
              退出相关
            </Title>
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  label='退出时间：'
                  name='exitDate'
                  rules={[{ required: true, message: '请选择退出时间' }]}
                  labelCol={{ flex: '120px' }}
                  wrapperCol={{ flex: 1 }}
                  labelAlign='right'
                >
                  <DatePicker
                    style={{ width: '100%' }}
                    format='YYYY-MM-DD'
                    placeholder='请选择退出时间'
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label='退出后岗位：'
                  name='postExitPosition'
                  rules={[{ required: true, message: '请填写退出后岗位' }]}
                  labelCol={{ flex: '120px' }}
                  wrapperCol={{ flex: 1 }}
                  labelAlign='right'
                >
                  <Input placeholder='请填写退出后岗位' />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={24}>
                <Form.Item
                  label='退出原因：'
                  name='exitReason'
                  rules={[{ required: true, message: '请选择退出原因' }]}
                  labelCol={{ flex: '120px' }}
                  wrapperCol={{ flex: 1 }}
                  labelAlign='right'
                >
                  <Select placeholder='请选择退出原因' onChange={handleExitReasonChange} allowClear>
                    {exitReasonList.map(opt => (
                      <Option key={opt.enumId} value={opt.enumName}>
                        {opt.enumName}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
            {exitReason === '其他' && (
              <Row gutter={24}>
                <Col span={24}>
                  <Form.Item
                    label='自定义退出原因：'
                    name='reason'
                    rules={[{ required: true, message: '请填写自定义退出原因' }]}
                    labelCol={{ flex: '120px' }}
                    wrapperCol={{ flex: 1 }}
                    labelAlign='right'
                  >
                    <Input
                      placeholder='请输入自定义退出原因'
                      value={reason}
                      onChange={e => setReason(e.target.value)}
                    />
                  </Form.Item>
                </Col>
              </Row>
            )}
          </div>
        )}

        {/* 人员类别 */}
        <Col span={5}>
          <Title level={5} style={{ fontWeight: 'bold', margin: '0 0 8px 10px' }}>
            人员类别
          </Title>
        </Col>
        <Row gutter={24}>
          <Col span={8}>
            <Form.Item
              label='三级经理：'
              name='isManagerLevel3'
              labelCol={{ flex: '120px' }}
              wrapperCol={{ flex: 1 }}
              labelAlign='right'
            >
              <Select
                allowClear
                placeholder='请选择三级经理'
                value={form.getFieldValue('isManagerLevel3') || undefined}
              >
                {managerLevelList.map(opt => (
                  <Option key={opt.enumId} value={opt.enumName}>
                    {opt.enumName}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label='基层责任单元经理：'
              name='isUnitManager'
              labelCol={{ flex: '120px' }}
              wrapperCol={{ flex: 1 }}
              labelAlign='right'
            >
              <Select
                allowClear
                placeholder='请选择基层责任单元经理'
                value={form.getFieldValue('isUnitManager') || undefined}
              >
                {unitManagerList.map(opt => (
                  <Option key={opt.enumId} value={opt.enumName}>
                    {opt.enumName}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label='人才：'
              name='talentLevel'
              labelCol={{ flex: '120px' }}
              wrapperCol={{ flex: 1 }}
              labelAlign='right'
            >
              <Select
                allowClear
                placeholder='请选择人才'
                value={form.getFieldValue('talentLevel') || undefined}
              >
                {talentList.map(opt => (
                  <Option key={opt.enumId} value={opt.enumName}>
                    {opt.enumName}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        {/* 三级经理任职信息 */}
        <Col span={5}>
          <Title level={5} style={{ fontWeight: 'bold', margin: '0 0 8px 10px' }}>
            三级经理任职信息
          </Title>
        </Col>
        <Row gutter={24}>
          <Col span={8}>
            <Form.Item
              label='任期起始时间：'
              name='managerLevel3StartDate'
              labelCol={{ flex: '120px' }}
              wrapperCol={{ flex: 1 }}
              labelAlign='right'
            >
              <DatePicker
                style={{ width: '100%' }}
                format='YYYY-MM-DD'
                placeholder='请选择任期起始时间'
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label='任现级别时间：'
              name='currentLevelDate'
              labelCol={{ flex: '120px' }}
              wrapperCol={{ flex: 1 }}
              labelAlign='right'
            >
              <DatePicker
                style={{ width: '100%' }}
                format='YYYY-MM-DD'
                placeholder='请选择任现级别时间'
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label='任现岗时间：'
              name='currentPositionDate'
              labelCol={{ flex: '120px' }}
              wrapperCol={{ flex: 1 }}
              labelAlign='right'
            >
              <DatePicker
                style={{ width: '100%' }}
                format='YYYY-MM-DD'
                placeholder='请选择任现岗时间'
              />
            </Form.Item>
          </Col>
        </Row>

        {/* 基层责任单元经理任职信息 */}
        <Col span={5}>
          <Title level={5} style={{ fontWeight: 'bold', margin: '0 0 8px 10px' }}>
            基层责任单元经理任职信息
          </Title>
        </Col>
        <Row gutter={24}>
          <Col span={8}>
            <Form.Item
              label='任期起始时间：'
              name='unitManagerStartDate'
              labelCol={{ flex: '120px' }}
              wrapperCol={{ flex: 1 }}
              labelAlign='right'
            >
              <DatePicker
                style={{ width: '100%' }}
                format='YYYY-MM-DD'
                placeholder='请选择任期起始时间'
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label='任现岗时间：'
              name='unitManagerPositionDate'
              labelCol={{ flex: '120px' }}
              wrapperCol={{ flex: 1 }}
              labelAlign='right'
            >
              <DatePicker
                style={{ width: '100%' }}
                format='YYYY-MM-DD'
                placeholder='请选择任现岗时间'
              />
            </Form.Item>
          </Col>
        </Row>

        {/* 底部按钮居中 */}
        <Row justify='center' style={{ marginTop: 32 }}>
          <Col>
            <Form.Item>
              <div style={{ display: 'flex', gap: 24, justifyContent: 'center' }}>
                <Button type='primary' onClick={handleOk} style={{ width: 100 }}>
                  确定
                </Button>
                <Button onClick={onCancel} style={{ width: 100 }}>
                  取消
                </Button>
              </div>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Modal>
  )
}

export default EditModal
