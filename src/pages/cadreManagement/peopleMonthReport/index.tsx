import styles from '../Index.module.scss'
import {
  Button,
  Col,
  DatePicker,
  Form,
  FormProps,
  Row,
  Space,
  message,
  Select,
  Input,
  TableProps,
  Popconfirm,
  Modal
} from 'antd'
import {
  DownloadOutlined,
  FullscreenExitOutlined,
  FullscreenOutlined,
  PlusOutlined,
  DeleteOutlined
} from '@ant-design/icons'
import { useEffect, useRef, useState } from 'react'
import dayjs from 'dayjs'
import { useRequest } from '@/hooks'
import { useDebounce } from '@/utils/debounce.ts'
import cadreManageService from '@/pages/cadreManagement/service.ts'
import { downFile, openNotification } from '@/utils/down.tsx'
import ResizableTable from '@/components/resizeTable/index.jsx'
import AddModal from './components/addModal'
import EditModal from './components/EditModal'
//import { useMatches } from 'react-router-dom'
import { useUserStore } from '@/store/user'
type TableRowSelection<T extends object = object> = TableProps<T>['rowSelection']
type FieldType = {
  month?: string
  unit?: string
  cycleId?: null
  empId?: string
  empName: string
}
const PeopleMonthReport: React.FC = () => {
  const topRef = useRef(null)
  const contentRef = useRef(null)
  const tableTopRef = useRef(null)
  const [formRef] = Form.useForm()
  const [height, setHeight] = useState(0)
  const [addModalOpen, setAddModalOpen] = useState(false)
  // const [columnTableHeight, setColumnTableHeight] = useState(0)
  const [unitList, setUnitList] = useState<any[]>([])
  const [personnelChangeList, setPersonnelChangeList] = useState<any[]>([])
  const [managerLevelList, setManagerLevelList] = useState<any[]>([])
  const [unitManagerList, setUnitManagerList] = useState<any[]>([])
  const [talentList, setTalentList] = useState<any[]>([])
  const [exitReasonList, setExitReasonList] = useState<any[]>([])
  const [tableData, setTableData] = useState<any>([])
  const [selectValue, setSelectValue] = useState<any>([])
  const [columnList, setColumnList] = useState([])
  const [tableLoading, setTableLoading] = useState<boolean>(false)
  // const [confirmLoading, setConfirmLoading] = useState<boolean>(false)
  const [isReset, setIsReset] = useState<number>(0)
  const [showTitle, setShowTitle] = useState<boolean>(true)
  const [pagination, setPagination] = useState({
    total: 0,
    pageNum: 1,
    pageSize: 50
  })
  const [editModalOpen, setEditModalOpen] = useState(false)
  const [editRecord, setEditRecord] = useState<any>(null)
  //const matches = useMatches()
  const { currentUser } = useUserStore()

  // 获取枚举类型
  const { runAsync: getEnumType } = useRequest(cadreManageService.getEnumType, { manual: true })

  // 获取员工信息
  const { runAsync: getEmployeeByEmpId } = useRequest(cadreManageService.getEmployeeByEmpId, {
    manual: true
  })

  // 查询
  const { runAsync: getEmployeeMonthlyList } = useRequest(
    cadreManageService.getEmployeeMonthlyList,
    {
      manual: true
    }
  )

  // 导出
  const { runAsync: exportPeopleMonthReport } = useRequest(
    cadreManageService.exportPeopleMonthReport,
    {
      manual: true
    }
  )

  // 删除
  const { runAsync: deleteEmployeeMonthlyList } = useRequest(
    cadreManageService.deleteEmployeeMonthlyList,
    {
      manual: true
    }
  )

  // 新增
  const { runAsync: addEmployeeMonthlyReport } = useRequest(
    cadreManageService.addEmployeeMonthlyReport,
    {
      manual: true
    }
  )

  // 修改
  const { runAsync: updateEmployeeMonthlyReport } = useRequest(
    cadreManageService.updateEmployeeMonthlyReport,
    {
      manual: true
    }
  )

  const [allColumns, setAllColumns] = useState<any>([
    {
      title: '单位',
      key: 'unit',
      dataIndex: 'unit',
      actionType: 'select',
      actionOptionName: 'unitList',
      align: 'center',
      fixed: 'left',
      width: 180,
      children: []
    },
    {
      title: '人员变化',
      key: 'personnelChange',
      dataIndex: 'personnelChange',
      align: 'center',
      width: 130,
      children: []
    },
    {
      title: '人员类别',
      key: 'personnelType',
      dataIndex: 'personnelType',
      align: 'center',
      width: 130,
      children: [
        {
          title: '三级经理',
          key: 'isManagerLevel3',
          dataIndex: 'isManagerLevel3',
          actionType: 'select',
          actionOptionName: 'managerLevelList',
          align: 'center',
          width: 130,
          children: []
        },
        {
          title: '基层责任单元经理',
          key: 'isUnitManager',
          dataIndex: 'isUnitManager',
          actionType: 'select',
          actionOptionName: 'unitManagerList',
          align: 'center',
          width: 130,
          children: []
        },
        {
          title: '人才每月自动获取更新人员名单',
          key: 'talentLevel',
          dataIndex: 'talentLevel',
          actionType: 'select',
          actionOptionName: 'talentList',
          align: 'center',
          width: 130,
          children: []
        }
      ]
    },
    {
      title: '员工编号',
      key: 'employeeId',
      dataIndex: 'employeeId',
      align: 'center',
      width: 130,
      children: []
    },
    {
      title: '姓名',
      key: 'employeeName',
      dataIndex: 'employeeName',
      align: 'center',
      width: 130,
      children: []
    },
    {
      title: '部门',
      key: 'department',
      dataIndex: 'department',
      align: 'center',
      width: 130,
      children: []
    },
    {
      title: '基层责任单元',
      key: 'basicResponsibilityUnit',
      dataIndex: 'basicResponsibilityUnit',
      align: 'center',
      width: 130,
      children: []
    },
    {
      title: '岗位名称',
      key: 'position',
      dataIndex: 'position',
      align: 'center',
      width: 130,
      children: []
    },
    {
      title: '岗级',
      key: 'positionLevel',
      dataIndex: 'positionLevel',
      align: 'center',
      width: 130,
      children: []
    },
    {
      title: '性别',
      key: 'gender',
      dataIndex: 'gender',
      align: 'center',
      width: 130,
      children: []
    },
    {
      title: '民族',
      key: 'ethnicity',
      dataIndex: 'ethnicity',
      align: 'center',
      width: 130,
      children: []
    },
    {
      title: '出生年月',
      key: 'birthDate',
      dataIndex: 'birthDate',
      align: 'center',
      width: 130,
      children: []
    },
    {
      title: '年龄',
      key: 'age',
      dataIndex: 'age',
      align: 'center',
      children: [
        {
          title: '年',
          key: 'fulltimeEducationYear',
          dataIndex: 'fulltimeEducationYear',
          align: 'center',
          width: 100,
          children: []
        },
        {
          title: '月',
          key: 'fulltimeEducationMonth',
          dataIndex: 'fulltimeEducationMonth',
          align: 'center',
          width: 100,
          children: []
        }
      ]
    },
    {
      title: '全日制教育',
      key: 'fulltimeEducation',
      dataIndex: 'fulltimeEducation',
      align: 'center',
      width: 130,
      children: [
        {
          title: '学历学位',
          key: 'fulltimeEducationDegree',
          dataIndex: 'fulltimeEducationDegree',
          align: 'center',
          width: 130,
          children: []
        },
        {
          title: '毕业院校系及专业',
          key: 'fulltimeEducationSchool',
          dataIndex: 'fulltimeEducationSchool',
          align: 'center',
          width: 130,
          children: []
        }
      ]
    },
    {
      title: '最高学历',
      key: 'highestEducation',
      dataIndex: 'highestEducation',
      align: 'center',
      width: 130,
      children: [
        {
          title: '学历学位',
          key: 'highestEducationDegree',
          dataIndex: 'highestEducationDegree',
          align: 'center',
          width: 130,
          children: []
        },
        {
          title: '毕业院校系及专业',
          key: 'highestEducationSchool',
          dataIndex: 'highestEducationSchool',
          align: 'center',
          width: 130,
          children: []
        }
      ]
    },
    {
      title: '入党时间',
      key: 'partyJoinDate',
      dataIndex: 'partyJoinDate',
      align: 'center',
      width: 130,
      children: []
    },
    {
      title: '参加工作时间',
      key: 'workStartDate',
      dataIndex: 'workStartDate',
      align: 'center',
      width: 130,
      children: []
    },
    {
      title: '党内职务',
      key: 'partyPosition',
      dataIndex: 'partyPosition',
      align: 'center',
      width: 130,
      children: []
    },
    {
      title: '职级薪档',
      key: 'salaryGrade',
      dataIndex: 'salaryGrade',
      align: 'center',
      width: 130,
      children: []
    },
    {
      title: '电话',
      key: 'phone',
      dataIndex: 'phone',
      align: 'center',
      width: 150,
      children: []
    },
    {
      title: '综合考评',
      key: 'comprehensiveAssessment',
      dataIndex: 'comprehensiveAssessment',
      align: 'center',
      width: 130,
      children: []
    },
    {
      title: '三级经理任职信息',
      key: 'threeLevelManager',
      dataIndex: 'threeLevelManager',
      align: 'center',
      width: 130,
      children: [
        {
          title: '任三级经理起始时间（含正副职）',
          key: 'managerLevel3StartDate',
          dataIndex: 'managerLevel3StartDate',
          actionType: 'datePicker',
          align: 'center',
          width: 150,
          children: []
        },
        {
          title: '任现级别时间',
          key: 'currentLevelDate',
          dataIndex: 'currentLevelDate',
          actionType: 'datePicker',
          align: 'center',
          width: 150,
          children: []
        },
        {
          title: '任现岗时间',
          key: 'currentPositionDate',
          actionType: 'datePicker',
          dataIndex: 'currentPositionDate',
          align: 'center',
          width: 150,
          children: []
        }
      ]
    },
    {
      title: '基层责任单元经理任职信息',
      key: 'baseResponsibilityUnitManager',
      dataIndex: 'baseResponsibilityUnitManager',
      align: 'center',
      width: 130,
      children: [
        {
          title: '任基层责任单元经理起始时间',
          key: 'unitManagerStartDate',
          dataIndex: 'unitManagerStartDate',
          actionType: 'datePicker',
          align: 'center',
          width: 150,
          children: []
        },
        {
          title: '任现岗时间',
          key: 'unitManagerPositionDate',
          dataIndex: 'unitManagerPositionDate',
          actionType: 'datePicker',
          align: 'center',
          width: 150,
          children: []
        }
      ]
    },
    {
      title: '人才信息',
      key: 'talentInfo',
      dataIndex: 'talentInfo',
      align: 'center',
      width: 130,
      children: [
        {
          title: '专业类别',
          key: 'professionalCategory',
          dataIndex: 'professionalCategory',
          actionType: 'select',
          actionOptionName: 'professionalKindList',
          align: 'center',
          width: 150,
          children: []
        },
        {
          title: '人才等级',
          key: 'currentTalentLevel',
          dataIndex: 'currentTalentLevel',
          actionType: 'select',
          actionOptionName: 'talentLevelList',
          align: 'center',
          width: 130,
          children: []
        },
        {
          title: '现等级人才时间',
          key: 'currentTalentLevelDate',
          dataIndex: 'currentTalentLevelDate',
          align: 'center',
          width: 150,
          children: []
        }
      ]
    },
    {
      title: '退出情况说明',
      key: 'exitSituation',
      dataIndex: 'exitSituation',
      align: 'center',
      width: 130,
      children: [
        {
          title: '退出时间',
          key: 'exitDate',
          dataIndex: 'exitDate',
          actionType: 'datePicker',
          align: 'center',
          width: 150,
          children: []
        },
        {
          title: '退出原因',
          key: 'exitReason',
          dataIndex: 'exitReason',
          actionType: 'select',
          actionOptionName: 'exitReasonList',
          align: 'center',
          width: 150,
          children: []
        },
        {
          title: '退出后岗位',
          key: 'postExitPosition',
          dataIndex: 'postExitPosition',
          align: 'center',
          width: 150,
          children: []
        }
      ]
    },
    {
      title: '操作',
      key: 'action',
      dataIndex: 'action',
      align: 'center',
      width: 200,
      fixed: 'right',
      render: (_, record) => {
        return (
          <Space size='middle'>
            <Button type='link' onClick={() => handleEdit(record)}>
              编辑
            </Button>
            <Popconfirm
              title='删除'
              description='确定删除此条数据吗？'
              placement='topLeft'
              onConfirm={() => handleSingleDelete(record)}
              okText='确认'
              cancelText='取消'
            >
              <Button type='link' danger>
                删除
              </Button>
            </Popconfirm>
          </Space>
        )
      }
    }
  ])

  // 更新综合考评年份列
  const updateAssessmentColumns = () => {
    const selectedYear = Number(dayjs(formRef.getFieldsValue().cycleId).format('YYYY'))
    const newColumns = [...allColumns]

    // 找到综合考评列的索引
    const assessmentIndex = newColumns.findIndex(col => col.key === 'comprehensiveAssessment')

    if (assessmentIndex !== -1) {
      // 更新综合考评的子列
      newColumns[assessmentIndex].children = [
        {
          title: selectedYear - 4,
          key: 'evaluationFirst',
          dataIndex: 'evaluationFirst',
          align: 'center',
          width: 100,
          children: []
        },
        {
          title: selectedYear - 3,
          key: 'evaluationSecond',
          dataIndex: 'evaluationSecond',
          align: 'center',
          width: 100,
          children: []
        },
        {
          title: selectedYear - 2,
          key: 'evaluationThird',
          dataIndex: 'evaluationThird',
          align: 'center',
          width: 100,
          children: []
        },
        {
          title: selectedYear - 1,
          key: 'evaluationFourth',
          dataIndex: 'evaluationFourth',
          align: 'center',
          width: 100,
          children: []
        },
        {
          title: selectedYear,
          key: 'evaluationFifth',
          dataIndex: 'evaluationFifth',
          align: 'center',
          width: 100,
          children: []
        }
      ]

      setAllColumns(newColumns)

      // 如果表格已经在显示此列，更新表格列
      if (tableColumns.some(col => col.key === 'comprehensiveAssessment')) {
        changeSelectColumn(selectValue)
      }
    }
  }

  const handleEdit = (record: any) => {
    setEditRecord(record)
    // setEditRecord({
    //   cycleId: null,
    //   unit: '天津市和平区分公司',
    //   unitCode: null,
    //   personnelChange: '退出',
    //   isManagerLevel3: '三级正',
    //   isUnitManager: '三级经理兼任基层责任单元经理',
    //   talentLevel: 'B级',
    //   employeeId: '0014140',
    //   employeeName: '刘铮',
    //   department: '和平区分公司交付运营中心',
    //   basicResponsibilityUnit: '测试',
    //   position: '测试 1',
    //   positionLevel: '前端',
    //   gender: '女',
    //   ethnicity: '汉族',
    //   birthDate: '1970-11-01',
    //   fulltimeEducationYear: 54,
    //   fulltimeEducationMonth: 6,
    //   fulltimeEducationDegree: null,
    //   fulltimeEducationSchool: null,
    //   highestEducationDegree: null,
    //   highestEducationSchool: null,
    //   partyJoinDate: null,
    //   workStartDate: null,
    //   partyPosition: '部长',
    //   salaryGrade: null,
    //   phone: null,
    //   evaluationFirst: null,
    //   evaluationSecond: null,
    //   evaluationThird: null,
    //   evaluationFourth: null,
    //   evaluationFifth: null,
    //   managerLevel3StartDate: '2025-04-02T16:00:00.000Z',
    //   currentLevelDate: '2025-05-20T16:00:00.000Z',
    //   currentPositionDate: '2025-05-25T16:00:00.000Z',
    //   unitManagerStartDate: '2025-05-05T16:00:00.000Z',
    //   unitManagerPositionDate: '2025-05-13T16:00:00.000Z',
    //   professionalCategory: null,
    //   currentTalentLevel: null,
    //   currentTalentLevelDate: null,
    //   exitDate: '2025-05-13T16:00:00.000Z',
    //   exitReason: '其他（牛逼）',
    //   reason: '牛逼',
    //   backList: '其他',
    //   postExitPosition: '牛马',
    //   customExitReason: null,
    //   key: 0
    // })
    setEditModalOpen(true)
  }

  // 全日制教育（学历学位、毕业院校系及专业）、最高学历（学历学位、毕业院校系及专业）、入党时间、参加工作时间、职级薪档、电话、综合考评（4年+今年）、人才信息（专业类别、人才等级、现等级人才时间））
  const filterColumns = [
    'fulltimeEducation',
    'highestEducation',
    'partyJoinDate',
    'workStartDate',
    'salaryGrade',
    'phone',
    'comprehensiveAssessment',
    'talentInfo'
  ]

  //默认展示项 单位、人员变化、人员类别、员工编号，姓名、部门、基层责任单元、党内职务、三级经理任职信息、基层责任单元经理任职信息、人才信息
  const defaultKeys = [
    'unit',
    'personnelChange',
    'personnelType',
    'employeeId',
    'employeeName',
    'department',
    'basicResponsibilityUnit',
    'partyPosition',
    'threeLevelManager',
    'baseResponsibilityUnitManager',
    'talentInfo',
    'action'
  ]
  const [tableColumns, setTableColumns] = useState<any>(
    allColumns.filter(col => defaultKeys.includes(col.key))
  )

  useEffect(() => {
    console.log('当前菜单编码:', currentUser?.currentMenuCode)
    setTableLoading(false)
    if (topRef.current) {
      setHeight(topRef.current.offsetHeight)
    }
    getEnumTypes()
    // updateAssessmentColumns()
    queryTableData()
    window.addEventListener('resize', handleResize)
    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])

  // useEffect(() => {
  //   // 获取当前匹配的路由，默认为最后一个
  //   const route = matches.at(-1)
  //   // 从匹配的路由中取出自定义参数
  //   const handle = route?.handle as any

  //   console.log('当前菜单编码:', currentUser?.currentMenuCode)
  //   console.log('当前路由路径:', route?.pathname)
  //   console.log('当前路由参数:', handle)
  //   console.log('父级路径:', handle?.parentPaths)
  // }, [matches, currentUser?.currentMenuCode])

  const handleResize = () => {
    initHeight()
  }

  useEffect(() => {
    if (isReset > 0) {
      queryTableData()
    }
  }, [isReset])

  useEffect(() => {
    setTimeout(() => {
      initHeight()
    }, 100)
  }, [tableColumns])

  useEffect(() => {
    if (tableData?.length > 0) {
      initHeight()
    }
  }, [tableData])

  useEffect(() => {
    if (pagination?.total > 0) {
      queryTableData()
    }
  }, [pagination.pageNum, pagination.pageSize])

  const initHeight = () => {
    const headerHeight =
      (document.querySelector('.peopleMonthReport_table .ant-table-header') || {})[
        'offsetHeight'
      ] || 0
    const pageHeight =
      (document.querySelector('.peopleMonthReport_table .ant-table-pagination') || {})[
        'offsetHeight'
      ] || 26
    if (headerHeight && pageHeight) {
      setHeight(
        contentRef.current?.offsetHeight -
          (tableTopRef.current.offsetHeight + headerHeight + pageHeight)
      )
    }
    // setColumnTableHeight(contentRef.current?.offsetHeight - tableTopRef.current.offsetHeight)
  }

  const getEnumTypes = async () => {
    const [
      [unitError, unitData],
      [personnelChangeError, personnelChangeData],
      [managerLevelError, managerLevelData],
      [unitManagerError, unitManagerData],
      [talentError, talentData],
      // [professionalKindError, professionalKindData],
      // [talentLevelError, talentLevelData],
      [exitReasonError, exitReasonData]
    ] = await Promise.all([
      getEnumType({ code: '1010', tag: 1 }),
      getEnumType({ code: 'reportPersonChange' }),
      getEnumType({ code: 'reportManagerLevel' }),
      getEnumType({ code: 'reportUnitManager' }),
      getEnumType({ code: 'reportTalent' }),
      // getEnumType({ code: 'reportProfessionalKind' }),
      // getEnumType({ code: 'reportTalentLevel' }),
      getEnumType({ code: 'reportExitReason' })
    ])
    if (
      unitError ||
      personnelChangeError ||
      managerLevelError ||
      unitManagerError ||
      talentError ||
      // professionalKindError ||
      // talentLevelError ||
      exitReasonError
    ) {
      return
    }
    if (unitData.STATUS === '0000') {
      setUnitList(unitData.DATA)
    }
    if (personnelChangeData.STATUS === '0000') {
      setPersonnelChangeList(personnelChangeData.DATA)
    }
    if (managerLevelData.STATUS === '0000') {
      setManagerLevelList(managerLevelData.DATA)
    }
    if (unitManagerData.STATUS === '0000') {
      setUnitManagerList(unitManagerData.DATA)
    }
    if (talentData.STATUS === '0000') {
      setTalentList(talentData.DATA)
    }
    // if (professionalKindData.STATUS === '0000') {
    //   console.log(professionalKindData.DATA, 'professionalKindData')
    //   setProfessionalKindList(professionalKindData.DATA)
    // }
    // if (talentLevelData.STATUS === '0000') {
    //   console.log(talentLevelData.DATA, 'talentLevelData')
    //   setTalentLevelList(talentLevelData.DATA)
    // }
    if (exitReasonData.STATUS === '0000') {
      setExitReasonList(exitReasonData.DATA)
    }
    setColumnList([
      {
        enumId: 'position',
        level: null,
        sort: 1,
        enumName: '岗位名称',
        region: null
      },
      {
        enumId: 'positionLevel',
        level: null,
        sort: 2,
        enumName: '岗位级别',
        region: null
      },
      {
        enumId: 'gender',
        level: null,
        sort: 3,
        enumName: '性别',
        region: null
      },
      {
        enumId: 'ethnicity',
        level: null,
        sort: 4,
        enumName: '民族',
        region: null
      },
      {
        enumId: 'birthDate',
        level: null,
        sort: 5,
        enumName: '出生日期',
        region: null
      },
      {
        enumId: 'age',
        level: null,
        sort: 6,
        enumName: '年龄',
        region: null
      },
      {
        enumId: 'fulltimeEducation',
        level: null,
        sort: 7,
        enumName: '全日制学历',
        region: null
      },
      {
        enumId: 'highestEducation',
        level: null,
        sort: 8,
        enumName: '最高学历',
        region: null
      },
      {
        enumId: 'partyJoinDate',
        level: null,
        sort: 9,
        enumName: '入党时间',
        region: null
      },
      {
        enumId: 'workStartDate',
        level: null,
        sort: 10,
        enumName: '参加工作时间',
        region: null
      },
      {
        enumId: 'salaryGrade',
        level: null,
        sort: 11,
        enumName: '职级薪档',
        region: null
      },
      {
        enumId: 'phone',
        level: null,
        sort: 12,
        enumName: '电话',
        region: null
      },
      {
        enumId: 'comprehensiveAssessment',
        level: null,
        sort: 13,
        enumName: '综合考评',
        region: null
      },
      {
        enumId: 'exitSituation',
        level: null,
        sort: 14,
        enumName: '退出情况说明',
        region: null
      }
    ])
  }

  const handleSearchName = async e => {
    const [error, res] = await getEmployeeByEmpId({
      empId: e.target.value
    })
    if (error) {
      message.error(res?.DATA || res?.MESSAGE || '调用失败')
      return
    }
    if (res.STATUS === '0000') {
      const { DATA: userInfo } = res
      formRef.setFieldsValue({
        empName: userInfo?.empName,
        gender: userInfo?.gender,
        department: userInfo?.department,
        birthDate: userInfo?.birthDate,
        ethnicity: userInfo?.ethnicity
      })
    } else {
      message.error(res?.MESSAGE || res?.DATA || '调用失败')
    }
  }

  // 查询
  const queryTableData = async (page?: any) => {
    updateAssessmentColumns()
    setTableLoading(true)
    const newPage = page || pagination
    const values = formRef.getFieldsValue()
    const [error, res] = await getEmployeeMonthlyList({
      map: {
        cycleId: values?.cycleId.format('YYYYMM'),
        // cycleId: '202504',
        employeeId: values?.empId ?? '',
        employeeName: values?.empName ?? ''
      },
      pagination: {
        pageNum: newPage.pageNum,
        pageSize: newPage.pageSize
      }
    })
    setTableLoading(false)
    if (error) {
      message.error(res?.DATA || res?.MESSAGE || '调用失败')
      return
    }
    if (res.STATUS === '0000') {
      const {
        DATA: { data }
      } = res
      const dataWithKeys = data.map((item, index) => ({
        ...item,
        key: item.id || index
      }))
      setTableData(dataWithKeys)
      setPagination({
        ...pagination,
        total: res.DATA?.totalCount
      })
    } else {
      message.error(res?.MESSAGE || res?.DATA || '调用失败')
    }
  }

  const debouncedChange = useDebounce(handleSearchName, 500)
  const onFormFinish: FormProps<FieldType>['onFinish'] = () => {
    queryTableData()
  }

  // 导出
  const exportToExcelFun = async () => {
    try {
      openNotification('正在导出', 0, 'loading')
      const { cycleId, empId, empName } = formRef.getFieldsValue()
      const response = await exportPeopleMonthReport({
        map: {
          cycleId: cycleId?.format('YYYYMM'),
          employeeId: empId ?? '',
          employeeName: empName ?? ''
        }
      })
      downFile(response)
    } catch (error) {
      openNotification('导出失败', 1, 'error')
      console.error('Download failed:', error)
    }
  }

  const changeSelectColumn = e => {
    const actionColumn = allColumns.find(item => item.key === 'action')

    // 筛选默认显示的列（不包含操作列）
    const defaultColumns = allColumns.filter(
      item => defaultKeys.includes(item.key) && item.key !== 'action'
    )

    // 筛选用户选择的列（不包含操作列）
    const selectedColumns = allColumns.filter(
      item => e.some(y => y === item.key) && item.key !== 'action'
    )

    // 合并默认列和选择的列
    const columns = [...defaultColumns, ...selectedColumns]

    // 去除重复的列（防止一个列同时在默认列和选择列中出现两次）
    const uniqueColumns = Array.from(new Map(columns.map(item => [item.key, item])).values())

    if (actionColumn) {
      uniqueColumns.push(actionColumn)
    }

    setTableColumns(uniqueColumns)
    setSelectValue(e)
  }

  const onReset = () => {
    const newReset = isReset + 1
    formRef.resetFields()
    setIsReset(newReset)
  }

  const onChangePage = page => {
    const newPage = {
      total: page?.total,
      pageNum: page?.current,
      pageSize: page?.pageSize
    }
    setPagination(newPage)
  }

  const onSelectChange = (selectedRowKeys: React.Key[], selectedRows: any[]) => {
    console.log('selectedRowKeys changed: ', selectedRowKeys)
    console.log(selectedRows, 'selectedRows')
    setSelectedRowKeys(selectedRowKeys)
  }

  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([])
  const rowSelection: TableRowSelection<any> = {
    selectedRowKeys,
    onChange: onSelectChange
  }

  const handleSingleDelete = async (record: any) => {
    if (!record.employeeId) {
      message.error('该记录不包含有效员工编号')
      return
    }

    const [error, res] = await deleteEmployeeMonthlyList({
      map: {
        cycleId: formRef.getFieldsValue().cycleId?.format('YYYYMM'),
        employeeId: [record.employeeId]
      }
    })

    if (error) {
      message.error(res?.DATA || res?.MESSAGE || '调用失败')
      return
    }

    if (res.STATUS === '0000') {
      message.success(res?.MESSAGE || res?.DATA || '删除成功')
      queryTableData()
    } else {
      message.error(res?.MESSAGE || res?.DATA || '删除失败')
    }
  }

  const handleDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的记录')
      return
    }

    const selectedData = tableData.filter(item => selectedRowKeys.includes(item.key))
    const selectedEmployeeIds = selectedData.map(item => item.employeeId).filter(Boolean) // Get employee IDs

    if (selectedEmployeeIds.length === 0) {
      message.error('所选记录不包含有效员工编号')
      return
    }

    const [error, res] = await deleteEmployeeMonthlyList({
      map: {
        cycleId: formRef.getFieldsValue().cycleId?.format('YYYYMM'),
        employeeId: selectedEmployeeIds
      }
    })

    if (error) {
      message.error(res?.DATA || res?.MESSAGE || '调用失败')
      return
    }

    if (res.STATUS === '0000') {
      message.success(res?.MESSAGE || res?.DATA || '删除成功')
      setSelectedRowKeys([])
      queryTableData()
    } else {
      message.error(res?.MESSAGE || res?.DATA || '删除失败')
    }
  }

  const handleAddData = async data => {
    setTableLoading(true)
    console.log(data, 'data')
    const [error, res] = await addEmployeeMonthlyReport(data)
    if (error) {
      message.error(res?.DATA || res?.MESSAGE || '调用失败')
      setTableLoading(false)
      // setAddModalOpen(false)
      return
    }
    if (res.STATUS === '0000') {
      message.success(res?.MESSAGE || res?.DATA || '新增成功')
      setAddModalOpen(false)
      setTableLoading(false)
      queryTableData()
    } else {
      message.error(res?.MESSAGE || res?.DATA || '新增失败')
      setTableLoading(false)
    }
  }

  const handleEditData = async data => {
    const [error, res] = await updateEmployeeMonthlyReport({
      ...data,
      cycleId: formRef.getFieldsValue().cycleId?.format('YYYYMM')
    })
    if (error) {
      message.error(res?.DATA || res?.MESSAGE || '调用失败')
      setTableLoading(false)
      // setEditModalOpen(false)
      return
    }
    if (res.STATUS === '0000') {
      message.success(res?.MESSAGE || res?.DATA || '修改成功')
      setEditModalOpen(false)
      setTableLoading(false)
      queryTableData()
    } else {
      message.error(res?.MESSAGE || res?.DATA || '修改失败')
      setTableLoading(false)
    }
  }

  return (
    <div className={`${'pt-[0.5rem] px-[0rem] h-full flex flex-col'} ${styles.employment_page}`}>
      <div ref={topRef} className={'bg-white pt-2 px-8 mb-2 h-[2.6rem]'}>
        <Form
          form={formRef}
          initialValues={{
            tag: '',
            cycleId: dayjs()
          }}
          onFinish={onFormFinish}
          autoComplete='off'
        >
          <Row gutter={24}>
            <Col span={6}>
              <Form.Item label='月份' name='cycleId' wrapperCol={{ span: 20 }}>
                <DatePicker
                  className='w-full'
                  picker='month'
                  format='YYYYMM'
                  allowClear={false}
                  // onChange={e => setYear(dayjs(e).format('YYYY'))}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name='empId' label='员工编号' className='mb-[0.5rem]'>
                <Input
                  placeholder='请输入员工编号'
                  allowClear
                  style={{ width: '100%' }}
                  onChange={debouncedChange}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name='empName' label='员工姓名' className='mb-[0.5rem]'>
                <Input placeholder='请输入员工姓名' allowClear style={{ width: '100%' }} />
              </Form.Item>
            </Col>

            <Col span={6}>
              <Form.Item labelCol={{ span: 0 }} wrapperCol={{ span: 24 }}>
                <Space size='small'>
                  <Button type='primary' htmlType='submit'>
                    查询
                  </Button>
                  <Button htmlType='button' onClick={() => onReset()}>
                    重置
                  </Button>
                  <Button
                    danger
                    ghost
                    icon={<DownloadOutlined />}
                    onClick={() => exportToExcelFun()}
                  >
                    导出
                  </Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>
      <div
        ref={contentRef}
        className='relative bg-white px-5 pt-2.5'
        style={{ height: `calc(100% - ${topRef.current?.offsetHeight + 15}px)` }}
      >
        <div
          ref={tableTopRef}
          className={`${'flex justify-between items-center overflow-hidden mb-2'} ${styles.animation_box} ${showTitle ? 'h-[1.6rem]' : 'h-0'}`}
        >
          <div className={'flex '}>
            {showTitle ? (
              <FullscreenExitOutlined
                className={`${styles.shousuo_icon} text-[1rem]`}
                onClick={() => {
                  setShowTitle(false)
                  setTimeout(() => {
                    initHeight()
                  }, 200)
                }}
              />
            ) : (
              <FullscreenOutlined
                className={`${styles.shousuo_icon} ${'absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10'}`}
                onClick={() => {
                  setShowTitle(true)
                  setTimeout(() => {
                    initHeight()
                  }, 200)
                }}
              />
            )}
            <div className='font-bold text-[0.8rem] ml-3'>
              数据列表
              <span className='text-[0.7rem]' style={{ color: '#939393' }}>
                （单位：万元）
              </span>
            </div>
          </div>
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <Select
              placeholder={'请选择'}
              className='w-full'
              allowClear={false}
              showSearch
              value={selectValue}
              mode='multiple'
              style={{ marginRight: '0.4rem', width: '15rem' }}
              maxTagCount={1}
              onChange={changeSelectColumn}
              // filterOption={handleSearchFilter}
            >
              {
                // prettier-ignore
                columnList.map(unit => {
                  const { enumId, enumName } = unit;
                  return <Select.Option key={enumId} value={enumId}>{enumName}</Select.Option>
                })
              }
            </Select>
            <Button
              type='primary'
              className='ml-[0.4rem]'
              onClick={() => {
                setAddModalOpen(true)
              }}
            >
              <PlusOutlined />
              新增
            </Button>
            {/* <Button danger ghost className='ml-[0.4rem]' onClick={() => {}}>
              <DeleteOutlined />
              删除
            </Button> */}
            <Popconfirm
              title='删除'
              description='确定删除所选中的数据吗？'
              onConfirm={handleDelete}
              // onCancel={cancel}
              okText='确认'
              cancelText='取消'
            >
              <Button danger ghost className='ml-[0.4rem]' onClick={() => {}}>
                <DeleteOutlined />
                删除
              </Button>
            </Popconfirm>
          </div>
        </div>
        {/* {tableData.length === 1 ? (
          <ColumnData
            tableData={tableData}
            tableHeight={columnTableHeight}
            columns={tableColumns}
            year={year}
          ></ColumnData>
        ) : ( */}
        <ResizableTable
          className='peopleMonthReport_table'
          rowClassName={(_, i) => (i % 2 === 1 ? `customRow odd` : `customRow even`)}
          columns={tableColumns}
          dataSource={tableData}
          loading={tableLoading}
          rowSelection={rowSelection}
          bordered
          scroll={{
            y: `calc(${height}px - 0.625rem - 1.6rem - 0.5rem)`
          }}
          onChange={onChangePage}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            pageSizeOptions: ['10', '20', '50']
          }}
        />
        {/* )} */}
      </div>
      <Modal
        title='- 新增数据 -'
        destroyOnClose={true}
        open={addModalOpen}
        wrapClassName={styles.add_table_modal}
        centered
        footer={null}
        onCancel={() => setAddModalOpen(false)}
        width={'80%'}
      >
        <AddModal
          columns={allColumns.filter(item => !filterColumns.includes(item.dataIndex))}
          submitData={value => handleAddData(value)}
          unitList={unitList}
          managerLevelList={managerLevelList}
          unitManagerList={unitManagerList}
          talentList={talentList}
        />
      </Modal>
      <EditModal
        open={editModalOpen}
        onCancel={() => {
          setEditModalOpen(false)
          setEditRecord(null)
        }}
        onOk={handleEditData}
        initialValues={editRecord}
        unitList={unitList}
        personnelChangeList={personnelChangeList}
        managerLevelList={managerLevelList}
        unitManagerList={unitManagerList}
        talentList={talentList}
        exitReasonList={exitReasonList}
      />
    </div>
  )
}
export default PeopleMonthReport
