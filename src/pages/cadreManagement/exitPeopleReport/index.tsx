import {
  But<PERSON>,
  Col,
  DatePicker,
  Form,
  FormProps,
  message,
  Row,
  Select,
  Space,
  Tooltip
} from 'antd'
import dayjs from 'dayjs'
import { DownloadOutlined, FullscreenExitOutlined, FullscreenOutlined } from '@ant-design/icons'
import { useEffect, useRef, useState } from 'react'
import { useRequest } from '@/hooks'
import cadreManageService from '@/pages/cadreManagement/service.ts'
import { downFile, openNotification } from '@/utils/down.tsx'
import styles from '../Index.module.scss'
import { orgOption } from '@/pages/effect/employment/assest.ts'
import ResizableTable from '@/components/resizeTable/index.jsx'

type FieldType = {
  cycleId?: string
  unit?: string
  curPost?: string
}

const ExitPeopleReport: React.FC = () => {
  const [formRef] = Form.useForm()
  const topRef = useRef(null)
  const contentRef = useRef(null)
  const tableTopRef = useRef(null)
  const [height, setHeight] = useState(0)
  const [tableData, setTableData] = useState<any>([])
  const [tableLoading, setTableLoading] = useState<boolean>(false)
  const [tableColumns, setTableColumns] = useState<any>([])
  const [isReset, setIsReset] = useState<number>(0)
  const [orgList, setOrgList] = useState<any>([])
  const [showTitle, setShowTitle] = useState<boolean>(true)
  const [pagination, setPagination] = useState({
    total: 0,
    pageNum: 1,
    pageSize: 50
  })

  const getColumns = () => {
    const selectedDate = formRef.getFieldValue('cycleId')
    const lastYearDate = selectedDate
      ? dayjs(selectedDate).subtract(1, 'year').endOf('year')
      : dayjs().subtract(1, 'year').endOf('year')

    return [
      {
        title: '单位',
        key: 'unit',
        dataIndex: 'unit',
        align: 'center',
        fixed: 'left',
        width: 120,
        children: [],
        render: (text: string, _record: orgOption) => (
          <Tooltip title={text}>
            <div className={styles.over_ellipsis}>{text}</div>
          </Tooltip>
        )
      },
      {
        title: `上年末人数(${lastYearDate.format('YYYY.MM')})`,
        key: 'lastYearEndPeople',
        dataIndex: 'lastYearEndPeople',
        align: 'center',
        children: [
          {
            title: '三级经理',
            key: 'managerLevel3LastYear',
            dataIndex: 'managerLevel3LastYear',
            align: 'center',
            children: [],
            width: 120
          },
          {
            title: '基层责任单元经理',
            key: 'unitManagerLastYear',
            dataIndex: 'unitManagerLastYear',
            align: 'center',
            children: [],
            width: 120
          },
          {
            title: '合计',
            key: 'totalLastYear',
            dataIndex: 'totalLastYear',
            align: 'center',
            children: [],
            width: 100
          }
        ]
      },
      {
        title: '退出数（月度取数，不含提拔）',
        key: 'exitPeople',
        dataIndex: 'exitPeople',
        align: 'center',
        children: [
          {
            title: '三级经理',
            key: 'managerLevel3Quit',
            dataIndex: 'managerLevel3Quit',
            align: 'center',
            children: [],
            width: 120
          },
          {
            title: '基层责任单元经理',
            key: 'unitManagerQuit',
            dataIndex: 'unitManagerQuit',
            align: 'center',
            children: [],
            width: 120
          },
          {
            title: '合计',
            key: 'totalQuit',
            dataIndex: 'totalQuit',
            align: 'center',
            children: [],
            width: 100
          }
        ]
      },
      {
        title: '退出比例',
        key: 'exitRatio',
        dataIndex: 'exitRatio',
        align: 'center',
        children: [
          {
            title: '三级经理',
            key: 'managerLevel3Scale',
            dataIndex: 'managerLevel3Scale',
            align: 'center',
            children: [],
            width: 120
          },
          {
            title: '基层责任单元经理',
            key: 'unitManagerScale',
            dataIndex: 'unitManagerScale',
            align: 'center',
            children: [],
            width: 120
          },
          {
            title: '合计',
            key: 'totalScale',
            dataIndex: 'totalScale',
            align: 'center',
            children: [],
            width: 100
          }
        ]
      }
    ]
  }

  const { runAsync: getEnumType } = useRequest(cadreManageService.getEnumType, {
    manual: true
  })
  const { runAsync: getExitPeopleReportList } = useRequest(
    cadreManageService.getExitPeopleReportList,
    {
      manual: true
    }
  )
  const { runAsync: exportExitPeopleReport } = useRequest(
    cadreManageService.exportExitPeopleReport,
    {
      manual: true
    }
  )

  useEffect(() => {
    setTableColumns(getColumns())
    getOrgList()
    initDate()
    queryTableData()
    window.addEventListener('resize', handleResize)
    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])

  useEffect(() => {
    setTableColumns(getColumns())
  }, [formRef.getFieldValue('cycleId')])

  const handleResize = () => {
    initHeight()
  }

  useEffect(() => {
    initHeight()
  }, [(document.querySelector('.exitPeopleReport_table .ant-table-header') || {})['offsetHeight']])

  useEffect(() => {
    initHeight()
  }, [tableData])

  useEffect(() => {
    if (isReset > 0) {
      queryTableData()
    }
  }, [isReset])

  useEffect(() => {
    if (pagination?.total > 0) {
      queryTableData()
    }
  }, [pagination.pageNum, pagination.pageSize])

  const initHeight = () => {
    const headerHeight =
      (document.querySelector('.exitPeopleReport_table .ant-table-header') || {})['offsetHeight'] ||
      0
    const pageHeight =
      (document.querySelector('.exitPeopleReport_table .ant-table-pagination') || {})[
        'offsetHeight'
      ] || 26
    if (headerHeight && pageHeight) {
      setHeight(
        contentRef.current?.offsetHeight -
          (tableTopRef.current.offsetHeight + headerHeight + pageHeight)
      )
    }
  }

  const getOrgList = async () => {
    const [error, res] = await getEnumType({ code: '1010', tag: 1 })
    if (error) {
      message.error(res?.DATA || res?.MESSAGE || '调用失败')
      return
    }
    if (res.STATUS === '0000') {
      setOrgList(res.DATA)
    } else {
      message.error(res?.MESSAGE || res?.DATA || '调用失败')
    }
  }

  // 查询表格数据
  const queryTableData = async () => {
    const values = formRef.getFieldsValue()
    setTableLoading(true)
    const [error, res] = await getExitPeopleReportList({
      map: {
        unit: values?.unit ?? '',
        cycleId: values?.cycleId?.format('YYYYMM')
      },
      pagination: {
        ...pagination
      }
    })
    setTableLoading(false)
    if (error) {
      message.error(res?.DATA || res?.MESSAGE || '调用失败')
      return
    }
    if (res.STATUS === '0000') {
      const {
        DATA: { data }
      } = res
      const dataWithKeys = data.map((item, index) => ({
        ...item,
        key: item.id || index
      }))
      setTableData(dataWithKeys)
      setPagination({
        ...pagination,
        total: res.DATA?.totalCount
      })
    } else {
      message.error(res?.MESSAGE || res?.DATA || '调用失败')
    }
  }

  const initDate = () => {
    const date = dayjs().subtract(1, 'month')
    formRef.setFieldsValue({
      cycleId: date
    })
  }

  const onFormFinish: FormProps<FieldType>['onFinish'] = values => {
    console.log('Success:', values)
    queryTableData()
  }

  const exportToExcelFun = async () => {
    try {
      openNotification('正在导出', 0, 'loading')
      const values = formRef.getFieldsValue()
      const response = await exportExitPeopleReport({
        map: {
          ...values,
          cycleId: values?.cycleId?.format('YYYYMM')
        }
      })
      downFile(response)
    } catch (error) {
      openNotification('导出失败', 1, 'error')
      console.error('Download failed:', error)
    }
  }
  // 查询重置
  const onReset = () => {
    const newReset = isReset + 1
    formRef.resetFields()
    initDate()
    setIsReset(newReset)
  }

  // 切换分页
  const onChangePage = page => {
    const newPage = {
      total: page?.total,
      pageNum: page?.current,
      pageSize: page?.pageSize
    }
    setPagination(newPage)
  }

  const filter = (input, option) =>
    (option?.enumName ?? '').toLowerCase().includes(input.toLowerCase()?.trim())

  return (
    <div className={`${'pt-[0.5rem] px-[0rem] h-full flex flex-col'} ${styles.employment_page}`}>
      <div ref={topRef} className={'bg-white pt-2 px-8 mb-2'}>
        <Form
          form={formRef}
          initialValues={{
            tag: ''
          }}
          onFinish={onFormFinish}
          autoComplete='off'
        >
          <Row gutter={24}>
            <Col span={6}>
              <Form.Item
                label='月份'
                name='cycleId'
                wrapperCol={{ span: 20 }}
                className='mb-[0.5rem]'
              >
                <DatePicker className='w-full' allowClear={false} picker='month' />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label='单位' name='unit' wrapperCol={{ span: 20 }} className='mb-[0.5rem]'>
                <Select
                  placeholder={'请选择'}
                  className='w-full'
                  allowClear={true}
                  showSearch
                  filterOption={filter}
                  options={orgList}
                  fieldNames={{
                    value: 'enumName',
                    label: 'enumName'
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item labelCol={{ span: 0 }} wrapperCol={{ span: 24 }} className='mb-[0.5rem]'>
                <Space size='small'>
                  <Button type='primary' htmlType='submit'>
                    查询
                  </Button>
                  <Button htmlType='button' onClick={() => onReset()}>
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>
      <div
        ref={contentRef}
        className='relative bg-white px-5 pt-2.5'
        style={{ height: `calc(100% - ${topRef.current?.offsetHeight + 15}px)` }}
      >
        <div
          ref={tableTopRef}
          className={`${'flex justify-between items-center overflow-hidden mb-2'} ${styles.animation_box} ${showTitle ? 'h-[1.8rem]' : 'h-0'}`}
        >
          <div className={'flex '}>
            {showTitle ? (
              <FullscreenExitOutlined
                className={`${styles.shousuo_icon} text-[1rem]`}
                onClick={() => {
                  setShowTitle(false)
                  setTimeout(() => {
                    initHeight()
                  }, 200)
                }}
              />
            ) : (
              <FullscreenOutlined
                className={`${styles.shousuo_icon} ${'absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10'}`}
                onClick={() => {
                  setShowTitle(true)
                  setTimeout(() => {
                    initHeight()
                  }, 200)
                }}
              />
            )}
            <div className='font-bold text-[0.8rem] ml-3'>
              数据列表
              <span className='text-[0.7rem]' style={{ color: '#939393' }}>
                （单位：万元）
              </span>
            </div>
          </div>
          <Button danger ghost icon={<DownloadOutlined />} onClick={() => exportToExcelFun()}>
            导出
          </Button>
        </div>
        <ResizableTable
          className='exitPeopleReport_table'
          rowClassName={(_, i) => (i % 2 === 1 ? `customRow odd` : `customRow even`)}
          columns={tableColumns}
          dataSource={tableData}
          loading={tableLoading}
          bordered
          scroll={{
            y: `calc(${height}px - 0.625rem - 1.6rem - 0.5rem)`
          }}
          onChange={onChangePage}
          pagination={{
            ...pagination,
            total: pagination?.total,
            showSizeChanger: true,
            showQuickJumper: true,
            pageSizeOptions: ['10', '20', '50']
          }}
        />
      </div>
    </div>
  )
}
export default ExitPeopleReport
