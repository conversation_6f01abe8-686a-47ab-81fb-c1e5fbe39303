// const PeopleAnalysisReport: React.FC = () => {
//   return <>人员情况分析表</>
// }
// export default PeopleAnalysisReport

import { Button, Col, DatePicker, Form, FormProps, message, Row, Select, Space } from 'antd'
import dayjs from 'dayjs'
import { DownloadOutlined, FullscreenExitOutlined, FullscreenOutlined } from '@ant-design/icons'
import { useEffect, useRef, useState } from 'react'
import { useRequest } from '@/hooks'
import cadreManageService from '@/pages/cadreManagement/service.ts'
import { downFile, openNotification } from '@/utils/down.tsx'
import styles from '../Index.module.scss'
import ResizableTable from '@/components/resizeTable/index.jsx'

type FieldType = {
  cycleId?: string
  unit?: string
  curPost?: string
}

const PeopleAnalysisReport: React.FC = () => {
  const [formRef] = Form.useForm()
  const topRef = useRef(null)
  const contentRef = useRef(null)
  const tableTopRef = useRef(null)
  const [height, setHeight] = useState(0)
  const [tableData, setTableData] = useState<any>([])
  const [tableLoading, setTableLoading] = useState<boolean>(false)
  const [tableColumns, setTableColumns] = useState<any>([])
  const [isReset, setIsReset] = useState<number>(0)
  const [orgList, setOrgList] = useState<any>([])
  const [showTitle, setShowTitle] = useState<boolean>(true)
  const [pagination, setPagination] = useState({
    total: 0,
    pageNum: 1,
    pageSize: 50
  })

  const columns = [
    {
      title: '单位(当月）',
      key: 'unit',
      dataIndex: 'unit',
      align: 'center',
      fixed: 'left',
      width: 120,
      children: []
      // render: (text: string, _record: orgOption) => (
      //   <Tooltip title={text}>
      //     <div className={styles.over_ellipsis}>{text}</div>
      //   </Tooltip>
      // )
    },
    {
      title: '基层责任单元经理合计',
      key: 'um',
      dataIndex: 'um',
      align: 'center',
      children: [
        {
          title: '人数',
          key: 'umNumber',
          dataIndex: 'umNumber',
          align: 'center',
          children: [],
          width: 120
        },
        {
          title: '平均年龄',
          key: 'umAvg',
          dataIndex: 'umAvg',
          align: 'center',
          children: [],
          width: 120
        },
        {
          title: '其中：37 岁及以下',
          key: 'um37DownNumber',
          dataIndex: 'um37DownNumber',
          align: 'center',
          children: [],
          width: 140
        },
        {
          title: '其中：87后人数',
          key: 'um87AfterDownNumber',
          dataIndex: 'um87AfterDownNumber',
          align: 'center',
          children: [],
          width: 140
        },
        {
          title: '87后占比',
          key: 'um87AfterDownScale',
          dataIndex: 'um87AfterDownScale',
          align: 'center',
          children: [],
          width: 100
        },
        {
          title: '其中：32 岁及以下',
          key: 'um32DownNumber',
          dataIndex: 'um32DownNumber',
          align: 'center',
          children: [],
          width: 140
        },
        {
          title: '32及以下占比',
          key: 'um32DownScale',
          dataIndex: 'um32DownScale',
          align: 'center',
          children: [],
          width: 120
        }
      ]
    },
    {
      title: '三级经理合计',
      key: 'ml',
      dataIndex: 'ml',
      align: 'center',
      children: [
        {
          title: '人数',
          key: 'mlNumber',
          dataIndex: 'mlNumber',
          align: 'center',
          children: [],
          width: 120
        },
        {
          title: '平均年龄',
          key: 'mlAvg',
          dataIndex: 'mlAvg',
          align: 'center',
          children: [],
          width: 120
        },
        // {
        //   title: '其中：87后人数',
        //   key: 'ml87AfterDownNumber',
        //   dataIndex: 'ml87AfterDownNumber',
        //   align: 'center',
        //   children: [],
        //   width: 140
        // },
        {
          title: '87后占比',
          key: 'ml87AfterDownScale',
          dataIndex: 'ml87AfterDownScale',
          align: 'center',
          children: [],
          width: 120
        },
        {
          title: '其中: 90后',
          key: 'ml90AfterDownNumber',
          dataIndex: 'ml90AfterDownNumber',
          align: 'center',
          children: [],
          width: 120
        },
        {
          title: ' 90后占比',
          key: 'ml90AfterDownScale',
          dataIndex: 'ml90AfterDownScale',
          align: 'center',
          children: [],
          width: 120
        }
      ]
    },
    {
      title: '三级正',
      key: 'l3Principal',
      dataIndex: 'l3Principal',
      align: 'center',
      children: [
        {
          title: '人数',
          key: 'l3PrincipalNumber',
          dataIndex: 'l3PrincipalNumber',
          align: 'center',
          children: [],
          width: 120
        },
        {
          title: '平均年龄',
          key: 'l3PrincipalAvg',
          dataIndex: 'l3PrincipalAvg',
          align: 'center',
          children: [],
          width: 120
        },
        {
          title: '其中：37岁及以下',
          key: 'l3Principal37DownNumber',
          dataIndex: 'l3Principal37DownNumber',
          align: 'center',
          children: [],
          width: 140
        },
        {
          title: '37岁及以下占比',
          key: 'l3Principal37DownScale',
          dataIndex: 'l3Principal37DownScale',
          align: 'center',
          children: [],
          width: 140
        },
        {
          title: '其中：32岁及以下',
          key: 'l3Principal32DownNumber',
          dataIndex: 'l3Principal32DownNumber',
          align: 'center',
          children: [],
          width: 140
        },
        {
          title: '32岁及以下占比',
          key: 'l3Principal32DownScale',
          dataIndex: 'l3Principal32DownScale',
          align: 'center',
          children: [],
          width: 140
        }
      ]
    },
    {
      title: '三级副',
      key: 'l3Vice',
      dataIndex: 'l3Vice',
      align: 'center',
      children: [
        {
          title: '人数',
          key: 'l3ViceNumber',
          dataIndex: 'l3ViceNumber',
          align: 'center',
          children: [],
          width: 120
        },
        {
          title: '平均年龄',
          key: 'l3ViceAvg',
          dataIndex: 'l3ViceAvg',
          align: 'center',
          children: [],
          width: 120
        },
        {
          title: '其中：37岁及以下',
          key: 'l3Vice37DownNumber',
          dataIndex: 'l3Vice37DownNumber',
          align: 'center',
          children: [],
          width: 140
        },
        {
          title: '37岁及以下占比',
          key: 'l3Vice37DownScale',
          dataIndex: 'l3Vice37DownScale',
          align: 'center',
          children: [],
          width: 140
        },
        {
          title: '其中：32岁及以下',
          key: 'l3Vice32DownNumber',
          dataIndex: 'l3Vice32DownNumber',
          align: 'center',
          children: [],
          width: 140
        },
        {
          title: '32岁及以下占比',
          key: 'l3Vice32DownScale',
          dataIndex: 'l3Vice32DownScale',
          align: 'center',
          children: [],
          width: 140
        }
      ]
    }
  ]

  const { runAsync: getEnumType } = useRequest(cadreManageService.getEnumType, {
    manual: true
  })
  const { runAsync: getPeopleAnalysisList } = useRequest(cadreManageService.getPeopleAnalysisList, {
    manual: true
  })
  const { runAsync: exportPeopleAnalysis } = useRequest(cadreManageService.exportPeopleAnalysis, {
    manual: true
  })

  useEffect(() => {
    setTableColumns(columns)
    getOrgList()
    initDate()
    queryTableData()
    window.addEventListener('resize', handleResize)
    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])

  const handleResize = () => {
    initHeight()
  }

  useEffect(() => {
    initHeight()
  }, [
    (document.querySelector('.peopleAnalysisReport_table .ant-table-header') || {})['offsetHeight']
  ])

  useEffect(() => {
    initHeight()
  }, [tableData])

  useEffect(() => {
    if (isReset > 0) {
      queryTableData()
    }
  }, [isReset])

  useEffect(() => {
    if (pagination?.total > 0) {
      queryTableData()
    }
  }, [pagination.pageNum, pagination.pageSize])

  const initHeight = () => {
    const headerHeight =
      (document.querySelector('.peopleAnalysisReport_table .ant-table-header') || {})[
        'offsetHeight'
      ] || 0
    const pageHeight =
      (document.querySelector('.peopleAnalysisReport_table .ant-table-pagination') || {})[
        'offsetHeight'
      ] || 26
    if (headerHeight && pageHeight) {
      setHeight(
        contentRef.current?.offsetHeight -
          (tableTopRef.current.offsetHeight + headerHeight + pageHeight)
      )
    }
  }

  const getOrgList = async () => {
    const [error, res] = await getEnumType({ code: '1010', tag: 1 })
    if (error) {
      message.error(res?.DATA || res?.MESSAGE || '调用失败')
      return
    }
    if (res.STATUS === '0000') {
      setOrgList(res.DATA)
    } else {
      message.error(res?.MESSAGE || res?.DATA || '调用失败')
    }
  }

  // 查询表格数据
  const queryTableData = async () => {
    const values = formRef.getFieldsValue()
    setTableLoading(true)
    const [error, res] = await getPeopleAnalysisList({
      map: {
        ...values,
        cycleId: values?.cycleId?.format('YYYYMM')
      },
      pagination: {
        ...pagination
      }
    })
    setTableLoading(false)
    if (error) {
      message.error(res?.DATA || res?.MESSAGE || '调用失败')
      return
    }
    if (res.STATUS === '0000') {
      const {
        DATA: { data }
      } = res
      const dataWithKeys = data.map((item, index) => ({
        ...item,
        key: item.id || index
      }))
      setTableData(dataWithKeys)
      setPagination({
        ...pagination,
        total: res.DATA?.totalCount
      })
    } else {
      message.error(res?.MESSAGE || res?.DATA || '调用失败')
    }
  }

  const initDate = () => {
    const date = dayjs().subtract(1, 'month')
    formRef.setFieldsValue({
      cycleId: date
    })
  }

  const onFormFinish: FormProps<FieldType>['onFinish'] = values => {
    console.log('Success:', values)
    queryTableData()
  }

  const exportToExcelFun = async () => {
    try {
      openNotification('正在导出', 0, 'loading')
      const values = formRef.getFieldsValue()
      const response = await exportPeopleAnalysis({
        map: {
          ...values,
          cycleId: values?.cycleId?.format('YYYYMM')
        }
      })
      downFile(response)
    } catch (error) {
      openNotification('导出失败', 1, 'error')
      console.error('Download failed:', error)
    }
  }
  // 查询重置
  const onReset = () => {
    const newReset = isReset + 1
    formRef.resetFields()
    initDate()
    setIsReset(newReset)
  }

  // 切换分页
  const onChangePage = page => {
    const newPage = {
      total: page?.total,
      pageNum: page?.current,
      pageSize: page?.pageSize
    }
    setPagination(newPage)
  }

  const filter = (input, option) =>
    (option?.enumName ?? '').toLowerCase().includes(input.toLowerCase()?.trim())

  return (
    <div className={`${'pt-[0.5rem] px-[0rem] h-full flex flex-col'} ${styles.employment_page}`}>
      <div ref={topRef} className={'bg-white pt-2 px-8 mb-2'}>
        <Form
          form={formRef}
          initialValues={{
            tag: ''
          }}
          onFinish={onFormFinish}
          autoComplete='off'
        >
          <Row gutter={24}>
            <Col span={6}>
              <Form.Item
                label='月份'
                name='cycleId'
                wrapperCol={{ span: 20 }}
                className='mb-[0.5rem]'
              >
                <DatePicker className='w-full' allowClear={false} picker='month' />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label='单位' name='unit' wrapperCol={{ span: 20 }} className='mb-[0.5rem]'>
                <Select
                  placeholder={'请选择'}
                  className='w-full'
                  allowClear={true}
                  showSearch
                  filterOption={filter}
                  options={orgList}
                  fieldNames={{
                    value: 'enumName',
                    label: 'enumName'
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item labelCol={{ span: 0 }} wrapperCol={{ span: 24 }} className='mb-[0.5rem]'>
                <Space size='small'>
                  <Button type='primary' htmlType='submit'>
                    查询
                  </Button>
                  <Button htmlType='button' onClick={() => onReset()}>
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>
      <div
        ref={contentRef}
        className='relative bg-white px-5 pt-2.5'
        style={{ height: `calc(100% - ${topRef.current?.offsetHeight + 15}px)` }}
      >
        <div
          ref={tableTopRef}
          className={`${'flex justify-between items-center overflow-hidden mb-2'} ${styles.animation_box} ${showTitle ? 'h-[1.8rem]' : 'h-0'}`}
        >
          <div className={'flex '}>
            {showTitle ? (
              <FullscreenExitOutlined
                className={`${styles.shousuo_icon} text-[1rem]`}
                onClick={() => {
                  setShowTitle(false)
                  setTimeout(() => {
                    initHeight()
                  }, 200)
                }}
              />
            ) : (
              <FullscreenOutlined
                className={`${styles.shousuo_icon} ${'absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10'}`}
                onClick={() => {
                  setShowTitle(true)
                  setTimeout(() => {
                    initHeight()
                  }, 200)
                }}
              />
            )}
            <div className='font-bold text-[0.8rem] ml-3'>
              数据列表
              <span className='text-[0.7rem]' style={{ color: '#939393' }}>
                （单位：万元）
              </span>
            </div>
          </div>
          <Button danger ghost icon={<DownloadOutlined />} onClick={() => exportToExcelFun()}>
            导出
          </Button>
        </div>
        <ResizableTable
          className='peopleAnalysisReport_table'
          rowClassName={(_, i) => (i % 2 === 1 ? `customRow odd` : `customRow even`)}
          columns={tableColumns}
          dataSource={tableData}
          loading={tableLoading}
          bordered
          scroll={{
            y: `calc(${height}px - 0.625rem - 1.6rem - 0.5rem)`
          }}
          onChange={onChangePage}
          pagination={{
            ...pagination,
            total: pagination?.total,
            showSizeChanger: true,
            showQuickJumper: true,
            pageSizeOptions: ['10', '20', '50']
          }}
        />
      </div>
    </div>
  )
}
export default PeopleAnalysisReport
