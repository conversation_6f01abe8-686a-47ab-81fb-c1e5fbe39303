import request from '@/request'

const cadreManageService = {
  // 查询枚举
  getEnumType: params => {
    return request.post<any>('/zhyy/employee/getEnumType', params)
  },

  // 查询
  getEmployeeMonthlyList: params => {
    return request.post<any>('/zhyy/cadre/employeeMonthlyReport/list', params)
  },

  // 根据人员编码查人员信息
  getEmployeeByEmpId: params => {
    return request.get<any>('/zhyy/cadre/employeeMonthlyReport/getEmpInfo', { params })
  },

  // 新增
  addEmployeeMonthlyReport: params => {
    return request.post<any>('/zhyy/cadre/employeeMonthlyReport/save', params)
  },

  // 修改
  updateEmployeeMonthlyReport: params => {
    return request.post<any>('/zhyy/cadre/employeeMonthlyReport/update', params)
  },

  // 删除
  deleteEmployeeMonthlyList: params => {
    return request.post<any>('/zhyy/cadre/employeeMonthlyReport/deleteByEmpId', params)
  },

  // 导出
  exportPeopleMonthReport: params => {
    return request.post<any>('/zhyy/cadre/employeeMonthlyReport/exportExcel', params, {
      responseType: 'blob'
    })
  },

  // 查询退出人员统计表
  getExitPeopleReportList: params => {
    return request.post<any>('/zhyy/cadre/exitStatistics/list', params)
  },

  // 导出
  exportExitPeopleReport: params => {
    return request.post<any>('/zhyy/cadre/exitStatistics/exportExcel', params, {
      responseType: 'blob'
    })
  },

  // 查询人员情况分析表
  getPeopleAnalysisList: params => {
    return request.post<any>('/zhyy/cadre/personnelSituation/list', params)
  },

  // 导出
  exportPeopleAnalysis: params => {
    return request.post<any>('/zhyy/cadre/personnelSituation/exportExcel', params, {
      responseType: 'blob'
    })
  }
}

export default cadreManageService
