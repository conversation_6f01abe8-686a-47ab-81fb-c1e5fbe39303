import {
  Button,
  Cascader,
  CascaderProps,
  Col,
  DatePicker,
  DatePickerProps,
  Form,
  FormProps,
  GetProp,
  message,
  Row,
  Select,
  Space,
  Tooltip,
  Tag
} from 'antd'
import dayjs from 'dayjs'
import { orgOption } from '@/pages/effect/employment/assest.ts'
import { DownloadOutlined, FullscreenExitOutlined, FullscreenOutlined } from '@ant-design/icons'
import { useEffect, useRef, useState } from 'react'
import effectService from '@/pages/effect/employment/service.ts'
import { useRequest } from '@/hooks'
import { downFile, openNotification } from '@/utils/down.tsx'
import styles from '../Index.module.scss'
import ResizableTable from '@/components/resizeTable/index.jsx'

interface enumType {
  enumId: string
  level: string
  id: string
  sort: number | null
  type: string
  enumName: string
  region: string | null
}

type DefaultOptionType = GetProp<CascaderProps, 'options'>[number]

type FieldType = {
  monthId?: string
  unit?: string
  oneClass?: string
  role?: string
}

const roleColorMap = {
  公众线: 'cyan',
  政企线: 'blue',
  网络线: 'green',
  职能线: 'geekblue'
}
const classifyColorMap = {
  专业线: 'cyan',
  岗位: 'blue',
  重点岗位: 'green'
}

const ReportOrg: React.FC = () => {
  const tableColumns: any[] = [
    {
      title: '单位',
      key: 'orgName5',
      dataIndex: 'orgName5',
      align: 'center',
      width: 150,
      render: (text: string, _record: orgOption) => (
        <Tooltip title={text}>
          <div className={styles.over_ellipsis}>{text}</div>
        </Tooltip>
      )
    },
    {
      title: '部门',
      key: 'orgName6',
      dataIndex: 'orgName6',
      align: 'center',
      width: 200,
      render: (text: string, _record: orgOption) => (
        <Tooltip title={text}>
          <div className={styles.over_ellipsis}>
            {text && text.startsWith('天津市分公司') ? text.slice('天津市分公司'.length) : text}
          </div>
        </Tooltip>
      )
    },
    {
      title: '员工编号',
      key: 'employeeId',
      dataIndex: 'employeeId',
      align: 'center',
      width: 100
    },
    {
      title: '员工姓名',
      key: 'employeeName',
      dataIndex: 'employeeName',
      align: 'center',
      width: 100
    },
    {
      title: '当前岗位',
      key: 'postName',
      dataIndex: 'postName',
      align: 'center',
      width: 100
    },
    {
      title: '用工类型',
      key: 'employeeType',
      dataIndex: 'employeeType',
      align: 'center',
      width: 100
    },
    {
      title: '分类',
      key: 'oneClass',
      dataIndex: 'oneClass',
      align: 'center',
      width: 100,
      render: (text, _) =>
        text === '-' ? (
          text
        ) : (
          <Tag color={classifyColorMap[text] || 'purple'} style={{ marginRight: '0' }}>
            {text}
          </Tag>
        )
    },
    {
      title: '角色',
      key: 'twoClass',
      dataIndex: 'twoClass',
      align: 'center',
      width: 100,
      render: (text, _) =>
        text === '-' ? (
          text
        ) : (
          <Tag color={roleColorMap[text] || 'purple'} style={{ marginRight: '0' }}>
            {text}
          </Tag>
        )
    }
  ]

  const topRef = useRef(null)
  const contentRef = useRef(null)
  const tableTopRef = useRef(null)
  const [formRef] = Form.useForm()
  const [showTitle, setShowTitle] = useState<boolean>(true)
  const [tableLoading, setTableLoading] = useState<boolean>(false)
  const [height, setHeight] = useState<number>(0)
  const [tableData, setTableData] = useState<any>([])
  const [orgList, setOrgList] = useState<any>([])
  const [orgValueArr, setOrgValueArr] = useState([])
  const [isReset, setIsReset] = useState<number>(0)
  const [pagination, setPagination] = useState({
    total: 0,
    pageNum: 1,
    pageSize: 50
  })

  const [personTypeList, setPersonTypeList] = useState<enumType[]>([]) //人员
  const [roleTypeList, setRoleTypeList] = useState<enumType[]>([]) //角色
  const [employmentTypeList, setEmploymentTypeList] = useState<enumType[]>([]) //用工类型

  const { runAsync: exportEmployeeOrgDetailExcel } = useRequest(
    effectService.exportEmployeeOrgDetailExcel,
    {
      manual: true
    }
  )
  const { runAsync: build4LevelOrgTree } = useRequest(effectService.build4LevelOrgTree, {
    manual: true
  })
  const { runAsync: getEmployeeOrgDetailPag } = useRequest(effectService.getEmployeeOrgDetailPag, {
    manual: true
  })

  useEffect(() => {
    initDate()
    // 添加 resize 事件监听器
    window.addEventListener('resize', handleResize)
    // 清除事件监听器，防止内存泄漏
    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])
  const handleResize = () => {
    initHeight()
  }

  useEffect(() => {
    initHeight()
  }, [(document.querySelector('.report_org_detail_table .ant-table-header') || {})['offsetHeight']])

  useEffect(() => {
    if (isReset > 0) {
      initDate()
    }
  }, [isReset])
  useEffect(() => {
    if (tableData?.length > 0) {
      initHeight()
    }
  }, [tableData])
  useEffect(() => {
    if (pagination?.total > 0) {
      queryTableData()
    }
  }, [pagination.pageNum, pagination.pageSize])

  const initHeight = () => {
    const headerHeight =
      (document.querySelector('.report_org_detail_table .ant-table-header') || {})[
        'offsetHeight'
      ] || 1
    const pageHeight =
      (document.querySelector('.report_org_detail_table .ant-table-pagination') || {})[
        'offsetHeight'
      ] || 26
    if (headerHeight && pageHeight) {
      setHeight(
        contentRef.current?.offsetHeight -
          (tableTopRef.current.offsetHeight + headerHeight + pageHeight)
      )
    }
  }

  const getOrgTree = async () => {
    const [error, res] = await build4LevelOrgTree({
      monthId: formRef.getFieldValue('monthId')?.format('YYYYMM'),
      tag: '1'
    })
    if (error) {
      return
    }
    if (res.STATUS === '0000') {
      setOrgList(res.DATA)
      setOrgValueArr([])
      formRef.setFieldValue('unit', '')
    }
  }

  // 查询表格数据
  const queryTableData = async (newPage?: any) => {
    setTableLoading(true)
    const values = formRef.getFieldsValue()
    const curOrg = orgValueArr ? orgValueArr[orgValueArr.length - 1] : {}
    const page = newPage || pagination
    const [error, res] = await getEmployeeOrgDetailPag({
      monthId: values?.monthId?.format('YYYYMM'),
      org4: Number(curOrg?.level) === 4 ? curOrg?.orgId : '',
      org5: Number(curOrg?.level) === 5 ? curOrg?.orgId : '',
      org6: Number(curOrg?.level) === 6 ? curOrg?.orgId : '',
      oneClass: values?.oneClass,
      twoClass: values?.role,
      type: values?.employeeType,
      pageNum: page.pageNum,
      pageSize: page.pageSize
    })

    setTableLoading(false)
    if (error) {
      return
    }
    if (res.STATUS === '0000') {
      const {
        DATA: { data }
      } = res
      // 添加唯一的 key
      const dataWithKeys = data.map((item, index) => ({
        ...item,
        key: item.id || index // 使用数据中的唯一字段作为 key，或回退到索引
      }))
      console.log('@@@1', dataWithKeys)
      setTableData(dataWithKeys)
      setPagination({
        ...pagination,
        total: res.DATA?.totalCount
      })
    } else {
      message.error(res?.MESSAGE)
    }
  }

  const initDate = () => {
    const date = dayjs()
    formRef.setFieldsValue({
      monthId: date,
      unit: []
    })
    getOrgTree() //组织
    getPersonList(true) //人员分类
    getEmployeList() //用工类型
  }

  // 点击查询
  const onFormFinish: FormProps<FieldType>['onFinish'] = () => {
    queryTableData()
  }

  // 切换组织
  const handleOrgChange = (_value, selectedOptions) => {
    setOrgValueArr(selectedOptions)
  }

  // 导出
  const exportToExcelFun = async () => {
    try {
      // 调用接口获取 Blob 文件数据
      openNotification('正在导出', 0, 'loading')
      const values = formRef.getFieldsValue()
      // const unit = values?.unit;
      const curOrg = orgValueArr ? orgValueArr[orgValueArr.length - 1] : {}
      const response = await exportEmployeeOrgDetailExcel({
        monthId: values?.monthId?.format('YYYYMM'),
        org4: Number(curOrg?.level) === 4 ? curOrg?.orgId : '',
        org5: Number(curOrg?.level) === 5 ? curOrg?.orgId : '',
        org6: Number(curOrg?.level) === 6 ? curOrg?.orgId : '',
        oneClass: values?.oneClass,
        twoClass: values?.role,
        type: values?.employeeType
      })
      downFile(response)
    } catch (error) {
      openNotification('导出失败', 1, 'error')
      console.error('Download failed:', error)
    }
  }
  // 查询重置
  const onReset = () => {
    const newReset = isReset + 1
    formRef.resetFields()
    setOrgValueArr([])
    setIsReset(newReset)
  }

  const filter = (inputValue: string, path: DefaultOptionType[]) =>
    path.some(
      option => (option.orgName as string).toLowerCase().indexOf(inputValue.toLowerCase()) > -1
    )

  // 切换月份选择
  const onMonthChange: DatePickerProps['onChange'] = _date => {
    // setMonthDate(_date)
  }

  // 切换分页
  const onChangePage = page => {
    const newPage = {
      total: page?.total,
      pageNum: page?.current,
      pageSize: page?.pageSize
    }
    setPagination(newPage)
  }

  //获取组织架构
  const onValuesChange = changedValues => {
    if (changedValues.monthId) {
      getOrgTree()
    } else if (changedValues.oneClass) {
      const item = personTypeList.filter(item => item.enumName === changedValues.oneClass)[0]
      //调接口获取角色
      formRef.setFieldValue('role', '')
      getRoleList(item.enumId)
    }
  }

  //获取人员分类
  const { runAsync: getPersonType } = useRequest(effectService.getEnumType, { manual: true })
  const getPersonList = async (isInit: boolean) => {
    const [error, response] = await getPersonType({ code: '1030', region: '' })
    if (error) return
    if (response.STATUS === '0000') {
      const personList = response.DATA
      setPersonTypeList(personList)
      // 设置默认选中值
      if (personList.length > 0) {
        formRef.setFieldsValue({ oneClass: personList[0].enumName })
        getRoleList(personList[0].enumId, isInit) // 根据默认分类获取角色列表
      }
    }
  }
  //获取用工类型
  const { runAsync: getEmployeType } = useRequest(effectService.getEnumType, { manual: true })
  const getEmployeList = async () => {
    const [error, response] = await getEmployeType({ code: '1032', region: '' })
    if (error) return
    if (response.STATUS === '0000') {
      setEmploymentTypeList(
        response.DATA.map(item => ({
          key: item.enumId,
          label: item.enumName,
          value: item.enumName
        }))
      )
    }
  }
  //获取角色
  const { runAsync: getRoleType } = useRequest(effectService.getEnumType, { manual: true })
  const getRoleList = async (region: string, isInit?: boolean) => {
    const [error, response] = await getRoleType({ code: '1031', region })
    if (error) return
    if (response.STATUS === '0000') {
      const roleList = response.DATA.map(item => ({
        key: item.enumId,
        label: item.enumName,
        value: item.enumName
      }))
      setRoleTypeList(roleList)
      // 设置默认选中值
      if (roleList.length > 0) {
        formRef.setFieldsValue({ role: roleList[0].value })
      }
      if (isInit) {
        queryTableData()
      }
    }
  }

  return (
    <div className={`${'h-full pt-[0.5rem] flex flex-col'} ${styles.employment_page}`}>
      <div ref={topRef} className={'bg-white pt-[0.5rem] px-8 mb-[0.5rem]'}>
        <Form
          form={formRef}
          initialValues={{
            tag: ''
          }}
          onFinish={onFormFinish}
          onValuesChange={onValuesChange}
          // onFinishFailed={onFinishFailed}
          autoComplete='off'
        >
          <Row
            gutter={24}
            // justify="end"
          >
            <Col span={6}>
              <Form.Item label='月份' name='monthId' wrapperCol={{ span: 20 }}>
                <DatePicker
                  className='w-full'
                  allowClear={false}
                  onChange={onMonthChange}
                  picker='month'
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label='组织' name='unit' wrapperCol={{ span: 20 }}>
                <Cascader
                  allowClear={true}
                  changeOnSelect
                  expandTrigger='hover'
                  // showCheckedStrategy={Cascader.SHOW_CHILD}
                  displayRender={labels => labels[labels.length - 1]}
                  options={orgList}
                  onChange={handleOrgChange}
                  fieldNames={{
                    value: 'orgId',
                    label: 'orgName',
                    children: 'children'
                  }}
                  // onChange={(_, selectedOptions) => {
                  //   setCascaderSelected(selectedOptions)
                  // }}
                  placeholder='请选择'
                  showSearch={{ filter }}
                  onSearch={value => console.log(value)}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label='人员分类' name='oneClass' wrapperCol={{ span: 20 }}>
                <Select
                  placeholder='请选择'
                  className='w-full'
                  options={personTypeList.map(item => ({
                    label: item.enumName,
                    value: item.enumName,
                    key: item.enumId
                  }))}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label='角色' name='role' wrapperCol={{ span: 20 }}>
                <Select placeholder='请选择' className='w-full' options={roleTypeList} />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label='用工类型' name='employeeType' wrapperCol={{ span: 20 }}>
                <Select placeholder='请选择' allowClear options={employmentTypeList} />
              </Form.Item>
            </Col>

            <Col span={6}>
              <Form.Item labelCol={{ span: 0 }} wrapperCol={{ span: 24 }}>
                <Space size='small'>
                  <Button type='primary' htmlType='submit'>
                    查询
                  </Button>
                  <Button htmlType='button' onClick={() => onReset()}>
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>
      <div
        ref={contentRef}
        className='relative bg-white px-5 pt-2.5'
        style={{ height: `calc(100% - ${topRef.current?.offsetHeight + 15}px)` }}
      >
        <div
          ref={tableTopRef}
          className={`${'flex justify-between items-center mb-2 overflow-hidden'} ${styles.animation_box} ${showTitle ? 'h-[1.6rem]' : 'h-0'}`}
        >
          <div className={'flex '}>
            {showTitle ? (
              <FullscreenExitOutlined
                className={`${styles.shousuo_icon} text-[1rem]`}
                onClick={() => {
                  setShowTitle(false)
                  setTimeout(() => {
                    initHeight()
                  }, 200)
                }}
              />
            ) : (
              <FullscreenOutlined
                className={`${styles.shousuo_icon} ${'absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10'}`}
                onClick={() => {
                  setShowTitle(true)
                  setTimeout(() => {
                    initHeight()
                  }, 200)
                }}
              />
            )}
            <div className='font-bold text-[0.8rem] ml-3'>数据列表</div>
          </div>
          <Button danger ghost icon={<DownloadOutlined />} onClick={() => exportToExcelFun()}>
            导出
          </Button>
        </div>
        <ResizableTable
          className='report_org_detail_table'
          rowClassName={(_, i) => (i % 2 === 1 ? `customRow odd` : `customRow even`)}
          columns={tableColumns}
          dataSource={tableData}
          loading={tableLoading}
          bordered
          scroll={{
            y: `calc(${height}px - 0.625rem - 1.6rem - 0.5rem)`
          }}
          onChange={onChangePage}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            pageSizeOptions: ['10', '20', '50']
          }}
        />
      </div>
    </div>
  )
}
export default ReportOrg
