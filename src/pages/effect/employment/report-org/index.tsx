import {
  Button,
  Cascader,
  CascaderProps,
  Col,
  DatePicker,
  DatePickerProps,
  Form,
  FormProps,
  GetProp,
  message,
  Row,
  Select,
  Space,
  Tooltip,
  Tag
} from 'antd'
import dayjs from 'dayjs'
import { orgOption, perClassOptions } from '@/pages/effect/employment/assest.ts'
import { DownloadOutlined, FullscreenExitOutlined, FullscreenOutlined } from '@ant-design/icons'
import { useEffect, useRef, useState } from 'react'
import effectService from '@/pages/effect/employment/service.ts'
import { useRequest } from '@/hooks'
import { downFile, openNotification } from '@/utils/down.tsx'
import styles from '../Index.module.scss'
import ResizableTable from '@/components/resizeTable/index.jsx'

type DefaultOptionType = GetProp<CascaderProps, 'options'>[number]

type FieldType = {
  monthId?: string
  unit?: string
  oneClass?: string
}

const ReportOrg: React.FC = () => {
  const mergeCells = (text, data, key, index) => {
    if (!data[index]) {
      return
    }
    function isSame(nextIndex) {
      const same1 = data[index]['monthId'] === data[nextIndex]['monthId']
      const same2 = data[index]['orgName5'] === data[nextIndex]['orgName5']
      const same3 = data[index]['orgName6'] === data[nextIndex]['orgName6']
      const same4 = data[index]['oneClass'] === data[nextIndex]['oneClass']
      return (
        key === 'monthId' ||
        (key === 'orgName5' && same1) ||
        (key === 'orgName6' && same1 && same2) ||
        (key === 'oneClass' && same1 && same2 && same3) ||
        (key === 'twoClass' && same1 && same2 && same3 && same4)
      )
    }

    // 上一行该列数据是否一样
    if (data[index - 1] && index !== 0 && text === data[index - 1][key]) {
      // 满足  1、前几列和上一行数据相同  2、上一行是小计/普通数据，当前行是普通数据
      if (
        isSame(index - 1) &&
        data[index]['rowno'] === '3' &&
        ['3'].includes(data[index - 1]['rowno'])
      ) {
        return {
          rowSpan: 0
        }
      }
    }
    let rowSpan = 1
    for (let i = index + 1; i < data.length; i++) {
      // 反向判断： 1、下一行是否相等
      if ((data[i] && text !== data[i][key]) || !isSame(i)) {
        break
      }
      // 判断 1、当前为普通数据（rowno:2），并且之后的行都是2则合并   2、当前为小计，合计（rowno:1）并且之后的行都是小计则合并.
      // if (['2','3'].includes(data[index]['rowno']) &&  ['2','3'].includes(data[i]['rowno'])) {
      if (['3'].includes(data[index]['rowno']) && ['3'].includes(data[i]['rowno'])) {
        rowSpan++
      } else {
        return {
          rowSpan: rowSpan
        }
      }
    }
    const isAntRow = data[index]['rowno'] === '0'
    return {
      // colSpan: (isAntRow && key !== 'monthId' && key !== 'twoClass') ? 0 : (isAntRow && key === 'monthId') ? 4 : 1,
      rowSpan: isAntRow ? 1 : rowSpan
    }
  }
  const roleColorMap = {
    公众线: 'cyan',
    政企线: 'blue',
    网络线: 'green',
    职能线: 'geekblue',
    生产现业人员: 'lime',
    组织支撑人员: 'gold',
    营业员: 'purple',
    渠道经理: 'processing',
    商客经理: 'orange',
    智家工程师: 'volcano',
    要客客户经理: 'red',
    商企客户经理: 'magenta'
  }
  const classifyColorMap = {
    专业线: 'cyan',
    岗位: 'blue',
    重点岗位: 'green'
  }

  const columns: any[] = [
    // {
    //   title: '月份',
    //   key: 'monthId',
    //   dataIndex: 'monthId',
    //   fixed: 'left',
    //   align: 'center',
    //   width: 50,
    //   onCell: (record: orgOption, rowIndex: string) =>
    //     mergeCells(record?.monthId, tableData, 'monthId', rowIndex)
    // },
    {
      title: '单位',
      key: 'orgName5',
      dataIndex: 'orgName5',
      fixed: 'left',
      align: 'center',
      width: 150,
      onCell: (record: orgOption, rowIndex: string) =>
        mergeCells(record?.orgName5, tableData, 'orgName5', rowIndex),
      render: (text: string, _record: orgOption) => (
        <Tooltip title={text}>
          <div className={styles.over_ellipsis}>{text}</div>
        </Tooltip>
      )
    },
    {
      title: '部门',
      key: 'orgName6',
      dataIndex: 'orgName6',
      fixed: 'left',
      align: 'center',
      width: 200,
      onCell: (record: orgOption, rowIndex: string) =>
        mergeCells(record?.orgName6, tableData, 'orgName6', rowIndex),
      render: (text: string, _record: orgOption) => (
        <Tooltip title={text}>
          <div className={styles.over_ellipsis}>
            {text && text.startsWith('天津市分公司') ? text.slice('天津市分公司'.length) : text}
          </div>
        </Tooltip>
      )
    },
    {
      title: '人员分类',
      key: 'name',
      dataIndex: 'name',
      children: [
        {
          title: '分类',
          key: 'oneClass',
          dataIndex: 'oneClass',
          align: 'center',
          fixed: 'left',
          width: 60,
          onCell: (record: orgOption, rowIndex: string) =>
            mergeCells(record?.oneClass, tableData, 'oneClass', rowIndex),
          render: (text, _) =>
            text === '-' ? (
              text
            ) : (
              <Tag color={classifyColorMap[text] || 'purple'} style={{ marginRight: '0' }}>
                {text}
              </Tag>
            )
        },
        {
          title: '角色',
          key: 'twoClass',
          dataIndex: 'twoClass',
          align: 'center',
          fixed: 'left',
          width: 80,
          onCell: (record: orgOption, rowIndex: string) =>
            mergeCells(record?.twoClass, tableData, 'twoClass', rowIndex),
          render: (text, _) =>
            text === '-' ? (
              text
            ) : (
              <Tag color={roleColorMap[text] || 'purple'} style={{ marginRight: '0' }}>
                {text}
              </Tag>
            )
          // render: (text: string, record: orgOption) => <div>{record.rowno === '0' ? record.orgName6 : text}</div>
        },
        {
          title: '合计',
          key: 'ant',
          dataIndex: 'ant',
          align: 'center',
          fixed: 'left',
          width: 50
          // onCell: (record:orgOption,rowIndex:string) => mergeCells(record?.perSum,tableData,'perSum',rowIndex),
        }
      ]
    },
    {
      title: '用工类型',
      key: 'name',
      dataIndex: 'name',
      children: [
        {
          title: '合同制',
          key: 'antHt',
          dataIndex: 'antHt',
          align: 'center',
          width: 60
          // onCell: (record:orgOption,rowIndex:string) => mergeCells(record?.contractSystem,tableData,'contractSystem',rowIndex),
        },
        {
          title: '劳务派遣',
          key: 'antLp',
          dataIndex: 'antLp',
          align: 'center',
          width: 75
          // onCell: (record:orgOption,rowIndex:string) => mergeCells(record?.laborDispatching,tableData,'laborDispatching',rowIndex),
        },
        {
          title: '紧密型外包',
          key: 'antJm',
          dataIndex: 'antJm',
          align: 'center',
          width: 85
          // onCell: (record:orgOption,rowIndex:string) => mergeCells(record?.compactOutsourcing,tableData,'compactOutsourcing',rowIndex),
        },
        {
          title: '其他外包',
          key: 'antQt',
          dataIndex: 'antQt',
          align: 'center',
          width: 75
          // onCell: (record:orgOption,rowIndex:string) => mergeCells(record?.OtherOutsourcing,tableData,'OtherOutsourcing',rowIndex),
        }
      ]
    }
  ]

  const topRef = useRef(null)
  const contentRef = useRef(null)
  const tableTopRef = useRef(null)
  const [formRef] = Form.useForm()
  // const [cascaderSelected, setCascaderSelected] = useState<any>([])
  const [showTitle, setShowTitle] = useState<boolean>(true)
  const [tableLoading, setTableLoading] = useState<boolean>(false)
  const [height, setHeight] = useState<number>(0)
  // const [monthDate, setMonthDate] = useState<any>('')
  const [tableData, setTableData] = useState<any>([])
  const [tableColumns, setTableColumns] = useState<any>(columns)
  const [orgList, setOrgList] = useState<any>([])
  const [orgValueArr, setOrgValueArr] = useState([])
  const [isReset, setIsReset] = useState<number>(0)
  const [pagination, setPagination] = useState({
    total: 0,
    pageNum: 1,
    pageSize: 50
  })

  const { runAsync: exportEmployeeOrgExcel } = useRequest(effectService.exportEmployeeOrgExcel, {
    manual: true
  })
  const { runAsync: build4LevelOrgTree } = useRequest(effectService.build4LevelOrgTree, {
    manual: true
  })
  const { runAsync: getEmployeeOrgPag } = useRequest(effectService.getEmployeeOrgPag, {
    manual: true
  })

  useEffect(() => {
    initDate()
    queryTableData()
    // 添加 resize 事件监听器
    window.addEventListener('resize', handleResize)
    // 清除事件监听器，防止内存泄漏
    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])

  const handleResize = () => {
    initHeight()
  }

  useEffect(() => {
    initHeight()
  }, [(document.querySelector('.report_org_table .ant-table-header') || {})['offsetHeight']])

  // useEffect(() => {
  //   if (monthDate) {
  //     initColumns()
  //   }
  // }, [monthDate])

  useEffect(() => {
    if (isReset > 0) {
      queryTableData()
    }
  }, [isReset])

  useEffect(() => {
    if (tableData?.length > 0) {
      initColumns()
      initHeight()
    }
  }, [tableData])

  // useEffect(() => {
  //   if (pagination?.total > 0) {
  //     initColumns()
  //   }
  // }, [JSON.stringify(pagination)])

  useEffect(() => {
    if (pagination?.total > 0) {
      queryTableData()
    }
  }, [pagination.pageNum, pagination.pageSize])

  const initHeight = () => {
    const headerHeight = (document.querySelector('.report_org_table .ant-table-header') || {})[
      'offsetHeight'
    ]
    const pageHeight =
      (document.querySelector('.report_org_table .ant-table-pagination') || {})['offsetHeight'] ||
      26
    if (headerHeight && pageHeight) {
      setHeight(
        contentRef.current?.offsetHeight -
          (tableTopRef.current.offsetHeight + headerHeight + pageHeight)
      )
    }
  }

  const getEnumTypes = async () => {
    const [error, res] = await build4LevelOrgTree({
      monthId: formRef.getFieldValue('monthId')?.format('YYYYMM'),
      tag: '1'
    })
    if (error) {
      return
    }
    if (res.STATUS === '0000') {
      setOrgList(res.DATA)
      setOrgValueArr([])
      formRef.setFieldValue('unit', '')
    }
  }

  // 查询表格数据
  const queryTableData = async (newPage?: any) => {
    setTableLoading(true)
    initColumns()
    const values = formRef.getFieldsValue()
    const curOrg = orgValueArr ? orgValueArr[orgValueArr.length - 1] : {}
    const page = newPage || pagination
    const [error, res] = await getEmployeeOrgPag({
      monthId: values?.monthId?.format('YYYYMM'),
      org4: Number(curOrg?.level) === 4 ? curOrg?.orgId : '',
      org5: Number(curOrg?.level) === 5 ? curOrg?.orgId : '',
      org6: Number(curOrg?.level) === 6 ? curOrg?.orgId : '',
      oneClass: values?.oneClass,
      ...page
    })

    setTableLoading(false)
    if (error) {
      return
    }
    if (res.STATUS === '0000') {
      const {
        DATA: { data }
      } = res
      const dataWithKeys = data.map((item, index) => ({
        ...item,
        key: item.id || index, // 使用数据中的唯一字段作为 key，或回退到索引
        ant12: item.ant
      }))
      setTableData(dataWithKeys)
      setPagination({
        ...pagination,
        total: res.DATA?.totalCount
      })
    } else {
      message.error(res?.MESSAGE)
    }

    // const values = formRef.getFieldsValue()
    // const unitId = cascaderSelected?.length > 0 ? values?.unit[values?.unit?.length - 1] : ''
    // const campService =
    //   cascaderSelected?.length > 1 ? cascaderSelected[cascaderSelected?.length - 1].orgName : ''
    // const unit = cascaderSelected?.length > 1 ? cascaderSelected[1].orgName : ''
    // const list = orgTableList.filter(
    //   item =>
    //     item.month === dayjs(values?.month).format('YYYYMM') &&
    //     (unitId === '' ||
    //       item.unit === unit ||
    //       (cascaderSelected?.length > 1 && item?.campService === campService)) &&
    //     (!values?.perClass || item.perClass === values?.perClass)
    // )
    // setTableData(list)
  }

  const initDate = () => {
    const date = dayjs()
    formRef.setFieldsValue({
      monthId: date,
      unit: []
    })
    getEnumTypes()
    // setMonthDate(date)
    // queryTableData()
  }

  const initColumns = () => {
    const date = dayjs(formRef.getFieldValue('monthId'))
    const month = date.month()
    const newColumns = [...columns]
    for (let i = month; i >= 0; i--) {
      const index: string = (month - i + 1)?.toString()
      newColumns.push({
        title: `${date.subtract(i, 'month').format('YYYYMM')}`,
        key: `ant${index.length > 1 ? index : '0' + index}`,
        dataIndex: `ant${index.length > 1 ? index : '0' + index}`,
        align: 'center',
        onCell: undefined,
        width: 65
        // onCell: (record:orgOption,rowIndex:string) => mergeCells(record[`month${month+1}`],tableData,`month${month+1}`,rowIndex),
      })
    }
    setTableColumns(newColumns)
  }

  // 点击查询
  const onFormFinish: FormProps<FieldType>['onFinish'] = () => {
    queryTableData()
  }

  // 切换组织
  const handleOrgChange = (_value, selectedOptions) => {
    setOrgValueArr(selectedOptions)
  }

  // 点击导出
  const exportToExcelFun = async () => {
    try {
      // 调用接口获取 Blob 文件数据
      openNotification('正在导出', 0, 'loading')
      const values = formRef.getFieldsValue()
      // const unit = values?.unit;
      const curOrg = orgValueArr ? orgValueArr[orgValueArr.length - 1] : {}
      const response = await exportEmployeeOrgExcel({
        monthId: values?.monthId?.format('YYYYMM'),
        // org4: unit?.length === 1 ? unit[0] : '',
        // org5: unit?.length === 2 ? unit[1] : '',
        // org6: unit?.length > 2 ? unit[2] : '',
        org4: Number(curOrg?.level) === 4 ? curOrg?.orgId : '',
        org5: Number(curOrg?.level) === 5 ? curOrg?.orgId : '',
        org6: Number(curOrg?.level) === 6 ? curOrg?.orgId : '',
        oneClass: values?.oneClass
        // ...pagination
      })
      downFile(response)
    } catch (error) {
      openNotification('导出失败', 1, 'error')
      console.error('Download failed:', error)
    }
  }
  // 查询重置
  const onReset = () => {
    const newReset = isReset + 1
    formRef.resetFields()
    setOrgValueArr([])
    initDate()
    setIsReset(newReset)
  }

  const filter = (inputValue: string, path: DefaultOptionType[]) =>
    path.some(
      option => (option.orgName as string).toLowerCase().indexOf(inputValue.toLowerCase()) > -1
    )

  // 切换月份选择
  const onMonthChange: DatePickerProps['onChange'] = _date => {
    // setMonthDate(_date)
  }

  // 切换分页
  const onChangePage = page => {
    const newPage = {
      total: page?.total,
      pageNum: page?.current,
      pageSize: page?.pageSize
    }
    setPagination(newPage)
  }

  const onValuesChange = changedValues => {
    if (changedValues.monthId) {
      getEnumTypes()
    }
  }

  return (
    <div className={`${'h-full pt-[0.5rem] flex flex-col'} ${styles.employment_page}`}>
      <div ref={topRef} className={'bg-white pt-[0.5rem] px-8 mb-[0.5rem]'}>
        <Form
          form={formRef}
          initialValues={{
            tag: ''
          }}
          onFinish={onFormFinish}
          onValuesChange={onValuesChange}
          // onFinishFailed={onFinishFailed}
          autoComplete='off'
        >
          <Row
            gutter={24}
            // justify="end"
          >
            <Col span={6}>
              <Form.Item label='月份' name='monthId' wrapperCol={{ span: 20 }}>
                <DatePicker
                  className='w-full'
                  allowClear={false}
                  onChange={onMonthChange}
                  picker='month'
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label='组织' name='unit' wrapperCol={{ span: 20 }}>
                <Cascader
                  allowClear={true}
                  changeOnSelect
                  expandTrigger='hover'
                  // showCheckedStrategy={Cascader.SHOW_CHILD}
                  displayRender={labels => labels[labels.length - 1]}
                  options={orgList}
                  onChange={handleOrgChange}
                  fieldNames={{
                    value: 'orgId',
                    label: 'orgName',
                    children: 'children'
                  }}
                  // onChange={(_, selectedOptions) => {
                  //   setCascaderSelected(selectedOptions)
                  // }}
                  placeholder='请选择'
                  showSearch={{ filter }}
                  onSearch={value => console.log(value)}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label='人员分类' name='oneClass' wrapperCol={{ span: 20 }}>
                <Select
                  placeholder='请选择'
                  className='w-full'
                  allowClear={true}
                  options={perClassOptions}
                />
              </Form.Item>
            </Col>

            <Col span={6}>
              <Form.Item labelCol={{ span: 0 }} wrapperCol={{ span: 24 }}>
                <Space size='small'>
                  <Button type='primary' htmlType='submit'>
                    查询
                  </Button>
                  <Button htmlType='button' onClick={() => onReset()}>
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>
      <div
        ref={contentRef}
        className='relative bg-white px-5 pt-2.5'
        style={{ height: `calc(100% - ${topRef.current?.offsetHeight + 15}px)` }}
      >
        <div
          ref={tableTopRef}
          className={`${'flex justify-between items-center mb-2 overflow-hidden'} ${styles.animation_box} ${showTitle ? 'h-[1.6rem]' : 'h-0'}`}
        >
          <div className={'flex '}>
            {showTitle ? (
              <FullscreenExitOutlined
                className={`${styles.shousuo_icon} text-[1rem]`}
                onClick={() => {
                  setShowTitle(false)
                  setTimeout(() => {
                    initHeight()
                  }, 200)
                }}
              />
            ) : (
              <FullscreenOutlined
                className={`${styles.shousuo_icon} ${'absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10'}`}
                onClick={() => {
                  setShowTitle(true)
                  setTimeout(() => {
                    initHeight()
                  }, 200)
                }}
              />
            )}
            <div className='font-bold text-[0.8rem] ml-3'>数据列表</div>
          </div>

          <Button danger ghost icon={<DownloadOutlined />} onClick={() => exportToExcelFun()}>
            导出
          </Button>
        </div>
        <ResizableTable
          className='report_org_table'
          columns={tableColumns}
          dataSource={tableData}
          loading={tableLoading}
          bordered
          rowClassName={record => {
            if (['合计', '小计', '总计'].includes(record.orgName6)) {
              return styles.highlight_row // 为符合条件的行添加样式
            }
            return styles.customRow
          }}
          scroll={{
            // x: 'max-content',
            y: `calc(${height}px - 0.625rem - 1.6rem - 0.5rem)`
          }}
          onChange={onChangePage}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            pageSizeOptions: ['10', '20', '50']
          }}
        />
      </div>
    </div>
  )
}
export default ReportOrg
