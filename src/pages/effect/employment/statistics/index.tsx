import {
  Button,
  Cascader,
  CascaderProps,
  Col,
  DatePicker,
  Form,
  FormProps,
  GetProp,
  Row,
  Space,
  Tooltip
} from 'antd'
import { DownloadOutlined, FullscreenExitOutlined, FullscreenOutlined } from '@ant-design/icons'
import { useEffect, useRef, useState } from 'react'
import dayjs from 'dayjs'
import { useRequest } from '@/hooks'
import effectService from '@/pages/effect/employment/service.ts'
import { downFile, openNotification } from '@/utils/down.tsx'
import styles from '../Index.module.scss'
import { orgOption } from '@/pages/effect/employment/assest.ts'
import ResizableTable from '@/components/resizeTable/index.jsx'

type DefaultOptionType = GetProp<CascaderProps, 'options'>[number]

type FieldType = {
  month?: string
  unit?: string
  perClass?: string
}

const Statistics: React.FC = () => {
  const columns = [
    // {
    //   title: '月份',
    //   key: 'monthId',
    //   dataIndex: 'monthId',
    //   align: 'center',
    //   fixed: 'left',
    //   width: 50,
    //   children: []
    //   // onCell: (record:orgOption,rowIndex:string) => mergeCells(record?.unit,tableData,'unit',rowIndex),
    // },
    {
      title: '单位',
      key: 'orgName',
      dataIndex: 'orgName',
      align: 'center',
      fixed: 'left',
      width: 150,
      children: [],
      render: (text: string, _record: orgOption) => (
        <Tooltip title={text}>
          <div className={styles.over_ellipsis}>{text}</div>
        </Tooltip>
      )
    },
    {
      title: '定比上一年度末变动人数',
      key: 'list1',
      dataIndex: 'list1',
      align: 'center',
      children: [
        {
          title: '单位变动人数-增加',
          key: 'orgIncMomLydec',
          dataIndex: 'orgIncMomLydec',
          align: 'center',
          children: [],
          width: 130
        },
        {
          title: '单位变动人数-减少',
          key: 'orgDecMomLydec',
          dataIndex: 'orgDecMomLydec',
          align: 'center',
          children: [],
          width: 130
        },
        {
          title: (
            <Tooltip title='不含跨单位'>
              <span>岗位人数</span>
            </Tooltip>
          ),
          key: 'postNum',
          dataIndex: 'postNum',
          align: 'center',
          children: [],
          width: 75
        },
        {
          title: (
            <Tooltip title='不含跨单位'>
              <span>岗位提升人数</span>
            </Tooltip>
          ),
          key: 'postUpMomLydec',
          dataIndex: 'postUpMomLydec',
          align: 'center',
          children: [],
          width: 95
        },
        {
          title: (
            <Tooltip title='不含跨单位'>
              <span>岗位下降人数</span>
            </Tooltip>
          ),
          key: 'postDownMomLydec',
          dataIndex: 'postDownMomLydec',
          align: 'center',
          children: [],
          width: 95
        }
      ]
    },
    {
      title: '环比上月人员变动情况',
      key: 'list2',
      dataIndex: 'list2',
      align: 'center',
      children: [
        {
          title: '单位变动人数-增加',
          key: 'orgIncMom',
          dataIndex: 'orgIncMom',
          align: 'center',
          children: [],
          width: 130
        },
        {
          title: '单位变动人数-减少',
          key: 'orgDecMom',
          dataIndex: 'orgDecMom',
          align: 'center',
          children: [],
          width: 130
        },
        {
          title: (
            <Tooltip title='不含跨单位'>
              <span>岗位人数</span>
            </Tooltip>
          ),
          key: 'postNum',
          dataIndex: 'postNum',
          align: 'center',
          children: [],
          width: 75
        },
        {
          title: (
            <Tooltip title='不含跨单位'>
              <span>岗位提升人数</span>
            </Tooltip>
          ),
          key: 'postUpMom',
          dataIndex: 'postUpMom',
          align: 'center',
          children: [],
          width: 95
        },
        {
          title: (
            <Tooltip title='不含跨单位'>
              <span>岗位下降人数</span>
            </Tooltip>
          ),
          key: 'postDownMom',
          dataIndex: 'postDownMom',
          align: 'center',
          children: [],
          width: 95
        }
      ]
    }
  ]

  const topRef = useRef(null)
  const contentRef = useRef(null)
  const tableTopRef = useRef(null)
  const [formRef] = Form.useForm()
  const [height, setHeight] = useState(0)
  const [orgList, setOrgList] = useState<any>([])
  const [tableData, setTableData] = useState<any>([])
  const [tableColumns, setTableColumns] = useState<any>(columns)
  const [orgValueArr, setOrgValueArr] = useState([])
  const [tableLoading, setTableLoading] = useState<boolean>(false)
  const [isReset, setIsReset] = useState<number>(0)
  const [showTitle, setShowTitle] = useState<boolean>(true)
  const [pagination, setPagination] = useState({
    total: 0,
    pageNum: 1,
    pageSize: 50
  })

  const { runAsync: exportEmployeeChangeExcel } = useRequest(
    effectService.exportEmployeeChangeExcel,
    { manual: true }
  )
  // const { runAsync: getEnumType } = useRequest(peopleService.getEnumType, { manual: true })
  const { runAsync: getEmployeeChangePag } = useRequest(effectService.getEmployeeChangePag, {
    manual: true
  })
  const { runAsync: build4LevelOrgTree2 } = useRequest(effectService.build4LevelOrgTree2, {
    manual: true
  })

  useEffect(() => {
    setTableLoading(false)
    // setTableData(staList)
    if (topRef.current) {
      setHeight(topRef.current.offsetHeight)
    }
    // getEnumTypes();
    initDate()
    queryTableData()
    window.addEventListener('resize', handleResize)
    // 清除事件监听器，防止内存泄漏
    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])
  const handleResize = () => {
    initHeight()
  }

  useEffect(() => {
    if (isReset > 0) {
      queryTableData()
    }
  }, [isReset])

  useEffect(() => {
    initHeight()
  }, [(document.querySelector('.statistics_table .ant-table-header') || {})['offsetHeight']])

  useEffect(() => {
    if (tableData?.length > 0) {
      setTableColumns(columns)
      initHeight()
    }
  }, [tableData])

  useEffect(() => {
    if (pagination?.total > 0) {
      queryTableData()
    }
  }, [pagination.pageNum, pagination.pageSize])

  const initHeight = () => {
    const headerHeight = (document.querySelector('.statistics_table .ant-table-header') || {})[
      'offsetHeight'
    ]
    const pageHeight =
      (document.querySelector('.statistics_table .ant-table-pagination') || {})['offsetHeight'] ||
      26
    if (headerHeight && pageHeight) {
      setHeight(
        contentRef.current?.offsetHeight -
          (tableTopRef.current.offsetHeight + headerHeight + pageHeight)
      )
    }
  }

  const get4LevelOrgTree = async () => {
    const [error, res] = await build4LevelOrgTree2({
      monthId: formRef.getFieldValue('monthId')?.format('YYYYMM'),
      tag: '1'
    })
    if (error) {
      return
    }
    if (res.STATUS === '0000') {
      setOrgList(res.DATA)
      setOrgValueArr([])
      formRef.setFieldValue('orgId', '')
    }
  }

  // const getEnumTypes = async () => {
  //   const [error, res] = await getEnumType({ code: '1010', region: '' })
  //   if (error) {
  //     return
  //   }
  //   if (res.STATUS === '0000') {
  //     setOrgList(res.DATA)
  //   }
  // }

  // 查询表格数据
  const queryTableData = async (page?: any) => {
    setTableLoading(true)
    const newPage = page || pagination
    const values = formRef.getFieldsValue()
    // const orgIdArr = values?.orgId;
    const curOrg = orgValueArr ? orgValueArr[orgValueArr.length - 1] : {}
    const [error, res] = await getEmployeeChangePag({
      ...newPage,
      ...values,
      orgId: undefined,
      orgId4: Number(curOrg?.level) === 4 ? curOrg?.orgId : '',
      orgId5: Number(curOrg?.level) === 5 ? curOrg?.orgId : '',
      monthId: formRef.getFieldsValue()?.monthId?.format('YYYYMM')
    })
    setTableLoading(false)
    if (error) {
      return
    }
    if (res.STATUS === '0000') {
      const {
        DATA: { data }
      } = res
      const dataWithKeys = data.map((item, index) => ({
        ...item,
        key: item.id || index // 使用数据中的唯一字段作为 key，或回退到索引
      }))
      setTableData(dataWithKeys)
      setPagination({
        ...pagination,
        total: res.DATA?.totalCount
      })
    }
  }

  // 初始化查询条件
  const initDate = () => {
    const date = dayjs()
    formRef.setFieldsValue({
      monthId: date,
      orgId: ''
    })
    get4LevelOrgTree()
    // queryTableData()
  }

  const onValuesChange = changedValues => {
    if (changedValues.monthId) {
      get4LevelOrgTree()
    }
  }

  // 点击查询
  const onFormFinish: FormProps<FieldType>['onFinish'] = () => {
    queryTableData()
  }

  // 点击导出
  const exportToExcelFun = async () => {
    try {
      // 调用接口获取 Blob 文件数据
      openNotification('正在导出', 0, 'loading')
      const values = formRef.getFieldsValue()
      const curOrg = orgValueArr ? orgValueArr[orgValueArr.length - 1] : {}
      const response = await exportEmployeeChangeExcel({
        ...values,
        orgId: undefined,
        orgId4: Number(curOrg?.level) === 4 ? curOrg?.orgId : '',
        orgId5: Number(curOrg?.level) === 5 ? curOrg?.orgId : '',
        monthId: formRef.getFieldsValue()?.monthId?.format('YYYYMM')
        // ...pagination
      })
      downFile(response)
    } catch (error) {
      openNotification('导出失败', 1, 'error')
      console.error('Download failed:', error)
    }
  }
  // 查询重置
  const onReset = () => {
    const newReset = isReset + 1
    formRef.resetFields()
    setOrgValueArr([])
    initDate()
    setIsReset(newReset)
  }

  // 切换分页
  const onChangePage = page => {
    const newPage = {
      total: page?.total,
      pageNum: page?.current,
      pageSize: page?.pageSize
    }
    setPagination(newPage)
  }

  // 搜索过滤空格
  const filter = (inputValue: string, path: DefaultOptionType[]) =>
    path.some(
      option =>
        (option.orgName as string).toLowerCase().indexOf(inputValue.trim().toLowerCase()) > -1
    )

  // 切换组织
  const handleOrgChange = (_value, selectedOptions) => {
    setOrgValueArr(selectedOptions)
  }

  // const handleSearchFilter = (input, option) => {
  //   const cleanedInput = input.trim() // 去除输入中的空格
  //   return option.children.toLowerCase().includes(cleanedInput.toLowerCase())
  // }

  return (
    <div className={`${'h-full pt-[0.5rem] flex flex-col'} ${styles.employment_page}`}>
      <div ref={topRef} className={'bg-white pt-[0.5rem] px-8 mb-[0.5rem]'}>
        <Form
          form={formRef}
          initialValues={{
            tag: ''
          }}
          onFinish={onFormFinish}
          onValuesChange={onValuesChange}
          // onFinishFailed={onFinishFailed}
          autoComplete='off'
        >
          <Row
            gutter={24}
            // justify="end"
          >
            <Col span={6}>
              <Form.Item label='月份' name='monthId' wrapperCol={{ span: 20 }}>
                <DatePicker className='w-full' picker='month' />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label='单位' name='orgId' wrapperCol={{ span: 20 }}>
                <Cascader
                  allowClear={true}
                  changeOnSelect
                  expandTrigger='hover'
                  displayRender={labels => labels[labels.length - 1]}
                  options={orgList}
                  onChange={handleOrgChange}
                  fieldNames={{
                    value: 'orgId',
                    label: 'orgName',
                    children: 'children'
                  }}
                  // showCheckedStrategy={Cascader.SHOW_CHILD}
                  // onChange={(value, selectedOptions) => {
                  //   console.log(value, selectedOptions)
                  //   setCascaderSelected(selectedOptions)
                  // }}
                  placeholder='请选择'
                  showSearch={{ filter }}
                  onSearch={value => console.log(value)}
                />
              </Form.Item>
            </Col>
            {/*<Col span={6}>*/}
            {/*  <Form.Item label="人员分类" name="perClass"*/}
            {/*             wrapperCol={{span: 20}}>*/}
            {/*    <Select className="w-full" options={perClassOptions}/>*/}
            {/*  </Form.Item>*/}
            {/*</Col>*/}

            <Col span={6}>
              <Form.Item labelCol={{ span: 0 }} wrapperCol={{ span: 24 }}>
                <Space size='small'>
                  <Button type='primary' htmlType='submit'>
                    查询
                  </Button>
                  <Button htmlType='button' onClick={() => onReset()}>
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>
      <div
        ref={contentRef}
        className='relative bg-white px-5 pt-2.5'
        style={{ height: `calc(100% - ${topRef.current?.offsetHeight + 15}px)` }}
      >
        <div
          ref={tableTopRef}
          className={`${'flex justify-between items-center mb-2 overflow-hidden'} ${styles.animation_box} ${showTitle ? 'h-[1.6rem]' : 'h-0'}`}
        >
          <div className={'flex '}>
            {showTitle ? (
              <FullscreenExitOutlined
                className={`${styles.shousuo_icon} text-[1rem]`}
                onClick={() => {
                  setShowTitle(false)
                  setTimeout(() => {
                    initHeight()
                  }, 200)
                }}
              />
            ) : (
              <FullscreenOutlined
                className={`${styles.shousuo_icon} ${'absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10'}`}
                onClick={() => {
                  setShowTitle(true)
                  setTimeout(() => {
                    initHeight()
                  }, 200)
                }}
              />
            )}
            <div className='font-bold text-[0.8rem] ml-3'>数据列表</div>
          </div>
          <Button danger ghost icon={<DownloadOutlined />} onClick={() => exportToExcelFun()}>
            导出
          </Button>
        </div>
        <ResizableTable
          className='statistics_table'
          rowClassName={(_, i) => (i % 2 === 1 ? `customRow odd` : `customRow even`)}
          columns={tableColumns}
          dataSource={tableData}
          loading={tableLoading}
          bordered
          scroll={{
            // x: 'max-content',
            y: `calc(${height}px - 0.625rem - 1.6rem - 0.5rem)`
          }}
          onChange={onChangePage}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            pageSizeOptions: ['10', '20', '50']
          }}
        />
      </div>
    </div>
  )
}
export default Statistics
