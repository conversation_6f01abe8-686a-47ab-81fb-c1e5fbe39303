interface selectOption {
  value: string
  label: string
}

export interface orgOption {
  rowno: string
  orgId6: string
  orgName5: string
  monthId: string
  orgName6: string
  oneClass: string
  twoClass: string
  ant: string
  antHt: string
  antLp: string
  antJm: string
  antQt: string
  month1?: string
  month2?: string
  month3?: string
  month4?: string
  month5?: string
  month6?: string
  month7?: string
  month8?: string
  month9?: string
  month10?: string
  month11?: string
  month12?: string
}

export interface perOption {
  unit: string
  campService: string
  month: string
  employeeId: string
  employeeName: string
  curPost: string
  postClass: string

  unit1?: string
  unit2?: string
  unit3?: string
  unit4?: string
  unit5?: string
  unit6?: string
  unit7?: string
  unit8?: string
  unit9?: string
  unit10?: string
  unit11?: string
  unit12?: string

  post1?: string
  post2?: string
  post3?: string
  post4?: string
  post5?: string
  post6?: string
  post7?: string
  post8?: string
  post9?: string
  post10?: string
  post11?: string
  post12?: string

  postClass1?: string
  postClass2?: string
  postClass3?: string
  postClass4?: string
  postClass5?: string
  postClass6?: string
  postClass7?: string
  postClass8?: string
  postClass9?: string
  postClass10?: string
  postClass11?: string
  postClass12?: string
}

export interface staOption {
  unit: string
  month: string
  unitGain: string
  unitLose: string
  post: string
  postGain: string
  postLose: string
  unitGainMoth?: string
  unitLoseMoth?: string
  postMoth?: string
  postGainMoth?: string
  postLoseMoth?: string
}

export const perClassOptions: selectOption[] = [
  {
    value: '专业线',
    label: '专业线'
  },
  {
    value: '岗位',
    label: '岗位'
  },
  {
    value: '重点岗位',
    label: '重点岗位'
  }
]
