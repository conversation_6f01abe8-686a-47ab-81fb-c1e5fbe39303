import {
  <PERSON>ton,
  Cascader,
  CascaderProps,
  Col,
  DatePicker,
  Form,
  FormProps,
  GetProp,
  Row,
  Space,
  Tooltip,
  Select,
  message
} from 'antd'
import { DownloadOutlined, FullscreenExitOutlined, FullscreenOutlined } from '@ant-design/icons'
import { useEffect, useRef, useState } from 'react'
import dayjs from 'dayjs'
import { useRequest } from '@/hooks'
import effectService from '@/pages/effect/employment/service.ts'
import { downFile, openNotification } from '@/utils/down.tsx'
import styles from '../Index.module.scss'
import { orgOption } from '@/pages/effect/employment/assest.ts'
import ResizableTable from '@/components/resizeTable/index.jsx'

type DefaultOptionType = GetProp<CascaderProps, 'options'>[number]

type FieldType = {
  monthId?: string
  orgId?: string
  ratio?: string
  alteration?: string
}

const Statistics: React.FC = () => {
  const columns = [
    {
      title: '单位',
      key: 'orgName',
      dataIndex: 'orgName',
      align: 'center',
      width: 150,
      children: [],
      render: (text: string, _record: orgOption) => (
        <Tooltip title={text}>
          <div className={styles.over_ellipsis}>{text}</div>
        </Tooltip>
      )
    },
    {
      title: '员工编码',
      key: 'employeeId',
      dataIndex: 'employeeId',
      align: 'center',
      width: 100
    },
    {
      title: '姓名',
      key: 'employeeName',
      dataIndex: 'employeeName',
      align: 'center',
      width: 100
    },
    {
      title: '原单位',
      key: 'originalUnit',
      dataIndex: 'originalUnit',
      align: 'center',
      width: 100
    },
    {
      title: '现单位',
      key: 'presentUnit',
      dataIndex: 'presentUnit',
      align: 'center',
      width: 100
    },
    {
      title: '原岗位名称',
      key: 'originalPostName',
      dataIndex: 'originalPostName',
      align: 'center',
      width: 100
    },
    {
      title: '现岗位名称',
      key: 'presentPostName',
      dataIndex: 'presentPostName',
      align: 'center',
      width: 100
    },
    {
      title: '原岗级',
      key: 'originalPostLevel',
      dataIndex: 'originalPostLevel',
      align: 'center',
      width: 100
    },
    {
      title: '现岗级',
      key: 'presentPostLevel',
      dataIndex: 'presentPostLevel',
      align: 'center',
      width: 100
    }
  ]

  const topRef = useRef(null)
  const contentRef = useRef(null)
  const tableTopRef = useRef(null)
  const [formRef] = Form.useForm()
  const [height, setHeight] = useState(0)
  const [orgList, setOrgList] = useState<any>([])
  const [tableData, setTableData] = useState<any>([])
  const [tableColumns, setTableColumns] = useState<any>(columns)
  const [orgValueArr, setOrgValueArr] = useState([])
  const [tableLoading, setTableLoading] = useState<boolean>(false)
  const [isReset, setIsReset] = useState<number>(0)
  const [showTitle, setShowTitle] = useState<boolean>(true)
  const [pagination, setPagination] = useState({
    total: 0,
    pageNum: 1,
    pageSize: 50
  })

  const { runAsync: exportStatisticDetailExcel } = useRequest(
    effectService.exportStatisticDetailExcel,
    { manual: true }
  )
  // const { runAsync: getEnumType } = useRequest(peopleService.getEnumType, { manual: true })
  const { runAsync: getEmployeeChangeDetailPag } = useRequest(
    effectService.getEmployeeChangeDetailPag,
    {
      manual: true
    }
  )
  const { runAsync: build4LevelOrgTree2 } = useRequest(effectService.build4LevelOrgTree2, {
    manual: true
  })

  useEffect(() => {
    setTableLoading(false)
    // setTableData(staList)
    if (topRef.current) {
      setHeight(topRef.current.offsetHeight)
    }
    // getEnumTypes();
    initDate(true)
    // queryTableData()
    window.addEventListener('resize', handleResize)
    // 清除事件监听器，防止内存泄漏
    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])
  const handleResize = () => {
    initHeight()
  }

  useEffect(() => {
    if (isReset > 0) {
      queryTableData()
    }
  }, [isReset])

  useEffect(() => {
    initHeight()
  }, [(document.querySelector('.statistics_detail_table .ant-table-header') || {})['offsetHeight']])

  useEffect(() => {
    if (tableData?.length > 0) {
      const type = formRef.getFieldsValue()?.alteration
      if (['unitChange-up', 'unitChange-down'].includes(type)) {
        //单位变动
        setTableColumns(
          columns.map(item => ({
            ...item,
            hidden: [
              'originalPostName',
              'presentPostName',
              'originalPostLevel',
              'presentPostLevel'
            ].includes(item.key)
          }))
        )
      } else {
        //岗位变动
        setTableColumns(
          columns.map(item => ({
            ...item,
            hidden: ['originalUnit', 'presentUnit'].includes(item.key)
          }))
        )
      }

      initHeight()
    }
  }, [tableData])

  useEffect(() => {
    if (pagination?.total > 0) {
      queryTableData()
    }
  }, [pagination.pageNum, pagination.pageSize])

  const initHeight = () => {
    const headerHeight = (document.querySelector('.statistics_detail_table .ant-table-header') ||
      {})['offsetHeight']
    const pageHeight =
      (document.querySelector('.statistics_detail_table .ant-table-pagination') || {})[
        'offsetHeight'
      ] || 26
    if (headerHeight && pageHeight) {
      setHeight(
        contentRef.current?.offsetHeight -
          (tableTopRef.current.offsetHeight + headerHeight + pageHeight)
      )
    }
  }

  const get4LevelOrgTree = async () => {
    const [error, res] = await build4LevelOrgTree2({
      monthId: formRef.getFieldValue('monthId')?.format('YYYYMM'),
      tag: '1'
    })
    if (error) {
      return
    }
    if (res.STATUS === '0000') {
      setOrgList(res.DATA)
      setOrgValueArr([])
      formRef.setFieldValue('orgId', '')
    }
  }

  // const getEnumTypes = async () => {
  //   const [error, res] = await getEnumType({ code: '1010', region: '' })
  //   if (error) {
  //     return
  //   }
  //   if (res.STATUS === '0000') {
  //     setOrgList(res.DATA)
  //   }
  // }

  // 查询表格数据
  const queryTableData = async (newPage?: any) => {
    setTableLoading(true)
    const page = newPage || pagination
    const values = formRef.getFieldsValue()
    // const orgIdArr = values?.orgId;
    const curOrg = orgValueArr ? orgValueArr[orgValueArr.length - 1] : {}
    const type = values.alteration.split('-')
    const [error, res] = await getEmployeeChangeDetailPag({
      map: {
        monthId: formRef.getFieldsValue()?.monthId?.format('YYYYMM'),
        orgId: undefined,
        orgId4: Number(curOrg?.level) === 4 ? curOrg?.orgId : '',
        orgId5: Number(curOrg?.level) === 5 ? curOrg?.orgId : '',
        kind: values.ratio,
        unitChange: type[0] === 'unitChange' ? type[1] : undefined,
        postChange: type[0] === 'postChange' ? type[1] : undefined
      },
      pagination: {
        pageNum: page.pageNum,
        pageSize: page.pageSize
      }
    })
    setTableLoading(false)
    if (error) {
      return
    }
    if (res.STATUS === '0000') {
      const {
        DATA: { data }
      } = res
      const dataWithKeys =
        data?.map((item, index) => ({
          ...item,
          key: item.id || index // 使用数据中的唯一字段作为 key，或回退到索引
        })) || []
      setTableData(dataWithKeys)
      setPagination({
        ...pagination,
        total: res.DATA?.totalCount
      })
    } else {
      message.error(res?.MESSAGE)
    }
  }

  // 初始化查询条件
  const initDate = (isInit?: boolean) => {
    const date = dayjs()
    formRef.setFieldsValue({
      monthId: date,
      orgId: ''
    })
    get4LevelOrgTree()
    if (isInit) {
      queryTableData()
    }
  }

  const onValuesChange = changedValues => {
    if (changedValues.monthId) {
      get4LevelOrgTree()
    }
  }

  // 点击查询
  const onFormFinish: FormProps<FieldType>['onFinish'] = () => {
    queryTableData()
  }

  // 点击导出
  const exportToExcelFun = async () => {
    try {
      // 调用接口获取 Blob 文件数据
      openNotification('正在导出', 0, 'loading')
      const values = formRef.getFieldsValue()
      const curOrg = orgValueArr ? orgValueArr[orgValueArr.length - 1] : {}
      const type = values.alteration.split('-')
      const response = await exportStatisticDetailExcel({
        map: {
          monthId: formRef.getFieldsValue()?.monthId?.format('YYYYMM'),
          orgId: undefined,
          orgId4: Number(curOrg?.level) === 4 ? curOrg?.orgId : '',
          orgId5: Number(curOrg?.level) === 5 ? curOrg?.orgId : '',
          kind: values.ratio,
          unitChange: type[0] === 'unitChange' ? type[1] : undefined,
          postChange: type[0] === 'postChange' ? type[1] : undefined
        }
      })
      downFile(response)
    } catch (error) {
      openNotification('导出失败', 1, 'error')
      console.error('Download failed:', error)
    }
  }
  // 查询重置
  const onReset = () => {
    const newReset = isReset + 1
    formRef.resetFields()
    setOrgValueArr([])
    initDate()
    setIsReset(newReset)
  }

  // 切换分页
  const onChangePage = page => {
    const newPage = {
      total: page?.total,
      pageNum: page?.current,
      pageSize: page?.pageSize
    }
    setPagination(newPage)
  }

  // 搜索过滤空格
  const filter = (inputValue: string, path: DefaultOptionType[]) =>
    path.some(
      option =>
        (option.orgName as string).toLowerCase().indexOf(inputValue.trim().toLowerCase()) > -1
    )

  // 切换组织
  const handleOrgChange = (_value, selectedOptions) => {
    setOrgValueArr(selectedOptions)
  }

  // const handleSearchFilter = (input, option) => {
  //   const cleanedInput = input.trim() // 去除输入中的空格
  //   return option.children.toLowerCase().includes(cleanedInput.toLowerCase())
  // }

  return (
    <div className={`${'h-full pt-[0.5rem] flex flex-col'} ${styles.employment_page}`}>
      <div ref={topRef} className={'bg-white pt-[0.5rem] px-8 mb-[0.5rem]'}>
        <Form
          form={formRef}
          initialValues={{
            tag: '',
            ratio: 'HB',
            alteration: 'unitChange-up'
          }}
          onFinish={onFormFinish}
          onValuesChange={onValuesChange}
          // onFinishFailed={onFinishFailed}
          autoComplete='off'
        >
          <Row
            gutter={24}
            // justify="end"
          >
            <Col span={4}>
              <Form.Item label='月份' name='monthId' wrapperCol={{ span: 20 }}>
                <DatePicker className='w-full' picker='month' />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label='单位' name='orgId' wrapperCol={{ span: 20 }}>
                <Cascader
                  allowClear={true}
                  changeOnSelect
                  expandTrigger='hover'
                  displayRender={labels => labels[labels.length - 1]}
                  options={orgList}
                  onChange={handleOrgChange}
                  fieldNames={{
                    value: 'orgId',
                    label: 'orgName',
                    children: 'children'
                  }}
                  placeholder='请选择'
                  showSearch={{ filter }}
                  onSearch={value => console.log(value)}
                />
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item label='环比定比' name='ratio' wrapperCol={{ span: 20 }}>
                <Select
                  placeholder='请选择'
                  className='w-full'
                  options={[
                    { label: '环比', value: 'HB' },
                    { label: '定比', value: 'DB' }
                  ]}
                />
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item label='变动' name='alteration' wrapperCol={{ span: 20 }}>
                <Select
                  placeholder='请选择'
                  className='w-full'
                  options={[
                    { label: '单位变动-增加', value: 'unitChange-up' },
                    { label: '单位变动-减少', value: 'unitChange-down' },
                    { label: '岗位变动-提升', value: 'postChange-up' },
                    { label: '岗位变动-下降', value: 'postChange-down' }
                  ]}
                />
              </Form.Item>
            </Col>

            <Col span={6}>
              <Form.Item labelCol={{ span: 0 }} wrapperCol={{ span: 24 }}>
                <Space size='small'>
                  <Button type='primary' htmlType='submit'>
                    查询
                  </Button>
                  <Button htmlType='button' onClick={() => onReset()}>
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>
      <div
        ref={contentRef}
        className='relative bg-white px-5 pt-2.5'
        style={{ height: `calc(100% - ${topRef.current?.offsetHeight + 15}px)` }}
      >
        <div
          ref={tableTopRef}
          className={`${'flex justify-between items-center mb-2 overflow-hidden'} ${styles.animation_box} ${showTitle ? 'h-[1.6rem]' : 'h-0'}`}
        >
          <div className={'flex '}>
            {showTitle ? (
              <FullscreenExitOutlined
                className={`${styles.shousuo_icon} text-[1rem]`}
                onClick={() => {
                  setShowTitle(false)
                  setTimeout(() => {
                    initHeight()
                  }, 200)
                }}
              />
            ) : (
              <FullscreenOutlined
                className={`${styles.shousuo_icon} ${'absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10'}`}
                onClick={() => {
                  setShowTitle(true)
                  setTimeout(() => {
                    initHeight()
                  }, 200)
                }}
              />
            )}
            <div className='font-bold text-[0.8rem] ml-3'>数据列表</div>
          </div>
          <Button danger ghost icon={<DownloadOutlined />} onClick={() => exportToExcelFun()}>
            导出
          </Button>
        </div>
        <ResizableTable
          className='statistics_detail_table'
          rowClassName={(_, i) => (i % 2 === 1 ? `customRow odd` : `customRow even`)}
          columns={tableColumns}
          dataSource={tableData}
          loading={tableLoading}
          bordered
          scroll={{
            // x: 'max-content',
            y: `calc(${height}px - 0.625rem - 1.6rem - 0.5rem)`
          }}
          onChange={onChangePage}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            pageSizeOptions: ['10', '20', '50']
          }}
        />
      </div>
    </div>
  )
}
export default Statistics
