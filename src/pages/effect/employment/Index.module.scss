.employment_page {
  // :global {
  //   .ant-table-row {
  //     height: 22px !important;
  //     line-height: 22px !important;
  //     > td {
  //       padding: 0 !important;
  //     }
  //   }

  //   .ant-form-item{
  //     margin-bottom: 0.5rem !important;
  //   }

  //   .ant-table-thead > tr > th {
  //     padding-top: 2px; /* 设置表头单元格的上间距 */
  //     padding-bottom: 2px; /* 设置表头单元格的下间距 */
  //   }
  // }

  .animation_box {
    transition: height 0.2s ease;
  }
  
  .over_ellipsis {
    white-space: nowrap;         /* 不允许换行 */
    overflow: hidden;            /* 隐藏超出部分 */
    text-overflow: ellipsis;     /* 使用省略号显示超出文本 */
  }
  
  .highlight_row {
    > td {
      background-color: rgba(245, 247, 250, 1) !important;
      color: rgba(0,0,0,0.65);
    }
  }
}


