import {
  Button,
  Cascader,
  CascaderProps,
  Col,
  DatePicker,
  DatePickerProps,
  Form,
  FormProps,
  GetProp,
  Input,
  message,
  Row,
  Select,
  Space,
  Tooltip
} from 'antd'
import dayjs from 'dayjs'
import { DownloadOutlined, FullscreenExitOutlined, FullscreenOutlined } from '@ant-design/icons'
import { useEffect, useRef, useState } from 'react'
import { useRequest } from '@/hooks'
import peopleService from '@/pages/position/people/service.ts'
import effectService from '@/pages/effect/employment/service.ts'
import { Enum } from '@/pages/position/people/interface.ts'
import { downFile, openNotification } from '@/utils/down.tsx'
import styles from '../Index.module.scss'
import { orgOption } from '@/pages/effect/employment/assest.ts'
import ResizableTable from '@/components/resizeTable/index.jsx'

type DefaultOptionType = GetProp<CascaderProps, 'options'>[number]

type FieldType = {
  month?: string
  unit?: string
  curPost?: string
}

const ReportPer: React.FC = () => {
  const columns = [
    // {
    //   title: '月份',
    //   key: 'month',
    //   dataIndex: 'month',
    //   align: 'center',
    //   fixed: 'left',
    //   width: 50,
    //   children: []
    // },
    {
      title: '单位',
      key: 'unit',
      dataIndex: 'unit',
      align: 'center',
      fixed: 'left',
      width: 150,
      children: [],
      render: (text: string, _record: orgOption) => (
        <Tooltip title={text}>
          <div className={styles.over_ellipsis}>{text}</div>
        </Tooltip>
      )
    },
    {
      title: '部门',
      key: 'business',
      dataIndex: 'business',
      align: 'center',
      fixed: 'left',
      width: 200,
      children: [],
      render: (text: string, _record: orgOption) => (
        <Tooltip title={text}>
          <div className={styles.over_ellipsis}>
            {text && text.startsWith('天津市分公司') ? text.slice('天津市分公司'.length) : text}
          </div>
        </Tooltip>
      )
    },
    {
      title: '员工编号',
      key: 'code',
      dataIndex: 'code',
      align: 'center',
      fixed: 'left',
      width: 75,
      children: []
    },
    {
      title: '员工姓名',
      key: 'name',
      dataIndex: 'name',
      align: 'center',
      fixed: 'left',
      width: 75,
      children: []
    },
    {
      title: '当前岗位',
      key: 'post',
      dataIndex: 'post',
      align: 'center',
      width: 110,
      children: [],
      render: (text: string, _record: orgOption) => (
        <Tooltip title={text}>
          <div className={styles.over_ellipsis}>{text}</div>
        </Tooltip>
      )
    },
    {
      title: '岗级',
      key: 'level',
      dataIndex: 'level',
      align: 'center',
      children: [],
      width: 50
    }
  ]

  const topRef = useRef(null)
  const contentRef = useRef(null)
  const tableTopRef = useRef(null)
  const [formRef] = Form.useForm()
  // const [cascaderSelected, setCascaderSelected] = useState<any>([])
  const [height, setHeight] = useState(0)
  // const [monthDate, setMonthDate] = useState<any>('')
  const [tableData, setTableData] = useState<any>([])
  const [tableLoading, setTableLoading] = useState<boolean>(false)
  const [tableColumns, setTableColumns] = useState<any>(columns)
  const [postList, setPostList] = useState<Enum[]>([])
  const [orgValue, setOrgValue] = useState(formRef.getFieldValue([]))
  const [orgValueArr, setOrgValueArr] = useState([])
  const [isReset, setIsReset] = useState<number>(0)
  const [orgList, setOrgList] = useState<any>([])
  const [showTitle, setShowTitle] = useState<boolean>(true)
  const [pagination, setPagination] = useState({
    total: 0,
    pageNum: 1,
    pageSize: 50
  })

  const { runAsync: getEnumType } = useRequest(peopleService.getEnumType, { manual: true })
  const { runAsync: build4LevelOrgTree } = useRequest(effectService.build4LevelOrgTree, {
    manual: true
  })
  const { runAsync: getEmployeePerPag } = useRequest(effectService.getEmployeePerPag, {
    manual: true
  })
  const { runAsync: exportEmployeePerExcel } = useRequest(effectService.exportEmployeePerExcel, {
    manual: true
  })

  useEffect(() => {
    getEnumTypes()
    initDate()
    queryTableData()
    // 添加 resize 事件监听器
    window.addEventListener('resize', handleResize)
    // 清除事件监听器，防止内存泄漏
    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])
  const handleResize = () => {
    initHeight()
  }

  useEffect(() => {
    initHeight()
  }, [(document.querySelector('.report_personal_table .ant-table-header') || {})['offsetHeight']])

  // useEffect(() => {
  //   if (monthDate) {
  //     initColumns()
  //   }
  // }, [monthDate])

  useEffect(() => {
    initHeight()
  }, [tableData])

  useEffect(() => {
    if (isReset > 0) {
      queryTableData()
    }
  }, [isReset])

  useEffect(() => {
    if (pagination?.total > 0) {
      queryTableData()
    }
  }, [pagination.pageNum, pagination.pageSize])

  const initHeight = () => {
    const headerHeight = (document.querySelector('.report_personal_table .ant-table-header') || {})[
      'offsetHeight'
    ]
    const pageHeight =
      (document.querySelector('.report_personal_table .ant-table-pagination') || {})[
        'offsetHeight'
      ] || 26
    if (headerHeight && pageHeight) {
      setHeight(
        contentRef.current?.offsetHeight -
          (tableTopRef.current.offsetHeight + headerHeight + pageHeight)
      )
    }
  }

  const getEnumTypes = async () => {
    const [error, res] = await getEnumType({ code: '1014', region: '' })
    if (error) {
      return
    }
    if (res.STATUS === '0000') {
      setPostList(res.DATA)
    }
  }

  const getOrgList = async () => {
    const [error, res] = await build4LevelOrgTree({
      monthId: formRef.getFieldValue('month')?.format('YYYYMM'),
      tag: '2'
    })
    if (error) {
      return
    }
    if (res.STATUS === '0000') {
      setOrgList(res.DATA)
      setOrgValueArr([])
      formRef.setFieldValue('org', '')
    }
  }

  // 查询表格数据
  const queryTableData = async (newPage?: any) => {
    setTableLoading(true)
    initColumns()
    const values = formRef.getFieldsValue()
    const page = newPage || pagination
    const curOrg = orgValueArr ? orgValueArr[orgValueArr.length - 1] : {}
    const [error, res] = await getEmployeePerPag({
      ...values,
      month: values?.month?.format('YYYYMM'),
      // org: values?.org,
      orgId4: Number(curOrg?.level) === 4 ? curOrg?.orgId : '',
      orgId5: Number(curOrg?.level) === 5 ? curOrg?.orgId : '',
      orgId6: Number(curOrg?.level) === 6 ? curOrg?.orgId : '',
      post: values?.post?.split(',')[0],
      ...page
    })
    setTableLoading(false)
    if (error) {
      return
    }
    if (res.STATUS === '0000') {
      const {
        DATA: { data }
      } = res
      const dates = dayjs(formRef.getFieldValue('month')).format('YYYYMM')
      const dataWithKeys = data.map((item, index) => ({
        ...item,
        key: item.id || index, // 使用数据中的唯一字段作为 key，或回退到索引
        [`unit${dates}`]: item.unit,
        [`post${dates}`]: item.post,
        [`level${dates}`]: item.level
      }))
      setTableData(dataWithKeys)
      setPagination({
        ...pagination,
        total: res.DATA?.totalCount
      })
    } else {
      message.error(res?.MESSAGE)
    }
  }

  // 初始化查询条件
  const initDate = () => {
    const date = dayjs()
    formRef.setFieldsValue({
      month: date,
      org: []
    })
    getOrgList()
    // setMonthDate(date)
  }

  const initColumns = () => {
    const date = dayjs(formRef.getFieldValue('month'))
    const month = date.month()
    const newColumns = [...columns]
    for (let i = month + 1; i >= 0; i--) {
      const name = `${date.subtract(i, 'month').format('YYYYMM')}`
      newColumns.push({
        title: name,
        key: `month${name}`,
        dataIndex: `month${name}`,
        align: 'center',
        width: 500,
        children: [
          {
            title: '单位',
            key: `unit${name}`,
            dataIndex: `unit${name}`,
            align: 'center',
            width: 150,
            render: (text: string, _record: orgOption) => (
              <Tooltip title={text}>
                <div className={styles.over_ellipsis}>{text}</div>
              </Tooltip>
            )
          },
          {
            title: '岗位',
            key: `post${name}`,
            dataIndex: `post${name}`,
            align: 'center',
            width: 110,
            render: (text: string, _record: orgOption) => (
              <Tooltip title={text}>
                <div className={styles.over_ellipsis}>{text}</div>
              </Tooltip>
            )
          },
          {
            title: '岗级',
            key: `level${name}`,
            dataIndex: `level${name}`,
            align: 'center',
            width: 50
          }
        ]
        // onCell: (record:orgOption,rowIndex:string) => mergeCells(record[`month${month+1}`],tableData,`month${month+1}`,rowIndex),
      })
    }
    setTableColumns(newColumns)
  }

  const onFormChange = () => {
    setOrgValue(formRef.getFieldValue('org'))
  }

  // 切换组织
  const handleOrgChange = (_value, selectedOptions) => {
    setOrgValueArr(selectedOptions)
  }

  // 点击查询
  const onFormFinish: FormProps<FieldType>['onFinish'] = values => {
    console.log('Success:', values)
    queryTableData()
  }
  // 点击导出
  const exportToExcelFun = async () => {
    try {
      // 调用接口获取 Blob 文件数据
      openNotification('正在导出', 0, 'loading')
      const values = formRef.getFieldsValue()
      const curOrg = orgValueArr ? orgValueArr[orgValueArr.length - 1] : {}
      const response = await exportEmployeePerExcel({
        ...values,
        orgId4: Number(curOrg?.level) === 4 ? curOrg?.orgId : '',
        orgId5: Number(curOrg?.level) === 5 ? curOrg?.orgId : '',
        orgId6: Number(curOrg?.level) === 6 ? curOrg?.orgId : '',
        month: values?.month?.format('YYYYMM')
        // ...pagination
      })
      downFile(response)
    } catch (error) {
      openNotification('导出失败', 1, 'error')
      console.error('Download failed:', error)
    }

    // fetch('/efop/file/人员统计报表（个人）.xls', {
    //   method: 'GET',
    //   headers: {
    //     'Content-Type': 'application/vnd.ms-excel' // MIME 类型
    //   },
    //   credentials: 'include' // 如果需要发送 cookie
    // })
    //   .then(response => {
    //     if (!response.ok) {
    //       throw new Error('Network response was not ok')
    //     }
    //     return response.blob() // 将响应转换为 Blob
    //   })
    //   .then(blob => {
    //     const link = document.createElement('a')
    //     link.href = window.URL.createObjectURL(blob) // 创建 Blob URL
    //     link.download = '人员统计报表（个人）.xls' // 下载文件时显示的文件名
    //     link.click() // 模拟点击下载
    //   })
    //   .catch(error => {
    //     console.error('Download error:', error)
    //   })
  }
  // 查询重置
  const onReset = () => {
    const newReset = isReset + 1
    formRef.resetFields()
    setOrgValueArr([])
    initDate()
    setIsReset(newReset)
  }

  const filter = (inputValue: string, path: DefaultOptionType[]) =>
    path.some(
      option =>
        (option.orgName as string).toLowerCase().indexOf(inputValue.trim().toLowerCase()) > -1
    )

  // 切换月份选择
  const onMonthChange: DatePickerProps['onChange'] = (date, dateString) => {
    console.log(date, dateString)
    // setMonthDate(date)
  }

  // 切换分页
  const onChangePage = page => {
    const newPage = {
      total: page?.total,
      pageNum: page?.current,
      pageSize: page?.pageSize
    }
    setPagination(newPage)
  }

  // 搜索过滤空格
  const handleSearchFilter = (input, option) => {
    const cleanedInput = input.trim() // 去除输入中的空格
    return option.children.toLowerCase().includes(cleanedInput.toLowerCase())
  }

  const onValuesChange = changedValues => {
    if (changedValues.month) {
      getOrgList()
    }
  }

  return (
    <div className={`${'h-full pt-[0.5rem] flex flex-col'} ${styles.employment_page}`}>
      <div ref={topRef} className={'bg-white pt-[0.5rem] px-8 mb-[0.5rem]'}>
        <Form
          form={formRef}
          initialValues={{
            tag: ''
          }}
          wrapperCol={{ span: 20 }}
          onFieldsChange={onFormChange}
          onFinish={onFormFinish}
          onValuesChange={onValuesChange}
          // onFinishFailed={onFinishFailed}
          autoComplete='off'
        >
          <Row
            gutter={24}
            // justify="end"
          >
            <Col span={6}>
              <Form.Item label='月份' name='month'>
                <DatePicker
                  className='w-full'
                  allowClear={false}
                  onChange={onMonthChange}
                  picker='month'
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label='组织' name='org'>
                <Cascader
                  allowClear={true}
                  changeOnSelect
                  expandTrigger='hover'
                  displayRender={labels => labels[labels.length - 1]}
                  options={orgList}
                  onChange={handleOrgChange}
                  fieldNames={{
                    value: 'orgId',
                    label: 'orgName',
                    children: 'children'
                  }}
                  // showCheckedStrategy={Cascader.SHOW_CHILD}
                  // onChange={(value, selectedOptions) => {
                  //   console.log(value, selectedOptions)
                  //   setCascaderSelected(selectedOptions)
                  // }}
                  placeholder='请选择'
                  showSearch={{ filter }}
                  onSearch={value => console.log(value)}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label='员工姓名' name='name'>
                <Input placeholder={'请输入员工姓名'} allowClear />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label='员工编号' name='code'>
                <Input placeholder={'请输入员工编号'} allowClear />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label='岗位' name='post'>
                {orgValue && orgValue[0] === '49757' ? (
                  <Select
                    placeholder={'请选择'}
                    className='w-full'
                    allowClear={true}
                    showSearch
                    filterOption={handleSearchFilter}
                  >
                    {
                      // prettier-ignore
                      postList.map(unit => {
                              const {region, enumName} = unit;
                              return <Select.Option key={`${enumName},${region}`}
                                                    value={`${enumName},${region}`}>{enumName}</Select.Option>
                            })
                    }
                  </Select>
                ) : (
                  <Input placeholder={'请输入'} allowClear />
                )}
                {/*{*/}
                {/*    formRef.getFieldValue('org') && ['49682', '49746'].includes(formRef.getFieldValue('org')[0]) &&*/}
                {/*      <Input placeholder={'请输入'}/>*/}
                {/*}*/}
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item labelCol={{ span: 0 }} wrapperCol={{ span: 24 }}>
                <Space size='small'>
                  <Button type='primary' htmlType='submit'>
                    查询
                  </Button>
                  <Button htmlType='button' onClick={() => onReset()}>
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>
      <div
        ref={contentRef}
        className='relative bg-white px-5 pt-2.5'
        style={{ height: `calc(100% - ${topRef.current?.offsetHeight + 15}px)` }}
      >
        <div
          ref={tableTopRef}
          className={`${'flex justify-between items-center mb-2 overflow-hidden'} ${styles.animation_box} ${showTitle ? 'h-[2rem]' : 'h-0'}`}
        >
          <div className={'flex '}>
            {showTitle ? (
              <FullscreenExitOutlined
                className={`${styles.shousuo_icon} text-[1rem]`}
                onClick={() => {
                  setShowTitle(false)
                  setTimeout(() => {
                    initHeight()
                  }, 200)
                }}
              />
            ) : (
              <FullscreenOutlined
                className={`${styles.shousuo_icon} ${'absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10'}`}
                onClick={() => {
                  setShowTitle(true)
                  setTimeout(() => {
                    initHeight()
                  }, 200)
                }}
              />
            )}
            <div className='font-bold text-[0.8rem] ml-3'>数据列表</div>
          </div>
          <Button danger ghost icon={<DownloadOutlined />} onClick={() => exportToExcelFun()}>
            导出
          </Button>
        </div>
        <ResizableTable
          className='report_personal_table'
          rowClassName={(_, i) => (i % 2 === 1 ? `customRow odd` : `customRow even`)}
          columns={tableColumns}
          dataSource={tableData}
          loading={tableLoading}
          bordered
          scroll={{
            // x: 'max-content',
            y: `calc(${height}px - 0.625rem - 1.6rem - 0.5rem)`
          }}
          onChange={onChangePage}
          pagination={{
            ...pagination,
            total: pagination?.total,
            showSizeChanger: true,
            showQuickJumper: true,
            pageSizeOptions: ['10', '20', '50']
          }}
        />
      </div>
    </div>
  )
}
export default ReportPer
