import React, { useMemo, cloneElement, createElement } from 'react'
import { Form, Input, Select, Radio, Button, Dropdown, Space } from 'antd'
import SvgIcon from '@/components/SvgIcons'
import { getFlatMenus } from '@/utils'
import { useRequest } from '@/hooks'
import { svgPaths } from '@/config'
import { antdIcons } from '@/assets/add-icons'
import menuService from '../service'

import type { MenuDetailProps } from '../interface'
import type { Menu } from '@/types'
import type { MenuProps } from 'antd'

const menuStyle: React.CSSProperties = {
  display: 'flex',
  maxWidth: '25rem',
  flexWrap: 'wrap'
}

const MenuDetail: React.FC<MenuDetailProps> = ({ modeType, record, closeModel }) => {
  const [form] = Form.useForm()
  const { runAsync: addMenu } = useRequest(menuService.addMenu, { manual: true })
  const { loading, runAsync: updateMenu } = useRequest(menuService.updateMenu, { manual: true })

  const formatMenus = (
    menus: Menu[],
    menuGroup: Record<string, Menu[]>,
    parentMenu?: Menu
  ): Menu[] => {
    return menus.map(menu => {
      const children = menuGroup[menu.menuCode]

      const parentPaths = parentMenu?.parentPaths || []
      const path = (parentMenu ? `${parentPaths.at(-1)}${menu.url}` : menu.url) || ''
      return {
        ...menu,
        path,
        parentPaths,
        menuLink: `${path}`,
        menuTag: menu?.tag,
        displayType: menu?.showType,
        children: children?.length
          ? formatMenus(children, menuGroup, {
              ...menu,
              parentPaths: [...parentPaths, path || ''].filter(o => o)
            })
          : undefined
      }
    })
  }

  /** 菜单图标下拉选中 */
  const handleMenuTag = (menuTag: string) => {
    form.setFieldValue('menuTag', menuTag)
  }

  const dropDownItems: MenuProps['items'] = useMemo(() => {
    return svgPaths.map(svg => {
      return {
        key: svg,
        label: (
          <SvgIcon key={svg} name={svg} color='#F66A5C' onClick={handleMenuTag.bind(null, svg)} />
        )
      }
    })
  }, [svgPaths])

  const newRecord = useMemo(() => {
    if (modeType === 'edit') {
      const flatMenus = getFlatMenus([record])
      const menuGroup = flatMenus.reduce<Record<string, Menu[]>>((prev, menu) => {
        if (!menu.parentCode) {
          return prev
        }

        if (!prev[menu.parentCode]) {
          prev[menu.parentCode] = []
        }

        prev[menu.parentCode].push(menu)
        return prev
      }, {})
      return formatMenus(flatMenus, menuGroup)[0]
    }
    if (modeType === 'add') {
      const { menuCode, nextMenuCode } = record
      return {
        parentMenuCode: menuCode,
        menuCode: nextMenuCode,
        sortId: nextMenuCode,
        menuType: '1',
        displayType: '1'
      }
    }
  }, [record])

  const handleSumbit = async () => {
    const {
      parentCode,
      menuCode,
      menuName,
      menuType,
      outMenuCode,
      outMenuLink,
      menuLink,
      displayType,
      menuTag,
      sortId
    } = form.getFieldsValue()
    const params: any = {
      parentCode,
      menuCode,
      menuName,
      menuType,
      aliasCode: outMenuCode,
      referUrl: outMenuLink,
      url: menuLink,
      showType: displayType,
      tag: menuTag,
      sortId
    }
    if (modeType === 'edit') params.id = record?.id
    const method = modeType === 'edit' ? updateMenu : addMenu
    const [submitError, data] = await method(params)

    if (submitError) {
      return
    }

    if (data.STATUS === '0000') {
      closeModel()
    }
  }

  return (
    <Form form={form} labelCol={{ span: 6 }} initialValues={newRecord} onFinish={handleSumbit}>
      <Form.Item name='parentCode' label='父级菜单编码'>
        <Input placeholder='请输入父级菜单编码' autoComplete='off' />
      </Form.Item>
      <Form.Item label='菜单编码' name='menuCode'>
        <Input placeholder='请输入菜单编码' autoComplete='off' />
      </Form.Item>
      <Form.Item
        label='菜单名称'
        name='menuName'
        rules={[{ required: true, message: '请输入菜单名称' }]}
      >
        <Input placeholder='请输入菜单名称' autoComplete='off' />
      </Form.Item>
      <Form.Item label='菜单类型' name='menuType'>
        <Select placeholder='请选择菜单类型'>
          <Select.Option key='1' value='1'>
            内部菜单
          </Select.Option>
          <Select.Option key='2' value='2'>
            外部链接
          </Select.Option>
        </Select>
      </Form.Item>
      <Form.Item label='链接' name='menuLink'>
        <Input placeholder='请输入链接' allowClear />
      </Form.Item>
      <Form.Item label='显示类型' name='displayType'>
        <Radio.Group buttonStyle='solid'>
          <Radio.Button value='1'>默认</Radio.Button>
          <Radio.Button value='2'>嵌入</Radio.Button>
          <Radio.Button value='3'>跳转</Radio.Button>
          <Radio.Button value='4'>无侧边栏菜单</Radio.Button>
        </Radio.Group>
      </Form.Item>
      <Form.Item name='menuTag' noStyle />
      <Form.Item
        label='菜单图标'
        shouldUpdate={(prevValues, curValues) => prevValues.menuTag !== curValues.menuTag}
      >
        {({ getFieldValue }) => {
          const menuTag = getFieldValue('menuTag') || ''
          return (
            <Dropdown
              trigger={['click']}
              menu={{ items: dropDownItems }}
              dropdownRender={menus => {
                return <div>{cloneElement(menus as React.ReactElement, { style: menuStyle })}</div>
              }}
            >
              <a onClick={e => e.preventDefault()}>
                <Space>
                  {menuTag ? <SvgIcon name={menuTag} color='#F66A5C' /> : '请选择图标'}
                  {createElement(antdIcons['DownOutlined'])}
                </Space>
              </a>
            </Dropdown>
          )
        }}
      </Form.Item>
      <Form.Item
        label='排序字段'
        name='sortId'
        rules={[{ pattern: /^[+]{0,1}(\d+)$/, message: '只支持数字，请正确输入！' }]}
      >
        <Input placeholder='请输入排序字段' autoComplete='off' />
      </Form.Item>
      <Form.Item wrapperCol={{ offset: 6 }}>
        <Button loading={loading} type='primary' htmlType='submit'>
          确定
        </Button>
      </Form.Item>
    </Form>
  )
}
export default MenuDetail
