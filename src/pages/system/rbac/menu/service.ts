import request from '@/request'

const menuService = {
  // 获取所有菜单数据
  getMenuTree: () => {
    return request.get<any>('/zhyy/manager/core/menu/buildMenuTree')
  },

  // 新增菜单
  addMenu: params => {
    return request.post<any>('/zhyy/manager/core/menu/addMenu', params)
  },

  // 修改菜单
  updateMenu: params => {
    return request.post<any>('/zhyy/manager/core/menu/updateMenu', params)
  },

  // 删除菜单
  deleteMenu: params => {
    return request.post<any>('/zhyy/manager/core/menu/deleteMenu', params)
  },

  // 获取子菜单菜单编码
  obtainNextMenuCode: params => {
    return request.post<any>('/zhyy/manager/core/menu/obtainNextMenuCode', params)
  },

  getFilterMenuData: params => {
    return request.post<any>('/zhyy/manager/core/menu/queryMenu', params)
  }
}

export default menuService
