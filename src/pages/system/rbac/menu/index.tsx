import { useEffect, useMemo, useState } from 'react'
import {
  Button,
  Form,
  Row,
  Col,
  Input,
  Table,
  Space,
  ConfigProvider,
  Popconfirm,
  Modal
} from 'antd'
import MenuDetail from './components/MenuDetail'
import { useRequest } from '@/hooks'
import menuService from './service'
import { antdUtils } from '@/utils'

import type { TableProps } from 'antd'
import type { DataType } from './interface'
import { Menu } from '@/types'
import styles from '../Index.module.scss'

const MenuManage = () => {
  const [form] = Form.useForm()
  const [visible, setVisible] = useState<boolean>(false)
  const [selectedRecord, setSelectedRecord] = useState<DataType>(null)
  const [modeType, setModeType] = useState<string>('')
  const [menuTreeData, setMenuTreeData] = useState<Menu[]>([])
  const { loading, runAsync: getMenuTreeData } = useRequest(menuService.getMenuTree, {
    manual: true
  })
  const { runAsync: delMenuData } = useRequest(menuService.deleteMenu, {
    manual: true
  })
  const { runAsync: getMenuCode } = useRequest(menuService.obtainNextMenuCode, { manual: true })
  const { runAsync: getFilterMenuData } = useRequest(menuService.getFilterMenuData, {
    manual: true
  })

  const columns: TableProps<DataType>['columns'] = [
    {
      title: '菜单名称',
      dataIndex: 'menuName',
      key: 'menuName',
      width: 200
    },
    {
      title: '菜单编码',
      dataIndex: 'menuCode',
      key: 'menuCode',
      width: 200
    },
    {
      title: 'URL',
      key: 'url',
      dataIndex: 'url',
      width: 400
    },
    {
      title: '操作',
      key: 'action',
      width: 250,
      render: (_, record) => {
        return (
          <Space>
            <Button type='link' onClick={showMenuModal.bind(null, 'edit', record)}>
              修改
            </Button>
            <Button type='link' onClick={showDelModal.bind(null, record)}>
              删除
            </Button>
            {!record.children ? (
              <Popconfirm
                title='该节点是叶子节点,增加子节点后该节点将不再是叶子节点,确定新增么?'
                onConfirm={showMenuModal.bind(null, 'add', record)}
                okText='确定'
                cancelText='取消'
              >
                <Button type='link'>新增子节点</Button>
              </Popconfirm>
            ) : (
              <Button type='link' onClick={showMenuModal.bind(null, 'add', record)}>
                新增子节点
              </Button>
            )}
          </Space>
        )
      }
    }
  ]

  useEffect(() => {
    queryMenuTreeData()
  }, [])

  const queryMenuTreeData = async (params = {}) => {
    const method = Object.keys(params).length ? getFilterMenuData : getMenuTreeData
    const [error, data] = await method(params)
    if (error) {
      return
    }
    if (data.STATUS === '0000') {
      const newData = Object.keys(params).length ? data?.DATA.data : data?.DATA
      setMenuTreeData(newData)
    }
  }

  const hanldeMenuData = arr =>
    arr?.map(item => ({
      ...item,
      key: item?.menuCode,
      children: item?.children ? hanldeMenuData(item?.children) : []
    }))

  const menuData = useMemo(() => {
    return hanldeMenuData(menuTreeData)
  }, [menuTreeData])

  /** 展示菜单详情弹窗 */
  const showMenuModal = async (modeType: string, record: DataType) => {
    if (modeType === 'add') {
      const [error, data] = await getMenuCode({ parentCode: record?.menuCode })
      if (error) return
      if (data.STATUS === '0000') {
        record.nextMenuCode = data?.DATA
      }
    }
    setVisible(true)
    setModeType(modeType)
    setSelectedRecord(record)
  }

  /** 关闭菜单详情弹窗 */
  const closeModal = () => {
    setVisible(false)
    queryMenuTreeData()
  }

  /** 显示删除菜单弹窗 */
  const showDelModal = record => {
    const { menuName, menuCode, children } = record
    antdUtils.modal.confirm({
      title: (
        <div>
          确定删除<span className='text-[#ff0000]'>{menuName}</span>菜单吗？
        </div>
      ),
      onOk: async () => {
        const params = { menuCode, deleteCascade: children.length }
        const [error, data] = await delMenuData(params)
        if (error) return
        if (data.STATUS === '0000') {
          antdUtils.message.success('删除成功！')
          queryMenuTreeData()
        }
      },
      footer: (_, { OkBtn, CancelBtn }) => (
        <>
          <OkBtn />
          <CancelBtn />
        </>
      )
    })
  }

  /** 查询 */
  const queryInfo = () => {
    const { queryName } = form.getFieldsValue()
    const params = queryName ? { queryName } : {}
    queryMenuTreeData(params)
  }

  /** 重置 */
  const reset = () => {
    form.resetFields()
  }

  return (
    <div className={`${'pt-[0.5rem] px-[0rem] h-full'} ${styles.rbac_page}`}>
      <div className='bg-white pt-[0.5rem] px-[1.75rem] mb-[0.5rem]'>
        <ConfigProvider
          theme={{
            components: {
              Form: {
                itemMarginBottom: 0
              }
            }
          }}
        >
          <Form form={form} labelCol={{ span: 6 }} onFinish={queryInfo}>
            <Row gutter={24}>
              <Col span={6}>
                <Form.Item name='queryName' label='菜单'>
                  <Input placeholder='请输入菜单名称或菜单编码' autoComplete='off' allowClear />
                </Form.Item>
              </Col>
              <Col span={18}>
                <Space>
                  <Button type='primary' htmlType='submit'>
                    查询
                  </Button>
                  <Button onClick={reset}>重置</Button>
                </Space>
              </Col>
            </Row>
          </Form>
        </ConfigProvider>
      </div>
      <div className='bg-white p-[0.5rem] px-[0.5rem] h-[calc(100%-3.8rem)] overflow-y-auto'>
        <Table loading={loading} columns={columns} dataSource={menuData} />
      </div>
      <Modal
        title={`${modeType === 'add' ? '新增' : '修改'}菜单`}
        open={visible}
        destroyOnClose
        onCancel={() => {
          setVisible(false)
        }}
        footer={null}
      >
        <MenuDetail modeType={modeType} record={selectedRecord} closeModel={closeModal} />
      </Modal>
    </div>
  )
}
export default MenuManage
