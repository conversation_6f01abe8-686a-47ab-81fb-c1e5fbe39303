import { useEffect, useState } from 'react'
import Api from '../service'
import { Form, Select, Button, message } from 'antd'

const RoleForm = ({ roleList, roleParams, cancelRoleModal }) => {
  const [form] = Form.useForm()
  const [selectLoading, setSelectLoading] = useState(false)
  const [btnloading, setBtnloading] = useState(false)

  useEffect(() => {
    if (typeof roleParams === 'string') {
      // 单条修改
      getRoleCodes()
    }
  }, [])

  // 获取用户角色编码
  const getRoleCodes = () => {
    const params = {
      userKey: roleParams
    }
    setSelectLoading(true)
    // prettier-ignore
    Api.queryUserRole(params).then(response => {
            const res = response[1]
            if (res.STATUS === '0000') {
                const arr = res.DATA?.map(item => {
                    return item?.roleCode;
                });
                form.setFieldsValue({roleCodes: arr});
            } else {
                message.error(res.MESSAGE);
            }
            setSelectLoading(false)
        }).catch(() => {
            setSelectLoading(false)
        });
  }

  // 确定
  const handleOk = formInfo => {
    let params = {}
    // roleParams: 类型为字符串时，单挑修改角色；类型为数组时，批量修改角色。
    params[`${typeof roleParams === 'string' ? 'roleCodes' : 'roleCode'}`] = formInfo?.roleCodes
    params[`${typeof roleParams === 'string' ? 'userKey' : 'list'}`] = roleParams
    const method = typeof roleParams === 'string' ? Api.setUserRole : Api.userListSetRole
    setBtnloading(true)
    // prettier-ignore
    method(params).then(response => {
            const res = response[1]
            if (res.STATUS === '0000') {
                message.success('修改角色成功');
                cancelRoleModal();
            } else {
                message.error(res.MESSAGE);
            }
            setBtnloading(false)
        }).catch(() => {
            setBtnloading(false)
        });
  }

  return (
    <Form
      form={form}
      labelCol={{ span: 4 }}
      wrapperCol={{ span: 19 }}
      labelAlign='right'
      onFinish={handleOk}
    >
      <Form.Item label='角色' name='roleCodes'>
        <Select
          placeholder='请选择用户角色'
          loading={selectLoading}
          mode='multiple'
          allowClear
          showSearch
          getPopupContainer={triggerNode => triggerNode.parentElement || document.body}
          filterOption={(input, option) =>
            option.children.toLowerCase().includes(input.toLowerCase())
          }
          style={{ width: '100%' }}
        >
          {roleList.map(item => {
            const { roleCode, roleName } = item
            return (
              <Select.Option key={roleCode} value={roleCode}>
                {roleName}
              </Select.Option>
            )
          })}
        </Select>
      </Form.Item>
      <Form.Item wrapperCol={{ offset: 4, span: 19 }}>
        <Button loading={btnloading} type='primary' htmlType='submit'>
          确定
        </Button>
      </Form.Item>
    </Form>
  )
}

export default RoleForm
