import { md5 } from '../utils.js'
import Api from '../service'
import { Form, Input, Button, message } from 'antd'
import { useEffect, useState } from 'react'
import CryptoJS from 'crypto-js';

const UserForm = ({ userType, userInfo, cancelAddModal }) => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    // 修改用户信息
    if (userType === 'edit') {
      const { userName, fullName, phoneNumber, oa } = userInfo
      form.setFieldsValue({
        account: userName,
        fullName,
        phoneNumber,
        oa
      })
    }
  }, [])

  // 确定
  const handleOk = formInfo => {
    const { account, fullName, password, phoneNumber, oa } = formInfo
    const params = {
      userName: account,
      fullName,
      phoneNumber,
      oa
    }
    // 新增用户
    if (userType === 'add') params.password = CryptoJS?.MD5(password).toString();
    // 修改用户
    if (userType === 'edit') params.userId = userInfo?.userKey
    const method = userType === 'add' ? Api.addUser : Api.updateUser
    setLoading(true)
    // prettier-ignore
    method(params).then(response => {
            const res = response[1]
            if (res.STATUS === '0000') {
                message.success(`${userType === 'add' ? '新增' : '修改'}用户成功！`);
                cancelAddModal();
            } else {
                message.error(res.MESSAGE);
            }
            setLoading(false)
        }).catch(() => {
            setLoading(false)
        });
  }

  return (
    <Form
      form={form}
      labelCol={{ span: 6 }}
      wrapperCol={{ span: 14 }}
      labelAlign='right'
      onFinish={handleOk}
    >
      <Form.Item label='账号' name='account' rules={[{ required: true, message: '请输入账号!' }]}>
        <Input placeholder='请输入账号!' disabled={userType === 'edit'} />
      </Form.Item>
      <Form.Item label='姓名' name='fullName' rules={[{ required: true, message: '请输入姓名!' }]}>
        <Input placeholder='请输入姓名!' disabled={userType === 'edit'} />
      </Form.Item>
      {
        // prettier-ignore
        userType === 'add' && <Form.Item label='密码' name='password' rules={[{
                    required: true,
                    validator: (rule, value) => {
                        let pwdRegex = new RegExp(
                            '(?=.*[0-9])(?=.*[A-Z])(?=.*[a-z])(?=.*[^a-zA-Z0-9]).{8,30}'
                        );
                        if (!pwdRegex.test(value)) {
                            Promise.resolve();
                            return Promise.reject('8-16位字符，必须包括大小写字母、数字和特殊符号');
                        } else {
                            return Promise.resolve(); // 校验通过
                        }
                    }
                }]}>
                    <Input.Password placeholder='请输入密码!' autoComplete='new-password'/>
                </Form.Item>
      }
      <Form.Item
        label='电话号码'
        name='phoneNumber'
        rules={[{ required: true, message: '请输入电话号码!' }]}
      >
        <Input placeholder='请输入电话号码!' disabled={userType === 'edit'} />
      </Form.Item>
      <Form.Item
          label='员工编码'
          name='oa'
          rules={[{ required: true, message: '请输入员工编码!' }]}
      >
        <Input placeholder='请输入员工编码' />
      </Form.Item>
      <Form.Item wrapperCol={{ offset: 6, span: 14 }}>
        <Button loading={loading} type='primary' htmlType='submit'>
          确定
        </Button>
      </Form.Item>
    </Form>
  )
}

export default UserForm
