import request from '@/request'

const serviceList = {
  getQueryConditions: params => {
    return request.get('/zhyy/getQueryConditions', { params })
  },
  queryAllEnableRole: params => {
    return request.get('/zhyy/manager/core/role/queryAllEnableRole', params)
  },
  queryUserInfo: params => {
    return request.post('/zhyy/extra/manager/user/queryUserInfo', params)
  },
  addUser: params => {
    return request.post('/zhyy/manager/core/user/addUser', params)
  },
  setUserRole: params => {
    return request.post('/zhyy/manager/core/role/setUserRole', params)
  },
  userListSetRole: params => {
    return request.post('/zhyy/manager/extra/role/userListSetRole', params)
  },
  queryUserRole: params => {
    return request.post('/zhyy/manager/core/role/queryUserRole', params)
  },
  deleteUser: params => {
    return request.post('/zhyy/manager/core/user/deleteUser', params)
  },
  updateUser: params => {
    return request.post('/zhyy/manager/core/user/updateUser', params)
  },
  buildUserOrgSelectTree: params => {
    return request.post('/zhyy/manager/core/org/buildUserOrgSelectTree', params)
  },
  setUserOrg: params => {
    return request.post('/zhyy/manager/core/org/setUserOrg', params)
  },
  buildUserOrgAuthSelectTree: params => {
    return request.post('/zhyy/manager/core/org/buildUserOrgAuthSelectTree', params)
  },
  setUserOrgAuth: params => {
    return request.post('/zhyy/manager/core/org/setUserOrgAuth', params)
  },
  extraEnableUser: params => {
    return request.post('/zhyy/extra/manager/user/extraEnableUser', params)
  },
  extraDisableUser: params => {
    return request.post('/zhyy/extra/manager/user/extraDisableUser', params)
  },
  exportUserInfo: params => {
    return request.get('/zhyy/extra/manager/user/exportUserInfo', {
      params,
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      },
      responseType: 'blob'
    })
  }
}

export default serviceList
