import { useState, useEffect } from 'react'
import UserForm from './components/UserForm'
import RoleForm from './components/RoleForm'
import { handleOrgTree, handleAreaTree } from './assets/utils'
import { download } from './utils.js'
import Api from './service'
import {
  Form,
  Input,
  Button,
  Modal,
  Table,
  Switch,
  Popconfirm,
  Select,
  message,
  Tree,
  TreeSelect,
  Radio,
  Tooltip,
  Skeleton,
  Row,
  Col
} from 'antd'
import { ExclamationCircleOutlined } from '@ant-design/icons'
import { useRequest } from '@/hooks'
import effectService from '@/pages/effect/employment/service.ts'
import styles from '../Index.module.scss'

const UserManagement = () => {
  // 初始化状态
  const [amountFlag, setAmountFlag] = useState(true)
  const [userName, setUserName] = useState('') // 用户名称
  const [areaCode, setAreaCode] = useState('') // 组织机构编码
  const [areaLevel, setAreaLevel] = useState('')
  // @ts-ignore
  const [areaList, setAreaList] = useState([]) // 组织机构数据
  const [deptName, setDeptName] = useState('') // 部门名称
  const [roleCode, setRoleCode] = useState('') // 角色编码
  const [roleList, setRoleList] = useState([]) // 角色数据
  const [sourceCode, setSourceCode] = useState('') // 用户来源
  const [status, setStatus] = useState('1') // 状态

  // 下载数据
  const [downloadLoading, setDownloadLoading] = useState(false)

  // 表格数据
  const [tableLoading, setTableLoading] = useState(false)
  const [tableTotal, setTableTotal] = useState(0)
  const [tableData, setTableData] = useState([])
  const [pageNum, setPageNum] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [queryKey, setQueryKey] = useState(0)
  const [selectedRowKeys, setSelectedRowKeys] = useState([])
  const [selectedRows, setSelectedRows] = useState([])
  const [selectedRecord, setSelectedRecord] = useState<any>({})

  // 用户弹窗数据
  const [userVisible, setUserVisible] = useState(false)
  const [userType, setUserType] = useState('') // add-新增 edit-编辑

  // 用户角色弹窗数据
  const [roleVisible, setRoleVisible] = useState(false)
  const [roleTitle, setRoleTitle] = useState('')
  const [roleParams, setRoleParams] = useState(null)

  // 删除用户弹窗数据
  const [delVisible, setDelVisible] = useState(false)
  const [delLoading, setDelLoading] = useState(false)

  // 所属组织弹窗数据
  const [belongOrgVisible, setBelongOrgVisible] = useState(false)
  const [belongOrgTitle, setBelongOrgTitle] = useState('')
  const [belongOrgLoading, setBelongOrgLoading] = useState(false)
  const [belongOrgOkLoading, setBelongOrgOkLoading] = useState(false)
  const [orgTreeCode, setOrgTreeCode] = useState([])
  const [orgTreeData, setOrgTreeData] = useState([])
  const [orgTreeExpandKeys, setOrgTreeExpandKeys] = useState([])

  // 组织赋权弹窗数据
  const [orgRightsVisible, setOrgRightsVisible] = useState(false)
  const [orgRightsTitle, setOrgRightsTitle] = useState('')
  const [orgRightsLoading, setOrgRightsLoading] = useState(false)
  const [orgRightsOkLoading, setOrgRightsOkLoading] = useState(false)
  const [areaTreeCode, setAreaTreeCode] = useState([])
  const [areaTreeData, setAreaTreeData] = useState([])
  const [areaTreeExpandKeys, setAreaTreeExpandKeys] = useState([])
  const { runAsync: build4LevelOrgTree2 } = useRequest(effectService.build4LevelOrgTree2, {
    manual: true
  })

  useEffect(() => {
    getRoleList()
    //使用async函数来包含await
    ;(async () => {
      // getAreaList()
      await get4LevelOrgTree() // 等待组织机构数据加载完成
    })()
  }, [])

  useEffect(() => {
    updateBelongOrg(1);
    updateOrgRights(1);
  }, [])

  const get4LevelOrgTree = async () => {
    const params = {
      menuType: '',
      centerType: 'UserManager',
      // monthId: formRef.getFieldValue('monthId')?.format('YYYYMM'),
      tag: "1"
    }
    const [error, res] = await build4LevelOrgTree2(params)
    if (error) {
      return
    }
    if (res.STATUS === '0000') {
      const orgTree = res.DATA
      setAreaList(orgTree)
      // setAreaCode(orgTree[0]?.orgId);
      // setAreaLevel(orgTree[0]?.level);
      // getTableData(orgTree[0]) // 然后获取表格数据
      getTableData()
    }
  }

  // // 获取组织机构数据
  // const getAreaList = async () => {
  //   const params = { menuType: '', centerType: 'UserManager' }
  //   const response: any = await Api.getQueryConditions(params)
  //   const res = response[1]
  //   if (res.STATUS === '0000') {
  //     const { orgTree } = res.DATA
  //     setAreaCode(orgTree[0]?.areaCode)
  //     setAreaLevel(orgTree[0]?.areaLevel)
  //     setAreaList(orgTree)
  //   } else {
  //     message.error(res.MESSAGE)
  //   }
  // }

  // 获取角色数据
  const getRoleList = () => {
    Api.queryAllEnableRole({}).then((response: any) => {
      const res = response[1]
      if (res.STATUS === '0000') {
        setRoleList(res.DATA)
      } else {
        message.error(res.MESSAGE)
      }
    })
  }

  // 获取所属组织数据
  const getBelongOrgList = async () => {
    setBelongOrgLoading(true)
    const params = { userKey: selectedRecord?.userKey }
    const response: any = await Api.buildUserOrgSelectTree(params)
    const res = response[1]
    if (res.STATUS === '0000') {
      const list = handleOrgTree(res.DATA)
      const orgTreeCode = list.length > 0 ? list[list.length - 1] : []
      const orgTreeExpandKeys = list.length > 0 ? list.slice(0, list.length - 1) : []
      setOrgTreeData(res.DATA)
      setOrgTreeCode(orgTreeCode)
      setOrgTreeExpandKeys(orgTreeExpandKeys)
      setBelongOrgLoading(false)
    } else {
      setBelongOrgLoading(false)
      message.error(res.MESSAGE)
    }
  }

  // 获取组织机构数据
  const getOrgRightsList = () => {
    const params = {
      userKey: selectedRecord?.userKey
    }
    setOrgRightsLoading(true)
    // prettier-ignore
    Api.buildUserOrgAuthSelectTree(params).then((response: any) => {
            const res = response[1]
            if (res.STATUS === '0000') {
                const list = handleAreaTree(res.DATA);
                setAreaTreeData(res.DATA)
                setAreaTreeCode(list)
                setAreaTreeExpandKeys([res.DATA[0]?.areaCode])
            } else {
                message.error(res.MESSAGE);
            }
            setOrgRightsLoading(false)
        }).catch(() => {
            setOrgRightsLoading(false)
        });
  }

  // 获取表格数据
  const getTableData = (orgData?: any) => {
    setQueryKey(prevKey => prevKey + 1) // 更新 queryKey，触发查询
    const params = {
      queryName: userName,
      areaCode: orgData?.orgId || areaCode,
      areaLevel: orgData?.level || areaLevel,
      districtName: deptName,
      roleCode,
      sourceSystem: sourceCode,
      enable: status,
      pageNum,
      pageSize
    }
    if (!amountFlag) setTableLoading(true)
    // prettier-ignore
    Api.queryUserInfo(params).then((response: any) => {
            const res = response[1]
            if (res.STATUS === '0000') {
                const {data, totalCount} = res.DATA;
                const tableData = data.map((item, index) => {
                    const {roleDOList} = item;
                    return {
                        ...item,
                        key: index,
                        num: (pageNum - 1) * pageSize + index + 1,
                        roleName: roleDOList?.map(item => item.roleName).join(',')
                    };
                })
                setTableTotal(totalCount)
                setTableData(tableData)
            } else {
                message.error(res.MESSAGE);
            }
            if (!amountFlag) setTableLoading(false);
            if (amountFlag) setAmountFlag(false);
        }).catch(() => {
            if (!amountFlag) setTableLoading(false);
            if (amountFlag) setAmountFlag(false);
        });
  }

  // 用户启用状态切换
  const changeUserStatus = record => {
    const { userKey, enable } = record
    const params = { userKey }
    const method = enable === '0' ? Api.extraEnableUser : Api.extraDisableUser
    method(params).then((response: any) => {
      const res = response[1]
      if (res.STATUS === '0000') {
        message.success('切换成功！')
        queryInfo()
      } else {
        message.error(res.MESSAGE)
      }
    })
  }
  // 用户改变
  const changeUserName = e => {
    setUserName(e.target.value)
  }

  // 组织结构切换
  // @ts-ignore
  const changeAreaCode = (value, node) => {
    setAreaCode(value)
    setAreaLevel(node?.level)
  }
  // @ts-ignore
  const clearAreaCode = value => {
    if (!value) {
      setAreaCode('')
      setAreaLevel('')
    }
  }

  // 部门改变
  // @ts-ignore
  const changeDeptName = e => {
    setDeptName(e.target.value)
  }

  // 角色切换
  const changeRoleCode = value => {
    setRoleCode(value)
  }

  // 用户来源切换
  // @ts-ignore
  const changeSourceCode = value => {
    setSourceCode(value)
  }

  // 状态切换
  useEffect(() => {
    if (status !== '1') {
      queryInfo()
    }
  }, [status])
  const changeStatus = e => {
    setStatus(e.target.value)
  }

  // 文件导出
  const exportFile = () => {
    const params = {
      queryName: userName,
      areaCode,
      areaLevel,
      roleCode,
      sourceSystem: sourceCode,
      enable: status
    }
    message.warning('文件正在导出，请稍后......')
    setDownloadLoading(true)
    // prettier-ignore
    Api.exportUserInfo(params).then((response: any) => {
            const res = response[1]
            if (!Error) return
            let fileName = res.headers['content-disposition']?.match(/filename=(.*)/)[1] || new Date().valueOf();
            fileName = `${decodeURI(fileName)}`;
            download({fileName, res: res.data});
            setDownloadLoading(false)
        }).catch(() => {
            setDownloadLoading(false)
        });
  }

  // 显示用户弹窗
  const showUserModal = (type, record) => {
    // 更新状态
    setUserVisible(true)
    setUserType(type)

    // 如果type是'edit'，则更新selectedRecord
    if (type === 'edit') {
      setSelectedRecord(record)
    }
  }

  // 取消用户弹窗
  const cancelUserModal = () => {
    setUserVisible(false)
    setUserType('')
    setSelectedRecord({})
    getTableData()
  }

  // 显示用户角色弹窗
  const showRoleModal = () => {
    if (selectedRowKeys.length === 0 && selectedRows.length === 0) {
      message.warning('没有勾选角色，请先勾选要操作的角色！')
      return
    }
    setRoleVisible(true)
    setRoleTitle('批量修改用户角色')
    setRoleParams(selectedRows)
  }

  // 取消用户角色弹窗
  const cancelRoleModal = () => {
    setRoleVisible(false)
    setRoleParams(null)
    if (typeof roleParams !== 'string') {
      setSelectedRowKeys([])
      setSelectedRows([])
    }
    queryInfo()
  }

  // 显示删除用户弹窗
  const showDelModal = record => {
    setDelVisible(true)
    setSelectedRecord(record)
  }

  // 删除用户弹窗确定
  const handleDelOk = () => {
    const params = { userKey: selectedRecord?.userKey }
    setDelLoading(true)
    // prettier-ignore
    Api.deleteUser(params).then((response: any) => {
            const res = response[1]
            if (res.STATUS === '0000') {
                message.success('删除用户成功！');
                setDelVisible(false);
                setSelectedRecord({});
                queryInfo();
            } else {
                message.error(res.MESSAGE);
            }
            setDelLoading(false);
        }).catch(() => {
            setDelLoading(false);
        });
  }

  // 修改用户角色
  const updateRole = record => {
    const { userKey, fullName } = record
    setRoleVisible(true)
    setRoleTitle(fullName)
    setRoleParams(userKey)
  }

  // 修改用户所属组织
  const updateBelongOrg = record => {
    if (record) {
      return
    }
    const { fullName } = record
    setTimeout(() => {
      setBelongOrgVisible(true)
      setBelongOrgTitle(fullName)
      setSelectedRecord(record)
      setOrgTreeCode([])
      setOrgTreeExpandKeys([])
      setOrgTreeData([])
      getBelongOrgList()
    })
  }

  // 所属组织弹窗确定
  const handleBelongOrgOk = () => {
    const params = {
      userKey: selectedRecord?.userKey,
      orgCodeList: orgTreeCode
    }
    setBelongOrgOkLoading(true)
    Api.setUserOrg(params)
      .then((response: any) => {
        const res = response[1]
        if (res.STATUS === '0000') {
          message.success('修改所属组织成功！')
          setBelongOrgVisible(false)
          queryInfo()
        } else {
          message.error(res.MESSAGE)
        }
        setBelongOrgOkLoading(false)
      })
      .catch(() => {
        setBelongOrgOkLoading(false)
      })
  }

  // 所属组织切换
  const changeOrgTreeCode = value => {
    setOrgTreeCode([value])
  }

  // 所属组织展开
  const changeOrgTreeExpandKeys = value => {
    setOrgTreeExpandKeys(value)
  }

  // 组织授权
  const updateOrgRights = record => {
    if (record) {
      return
    }
    const { fullName } = record
    setTimeout(() => {
      setOrgRightsVisible(true)
      setOrgRightsTitle(fullName)
      setSelectedRecord(record)
      setAreaTreeCode([])
      setAreaTreeExpandKeys([])
      setAreaTreeData([])
      getOrgRightsList()
    })
  }

  // 组织授权弹窗确定
  const handleOrgRightsOk = () => {
    const params = {
      userKey: selectedRecord?.userKey,
      orgCodeList: areaTreeCode
    }
    setOrgRightsOkLoading(true)
    // prettier-ignore
    Api.setUserOrgAuth(params).then((response: any) => {
            const res = response[1]
            if (res.STATUS === '0000') {
                message.success('修改组织授权成功！');
                setOrgRightsVisible(false);
                queryInfo();
            } else {
                message.error(res.MESSAGE);
            }
            setOrgRightsOkLoading(false)
        }).catch(() => {
            setOrgRightsOkLoading(false)
        });
  }

  // 所属组织切换
  const changeAreaTreeCode = value => {
    setAreaTreeCode(value)
  }

  // 所属组织展开
  const changeAreaTreeExpandKeys = value => {
    setAreaTreeExpandKeys(value)
  }
  useEffect(() => {
    if (queryKey > 0) {
      getTableData()
    }
  }, [pageSize, pageNum])

  // 查询
  const queryInfo = () => {
    // setPageNum(1)
    // setPageSize(10)
    getTableData()
  }
  // 分页切换
  const pageChange = (page, pageSize) => {
    setPageNum(page)
    setPageSize(pageSize)
  }

  const rowSelection = {
    onChange: (newSelectedRowKeys, newSelectedRows) => {
      setSelectedRowKeys(newSelectedRowKeys)
      setSelectedRows(newSelectedRows.map(item => item.userName))
    },
    selectedRowKeys: selectedRowKeys
  }

  const columns = [
    {
      title: '序号',
      key: 'num',
      dataIndex: 'num',
      width: 100,
      align: 'center' as const
    },
    {
      title: '账号',
      key: 'userName',
      dataIndex: 'userName',
      width: 120,
      align: 'center' as const
    },
    {
      title: '姓名',
      key: 'fullName',
      dataIndex: 'fullName',
      width: 120,
      align: 'center' as const
    },
    {
      title: '是否启用',
      key: 'enable',
      dataIndex: 'enable',
      width: 150,
      align: 'center' as const,
      render: (text, record) => {
        //const flag = authCodeList.includes('S100-89');
        return (
          <Popconfirm
            title='确定切换启用状态?'
            onConfirm={() => changeUserStatus(record)}
            okText=' 确认'
            cancelText='取消'
            //disabled={!flag}
          >
            <Switch
              checked={text === '1'}
              //disabled={!flag}
            />
          </Popconfirm>
        )
      }
    },
    {
      title: '角色',
      key: 'roleName',
      dataIndex: 'roleName',
      width: 150,
      align: 'center' as const,
      ellipsis: { showTitle: false },
      // prettier-ignore
      render: text => <Tooltip placement="topLeft" title={text}
                                     getPopupContainer={tiggerNode => tiggerNode?.closest('div.ant-table-body') || document.body}>{text}</Tooltip>
    },
    // {
    //   title: '地市',
    //   key: 'areaName',
    //   dataIndex: 'areaName',
    //   width: 150,
    //   align: 'center' as const
    // },
    // {
    //   title: '部门',
    //   key: 'orgName',
    //   dataIndex: 'orgName',
    //   width: 200,
    //   align: 'center' as const,
    //   ellipsis: { showTitle: false },
    //   // prettier-ignore
    //   render: text => <Tooltip placement="topLeft" title={text}
    //                                  getPopupContainer={tiggerNode => tiggerNode?.closest('div.ant-table-body') || document.body}>{text}</Tooltip>
    // },
    // {
    //   title: '区县',
    //   key: 'districtName',
    //   dataIndex: 'districtName',
    //   width: 200,
    //   align: 'center' as const,
    //   ellipsis: { showTitle: false },
    //   // prettier-ignore
    //   render: text => <Tooltip placement="topLeft" title={text}
    //                                  getPopupContainer={tiggerNode => tiggerNode?.closest('div.ant-table-body') || document.body}>{text}</Tooltip>
    // },
    // {
    //   title: '网格',
    //   key: 'gridName',
    //   dataIndex: 'gridName',
    //   width: 150,
    //   align: 'center' as const,
    //   ellipsis: { showTitle: false },
    //   // prettier-ignore
    //   render: text => <Tooltip placement="topLeft" title={text}
    //                                  getPopupContainer={tiggerNode => tiggerNode?.closest('div.ant-table-body') || document.body}>{text}</Tooltip>
    // },
    {
      title: '操作',
      width: 300,
      fixed: 'right' as const,
      align: 'center' as const,
      render: (_, record) => {
        const { sourceSystem } = record
        //const flag = authCodeList.includes('S100-90');
        // prettier-ignore
        return <div>
                    <Button
                        size='small'
                        type='link'
                        // disabled={sourceSystem !== '20'}
                        onClick={() => showUserModal('edit', record)}
                    >
                        修改
                    </Button>
                    <Button
                        size='small'
                        type='link'
                        disabled={sourceSystem !== '20'}
                        onClick={() => showDelModal(record)}
                    >
                        删除
                    </Button>
                    <Button
                        size='small'
                        type='link'
                        //disabled={!flag}
                        onClick={() => updateRole(record)}
                    >
                        角色
                    </Button>
                    {/*<Button*/}
                    {/*    size='small'*/}
                    {/*    type='link'*/}
                    {/*    //disabled={!flag}*/}
                    {/*    onClick={() => updateBelongOrg(record)}*/}
                    {/*>*/}
                    {/*    所属组织*/}
                    {/*</Button>*/}
                    {/*<Button*/}
                    {/*    size='small'*/}
                    {/*    type='link'*/}
                    {/*    //disabled={!flag}*/}
                    {/*    onClick={() => updateOrgRights(record)}*/}
                    {/*>*/}
                    {/*    组织授权*/}
                    {/*</Button>*/}
                </div>
      }
    }
  ]
  const [form] = Form.useForm()
  return (
    <div className={`${'pt-[0.5rem] px-[0rem] h-full'} ${styles.rbac_page}`}>
      <div className='bg-white pt-[0.5rem] px-[1.75rem] mb-[0.5rem]'>
        <Form form={form} labelCol={{ span: 6 }}>
          <Row gutter={24}>
            <Col span={6}>
              <Form.Item name='code' label='账号'>
                <Input
                  placeholder='请输入姓名或账号'
                  allowClear
                  onChange={changeUserName}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            {/*<Col span={6}>*/}
            {/*  <Form.Item name='name' label='组织'>*/}
            {/*    <TreeSelect*/}
            {/*      placeholder='请选择组织机构'*/}
            {/*      value={areaCode}*/}
            {/*      treeData={areaList}*/}
            {/*      showSearch*/}
            {/*      allowClear*/}
            {/*      // fieldNames={{ label: 'areaName', value: 'areaCode' }}*/}
            {/*      fieldNames={{*/}
            {/*        value: 'orgId',*/}
            {/*        label: 'orgName',*/}
            {/*        children: 'children'*/}
            {/*      }}*/}
            {/*      dropdownStyle={{ minWidth: '20rem', maxHeight: '20rem', overflow: 'auto' }}*/}
            {/*      dropdownMatchSelectWidth={false}*/}
            {/*      getPopupContainer={triggerNode => triggerNode.parentElement || document.body}*/}
            {/*      filterTreeNode={(inputValue, treeNode) => {*/}
            {/*        return treeNode?.areaName?.includes(inputValue)*/}
            {/*      }}*/}
            {/*      onSelect={changeAreaCode}*/}
            {/*      onChange={clearAreaCode}*/}
            {/*      style={{ width: '100%' }}*/}
            {/*    />*/}
            {/*  </Form.Item>*/}
            {/*</Col>*/}
            {/*<Col span={6}>*/}
            {/*  <Form.Item name='type' label='部门'>*/}
            {/*    <Input*/}
            {/*      placeholder='请输入部门'*/}
            {/*      allowClear*/}
            {/*      onChange={changeDeptName}*/}
            {/*      style={{ width: '100%' }}*/}
            {/*    />*/}
            {/*  </Form.Item>*/}
            {/*</Col>*/}
            <Col span={6}>
              <Form.Item name='unit' label='角色'>
                <Select
                  placeholder='请选择用户角色'
                  allowClear
                  showSearch
                  getPopupContainer={triggerNode => triggerNode.parentElement || document.body}
                  filterOption={(input, option) =>
                    (option.children as any).toLowerCase().includes(input.toLowerCase())
                  }
                  onChange={changeRoleCode}
                  style={{ width: '100%' }}
                >
                  {roleList.map(item => {
                    const { roleCode, roleName } = item
                    return (
                      <Select.Option key={roleCode} value={roleCode}>
                        {roleName}
                      </Select.Option>
                    )
                  })}
                </Select>
              </Form.Item>
            </Col>
            {/*<Col span={6}>*/}
            {/*  <Form.Item name='postId' label='用户来源'>*/}
            {/*    <Select*/}
            {/*      placeholder='请选择用户来源'*/}
            {/*      allowClear*/}
            {/*      showSearch*/}
            {/*      getPopupContainer={triggerNode => triggerNode.parentElement || document.body}*/}
            {/*      filterOption={(input, option) =>*/}
            {/*        (option.children as any).toLowerCase().includes(input.toLowerCase())*/}
            {/*      }*/}
            {/*      onChange={changeSourceCode}*/}
            {/*      style={{ width: '100%' }}*/}
            {/*    >*/}
            {/*      <Select.Option value='20'>自建</Select.Option>*/}
            {/*      <Select.Option value='hrn'>HRN</Select.Option>*/}
            {/*    </Select>*/}
            {/*  </Form.Item>*/}
            {/*</Col>*/}
            <Col span={6}>
              <Form.Item name='level' label='状态'>
                <Radio.Group defaultValue='1' onChange={changeStatus} buttonStyle='solid'>
                  <Radio.Button value='1'>启用</Radio.Button>
                  <Radio.Button value='0'>禁用</Radio.Button>
                </Radio.Group>
              </Form.Item>
            </Col>
            <Col span={6}>
              <div className='text-right'>
                <Button type='primary' onClick={queryInfo} className='ml-[0.4rem]'>
                  查询
                </Button>
                {
                  // prettier-ignore
                  //authCodeList.includes('S100-91') && <>
                  true && <>
                                        <Button type='primary' onClick={showRoleModal}
                                                className='ml-[0.4rem]'>批量赋权</Button>
                                        <Button type='primary' onClick={() => showUserModal('add', {})}
                                                className='ml-[0.4rem]'>新增用户</Button>
                                    </>
                }
                {
                  // prettier-ignore
                  //authCodeList.includes('S100-55') && <Button
                  true && <Button
                                        type='primary'
                                        loading={downloadLoading}
                                        onClick={exportFile}
                                        className='ml-[0.4rem]'
                                    >
                                        导出
                                    </Button>
                }
              </div>
            </Col>
          </Row>
        </Form>
      </div>
      <div className='bg-white pt-[0.5rem] px-[0.5rem] h-[calc(100%-4.8rem)] overflow-y-auto'>
        <Table
          loading={tableLoading}
          columns={columns}
          dataSource={tableData}
          rowSelection={rowSelection}
          size='small'
          //sticky={{getContainer: () => document.getElementsByClassName('ant-layout-content')[0]}}
          bordered
          scroll={{ x: 1140 }}
          pagination={{
            total: tableTotal,
            showTotal: () => `共 ${tableTotal} 条`,
            defaultCurrent: 1,
            defaultPageSize: 10,
            current: pageNum,
            pageSize,
            showSizeChanger: true,
            showQuickJumper: true,
            onChange: pageChange
          }}
        />
      </div>
      <Modal
        title={userType === 'add' ? '添加用户信息' : '修改用户信息'}
        open={userVisible}
        destroyOnClose
        onCancel={() => {
          setUserVisible(false)
        }}
        footer={null}
      >
        <UserForm userInfo={selectedRecord} userType={userType} cancelAddModal={cancelUserModal} />
      </Modal>
      <Modal
        // prettier-ignore
        title={<div>修改用户角色<span style={{color: '#bfbfbf'}}>{`(${roleTitle})`}</span></div>}
        open={roleVisible}
        destroyOnClose
        onCancel={() => {
          setRoleVisible(false)
        }}
        footer={null}
      >
        <RoleForm roleParams={roleParams} roleList={roleList} cancelRoleModal={cancelRoleModal} />
      </Modal>
      {/*删除用户弹窗*/}
      <Modal
        width={400}
        open={delVisible}
        destroyOnClose
        closable={false}
        onOk={handleDelOk}
        confirmLoading={delLoading}
        onCancel={() => {
          setDelVisible(false)
        }}
      >
        <div style={{ fontSize: '0.8rem' }}>
          <ExclamationCircleOutlined style={{ color: '#faad14', marginRight: '0.8rem' }} />
          {/* prettier-ignore */}
          <span>是否删除<span style={{color: '#ff0000'}}>{selectedRecord?.fullName}</span>!</span>
        </div>
      </Modal>
      {/* 所属组织弹窗 */}
      <Modal
        // prettier-ignore
        title={<div>修改所属组织<span style={{color: '#bfbfbf'}}>{`(${belongOrgTitle})`}</span></div>}
        open={belongOrgVisible}
        destroyOnClose
        onCancel={() => {
          setBelongOrgVisible(false)
        }}
        footer={null}
      >
        <Form
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 19 }}
          labelAlign='right'
          onFinish={handleBelongOrgOk}
        >
          <Form.Item label='所属组织'>
            <TreeSelect
              placeholder='请选择所属组织'
              loading={belongOrgLoading}
              value={orgTreeCode}
              treeExpandedKeys={orgTreeExpandKeys}
              treeData={orgTreeData}
              allowClear
              showSearch
              treeDefaultExpandAll={false}
              dropdownStyle={{ maxHeight: '20rem', overflow: 'auto' }}
              fieldNames={{ label: 'orgName', value: 'orgCode' }}
              getPopupContainer={triggerNode => triggerNode.parentElement || document.body}
              filterTreeNode={(inputValue, treeNode) => {
                return treeNode?.orgName?.includes(inputValue)
              }}
              onChange={changeOrgTreeCode}
              onTreeExpand={changeOrgTreeExpandKeys}
            />
          </Form.Item>
          <Form.Item wrapperCol={{ offset: 4, span: 19 }}>
            <Button
              disabled={selectedRecord?.sourceSystem !== '20'}
              loading={belongOrgOkLoading}
              type='primary'
              htmlType='submit'
            >
              确定
            </Button>
          </Form.Item>
        </Form>
      </Modal>
      {/* 组织授权弹窗 */}
      <Modal
        // prettier-ignore
        title={<div>修改授权组织<span style={{color: '#bfbfbf'}}>{`(${orgRightsTitle})`}</span></div>}
        open={orgRightsVisible}
        destroyOnClose
        getContainer={false}
        onCancel={() => {
          setOrgRightsVisible(false)
        }}
        footer={null}
      >
        {
          // prettier-ignore
          orgRightsLoading ? <Skeleton paragraph={{rows: 4}} title={false} active/> : <Form
                        labelCol={{span: 4}}
                        wrapperCol={{span: 19}}
                        labelAlign='right'
                        onFinish={handleOrgRightsOk}
                    >
                        <Form.Item label='组织'>
                            <Tree
                                //rootClassName={styles.treeWrapper}
                                checkedKeys={areaTreeCode}
                                expandedKeys={areaTreeExpandKeys}
                                treeData={areaTreeData}
                                checkable
                                fieldNames={{key: 'areaCode', title: 'areaName'}}
                                onCheck={changeAreaTreeCode}
                                onExpand={changeAreaTreeExpandKeys}
                            />
                        </Form.Item>
                        <Form.Item wrapperCol={{offset: 4, span: 19}}>
                            <Button loading={orgRightsOkLoading} type='primary' htmlType='submit'>
                                确定
                            </Button>
                        </Form.Item>
                    </Form>
        }
      </Modal>
    </div>
  )
}

export default UserManagement
