export const handleOrgTree = data => {
  const newArr = []
  for (let index = 0; index < data.length; index++) {
    const item = data[index]
    if (item?.select) {
      newArr.push(item?.orgCode)
    }
    if (item?.children?.length > 0) {
      newArr.push(...handleOrgTree(item?.children))
    }
  }
  return newArr
}

export const handleAreaTree = data => {
  const newArr = []
  for (let index = 0; index < data.length; index++) {
    const item = data[index]
    if (item?.originalSelect) {
      newArr.push(item?.areaCode)
    }
    if (item?.children?.length > 0) {
      newArr.push(...handleAreaTree(item?.children))
    }
  }
  return newArr
}
