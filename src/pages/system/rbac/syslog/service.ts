import request from '@/request'

const serviceList = {
  // 操作日志查询
  queryOperateLog: params => {
    return request.post<any>('/zhyy/manager/extra/operate/queryOperateLog', params)
  },

  // 导出接口
  exportOperateLogExcel: params => {
    return request.post<any>('/zhyy/manager/extra/operate/exportOperateLogExcel', params, {
      responseType: 'blob'
    })
  },

  // 操作日志查询
  build4LevelOrgTreeAll: params => {
    return request.post<any>('/zhyy/employee/org/build4LevelOrgTreeAll', params)
  },

  // 事件类型 下拉列表 树形结构
  buildEventTypeTree: params => {
    return request.post<any>('/zhyy/manager/extra/operate/buildEventTypeTree', params)
  },

  // 角色下拉列表
  queryAllEnableRole: () => {
    return request.get<any>('/zhyy/manager/core/role/queryAllEnableRole')
  },
}

export default serviceList
