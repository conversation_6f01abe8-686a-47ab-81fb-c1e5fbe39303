import {
  Button,
  Cascader,
  CascaderProps,
  Col,
  Form,
  FormProps,
  GetProp, Input,
  message, Modal,
  Row, Select,
  Space,
  Table,
} from 'antd'
import dayjs from 'dayjs'
import {DownloadOutlined, FullscreenExitOutlined, FullscreenOutlined} from '@ant-design/icons'
import { useEffect, useRef, useState } from 'react'
import { useRequest } from '@/hooks'
import syslogService from '@/pages/system/rbac/syslog/service.ts'
import { downFile, openNotification } from '@/utils/down.tsx'
import EditSysLogForm from './components/EditSysLogForm'
import styles from "@/pages/system/rbac/Index.module.scss";

type DefaultOptionType = GetProp<CascaderProps, 'options'>[number]

type FieldType = {
  month?: string
  unit?: string
  curPost?: string
}

const ReportPer: React.FC = () => {
  const columns = [
    {
      title: '序号',
      key: 'num',
      dataIndex: 'num',
      align: 'center',
      width: 50,
      render: (_text, _record, index) => {
        return pagination?.pageSize * (pagination?.pageNum - 1) + index + 1
      }
    },
    {
      title: '工号',
      key: 'oa',
      dataIndex: 'oa',
      align: 'center',
      fixed: 'left',
      width: 50,
      children: []
    },
    {
      title: '姓名',
      key: 'fullName',
      dataIndex: 'fullName',
      align: 'center',
      fixed: 'left',
      width: 75,
      children: []
      // onCell: (record:orgOption,rowIndex:string) => mergeCells(record?.unit,tableData,'unit',rowIndex),
    },
    {
      title: '部门',
      key: 'unitName',
      dataIndex: 'unitName',
      align: 'center',
      fixed: 'left',
      width: 150,
      children: []
    },
    {
      title: '角色',
      key: 'roleName',
      dataIndex: 'roleName',
      align: 'center',
      fixed: 'left',
      width: 100,
      children: []
    },
    {
      title: '事件类型',
      key: 'operateDetail',
      dataIndex: 'operateDetail',
      align: 'center',
      fixed: 'left',
      width: 100,
      children: []
    },
    {
      title: '操作时间',
      key: 'operateDate',
      dataIndex: 'operateDate',
      align: 'center',
      fixed: 'left',
      width: 100,
      children: []
    },
    // {
    //   title: '操作',
    //   key: 'name',
    //   dataIndex: 'name',
    //   align: 'center',
    //   fixed: 'left',
    //   width: 100,
    //   render: (_text, record) => {
    //     return <Space>
    //         <a style={{color: '#ff0000', marginRight: 10}} onClick={() => handleEditRecord(record)}>编辑</a>
    //         <Popconfirm
    //             title="您确定要删除这条数据吗?"
    //             onConfirm={() => handleDeleteRecord(record)}
    //             onCancel={() => {}}
    //             okText="确定"
    //             cancelText="取消"
    //         >
    //             <a style={{color: '#ff0000'}}>删除</a>
    //         </Popconfirm>
    //     </Space>
    //   }
    // },
  ]

  const topRef = useRef(null)
  const contentRef = useRef(null)
  const tableTopRef = useRef(null)
  const [formRef] = Form.useForm()
  const [height, setHeight] = useState(0)
  const [tableData, setTableData] = useState<any>([])
  const [tableLoading, setTableLoading] = useState<boolean>(false)
  const [tableColumns, setTableColumns] = useState<any>(columns)
  const [orgValueArr, setOrgValueArr] = useState([]);
  const [isReset, setIsReset] = useState<number>(0)
  const [pagination, setPagination] = useState({
    total: 0,
    pageNum: 1,
    pageSize: 50
  })

  const [orgList, setOrgList] = useState<any>([])
  const [eventList, setEventList] = useState<any>([])
  const [roleOptions, setRoleOptions] = useState([]);
  const [editRecord, setEditRecord] = useState({});
  const [editVisible, setEditVisible] = useState(false);
  const [showTitle, setShowTitle] = useState<boolean>(true)

  // 组织下拉列表
  const { runAsync: build4LevelOrgTreeAll } = useRequest(syslogService.build4LevelOrgTreeAll, {
    manual: true
  })
  // 事件类型下拉列表
  const { runAsync: buildEventTypeTree } = useRequest(syslogService.buildEventTypeTree, {
    manual: true
  })
  // 角色下拉列表
  const { runAsync: queryAllEnableRole } = useRequest(syslogService.queryAllEnableRole, {
    manual: true
  })
  const { runAsync: queryOperateLog } = useRequest(syslogService.queryOperateLog, {
    manual: true
  })
  const { runAsync: exportOperateLogExcel } = useRequest(syslogService.exportOperateLogExcel, {
    manual: true
  })

  useEffect(() => {
    getEnumTypes()
    setTableColumns(columns)
    queryTableData()
  }, [])

  useEffect(() => {
    initHeight()
  }, [(document.querySelector('.assessmentResults_table .ant-table-header') || {})['offsetHeight']])

  useEffect(() => {
    initHeight()
  }, [tableData])

  // useEffect(() => {
  //   if (isReset > 0) {
  //     queryTableData()
  //   }
  // }, [isReset])

  useEffect(() => {
    if (pagination?.total > 0) {
      queryTableData()
    }
  }, [pagination.pageNum, pagination.pageSize])

  const initHeight = () => {
    const headerHeight = (document.querySelector('.assessmentResults_table .ant-table-header') || {})[
        'offsetHeight'
        ]
    const pageHeight = (document.querySelector('.assessmentResults_table .ant-table-pagination') ||
        {})['offsetHeight'] || 26
    if (headerHeight) {
      setHeight(tableTopRef.current.offsetHeight + headerHeight + pageHeight)
    }
  }

  const getEnumTypes = async () => {
    const [
      [orgError, orgData],
      [eventError, eventData],
      [roleError, roleData],
    ] = await Promise.all([
      build4LevelOrgTreeAll({ code: '1002', region: '' }),
      buildEventTypeTree({ code: '1003', region: '' }),
      queryAllEnableRole({ code: '1009', region: '' }),
    ])
    if (
        orgError ||
        eventError ||
        roleError
    ) {
      return
    }
    if (orgData.STATUS === '0000') {
      setOrgList(orgData.DATA)
    }
    if (eventData.STATUS === '0000') {
      setEventList(eventData.DATA)
    }
    if (roleData.STATUS === '0000') {
      setRoleOptions(roleData.DATA)
    }
  }

  // 查询表格数据
  const queryTableData = async () => {
    const values = formRef.getFieldsValue()
    const curOrg = orgValueArr ? orgValueArr[orgValueArr.length - 1] : {};
    setTableLoading(true)
    const [error, res] = await queryOperateLog({
      ...values,
      // org: values?.org,
      eventType: values?.eventType ? values?.eventType[values?.eventType?.length-1] : '',
      orgId4: Number(curOrg?.level) === 4 ? curOrg?.orgId : '',
      orgId5: Number(curOrg?.level) === 5 ? curOrg?.orgId : '',
      orgId6: Number(curOrg?.level) === 6 ? curOrg?.orgId : '',
      ...pagination
    })
    setTableLoading(false)
    if (error) {
      return
    }
    if (res.STATUS === '0000') {
      const {
        DATA: { data }
      } = res
      setTableData(data)
      setPagination({
        ...pagination,
        total: res.DATA?.totalCount
      })
    } else {
      message.error(res?.MESSAGE)
    }
  }

    // const handleDeleteRecord = async (record) => {
    //     setTableLoading(true)
    //     const [error, res] = await getEmployeePerPag({
    //         ...record
    //     })
    //     if (error) {
    //       setTableLoading(false);
    //       return
    //     }
    //     if (res.STATUS === '0000') {
    //       antdUtils.message.success('删除成功！');
    //       queryTableData();
    //     } else {
    //       message.error(res?.MESSAGE);
    //       setTableLoading(false);
    //     }
    // }
    //
    // const handleEditRecord = (record) => {
    //   setEditVisible(true)
    //   setEditRecord(record)
    // }

  // 初始化查询条件
  const initDate = () => {
    const date = dayjs().subtract(1, 'month')
    formRef.setFieldsValue({
      month: date,
      org: []
    })
  }

  // 切换组织
  const handleOrgChange = (_value, selectedOptions) => {
    setOrgValueArr(selectedOptions);
  };


  // 点击查询
  const onFormFinish: FormProps<FieldType>['onFinish'] = values => {
    console.log('Success:', values)
    queryTableData()
  }
  // 点击导出
  const exportToExcelFun = async () => {
    try {
      // 调用接口获取 Blob 文件数据
      openNotification('正在导出', 0, 'loading')
      const values = formRef.getFieldsValue();
      const curOrg = orgValueArr ? orgValueArr[orgValueArr.length - 1] : {};
      const response = await exportOperateLogExcel({
        ...values,
        orgId4: Number(curOrg?.level) === 4 ? curOrg?.orgId : '',
        orgId5: Number(curOrg?.level) === 5 ? curOrg?.orgId : '',
        orgId6: Number(curOrg?.level) === 6 ? curOrg?.orgId : '',
        month: values?.month?.format('YYYYMM')
        // ...pagination
      })
      downFile(response)
    } catch (error) {
      openNotification('导出失败', 1, 'error')
      console.error('Download failed:', error)
    }

    // fetch('/efop/file/人员统计报表（个人）.xls', {
    //   method: 'GET',
    //   headers: {
    //     'Content-Type': 'application/vnd.ms-excel' // MIME 类型
    //   },
    //   credentials: 'include' // 如果需要发送 cookie
    // })
    //   .then(response => {
    //     if (!response.ok) {
    //       throw new Error('Network response was not ok')
    //     }
    //     return response.blob() // 将响应转换为 Blob
    //   })
    //   .then(blob => {
    //     const link = document.createElement('a')
    //     link.href = window.URL.createObjectURL(blob) // 创建 Blob URL
    //     link.download = '人员统计报表（个人）.xls' // 下载文件时显示的文件名
    //     link.click() // 模拟点击下载
    //   })
    //   .catch(error => {
    //     console.error('Download error:', error)
    //   })
  }
  // 查询重置
  const onReset = () => {
    const newReset = isReset + 1;
    formRef.resetFields();
    setOrgValueArr([]);
    initDate();
    setIsReset(newReset)
  }

  const filter = (inputValue: string, path: DefaultOptionType[]) =>
      path.some(
          option =>
              (option.orgName as string).toLowerCase().indexOf(inputValue.trim().toLowerCase()) > -1
      )

  // 切换分页
  const onChangePage = page => {
    const newPage = {
      total: page?.total,
      pageNum: page?.current,
      pageSize: page?.pageSize
    }
    setPagination(newPage)
  }

  return (
      <div className={`${'h-full pt-[0.5rem] px-[0rem]'} ${styles.rbac_page}`}>
        <div ref={topRef} className={'bg-white pt-2 px-8 mb-[0.5rem]'}>
          <Form
              form={formRef}
              labelCol={{span: 5}}
              wrapperCol={{span: 19}}
              initialValues={{
                tag: ''
              }}
              onFinish={onFormFinish}
              // onFinishFailed={onFinishFailed}
              autoComplete='off'
          >
            <Row
                gutter={24}
                // justify="end"
            >
              <Col span={6}>
                <Form.Item label='工号' name='oa'>
                  <Input placeholder={'请输入工号'} allowClear />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label='组织' name='org'>
                  <Cascader
                      allowClear={true}
                      changeOnSelect
                      expandTrigger='hover'
                      displayRender={labels => labels[labels.length - 1]}
                      options={orgList}
                      onChange={handleOrgChange}
                      fieldNames={{
                        value: 'orgId',
                        label: 'orgName',
                        children: 'children'
                      }}
                      // showCheckedStrategy={Cascader.SHOW_CHILD}
                      // onChange={(value, selectedOptions) => {
                      //   console.log(value, selectedOptions)
                      //   setCascaderSelected(selectedOptions)
                      // }}
                      placeholder='请选择组织'
                      showSearch={{filter}}
                      onSearch={value => console.log(value)}
                  />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label='事件类型' name='eventType'>
                  <Cascader
                      allowClear={true}
                      changeOnSelect
                      expandTrigger='hover'
                      displayRender={labels => labels[labels.length - 1]}
                      options={eventList}
                      onChange={handleOrgChange}
                      fieldNames={{
                        value: 'id',
                        label: 'name',
                        children: 'children'
                      }}
                      placeholder='请选择事件类型'
                      showSearch={{filter}}
                      onSearch={value => console.log(value)}
                  />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label='角色' name='roleCode'>
                  <Select className='w-full' placeholder='请选择角色' allowClear={true} showSearch options={roleOptions} fieldNames={{
                      value: 'roleCode',
                      label: 'roleName',
                    }}/>
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label='页面名称' name='menuName'>
                  <Input placeholder={'请输入页面名称'} allowClear />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label='姓名' name='fullName'>
                  <Input placeholder={'请输入姓名'} allowClear />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item labelCol={{span: 0}} wrapperCol={{span: 24}}>
                  <Space size='small'>
                    <Button type='primary' htmlType='submit'>
                      查询
                    </Button>
                    <Button htmlType='button' onClick={() => onReset()}>
                      重置
                    </Button>
                  </Space>
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </div>
        <div
            ref={contentRef}
            className='relative pb-[0.5rem] pt-[0.1rem] px-2 mt-[0.5rem] bg-white'
            style={{height: `calc(100% - ${topRef.current?.offsetHeight + 17}px)`}}
        >
          <div ref={tableTopRef} className={`${'flex justify-between items-center overflow-hidden mb-[0.1rem]'} ${styles.animation_box} ${showTitle ? 'h-[1.6rem]' : 'h-0'}`}>
            <>
              {
                showTitle ?
                    <FullscreenExitOutlined
                        className={`${styles.shousuo_icon} ${'absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10'}`}
                        onClick={() => {
                          setShowTitle(false);
                          setTimeout(() => {
                            initHeight();
                          }, 300)
                        }}/> :
                    <FullscreenOutlined
                        className={`${styles.shousuo_icon} ${'absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10'}`}
                        onClick={() => {
                          setShowTitle(true);
                          setTimeout(() => {
                            initHeight();
                          }, 300)
                        }}/>
              }
            </>
            <span className='font-bold text-[0.8rem] ml-[1rem]'>数据列表</span>
            <Button danger ghost icon={<DownloadOutlined/>} onClick={() => exportToExcelFun()}>
              导出
            </Button>
          </div>
          <Table
              className='assessmentResults_table'
              columns={tableColumns}
              dataSource={tableData}
              loading={tableLoading}
              bordered
              scroll={{
                // x: 'max-content',
                y: `calc(${contentRef.current?.offsetHeight - height}px - 1.4rem)`
              }}
              onChange={onChangePage}
              pagination={{
                ...pagination,
                total: pagination?.total,
                showSizeChanger: true,
                showQuickJumper: true,
                pageSizeOptions: ['10', '20', '50']
              }}
          />
        </div>

        <Modal
            title={'系统日志编辑'}
            open={editVisible}
            destroyOnClose
            onCancel={() => {
              setEditVisible(false)
              setEditRecord({})
            }}
            footer={null}
        >
          <EditSysLogForm
              record={editRecord}
              cancelRoleModal={
                () => {
                  setEditVisible(false)
                  setEditRecord({})
                }
              }
          />
        </Modal>
      </div>
  )
}
export default ReportPer
