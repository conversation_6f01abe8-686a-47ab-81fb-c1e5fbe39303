import { useState, useEffect } from 'react'
import Api from '../service'
import { Form, Input, Select, Switch, Button, message } from 'antd'
import { RedoOutlined, LoadingOutlined } from '@ant-design/icons'

const EditSysLogForm = props => {
  const { record, roleTypeList } = props
  const [form] = Form.useForm()
  const [roleCodeLoading, setRoleCodeLoading] = useState(false)
  const [confirmLoading, setConfirmLoading] = useState(false)

  useEffect(() => {
    // 编辑
    if (record) {
      // const { roleCode, roleName, roleType, sortId, isTotal, remark } = record
      form.setFieldsValue({
          ...record
      })
    }
  }, [])


  // 确定
  const handleOk = formInfo => {
  }
  return (
    <Form
      form={form}
      labelCol={{ span: 6 }}
      wrapperCol={{ span: 14 }}
      labelAlign='right'
      onFinish={handleOk}
    >
        <Form.Item
            label='工号'
            name='roleName'
            rules={[{ required: true, message: '请输入工号！' }]}
        >
            <Input placeholder='请输入工号' />
        </Form.Item>
      <Form.Item
        label='姓名'
        name='roleName'
        rules={[{ required: true, message: '请输入姓名！' }]}
      >
        <Input placeholder='请输入姓名' />
      </Form.Item>
        <Form.Item
            label='部门'
            name='roleName'
            rules={[{ required: true, message: '请输入部门！' }]}
        >
            <Input placeholder='请输入部门' />
        </Form.Item>
        <Form.Item
            label='角色'
            name='roleName'
            rules={[{ required: true, message: '请输入角色！' }]}
        >
            <Input placeholder='请输入角色' />
        </Form.Item>
      <Form.Item
          label='事件类型'
          name='roleName'
          rules={[{ required: true, message: '请输入事件类型！' }]}
      >
        <Input placeholder='请输入事件类型' />
      </Form.Item>
      <Form.Item
          label='操作时间'
          name='roleName'
          rules={[{ required: true, message: '请输入操作时间！' }]}
      >
        <Input placeholder='请输入操作时间' />
      </Form.Item>
      <Form.Item wrapperCol={{ offset: 6, span: 14 }}>
        <Button loading={confirmLoading} type='primary' htmlType='submit'>
          确定
        </Button>
      </Form.Item>
    </Form>
  )
}

export default EditSysLogForm
