import request from '@/request'

const entrylogService = {
  getQueryConditions: params => {
    return request.get<any>('/zhyy/getQueryConditions', { params })
  },

  queryList: params => {
    return request.get<any>('/zhyy/blue/sql/queryList', { params })
  },

  loginLog: params => {
    return request.get<any>('/zhyy/manager/extra/loginlog/list', { params })
  },

  loginlogDownload: params => {
    return request.get<any>('/zhyy/manager/extra/loginlog/download', {
      params,
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      },
      responseType: 'blob'
    })
  }
}

export default entrylogService
