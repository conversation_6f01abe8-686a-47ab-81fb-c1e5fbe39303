import request from '@/request'

const serviceList = {
  buildMenuTree: params => {
    return request.get('/zhyy/manager/core/menu/buildMenuTree', { params })
  },
  queryRole: params => {
    return request.post('/zhyy/manager/extra/core/resource/queryResource', params)
  },
  addResource: params => {
    return request.post('/zhyy/manager/core/resource/addResource', params)
  },
  updateResource: params => {
    return request.post('/zhyy/manager/core/resource/updateResource', params)
  },
  deleteMenu: params => {
    return request.post('/zhyy/manager/core/resource/deleteResource', params)
  }
}

export default serviceList
