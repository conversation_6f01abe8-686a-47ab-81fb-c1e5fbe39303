import { useState, useEffect } from 'react'
import Api from './service'
import {
  message,
  Table,
  Button,
  Input,
  Modal,
  Select,
  Tooltip,
  Skeleton,
  Form,
  TreeSelect,
  Radio,
  Row,
  Col
} from 'antd'
import styles from '../Index.module.scss'
const FuncManagement = () => {
  const [form] = Form.useForm()
  // 初始化state
  const [amountFlag, setAmountFlag] = useState(true)
  const [funcName, setFuncName] = useState('')
  const [menuName, setMenuName] = useState('')
  const [funcType, setFuncType] = useState('')

  // 表格数据
  const [tableLoading, setTableLoading] = useState(false)
  const [tableTotal, setTableTotal] = useState(0)
  const [tableData, setTableData] = useState([])
  const [pageNum, setPageNum] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [queryKey, setQuery<PERSON>ey] = useState(0)
  const [selectedRecord, setSelectedRecord] = useState<any>({})

  // 功能弹窗数据
  const [funcAmountFlag, setFuncAmountFlag] = useState(true)
  const [funcLoading, setFuncLoading] = useState(false)
  const [funcVisible, setFuncVisible] = useState(false)
  const [menuTreeData, setMenuTreeData] = useState([])
  //const [menuTreeCode, setMenuTreeCode] = useState('');
  const [menuTreeName, setMenuTreeName] = useState('')
  const [modeType, setModeType] = useState('')

  // 删除弹窗数据
  const [delVisible, setDelVisible] = useState(false)
  const [delLoading, setDelLoading] = useState(false)

  useEffect(() => {
    getTableData()
  }, [])

  // 获取表格数据
  const getTableData = () => {
    const params = {
      resourceName: funcName,
      menuName,
      resourceType: funcType,
      subResourceType: '',
      pageNum,
      pageSize
    }
    if (!amountFlag) setTableLoading(true)
    // prettier-ignore
    Api.queryRole(params).then((response: any) => {
            const res = response[1]
            if (res.STATUS === '0000') {
                const {data, totalCount} = res.DATA;
                const tableData = data?.map((item, index) => {
                    item.index = (pageNum - 1) * pageSize + index + 1;
                    return item;
                });
                setTableData(tableData)
                setTableTotal(totalCount)
            } else {
                message.error(res.MESSAGE);
            }
            if (!amountFlag) setTableLoading(false)
            if (amountFlag) setAmountFlag(false);
        }).catch(() => {
            if (!amountFlag) setTableLoading(false)
            if (amountFlag) setAmountFlag(false);
        });
  }

  // 获取菜单数据
  const getMenuTreeData = () => {
    return new Promise((resolve, reject) => {
      // prettier-ignore
      Api.buildMenuTree({}).then((response: any) => {
                const res = response[1]
                if (res.STATUS === '0000') {
                    setMenuTreeData(res.DATA);
                } else {
                    message.error(res.MESSAGE);
                }
                resolve(res);
            }).catch(e => reject(e));
    })
  }

  // 功能名称改变
  const changeFuncName = e => {
    setFuncName(e.target.value)
  }

  // 菜单名称改变
  const changeMenuName = e => {
    setMenuName(e.target.value)
  }

  // 功能类型切换
  const changeFuncType = value => {
    setFuncType(value)
  }

  // 显示功能弹窗
  const showFuncModal = (type, record) => {
    setFuncVisible(true)
    setModeType(type)
    setSelectedRecord(record)
    getMenuTreeData()
    let params = {
      funcCode: '',
      funcName: '',
      funcType: '100',
      menuTreeCode: undefined,
      sortId: ''
    }
    if (type === 'edit') {
      // 修改
      const { resourceCode, resourceName, resourceType, menuCode, sortId } = record
      params = {
        funcCode: resourceCode,
        funcName: resourceName,
        funcType: resourceType,
        menuTreeCode: menuCode,
        sortId
      }
    }
    setTimeout(() => {
      setFuncAmountFlag(false)
      form.setFieldsValue(params)
    })
  }

  // 取消功能弹窗
  const cancelFuncModal = () => {
    setFuncVisible(false)
    setModeType('')
    setSelectedRecord({})
    setFuncAmountFlag(true)
  }

  // 所在菜单切换
  const changeMenuTreeCode = (_, option) => {
    //setMenuTreeCode(value)
    setMenuTreeName(option[0])
  }

  // 确定
  const confirmFuncModal = formInfo => {
    const { funcCode, funcName, funcType, menuTreeCode, sortId } = formInfo
    const params = {
      resourceCode: funcCode,
      resourceName: funcName,
      resourceType: funcType,
      subResourceType: '',
      realCode: '',
      menuCode: menuTreeCode,
      menuName: menuTreeName,
      sortId,
      remark: ''
    }
    if (modeType === 'edit') (params as any).id = selectedRecord?.id
    const method = modeType === 'add' ? Api.addResource : Api.updateResource
    setFuncLoading(true)
    // prettier-ignore
    method(params).then((response: any) => {
            const res = response[1]
            if (res.STATUS === '0000') {
                message.success(`${modeType === 'add' ? '新建' : '修改'}菜单成功`);
                setFuncVisible(false);
                setModeType('');
                setSelectedRecord({});
                setFuncAmountFlag(true);
                queryInfo();
            } else {
                message.error(res.MESSAGE);
            }
            setFuncLoading(false)
        }).catch(() => {
            setFuncLoading(false)
        });
  }

  // 显示删除弹窗
  const showDelModal = record => {
    setDelVisible(true)
    setSelectedRecord(record)
  }

  // 取消删除弹窗
  const cancelDelModal = () => {
    setDelVisible(false)
    setSelectedRecord({})
  }

  // 删除弹窗确定
  const confirmDelModal = () => {
    const params = { id: selectedRecord?.id }
    setDelLoading(true)
    // prettier-ignore
    Api.deleteMenu(params).then((response: any) => {
            const res = response[1]
            if (res.STATUS === '0000') {
                message.success('删除成功！');
                cancelDelModal();
                queryInfo();
            } else {
                message.error(res.MESSAGE);
            }
            setDelLoading(false)
        }).catch(() => {
            setDelLoading(false)
        });
  }

  useEffect(() => {
    getTableData()
  }, [pageSize, pageNum, queryKey])

  // 查询
  const queryInfo = () => {
    setPageNum(1)
    setPageSize(10)
    setQueryKey(prevKey => prevKey + 1) // 更新 queryKey，触发查询
  }

  // 分页切换
  const pageChange = (page, pageSize) => {
    setPageNum(page)
    setPageSize(pageSize)
  }
  const columns = [
    {
      title: '序号',
      key: 'index',
      dataIndex: 'index',
      width: 100,
      align: 'center' as const
    },
    {
      title: '功能编码',
      key: 'resourceCode',
      dataIndex: 'resourceCode',
      width: 200,
      align: 'center' as const,
      ellipsis: { showTitle: false },
      // prettier-ignore
      render: text => <Tooltip placement="topLeft" title={text}
                                     getPopupContainer={tiggerNode => tiggerNode?.closest('div.ant-table-body') || document.body}>{text}</Tooltip>
    },
    {
      title: '功能名称',
      key: 'resourceName',
      dataIndex: 'resourceName',
      width: 200,
      align: 'center' as const
    },
    {
      title: '功能类型',
      key: 'resourceType',
      dataIndex: 'resourceType',
      width: 200,
      align: 'center' as const,
      render: text => (text === '100' ? '按钮' : text)
    },
    {
      title: '菜单',
      key: 'menuName',
      dataIndex: 'menuName',
      width: 200,
      align: 'center' as const
    },
    {
      title: '操作',
      width: 200,
      align: 'center' as const,
      // prettier-ignore
      render: (_, record) => <>
                <Button type='link' onClick={() => showFuncModal('edit', record)}>
                    修改
                </Button>
                <Button type='link' onClick={() => showDelModal(record)}>删除</Button>
            </>
    }
  ]

  return (
    <div className={`${'pt-[0.5rem] px-[0rem]'} ${styles.rbac_page}`}>
      <div className='bg-white pt-[0.5rem] px-[1.75rem] mb-[0.5rem]'>
        <Form labelCol={{ span: 6 }}>
          <Row gutter={24}>
            <Col span={6}>
              <Form.Item name='code1' label='功能名称'>
                <Input
                  placeholder='请输入功能名称'
                  allowClear
                  onChange={changeFuncName}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name='code2' label='菜单名称'>
                <Input
                  placeholder='请输入菜单名称'
                  allowClear
                  onChange={changeMenuName}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name='code3' label='功能类型'>
                <Select placeholder='请选择功能类型' allowClear onChange={changeFuncType}>
                  <Select.Option key='100' value='100'>
                    按钮
                  </Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <div className='text-right'>
                <Button type='primary' onClick={queryInfo}>
                  查询
                </Button>
                <Button
                  type='primary'
                  className='ml-[0.4rem]'
                  onClick={() => showFuncModal('add', {})}
                >
                  新增功能
                </Button>
              </div>
            </Col>
          </Row>
        </Form>
      </div>
      <div className='bg-white pt-[0.5rem] px-[0.5rem]'>
        <Table
          loading={tableLoading}
          dataSource={tableData}
          columns={columns}
          scroll={{ x: 'max-content' }}
          bordered
          size='small'
          sticky={true}
          pagination={{
            total: tableTotal,
            showTotal: () => `共 ${tableTotal} 条`,
            defaultCurrent: 1,
            defaultPageSize: 10,
            current: pageNum,
            pageSize,
            showSizeChanger: true,
            showQuickJumper: true,
            onChange: pageChange
          }}
        />
      </div>
      <Modal
        title={`${modeType === 'add' ? '新建' : '修改'}`}
        open={funcVisible}
        destroyOnClose
        onCancel={cancelFuncModal}
        footer={null}
      >
        {funcAmountFlag ? (
          <Skeleton paragraph={{ rows: 10 }} title={false} active />
        ) : (
          <Form
            form={form}
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 16 }}
            labelAlign='right'
            onFinish={confirmFuncModal}
          >
            <Form.Item
              label='功能编码'
              name='funcCode'
              rules={[{ required: true, message: '请输入功能编码！' }]}
            >
              <Input placeholder='请输入功能编码' />
            </Form.Item>
            <Form.Item
              label='功能名称'
              name='funcName'
              rules={[{ required: true, message: '请输入功能名称！' }]}
            >
              <Input placeholder='请输入功能名称' />
            </Form.Item>
            <Form.Item label='功能类型' name='funcType'>
              <Radio.Group buttonStyle='solid'>
                <Radio.Button value='100'>按钮</Radio.Button>
              </Radio.Group>
            </Form.Item>
            <Form.Item label='所在菜单' name='menuTreeCode'>
              <TreeSelect
                placeholder='请选择所在菜单'
                treeData={menuTreeData}
                showSearch
                treeDefaultExpandAll={false}
                dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
                fieldNames={{ label: 'menuName', value: 'menuCode' }}
                getPopupContainer={triggerNode => triggerNode.parentElement || document.body}
                filterTreeNode={(inputValue, treeNode) => {
                  return treeNode?.menuName?.includes(inputValue)
                }}
                onChange={changeMenuTreeCode}
              />
            </Form.Item>
            <Form.Item
              label='排序字段'
              name='sortId'
              rules={[{ pattern: /^[+]{0,1}(\d+)$/, message: '只支持数字，请正确输入！' }]}
            >
              <Input placeholder='请输入排序字段' />
            </Form.Item>
            <Form.Item wrapperCol={{ offset: 6, span: 16 }}>
              <Button loading={funcLoading} type='primary' htmlType='submit'>
                确定
              </Button>
            </Form.Item>
          </Form>
        )}
      </Modal>
      <Modal
        width='20%'
        open={delVisible}
        confirmLoading={delLoading}
        destroyOnClose
        closable={false}
        onOk={confirmDelModal}
        onCancel={cancelDelModal}
      >
        是否删除<span style={{ color: '#ff0000' }}>{selectedRecord?.resourceName}</span>?
      </Modal>
    </div>
  )
}

export default FuncManagement
