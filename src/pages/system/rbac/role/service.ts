import request from '@/request'

const serviceList = {
  exportRoleInfo: params => {
    return request.get('/zhyy/manager/extra/role/exportRoleInfo', {
      params,
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      },
      responseType: 'blob'
    })
  },
  queryList: params => {
    return request.get('/zhyy/blue/sql/queryList?key=station', params)
  },
  queryRoleType: params => {
    return request.get('/zhyy/blue/sql/queryList?key=dictionary&dictCode=roleType', params)
  },
  extraUpdateRole: params => {
    return request.post('/zhyy/manager/extra/role/extraUpdateRole', params)
  },
  deleteRole: params => {
    return request.post('/zhyy/manager/core/role/deleteRole', params)
  },
  smartSetRoleResource: params => {
    return request.post('/zhyy/manager/core/resource/smartSetRoleResource', params)
  },
  queryRoleResource: params => {
    return request.post('/zhyy/manager/core/resource/queryRoleResource', params)
  },
  queryRoleStationAuth: params => {
    return request.post('/zhyy/manager/core/station/queryRoleStationAuth', params)
  },
  setRoleStationAuth: params => {
    return request.post('/zhyy/manager/core/station/setRoleStationAuth', params)
  },
  setRoleMenuRecursive2: params => {
    return request.post('/zhyy/manager/core/menu/setRoleMenuRecursive2', params)
  },
  listSetRoleResource: params => {
    return request.post('/zhyy/manager/extra/role/listSetRoleResource', params)
  },
  listSetRoleStationAuth: params => {
    return request.post('/zhyy/manager/extra/role/listSetRoleStationAuth', params)
  },
  listSetRoleMenuRecursive: params => {
    return request.post('/zhyy/manager/extra/role/listSetRoleMenuRecursive', params)
  },
  queryAllResource: params => {
    return request.post('/zhyy/manager/extra/core/resource/queryAllResource', params)
  },
  buildRoleMenuSelectTree: params => {
    return request.post('/zhyy/manager/core/menu/buildRoleMenuSelectTree', params)
  },
  obtainNextRoleCode: params => {
    return request.post('/zhyy/manager/core/role/obtainNextRoleCode', params)
  },
  extraAddRole: params => {
    return request.post('/zhyy/manager/extra/role/extraAddRole', params)
  },
  extraQueryRole: params => {
    return request.post('/zhyy/manager/extra/role/extraQueryRole', params)
  }
}

export default serviceList
