import { useState, useEffect } from 'react'
import RoleForm from './components/RoleForm'
import Menu from './components/Menu'
import Station from './components/Station'
import Function from './components/Function'
import { download } from '../user/utils'
import { handleMenuTreeData } from './assets/utils'
import Api from './service'
import {
  Form,
  Input,
  Button,
  Modal,
  Table,
  Select,
  message,
  Row,
  Skeleton,
  Tabs,
  Col
} from 'antd'
import styles from '../Index.module.scss'

const RoleManagement = () => {
  // 初始化状态
  const [amountFlag, setAmountFlag] = useState(true)
  const [roleName, setRoleName] = useState('')
  const [roleTypeCode, setRoleTypeCode] = useState('')
  /* 表格数据 */
  const [tableLoading, setTableLoading] = useState(false)
  const [tableTotal, setTableTotal] = useState(0)
  const [tableData, setTableData] = useState([])
  const [pageNum, setPageNum] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [queryKey, setQueryKey] = useState(0)
  const [selectedRowKeys, setSelectedRowKeys] = useState([])
  const [selectedRows, setSelectedRows] = useState([])
  const [selectedRecord, setSelectedRecord] = useState<any>({})
  /* 下载数据 */
  const [downloadLoading, setDownloadLoading] = useState(false)
  /* 菜单数据 */
  const [menuAmountFlag, setMenuAmountFlag] = useState(true)
  const [menuVisible, setMenuVisible] = useState(false)
  const [menuLoading, setMenuLoading] = useState(false)
  const [menuTreeCode, setMenuTreeCode] = useState([])
  const [menuTreeData, setMenuTreeData] = useState([])
  const [menuExpandedKeys, setMenuExpandedKeys] = useState([])
  /* 岗位数据 */
  const [stationAmountFlag, setStationAmountFlag] = useState(true)
  const [stationLoading, setStationLoading] = useState(false)
  const [stationVisible, setStationVisible] = useState(false)
  const [stationId, setStationId] = useState([])
  const [stationList, setStationList] = useState([])
  /* 功能数据 */
  const [funcAmountFlag, setFuncAmountFlag] = useState(true)
  const [funcLoading, setFuncLoading] = useState(false)
  const [funcVisible, setFuncVisible] = useState(false)
  const [funcTableTotal, setFuncTableTotal] = useState(0)
  const [funcTableData, setFuncTableData] = useState([])
  const [funcSearchData, setFuncSearchData] = useState([])
  const [funcSelectedRowKeys, setFuncSelectedRowKeys] = useState([])
  const [funcSelectedRows, setFuncSelectedRows] = useState([])
  /* 角色弹窗数据 */
  const [roleVisible, setRoleVisible] = useState(false)
  const [roleMode, setRoleMode] = useState('')
  /* 删除弹窗数据 */
  const [delVisible, setDelVisible] = useState(false)
  /* 批量操作弹窗数据 */
  const [bacthAmountFlag, setBacthAmountFlag] = useState(true)
  const [batchVisible, setBatchVisible] = useState(false)
  const [batchLoading, setBatchLoading] = useState(false)
  const [delLoading, setDelLoading] = useState(false)
  const [roleTypeList, setRoleTypeList] = useState([])

  useEffect(() => {
    queryRoleType()
    getTableData()
  }, [])
  //获取角色类型
  const queryRoleType = () => {
    Api.queryRoleType({}).then((response: any) => {
      const res = response[1]
      if (res.STATUS === '0000') {
        const roleTypeList = res.DATA
        setRoleTypeList(roleTypeList)
      } else {
        message.error(res.MESSAGE)
      }
    })
  }

  // 获取表格数据
  const getTableData = () => {
    const params = {
      queryName: roleName,
      roleType: roleTypeCode,
      pageNum,
      pageSize
    }
    if (!amountFlag) setTableLoading(true)
    // prettier-ignore
    Api.extraQueryRole(params).then((response: any) => {
            const res = response[1]
            if (res.STATUS === '0000') {
                const {data, totalCount} = res.DATA;
                const tableData = data?.map((item, index) => {
                    const num = (pageNum - 1) * pageSize + index + 1;
                    return {
                        ...item,
                        key: num,
                        num,
                    };
                })
                setTableTotal(totalCount);
                setTableData(tableData);
            } else {
                message.error(res.MESSAGE);
            }
            if (!amountFlag) setTableLoading(false);
            if (amountFlag) setAmountFlag(false);
        }).catch(() => {
            if (!amountFlag) setTableLoading(false);
            if (amountFlag) setAmountFlag(false);
        });
  }

  // 文件导出
  const exportFile = () => {
    const params = {
      roleName,
      roleType: roleTypeCode
    }
    message.warning('文件正在导出，请稍后......')
    setDownloadLoading(true)
    // prettier-ignore
    Api.exportRoleInfo(params).then((response: any) => {
            const res = response[1]
            let fileName = res.headers['content-disposition']?.match(/filename=(.*)/)[1] || new Date().valueOf();
            fileName = `${decodeURI(fileName)}`;
            download({fileName, res: res.data});
            setDownloadLoading(false);
        }).catch(() => {
            setDownloadLoading(false);
        });
  }

  // 获取菜单数据
  const getMenuTreeData = selectedRecord => {
    // prettier-ignore
    return new Promise((resolve, reject) => {
            const roleCode = JSON.stringify(selectedRecord) == '{}' ?  '' : selectedRecord?.roleCode
            const params = {roleCode};
            Api.buildRoleMenuSelectTree(params).then((response: any) => {
                const res = response[1]
                if (res.STATUS === '0000') {
                    const menuTreeCode = handleMenuTreeData(res.DATA);
                    setMenuTreeCode(menuTreeCode);
                    setMenuTreeData(res.DATA);
                } else {
                    message.error(res.MESSAGE);
                }
                resolve(res);
            }).catch(e => reject(e));
        });
  }

  // 获取角色已选的岗位数据
  const getStationId = selectedRecord => {
    // prettier-ignore
    return new Promise((resolve, reject) => {
            const params = {
                roleCode: selectedRecord?.roleCode
            };
            Api.queryRoleStationAuth(params).then((response: any) => {
                const res = response[1]
                if (res.STATUS === '0000') {
                    const stationId = res.DATA?.map(item => item?.stationId);
                    setStationId(stationId);
                } else {
                    message.error(res.MESSAGE);
                }
                resolve(res);
            }).catch(e => reject(e));
        });
  }

  // 获取岗位数据
  const getStationList = () => {
    // prettier-ignore
    return new Promise((resolve, reject) => {
            Api.queryList({}).then((response: any) => {
                const res = response[1]
                if (res.STATUS === '0000') {
                    setStationList(res.DATA);
                } else {
                    message.error(res.MESSAGE);
                }
                resolve(res);
            }).catch(e => reject(e));
        });
  }

  // 获取角色已选的功能数据
  const getFuncSelectedData = selectedRecord => {
    // prettier-ignore
    return new Promise((resolve, reject) => {
            const params = {
                roleCode: selectedRecord?.roleCode,
                processorType: 'function'
            };
            Api.queryRoleResource(params).then((response: any) => {
                const res = response[1]
                if (res.STATUS === '0000') {
                    const funcSelectedRowKeys = res.DATA?.map(item => item?.resourceCode);
                    setFuncSelectedRowKeys(funcSelectedRowKeys);
                    setFuncSelectedRows(res.DATA);
                } else {
                    message.error(res.MESSAGE);
                }
                resolve(res);
            }).catch(e => reject(e));
        });
  }

  // 获取功能数据
  const getFuncTableData = () => {
    // prettier-ignore
    return new Promise((resolve, reject) => {
            Api.queryAllResource({}).then((response: any) => {
                const res = response[1]
                if (res.STATUS === '0000') {
                    const data = res.DATA?.map(item => {
                        const newItem = {...item, key: item?.resourceCode}
                        return newItem
                    });
                    setFuncTableTotal(data?.length || 0);
                    setFuncTableData(data);
                    setFuncSearchData(data);
                } else {
                    message.error(res.MESSAGE);
                }
                setFuncLoading(false);
                resolve(res);
            }).catch(e => reject(e));
        });
  }

  // 角色名称改变
  const changeRoleName = e => {
    setRoleName(e.target.value)
  }

  // 角色类型改变
  const changeRoleTypeCode = value => {
    setRoleTypeCode(value)
  }

  // 显示批量操作弹窗
  const showBatchModal = () => {
    if (selectedRowKeys.length === 0 && selectedRows.length === 0) {
      message.warning('没有勾选角色，请先勾选要操作的角色！')
      return
    }
    setTimeout(() => {
      setBatchVisible(true)
      Promise.all([getMenuTreeData(selectedRecord), getStationList(), getFuncTableData()]).then(
        () => {
          setBacthAmountFlag(false)
        }
      )
    })
  }

  // 取消批量操作弹窗
  const cancelBatchModal = () => {
    setBatchVisible(false)
    setBacthAmountFlag(true)
    setSelectedRowKeys([])
    setSelectedRows([])
  }

  // 菜单提交
  const handleMenuOk = () => {
    return new Promise((resolve, reject) => {
      const params = {
        roleCode: selectedRows,
        list: menuTreeCode
      }
      // prettier-ignore
      Api.listSetRoleMenuRecursive(params).then((response: any) => {
                const res = response[1]
                resolve(res);
            }).catch(e => reject(e));
    })
  }

  // 岗位提交
  const handleStationOk = () => {
    return new Promise((resolve, reject) => {
      const params = {
        roleCode: selectedRows,
        list: stationId
      }
      // prettier-ignore
      Api.listSetRoleStationAuth(params).then((response: any) => {
                const res = response[1]
                resolve(res);
            }).catch(e => reject(e));
    })
  }

  // 功能提交
  const handleFuncOk = () => {
    return new Promise((resolve, reject) => {
      const list = funcSelectedRows.map(item => {
        const { resourceType, subResourceType, resourceCode, resourceName } = item
        return { resourceType, subResourceType, resourceCode, resourceName }
      })
      const params = { roleCode: selectedRows, list }
      // prettier-ignore
      Api.listSetRoleResource(params).then((response: any) => {
                const res = response[1]
                resolve(res);
            }).catch(e => reject(e));
    })
  }

  // 批量弹窗提交
  const handleBatchOk = () => {
    setTimeout(() => {
      setBatchLoading(true)
      // prettier-ignore
      Promise.all([
                menuTreeCode.length > 0 ? handleMenuOk() : Promise.resolve(),
                stationId.length > 0 ? handleStationOk() : Promise.resolve(),
                funcSelectedRows.length > 0 ? handleFuncOk() : Promise.resolve()
            ]).then(() => {
                message.success('批量修改成功！')
                setBatchLoading(false);
                setBatchVisible(false);
                setBacthAmountFlag(true);
                setSelectedRowKeys([]);
                setSelectedRows([]);
                queryInfo();
            }).catch(() => {
                setBatchLoading(false);
            })
    })
  }

  // 显示菜单弹窗
  const showMenuModal = async record => {
    setMenuVisible(true)
    setSelectedRecord(record)
    await getMenuTreeData(record)
    setMenuAmountFlag(false)
  }

  // 取消菜单弹窗
  const cancelMenuModal = () => {
    setMenuVisible(false)
    setSelectedRecord({})
    setMenuAmountFlag(true)
  }

  // 菜单弹窗节点展开
  const changeMenuExpandedKeys = value => {
    setMenuExpandedKeys(value)
  }

  // 菜单弹窗选中节点改变
  const changeMenuTreeCode = value => {
    setMenuTreeCode(value)
  }

  // 菜单弹窗确定
  const confirmMenuModal = () => {
    const params = {
      roleCode: selectedRecord?.roleCode,
      menuCodes: menuTreeCode
    }
    setMenuLoading(true)
    // prettier-ignore
    Api.setRoleMenuRecursive2(params).then((response: any) => {
            const res = response[1]
            if (res.STATUS === '0000') {
                message.success('修改菜单成功！');
                setMenuLoading(false);
                setMenuVisible(false);
                setSelectedRecord({});
                setMenuAmountFlag(true);
                queryInfo();
            } else {
                setMenuLoading(false);
                message.error(res.MESSAGE);
            }
        }).catch(() => {
            setMenuLoading(false);
        });
  }

  // 显示岗位弹窗
  const showStationModal = record => {
    setStationVisible(true)
    setSelectedRecord(record)
    Promise.all([getStationList(), getStationId(record)])
      .then(() => {
        setStationAmountFlag(false)
      })
      .catch(() => {
        setStationAmountFlag(false)
      })
  }

  // 取消岗位弹窗
  const cancelStationModal = () => {
    setStationVisible(false)
    setSelectedRecord({})
    setStationId([])
    setStationAmountFlag(true)
  }

  // 岗位弹窗岗位切换
  const changeStationId = value => {
    setStationId(value)
  }

  // 岗位弹窗确定
  const confirmStationModal = () => {
    const params = {
      roleCode: selectedRecord?.roleCode,
      stationCodes: stationId
    }
    setStationLoading(true)
    // prettier-ignore
    Api.setRoleStationAuth(params).then((response: any) => {
            const res = response[1]
            if (res.STATUS === '0000') {
                message.success('修改用户岗位成功');
                setStationAmountFlag(true);
                setStationLoading(false);
                setStationVisible(false);
                setStationId([]);
                setSelectedRecord({});
                queryInfo()
            } else {
                message.error(res.MESSAGE);
                setStationLoading(false);
            }
        }).catch(() => {
            setStationLoading(false);
        })
  }

  // 显示功能弹窗
  const showFuncModal = record => {
    setFuncVisible(true)
    setSelectedRecord(record)
    Promise.all([getFuncSelectedData(record), getFuncTableData()])
      .then(() => {
        setFuncAmountFlag(false)
      })
      .catch(() => {
        setFuncAmountFlag(false)
      })
  }

  // 取消功能弹窗
  const cancelFuncModal = () => {
    setFuncVisible(false)
    setFuncAmountFlag(true)
    setSelectedRecord({})
    setFuncSelectedRowKeys([])
    setFuncSelectedRows([])
  }

  // 功能弹窗选中数据切换
  const changeSelectedData = (selectedRowKeys, selectedRows) => {
    setFuncSelectedRowKeys(selectedRowKeys)
    setFuncSelectedRows(selectedRows)
  }

  // 功能弹窗查询
  const queryFuncTableData = (filterRoleName, filterMenuName) => {
    let newTableData = []
    if (!filterRoleName && !filterMenuName) {
      newTableData = funcSearchData
    }
    if (filterRoleName || filterMenuName) {
      newTableData = funcSearchData.filter(item => {
        const { resourceCode, resourceName, menuName } = item
        // prettier-ignore
        return (resourceCode.search(filterRoleName) >= 0 || resourceName.search(filterRoleName) >= 0) && menuName.search(filterMenuName) >= 0
      })
    }
    setFuncTableData(newTableData)
  }

  // 功能弹窗确定
  const confirmFuncModal = () => {
    const params = {
      roleCode: selectedRecord?.roleCode,
      clearAllResource: false,
      resourceList: funcSelectedRows
    }
    setFuncLoading(true)
    // prettier-ignore
    Api.smartSetRoleResource(params).then((response: any) => {
            const res = response[1]
            if (res.STATUS === '0000') {
                message.success('功能授权成功！');
                // prettier-ignore
                setFuncVisible(false);
                setFuncLoading(false);
                setFuncAmountFlag(true);
                setSelectedRecord({});
                setFuncSelectedRowKeys([]);
                setFuncSelectedRows([]);
                queryInfo()
            } else {
                message.error(res.MESSAGE);
                setFuncLoading(false);
            }
        }).catch(() => {
            setFuncLoading(false);
        });
  }

  // 显示角色弹窗
  const showRoleModal = (type, record) => {
    setRoleVisible(true)
    setRoleMode(type)
    setSelectedRecord(record)
  }

  // 取消角色弹窗
  const cancelRoleModal = () => {
    setRoleVisible(false)
    setRoleMode('')
    setSelectedRecord({})
    queryInfo()
  }

  // 显示删除弹窗
  const showDelModal = record => {
    setDelVisible(true)
    setSelectedRecord(record)
  }

  // 取消删除弹窗
  const cancelDelModal = () => {
    setDelVisible(false)
    setSelectedRecord({})
  }

  // 删除弹窗确定
  const confirmDelModal = () => {
    const params = {
      roleId: selectedRecord?.roleId
    }
    setDelLoading(true)
    // prettier-ignore
    Api.deleteRole(params).then((response: any) => {
            const res = response[1]
            if (res.STATUS === '0000') {
                message.success('删除成功！');
                setDelVisible(false);
                setSelectedRecord({});
                setDelLoading(false);
                queryInfo();
            } else {
                message.error(res.MESSAGE);
                setDelLoading(false);
            }
        }).catch(() => {
            setDelLoading(false);
        });
  }

  useEffect(() => {
    getTableData() // 替换为你的数据获取函数
  }, [pageNum, pageSize, queryKey])
  // 查询
  const queryInfo = () => {
    setSelectedRowKeys([])
    setSelectedRows([])
    // setPageNum(1)
    // setPageSize(10)
    setQueryKey(prevKey => prevKey + 1) // 更新 queryKey，触发查询
  }
  // 分页切换
  const pageChange = (page, pageSize) => {
    setPageNum(page)
    setPageSize(pageSize)
  }
  const rowSelection = {
    onChange: (newSelectedRowKeys, newSelectedRows) => {
      setSelectedRowKeys(newSelectedRowKeys)
      setSelectedRows(newSelectedRows.map(item => item?.roleCode))
    },
    selectedRowKeys
  }

  const columns = [
    {
      title: '序号',
      key: 'num',
      dataIndex: 'num',
      width: 50,
      align: 'center' as const
    },
    {
      title: '角色编码',
      key: 'roleCode',
      dataIndex: 'roleCode',
      width: 140,
      align: 'center' as const
    },
    {
      title: '角色名称',
      key: 'roleName',
      dataIndex: 'roleName',
      width: 140,
      align: 'center' as const
    },
    {
      title: '角色类型',
      key: 'roleType',
      dataIndex: 'roleType',
      width: 140,
      align: 'center' as const,
      // prettier-ignore
      render: (text) => {
                const roleTypeItem = roleTypeList.find(item => item.valueCode === text);
                return roleTypeItem ? roleTypeItem.valueName : '';
            }
    },
    // {
    //   title: '是否启用',
    //   key: 'enable',
    //   dataIndex: 'enable',
    //   width: 120,
    //   align: 'center' as const,
    //   render: text => (
    //     <Popconfirm
    //       title='确定切换启用状态?'
    //         //this.disableUser(record);
    //       okText=' 确认'
    //       cancelText='取消'
    //     >
    //       <Switch checked={text === '1'}></Switch>
    //     </Popconfirm>
    //   )
    // },
    {
      title: '操作',
      width: 175,
      align: 'center' as const,
      render: (_, record) => {
        return (
          <div>
            <Button size='small' type='link' onClick={() => showMenuModal(record)}>
              菜单
            </Button>
            <Button style={{display: 'none'}} size='small' type='link' onClick={() => showStationModal(record)}>
              岗位
            </Button>
            <Button size='small' type='link' onClick={() => showFuncModal(record)}>
              功能
            </Button>
            <Button size='small' type='link' onClick={() => showRoleModal('edit', record)}>
              修改
            </Button>
            <Button size='small' type='link' onClick={() => showDelModal(record)}>
              删除
            </Button>
          </div>
        )
      }
    }
  ]

  return (
    <div className={`${'pt-[0.5rem] px-[0rem] h-full'} ${styles.rbac_page}`}>
      <div className='bg-white pt-[0.5rem] px-[1.75rem] mb-[0.5rem]'>
        <Form labelCol={{ span: 6 }}>
          <Row gutter={24}>
            <Col span={6}>
              <Form.Item name='code1' label='角色'>
                <Input placeholder='请输入角色名称' allowClear onChange={changeRoleName} />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name='code2' label='角色类型'>
                <Select placeholder='请选择角色类型' allowClear onChange={changeRoleTypeCode}>
                  {roleTypeList.map(item => {
                    const { valueCode, valueName } = item
                    return (
                      <Select.Option key={valueCode} value={valueCode} option={item}>
                        {valueName}
                      </Select.Option>
                    )
                  })}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <div className='text-right'>
                <Button type='primary' onClick={queryInfo}>
                  查询
                </Button>
                <Button type='primary' className='ml-[0.4rem]' onClick={showBatchModal}>
                  批量操作
                </Button>
                <Button
                  type='primary'
                  className='ml-[0.4rem]'
                  onClick={() => showRoleModal('add', {})}
                >
                  新增角色
                </Button>
                <Button
                  type='primary'
                  className='ml-[0.4rem]'
                  onClick={exportFile}
                  loading={downloadLoading}
                >
                  导出
                </Button>
              </div>
            </Col>
          </Row>
        </Form>
      </div>
      <div className='bg-white pt-[0.5rem] px-[0.5rem] h-[calc(100%-4.8rem)] overflow-y-auto'>
        <Table
          loading={tableLoading}
          columns={columns}
          dataSource={tableData}
          rowSelection={rowSelection}
          size='small'
          //sticky={{getContainer: () => document.getElementsByClassName('ant-layout-content')[0]}}
          bordered
          scroll={{ x: 765 }}
          pagination={{
            total: tableTotal,
            showTotal: () => `共 ${tableTotal} 条`,
            defaultCurrent: 1,
            defaultPageSize: 10,
            current: pageNum,
            pageSize,
            showSizeChanger: true,
            showQuickJumper: true,
            onChange: pageChange
          }}
        />
      </div>
      <Modal
        title='批量操作'
        open={batchVisible}
        bodyStyle={{ paddingTop: '1rem' }}
        width='50%'
        destroyOnClose
        okText='提交'
        confirmLoading={batchLoading}
        onOk={handleBatchOk}
        onCancel={cancelBatchModal}
      >
        {
          // prettier-ignore
          bacthAmountFlag ? <Skeleton
                        paragraph={{rows: 8}}
                        title={false}
                        active
                    /> : <Tabs
                        defaultActiveKey='1'
                        items={[
                            {
                                key: '1',
                                label: '修改菜单',
                                children: <Menu
                                    menuTreeCode={menuTreeCode}
                                    menuTreeData={menuTreeData}
                                    expandedKeys={menuExpandedKeys}
                                    changeExpandedKeys={changeMenuExpandedKeys}
                                    changeCheckedKeys={changeMenuTreeCode}
                                />,
                                forceRender: true
                            },
                            {
                                key: '2',
                                label: '修改用户岗位',
                                children: <Station
                                    stationId={stationId}
                                    stationList={stationList}
                                    changeStationId={changeStationId}
                                />,
                                forceRender: true
                            },
                            {
                                key: '3',
                                label: '功能授权',
                                children: <Function
                                    tableData={funcTableData}
                                    tableTotal={funcTableTotal}
                                    selectedRowKeys={funcSelectedRowKeys}
                                    selectedRows={funcSelectedRows}
                                    changeSelectedData={changeSelectedData}
                                    queryTableData={queryFuncTableData}
                                />,
                                forceRender: true
                            },
                        ]}
                    />
        }
      </Modal>
      <Modal
        title={roleMode === 'add' ? '添加角色信息' : '修改角色信息'}
        open={roleVisible}
        destroyOnClose
        onCancel={() => {
          setRoleVisible(false)
          setRoleMode('')
          setSelectedRecord({})
        }}
        footer={null}
      >
        <RoleForm
          type={roleMode}
          record={selectedRecord}
          roleTypeList={roleTypeList}
          cancelRoleModal={cancelRoleModal}
        />
      </Modal>
      <Modal
        // prettier-ignore
        title={<div>修改菜单<span style={{color: '#ff0000'}}> （{selectedRecord?.roleName}）</span></div>}
        open={menuVisible}
        destroyOnClose
        onCancel={cancelMenuModal}
        footer={null}
      >
        {
          // prettier-ignore
          menuAmountFlag ? <Skeleton
                        paragraph={{rows: 8}}
                        title={false}
                        active
                    /> : <Form
                        labelCol={{span: 4}}
                        wrapperCol={{span: 19}}
                        labelAlign='right'
                    >
                        <Form.Item label='菜单'>
                            <Menu
                                menuTreeCode={menuTreeCode}
                                menuTreeData={menuTreeData}
                                expandedKeys={menuExpandedKeys}
                                changeExpandedKeys={changeMenuExpandedKeys}
                                changeCheckedKeys={changeMenuTreeCode}
                            />
                        </Form.Item>
                        <Form.Item wrapperCol={{offset: 4, span: 19}}>
                            <Button type='primary' loading={menuLoading} onClick={confirmMenuModal}>确定</Button>
                        </Form.Item>
                    </Form>
        }
      </Modal>
      <Modal
        // prettier-ignore
        title={<div>修改用户岗位<span style={{color: '#ff0000'}}> （{selectedRecord?.roleName}）</span></div>}
        open={stationVisible}
        destroyOnClose
        onCancel={cancelStationModal}
        footer={null}
      >
        {
          // prettier-ignore
          stationAmountFlag ? <Skeleton
                        paragraph={{rows: 8}}
                        title={false}
                        active
                    /> : <Form
                        labelCol={{span: 4}}
                        wrapperCol={{span: 19}}
                        labelAlign='right'
                    >
                        <Form.Item label='岗位'>
                            <Station stationId={stationId} stationList={stationList}
                                     changeStationId={changeStationId}/>
                        </Form.Item>
                        <Form.Item wrapperCol={{offset: 4, span: 19}}>
                            <Button type='primary' loading={stationLoading}
                                    onClick={confirmStationModal}>确定</Button>
                        </Form.Item>
                    </Form>
        }
      </Modal>
      <Modal
        // prettier-ignore
        title={<div>功能授权<span style={{color: '#ff0000'}}> （{selectedRecord?.roleName}）</span></div>}
        open={funcVisible}
        width='60%'
        destroyOnClose
        onOk={confirmFuncModal}
        confirmLoading={funcLoading}
        onCancel={cancelFuncModal}
      >
        {
          // prettier-ignore
          funcAmountFlag ? <Skeleton
                        paragraph={{rows: 8}}
                        title={false}
                        active
                    /> : <Function
                        tableData={funcTableData}
                        tableTotal={funcTableTotal}
                        selectedRowKeys={funcSelectedRowKeys}
                        selectedRows={funcSelectedRows}
                        changeSelectedData={changeSelectedData}
                        queryTableData={queryFuncTableData}
                    />
        }
      </Modal>
      <Modal
        open={delVisible}
        width='20%'
        closable={false}
        destroyOnClose
        onOk={confirmDelModal}
        confirmLoading={delLoading}
        onCancel={cancelDelModal}
      >
        是否删除<span style={{ color: '#ff0000' }}>{selectedRecord?.roleName}</span>?
      </Modal>
    </div>
  )
}

export default RoleManagement
