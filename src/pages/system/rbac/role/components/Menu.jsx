import { Tree } from 'antd'

const Menu = ({
  menuTreeCode,
  menuTreeData,
  expandedKeys,
  changeExpandedKeys,
  changeCheckedKeys
}) => {
  return (
    <div className='my-[0.75rem]'>
      <Tree
        expandedKeys={expandedKeys}
        checkedKeys={menuTreeCode}
        treeData={menuTreeData}
        checkable
        fieldNames={{ key: 'menuCode', title: 'menuName' }}
        onExpand={changeExpandedKeys}
        onCheck={changeCheckedKeys}
        style={{
          border: '1px solid #0000003d',
          padding: '0.5rem',
          height: '20rem',
          overflow: 'auto'
        }}
      />
    </div>
  )
}

export default Menu
