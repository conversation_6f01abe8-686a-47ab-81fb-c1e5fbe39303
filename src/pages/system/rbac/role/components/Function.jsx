import { useState, useEffect } from 'react'
import { Button, Input, Table, Tooltip } from 'antd'

const Function = props => {
  const { tableData, tableTotal, selectedRowKeys } = props
  const [roleName, setRoleName] = useState('') // 角色编码或角色名称
  const [menuName, setMenuName] = useState('') // 菜单名称

  // 角色编码或角色名称改变
  const changeRoleName = e => {
    setRoleName(e.target.value)
  }

  // 菜单名称改变
  const changeMenuName = e => {
    setMenuName(e.target.value)
  }

  // 查询
  const queryInfo = () => {
    props.queryTableData(roleName, menuName)
  }
  const columns = [
    {
      title: '功能编码',
      key: 'resourceCode',
      dataIndex: 'resourceCode',
      width: 120,
      align: 'center'
    },
    {
      title: '功能名称',
      key: 'resourceName',
      dataIndex: 'resourceName',
      width: 200,
      align: 'center',
      ellipsis: { showTitle: false },
      // prettier-ignore
      render: text => <Tooltip placement="topLeft" title={text}
									 getPopupContainer={tiggerNode => tiggerNode?.closest('div.ant-table-body') || document.body}>{text}</Tooltip>
    },
    {
      title: '功能类型',
      key: 'resourceType',
      dataIndex: 'resourceType',
      width: 120,
      align: 'center',
      render: text => (text === '100' ? '按钮' : text)
    },
    {
      title: '菜单名称',
      key: 'menuName',
      width: 200,
      dataIndex: 'menuName',
      align: 'center',
      ellipsis: { showTitle: false },
      // prettier-ignore
      render: text => <Tooltip placement="topLeft" title={text}
									 getPopupContainer={tiggerNode => tiggerNode?.closest('div.ant-table-body') || document.body}>{text}</Tooltip>
    }
  ]

  const rowSelection = {
    onChange: (selectedRowKeys, selectedRows) => {
      props.changeSelectedData(selectedRowKeys, selectedRows)
    },
    selectedRowKeys: selectedRowKeys
  }

  return (
    <div className='my-[0.75rem]'>
      <div style={{ display: 'flex', alignItems: 'center', columnGap: '1%', marginBottom: '1rem' }}>
        <div>功能:</div>
        <Input
          placeholder='请输入功能名称或功能编码'
          allowClear
          onChange={changeRoleName}
          style={{ width: '35%' }}
        />
        <div>菜单:</div>
        <Input
          placeholder='请输入菜单名称'
          allowClear
          onChange={changeMenuName}
          style={{ width: '30%' }}
        />
        <Button type='primary' onClick={queryInfo}>
          查询
        </Button>
      </div>
      <Table
        columns={columns}
        dataSource={tableData}
        size='small'
        bordered
        checkable
        rowSelection={rowSelection}
        scroll={{ x: 'max-content', y: '20rem' }}
        pagination={false}
      />
      <div style={{ marginTop: '0.25rem', fontWeight: 600, textAlign: 'right' }}>
        共{tableTotal}条
      </div>
    </div>
  )
}

export default Function
