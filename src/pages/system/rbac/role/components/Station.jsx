import React from 'react'
import { Select } from 'antd'

class Station extends React.Component {
  render() {
    const { stationId, stationList } = this.props

    return (
      <div className='my-[0.75rem]'>
        <Select
          placeholder='请选择用户岗位'
          value={stationId}
          mode='multiple'
          allowClear
          showSearch
          getPopupContainer={triggerNode => triggerNode.parentElement || document.body}
          filterOption={(input, option) =>
            option.children.toLowerCase().includes(input.toLowerCase())
          }
          onChange={this.props.changeStationId}
          style={{ width: '15rem' }}
        >
          {
            // prettier-ignore
            stationList.map(item => {
						const { stationId, stationName } = item;
						return <Select.Option key={stationId} value={stationId}>
							{stationName}
						</Select.Option>
					})
          }
        </Select>
      </div>
    )
  }
}

export default Station
