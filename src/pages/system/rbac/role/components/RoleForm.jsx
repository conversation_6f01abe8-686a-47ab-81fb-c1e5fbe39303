import { useState, useEffect } from 'react'
import Api from '../service'
import { Form, Input, Select, Switch, Button, message } from 'antd'
import { RedoOutlined, LoadingOutlined } from '@ant-design/icons'

const RoleForm = props => {
  const { type, record, roleTypeList } = props
  const [form] = Form.useForm()
  const [roleCodeLoading, setRoleCodeLoading] = useState(false)
  const [confirmLoading, setConfirmLoading] = useState(false)

  useEffect(() => {
    // 新增
    if (type === 'add') {
      getNewRoleCode()
    }
    // 编辑
    if (type === 'edit') {
      const { roleCode, roleName, roleType, sortId, isTotal, remark } = record
      form.setFieldsValue({
        remark,
        roleCode,
        roleName,
        roleType,
        sortId,
        isTotal: isTotal === 1
      })
    }
  }, [])

  // 获取新的角色编码
  const getNewRoleCode = () => {
    setRoleCodeLoading(true)
    // prettier-ignore
    Api.obtainNextRoleCode({}).then(response => {
			const res = response[1]
			if (res.STATUS === '0000') {
				form.setFieldsValue({roleCode: res.DATA});
			} else {
				message.error(res.MESSAGE);
			}
			setRoleCodeLoading(false);
		}).catch(() => {
			setRoleCodeLoading(false);
		});
  }

  // 确定
  const handleOk = formInfo => {
    const { roleCode, roleName, roleType, sortId, remark, isTotal } = formInfo
    const params = {
      roleCode,
      roleName,
      roleType,
      sortId,
      remark,
      isTotal: isTotal ? 1 : 0
    }
    if (type === 'edit') params.roleId = record?.roleId
    const method = type === 'add' ? Api.extraAddRole : Api.extraUpdateRole
    setConfirmLoading(true)
    // prettier-ignore
    method(params).then(response => {
			const res = response[1]
			if (res.STATUS === '0000') {
				message.success(`${type === 'add' ? '新增' : '修改'}角色成功`);
				props.cancelRoleModal();
			} else {
				message.error(res.MESSAGE);
			}
			setConfirmLoading(false);
		}).catch(() => {
			setConfirmLoading(false);
		});
  }
  return (
    <Form
      form={form}
      labelCol={{ span: 6 }}
      wrapperCol={{ span: 14 }}
      labelAlign='right'
      onFinish={handleOk}
    >
      <Form.Item
        label='角色编码'
        name='roleCode'
        rules={[{ required: true, message: '请输入角色编码！' }]}
      >
        {/* prettier-ignore */}
        <Input
					placeholder='请输入角色编码'
					disabled={type === 'edit'}
					suffix={
						type === 'add' ? (
							roleCodeLoading ? <LoadingOutlined/> : <RedoOutlined
								onClick={getNewRoleCode}
								style={{color: '#1879ff', cursor: 'pointer'}}
							/>
						) : null
					}
				/>
      </Form.Item>
      <Form.Item
        label='角色名称'
        name='roleName'
        rules={[{ required: true, message: '请输入角色名称！' }]}
      >
        <Input placeholder='请输入角色名称' />
      </Form.Item>
      <Form.Item
        label='角色类型'
        name='roleType'
        rules={[{ required: true, message: '请填写角色类型！' }]}
      >
        <Select placeholder='请选择角色类型' allowClear style={{ width: '100%' }}>
          {roleTypeList.map(item => {
            const { valueCode, valueName } = item
            return (
              <Select.Option key={valueCode} value={valueCode} option={item}>
                {valueName}
              </Select.Option>
            )
          })}
        </Select>
      </Form.Item>
      <Form.Item
        label='排序'
        name='sortId'
        rules={[{ pattern: /^[+]{0,1}(\d+)$/, message: '只支持数字，请正确输入！' }]}
      >
        <Input placeholder='请输入排序' />
      </Form.Item>
      <Form.Item label='备注' name='remark'>
        <Input placeholder='请输入备注' />
      </Form.Item>
      <Form.Item label='是否统计' name='isTotal' valuePropName='checked'>
        <Switch />
      </Form.Item>
      <Form.Item wrapperCol={{ offset: 6, span: 14 }}>
        <Button loading={confirmLoading} type='primary' htmlType='submit'>
          确定
        </Button>
      </Form.Item>
    </Form>
  )
}

export default RoleForm
