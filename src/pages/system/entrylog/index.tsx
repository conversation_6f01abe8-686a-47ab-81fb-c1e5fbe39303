import {
  Button,
  Cascader,
  CascaderProps,
  Col,
  DatePicker,
  Form,
  FormProps,
  GetProp,
  Input,
  message,
  Row,
  Select,
  Space,
  Table,
  Tooltip
} from 'antd'
import { DownloadOutlined } from '@ant-design/icons'
import { useEffect, useRef, useState } from 'react'
import { useRequest } from '@/hooks'
import { downFile, openNotification } from '@/utils/down.tsx'
import entrylogService from '@/pages/system/entrylog/service.ts'
import dayjs from 'dayjs'
const { RangePicker } = DatePicker

type DefaultOptionType = GetProp<CascaderProps, 'options'>[number]

type FieldType = {
  month?: string
  unit?: string
  curPost?: string
}

const ReportPer: React.FC = () => {
  const columns = [
    {
      title: '序号',
      dataIndex: 'num',
      align: 'center',
      width: 50
    },
    {
      title: '账号',
      dataIndex: 'loginName',
      align: 'center',
      width: 100
    },
    {
      title: '姓名',
      dataIndex: 'fullName',
      align: 'center',
      width: 100
    },
    {
      title: '地市',
      dataIndex: 'areaName',
      align: 'center',
      width: 100
    },
    {
      title: '部门',
      dataIndex: 'orgName',
      align: 'center',
      width: 150,
      ellipsis: { showTitle: false },
      // prettier-ignore
      render: text => <Tooltip title={text} placement='topLeft' getPopupContainer={tiggerNode => tiggerNode?.closest('div.ant-table-body') || document.body}>{text}</Tooltip>
    },
    {
      title: '区县',
      dataIndex: 'districtName',
      align: 'center',
      width: 150,
      ellipsis: { showTitle: false },
      // prettier-ignore
      render: text => <Tooltip title={text} placement='topLeft' getPopupContainer={tiggerNode => tiggerNode?.closest('div.ant-table-body') || document.body}>{text}</Tooltip>
    },
    {
      title: '网格',
      dataIndex: 'gridName',
      align: 'center',
      width: 200,
      ellipsis: { showTitle: false },
      // prettier-ignore
      render: text => <Tooltip title={text} placement='topLeft' getPopupContainer={tiggerNode => tiggerNode?.closest('div.ant-table-body') || document.body}>{text}</Tooltip>
    },
    {
      title: '角色',
      dataIndex: 'roleNames',
      align: 'center',
      width: 200,
      ellipsis: { showTitle: false },
      // prettier-ignore
      render: text => <Tooltip title={text} placement='topLeft' getPopupContainer={tiggerNode => tiggerNode?.closest('div.ant-table-body') || document.body}>{text}</Tooltip>
    },
    {
      title: '登录时间',
      dataIndex: 'loginDate',
      align: 'center',
      width: 200
    },
    {
      title: '登录来源',
      dataIndex: 'loginFrom',
      align: 'center',
      width: 100,
      render: text => (text === '20' ? '执行力' : text === 'uac' ? '门户' : '')
    },
    {
      title: '事件类型',
      dataIndex: 'eventType',
      key: 'eventType',
      align: 'center',
      width: 100,
      render: text => {
        switch (text) {
          case '100':
            return <div>登录成功</div>
          case '200':
            return <div style={{ color: '#ff0000' }}>登录失败</div>
          case '201':
            return <div style={{ color: '#ff0000' }}>验证码错误</div>
          case '202':
            return <div style={{ color: '#ff0000' }}>用户禁用</div>
          case '203':
            return <div style={{ color: '#ff0000' }}>连续登录失败</div>
          case '204':
            return <div style={{ color: '#ff0000' }}>用户信息异常</div>
        }
      }
    },
    {
      title: 'IP',
      dataIndex: 'hostAddress',
      align: 'center',
      width: 120
    }
  ]

  const topRef = useRef(null)
  const [formRef] = Form.useForm()
  const [height, setHeight] = useState(0)
  const [tableColumns] = useState<any>(columns)
  const [tableData, setTableData] = useState<any>([])
  const [tableLoading, setTableLoading] = useState<boolean>(false)
  // const [tableColumns, setTableColumns] = useState<any>(columns)

  const [cascaderSelected, setCascaderSelected] = useState<any>([])
  const [roleList, setRoleList] = useState<any>([])
  const [areaData, setAreaData] = useState<any>([])
  // const [areaCode, setAreaCode] = useState<any>('')
  // const [areaLevel, setAreaLevel] = useState<any>('')
  const [pagination, setPagination] = useState({
    total: 0,
    pageNum: 1,
    pageSize: 50
  })
  const { runAsync: getQueryConditions } = useRequest(entrylogService.getQueryConditions, {
    manual: true
  })
  const { runAsync: loginlogDownload } = useRequest(entrylogService.loginlogDownload, {
    manual: true
  })
  const { runAsync: queryList } = useRequest(entrylogService.queryList, {
    manual: true
  })
  const { runAsync: loginLog } = useRequest(entrylogService.loginLog, {
    manual: true
  })

  useEffect(() => {
    if (topRef.current) {
      setHeight(topRef.current.offsetHeight)
    }
    getAreaData()
    getRoleList()
  }, [])

  useEffect(() => {
    if (pagination?.total > 0) {
      queryTableData()
    }
  }, [JSON.stringify(pagination)])

  // 获取组织机构数据
  const getAreaData = async () => {
    const [error, res] = await getQueryConditions({ menuType: '', centerType: 'loginJournal' })
    if (error) {
      return
    }
    if (res.STATUS === '0000') {
      const { orgTree } = res.DATA
      const curOrg = orgTree[0] || {}
      setAreaData(orgTree)
      setCascaderSelected([curOrg])
      formRef.setFieldsValue({ areaRecord: curOrg?.areaCode })
      queryTableData(curOrg)
      // setAreaCode(orgTree[0]?.areaCode);
      // setAreaLevel(orgTree[0]?.areaLevel);
    }
  }

  // 获取角色数据
  const getRoleList = async () => {
    const [error, res] = await queryList({ key: 'getRoleList' })
    if (error) {
      return
    }
    if (res.STATUS === '0000') {
      setRoleList(res.DATA)
    }
  }

  // 查询表格数据
  const queryTableData = async (org = null) => {
    const values = formRef.getFieldsValue()
    const area = org || cascaderSelected[cascaderSelected?.length - 1] || {}
    setTableLoading(true)
    const [error, res] = await loginLog({
      loginDateStart: values?.loginDete ? values?.loginDete[0].format('YYYYMMDD') : '',
      loginDateEnd: values?.loginDete ? values?.loginDete[1].format('YYYYMMDD') : '',
      adCode: area?.areaCode,
      level: area?.areaLevel,
      // loginFrom: 'uac',
      ...values,
      ...pagination,
      loginDete: undefined,
      areaRecord: undefined
    })
    setTableLoading(false)
    if (error) {
      return
    }
    if (res.STATUS === '0000') {
      const {
        DATA: { list }
      } = res
      setTableData(list)
      setPagination({
        ...pagination,
        total: res.DATA?.total
      })
    } else {
      message.error(res?.MESSAGE)
    }
  }

  // 点击查询
  const onFormFinish: FormProps<FieldType>['onFinish'] = values => {
    console.log('Success:', values)
    queryTableData()
  }
  // 点击导出
  const exportToExcelFun = async () => {
    try {
      // 调用接口获取 Blob 文件数据
      openNotification('正在导出', 0, 'loading')
      const values = formRef.getFieldsValue()
      const area = cascaderSelected[cascaderSelected?.length - 1] || {}
      const response = await loginlogDownload({
        loginDateStart: values?.loginDete ? values?.loginDete[0].format('YYYYMMDD') : '',
        loginDateEnd: values?.loginDete ? values?.loginDete[1].format('YYYYMMDD') : '',
        adCode: area?.areaCode,
        level: area?.areaLevel,
        // loginFrom: 'uac',
        ...values,
        ...pagination,
        loginDete: undefined,
        areaRecord: undefined
      })
      downFile(response)
    } catch (error) {
      openNotification('导出失败', 1, 'error')
      console.error('Download failed:', error)
    }
  }
  // 查询重置
  const onReset = () => {
    formRef.resetFields()
    const curOrg = areaData[0] || {}
    formRef.setFieldsValue({ areaRecord: curOrg?.areaCode })
  }

  const filter = (inputValue: string, path: DefaultOptionType[]) =>
    path.some(
      option => (option.label as string).toLowerCase().indexOf(inputValue.trim().toLowerCase()) > -1
    )

  // 切换分页
  const onChangePage = page => {
    setPagination({
      ...page,
      pageNum: page?.current
    })
  }

  // 搜索过滤空格
  const handleSearchFilter = (input, option) => {
    const cleanedInput = input.trim() // 去除输入中的空格
    return option.children.toLowerCase().includes(cleanedInput.toLowerCase())
  }

  return (
    <div className='h-full pt-4'>
      <div ref={topRef} className={'bg-white pt-4 px-8 mb-4'}>
        <Form
          form={formRef}
          initialValues={{
            loginDete: [dayjs().subtract(1, 'day'), dayjs()]
          }}
          onFinish={onFormFinish}
          // onFinishFailed={onFinishFailed}
          autoComplete='off'
        >
          <Row
            gutter={24}
            // justify="end"
          >
            <Col span={6}>
              <Form.Item label='用户' name='queryName' wrapperCol={{ span: 20 }}>
                <Input placeholder='请输入用户' />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label='登录时间' name='loginDete' wrapperCol={{ span: 20 }}>
                <RangePicker
                  style={{ width: '100%' }}
                  allowClear={false}
                  ranges={{
                    近一天: [dayjs().subtract(1, 'day'), dayjs()],
                    近三天: [dayjs().subtract(3, 'day'), dayjs()],
                    近七天: [dayjs().subtract(7, 'day'), dayjs()]
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label='组织机构' name='areaRecord' wrapperCol={{ span: 20 }}>
                <Cascader
                  allowClear={false}
                  changeOnSelect
                  expandTrigger='hover'
                  // showCheckedStrategy={Cascader.SHOW_CHILD}
                  displayRender={labels => labels[labels.length - 1]}
                  options={areaData}
                  fieldNames={{
                    value: 'areaCode',
                    label: 'areaName',
                    children: 'children'
                  }}
                  onChange={(value, selectedOptions) => {
                    console.log(value, selectedOptions)
                    setCascaderSelected(selectedOptions)
                  }}
                  placeholder='请选择组织机构'
                  showSearch={{ filter }}
                  onSearch={value => console.log(value)}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label='部门' name='orgName' wrapperCol={{ span: 20 }}>
                <Input placeholder='请输入部门' />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label='角色' name='roleCodes' wrapperCol={{ span: 20 }}>
                <Select
                  placeholder={'请选择角色'}
                  className='w-full'
                  allowClear={true}
                  showSearch
                  filterOption={handleSearchFilter}
                >
                  {
                    // prettier-ignore
                    roleList.map(item => {
                                        const {roleCode, roleName} = item;
                                        return <Select.Option key={roleCode}
                                                              value={roleCode}>{roleName}</Select.Option>
                                    })
                  }
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label='事件类型' name='eventType' wrapperCol={{ span: 20 }}>
                <Select
                  placeholder={'请选择'}
                  className='w-full'
                  allowClear={true}
                  showSearch
                  filterOption={handleSearchFilter}
                >
                  <Select.Option key='100' value='100'>
                    登录成功
                  </Select.Option>
                  <Select.Option key='200' value='200'>
                    登录失败
                  </Select.Option>
                  <Select.Option key='201' value='201'>
                    验证码错误
                  </Select.Option>
                  <Select.Option key='202' value='202'>
                    用户禁用
                  </Select.Option>
                  <Select.Option key='203' value='203'>
                    连续登录失败
                  </Select.Option>
                  <Select.Option key='204' value='204'>
                    用户信息异常
                  </Select.Option>
                </Select>
              </Form.Item>
            </Col>

            <Col span={6}>
              <Form.Item labelCol={{ span: 0 }} wrapperCol={{ span: 24 }}>
                <Space size='small'>
                  <Button type='primary' htmlType='submit'>
                    查询
                  </Button>
                  <Button htmlType='button' onClick={() => onReset()}>
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>
      <div
        className='py-4 px-8 mb-4 mt-4 bg-white overflow-auto'
        style={{ height: `calc(100% - ${height + 16}px)` }}
      >
        <div className={'flex justify-between items-center mb-4'}>
          <span className='font-bold text-[0.8rem]'>数据列表</span>
          <Button danger ghost icon={<DownloadOutlined />} onClick={() => exportToExcelFun()}>
            导出
          </Button>
        </div>
        <Table
          className='mb-8'
          columns={tableColumns}
          dataSource={tableData}
          loading={tableLoading}
          bordered
          scroll={{ x: 'max-content' }}
          onChange={onChangePage}
          pagination={{
            ...pagination,
            total: pagination?.total,
            showSizeChanger: true,
            showQuickJumper: true,
            pageSizeOptions: ['10', '20', '50']
          }}
        />
      </div>
    </div>
  )
}
export default ReportPer
