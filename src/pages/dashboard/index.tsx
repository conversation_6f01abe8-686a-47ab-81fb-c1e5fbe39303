import { useEffect, useRef, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { useUserStore } from '@/store/user'
import * as echarts from 'echarts'
import { Card, message, Modal, Transfer } from 'antd'
import { SoundFilled, CloseOutlined, BlockOutlined } from '@ant-design/icons'
import Api from './service'
import './index.scss'
import styles from './Index.module.scss'
import zgrs from './img/zgrs.png'

const colorStart = ['#EA5A52', '#FDA616', '#663EF4', '#ADBCD7']
const colorEnd = ['#FD988B', '#FFC97C', '#9A7DFC', '#D0DEF7']

const color = colorStart.map((item, index) => {
  return {
    itemStyle: {
      color: {
        type: 'linear',
        // x,y坐标是用来调整渐变色的旋转角度
        x: 0,
        y: 1,
        x2: 0,
        y2: 0,
        colorStops: [
          { offset: 0, color: item },
          { offset: 1, color: colorEnd[index] }
        ],
        global: false
      }
    }
  }
})
const UserManagement = () => {
  const navigate = useNavigate()
  const { currentUser, setCurrentUser } = useUserStore()
  const chartRef = useRef(null)
  const chartRef2 = useRef(null)
  const chartRef3 = useRef(null)
  const [ageObj, setAgeObj] = useState({})
  const [eduObj, setEduObj] = useState({})
  const [sexObj, setSexObj] = useState({})
  const [dutyObj, setDutyObj] = useState({})
  const [menuList, setMenuList] = useState([])

  // 添加菜单相关
  const [addMenuVisible, setAddMenuVisible] = useState(false)
  const [mockData, setMockData] = useState([])
  const [targetKeys, setTargetKeys] = useState([])
  const [selectedKeys, setSelectedKeys] = useState([])

  const drawChart = (ref, data, totalNum) => {
    const myChart = echarts.init(ref.current)

    // 指定图表的配置项和数据
    const option = {
      graphic: [
        {
          type: 'text',
          left: 'center',
          top: '35%',
          style: {
            text: '总人数',
            textAlign: 'center',
            fill: '#456183',
            fontSize: '0.7rem'
          }
        },
        {
          type: 'text',
          left: 'center',
          top: '50%',
          style: {
            text: `${(Number(totalNum) || 0).toLocaleString()}人`,
            textAlign: 'center',
            fill: '#000',
            fontSize: '1rem'
          }
        }
      ],
      series: [
        {
          type: 'pie',
          radius: ['75%', '100%'],
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: false,
            position: 'center'
          },
          data
        }
      ]
    }

    // 使用刚指定的配置项和数据显示图表。
    myChart.setOption(option)
    const handleResize = () => {
      myChart.resize()
    }

    window.addEventListener('resize', handleResize)
  }

  const queryData = () => {
    Api.getHomeData({}).then((response: any) => {
      const res = response[1]
      if (res.STATUS === '0000') {
        const { age, sex, duty, edu } = res.DATA
        setAgeObj(age)
        setSexObj(sex)
        setEduObj(edu)
        setDutyObj(duty)
        // setMenuList(userMenuRel)
        // setTargetKeys(userMenuRel?.map(item => item.menuCode))
        setMockData(
          currentUser?.menus
            ?.filter(elem => elem?.menuCode !== '33')
            ?.map(item => ({
              ...item,
              key: item.menuCode,
              title: (
                <div className={styles.add_menu_transfer_wrap}>
                  <div className={`${styles.img_css} ${styles[`menu${item.menuCode}`]}`} />
                  {item.menuName}
                </div>
              )
            }))
        )

        const ageList = [
          { name: '30岁以下', value: age['30Num'], ...color[0] },
          { name: '30~45岁', value: age['345Num'], ...color[1] },
          { name: '45岁以上', value: age['45Num'], ...color[2] }
        ]
        const eduList = [
          { name: '大专', value: edu['dzNum'], ...color[0] },
          { name: '本科', value: edu['bkNum'], ...color[1] },
          { name: '研究生及以上', value: edu['yjsNum'], ...color[2] },
          { name: '其他', value: edu['qtNum'], ...color[3] }
        ]
        const sexList = [
          { name: '男性', value: sex['xNum'], ...color[0] },
          { name: '女性', value: sex['oNum'], ...color[1] }
        ]
        drawChart(chartRef, ageList, age.ageNum)
        drawChart(chartRef2, eduList, edu.eduNum)
        drawChart(chartRef3, sexList, sex.sexNum)
      } else {
        message.error(res.MESSAGE)
      }
    })
  }

  const queryMenuInfo = () => {
    Api.getUserMenuRel({}).then((response: any) => {
      const res = response[1]
      if (res.STATUS === '0000') {
        const data = res.DATA
        setMenuList(data)
        setTargetKeys(data?.map(item => item?.menuCode))
      } else {
        message.error(res.MESSAGE)
      }
    })
  }

  const saveMenuInfo = () => {
    const sortList = mockData
      ?.filter(item => targetKeys?.includes(item.key))
      ?.map(record => ({
        // ...record,
        pictureId: '',
        menuUrl: record?.path,
        menuCode: record?.menuCode,
        menuName: record?.menuName,
        userName: currentUser?.userInfo?.userName,
        sort: targetKeys.indexOf(record?.menuCode) + 1
      }))
    Api.saveUserMenuRel(sortList).then((response: any) => {
      const res = response[1]
      if (res.STATUS === '0000') {
        queryMenuInfo()
        cancelAddMenu()
        message.success(res.MESSAGE)
      } else {
        message.error(res.MESSAGE)
      }
    })
  }

  // function findParentUrls(menu, targetMenuCode, parentUrls = '') {
  //     for (const node of menu) {
  //         // 如果找到了目标节点
  //         if (node.menuCode === targetMenuCode) {
  //             return parentUrls += node.menuUrl;
  //         }
  //
  //         // 如果当前节点有子节点，递归查找
  //         if (node.children && node.children.length > 0) {
  //             const result = findParentUrls(node.children, targetMenuCode, (parentUrls += node.menuUrl));
  //             if (result.length) {
  //                 return result;
  //             }
  //         }
  //     }
  //     return [];
  // }

  // 递归查找子节点的函数
  const findNodeByCode = (tree, targetCode) => {
    for (const node of tree) {
      if (node.code === targetCode) {
        return node // 找到目标节点
      }
      if (node.children && node.children.length > 0) {
        const result = findNodeByCode(node.children, targetCode) // 递归查找子节点
        if (result) return result // 如果在子树中找到了，返回结果
      }
    }
    return null // 如果没有找到，返回 null
  }

  const handleOk = () => {
    saveMenuInfo()
  }
  const openAddMenuModal = () => {
    setAddMenuVisible(true)
  }
  const cancelAddMenu = () => {
    setAddMenuVisible(false)
    setSelectedKeys([])
  }

  // 穿梭框
  const onChange = (nextTargetKeys, direction, moveKeys) => {
    console.log('targetKeys:', nextTargetKeys)
    console.log('direction:', direction)
    console.log('moveKeys:', moveKeys)
    setTargetKeys(nextTargetKeys)
  }
  const onSelectChange = (sourceSelectedKeys, targetSelectedKeys) => {
    console.log('sourceSelectedKeys:', sourceSelectedKeys)
    console.log('targetSelectedKeys:', targetSelectedKeys)
    setSelectedKeys([...sourceSelectedKeys, ...targetSelectedKeys])
  }
  const onScroll = (direction, e) => {
    console.log('direction:', direction)
    console.log('target:', e.target)
  }

  useEffect(() => {
    queryData()
    queryMenuInfo()
  }, [])

  return (
    <div className='p-[0.8rem] dashboard-css'>
      <div className='boxItem'>
        <div className='left-content flex flex-col'>
          <div className='notice pl-[0.7rem] pr-[1rem]'>
            <span className='notice-icon'>
              <SoundFilled className='text-[1rem] text-[#F36558]' />
            </span>
            <span className='notice-text ml-[1rem] text-[#456183] text-[0.8rem]'>
              公告：系统代办 <span className='text-[red]'>21</span> 条;系统通知{' '}
              <span className='text-[red]'>24</span> 条。
            </span>
            <span className='notice-btn'>
              <CloseOutlined className='text-[1rem] text-[#fff]' />
            </span>
          </div>
          <div className='box flex-1'>
            <div className='left-box1 mt-[0.8rem]'>
              <div className='b1-card1 p-[0.8rem]'>
                <p className='card-title text-[#456183] text-[0.8rem] h-[1.8rem]'>
                  <BlockOutlined className='text-[1rem] text-[#F66357] mr-[0.2rem]' />
                  在岗人数
                </p>
                <div className='echarts-box'>
                  <div className='person-icon pt-[1rem]'>
                    <img src={zgrs}></img>
                    <div className='num text-[#456183]'>
                      <span className='text-[1.6rem] text-[#E60027] font-bold'>
                        {Number(dutyObj['dutyNum'] || 0).toLocaleString()}
                      </span>
                      人
                    </div>
                  </div>
                  <div className='person-info text-[#456183]'>
                    <p>公众线：{dutyObj['gzNum'] || 0}人</p>
                    <p>网络线：{dutyObj['wlNum'] || 0}人</p>
                    <p>政企线：{dutyObj['zqNum'] || 0}人</p>
                    <p>职能线：{dutyObj['znNum'] || 0}人</p>
                  </div>
                </div>
              </div>
              <div className='b1-card2 p-[0.8rem]'>
                <p className='card-title text-[#456183] text-[0.8rem] h-[1.8rem]'>
                  <BlockOutlined className='text-[1rem] text-[#F66357] mr-[0.2rem]' />
                  常用功能
                </p>
                <div className='echarts-box'>
                  <div className='pt-[1rem]'>
                    {menuList?.map(item => (
                      <div
                        key={item?.menuCode}
                        className='img-box'
                        onClick={() => {
                          currentUser.currentMenuCode = item?.menuCode
                          setCurrentUser(currentUser)
                          navigate(findNodeByCode(currentUser?.menus, item?.menuCode)?.path)
                        }}
                      >
                        <div className={`img-css menu${item?.menuCode}`} />
                        <span>{item?.menuName}</span>
                      </div>
                    ))}
                    <div className='img-box' onClick={openAddMenuModal}>
                      <div className={`img-css img-tjgn ${menuList?.length === 0 && 'dark_img'}`} />
                      <span>添加功能</span>
                    </div>

                    {/*<div className='img-box' style={{cursor: 'no-drop'}}>*/}
                    {/*    <div className='img-css img-rygl'/>*/}
                    {/*    <span>人员管理</span>*/}
                    {/*</div>*/}
                    {/*<div className='img-box' style={{cursor: 'no-drop'}}>*/}
                    {/*    <div className='img-css img-xcgl'/>*/}
                    {/*    <span>薪酬管理</span>*/}
                    {/*</div>*/}
                    {/*<div className='img-box' onClick={() => {*/}
                    {/*    currentUser.currentMenuCode = '36'//暂时写死*/}
                    {/*    setCurrentUser(currentUser)*/}
                    {/*    navigate('/position/people')*/}
                    {/*}}>*/}
                    {/*    <div className='img-css img-gwgl'/>*/}
                    {/*    <span>岗位管理</span>*/}
                    {/*</div>*/}
                    {/*<div className='img-box'>*/}
                    {/*    <div className='img-css img-tjgn'/>*/}
                    {/*    <span>添加功能</span>*/}
                    {/*</div>*/}
                  </div>
                </div>
              </div>
            </div>
            <div className='left-box2 mt-[1rem]'>
              <div className='b2-card1 p-[0.8rem]'>
                <p className='card-title text-[#456183] text-[0.8rem] h-[1.8rem]'>
                  <BlockOutlined className='text-[1rem] text-[#F66357] mr-[0.2rem]' />
                  年龄结构
                </p>
                <div className='echarts-box'>
                  <div className='echarts-con' ref={chartRef} />
                  <div className='echarts-text'>
                    <p>
                      <span className='text-[#456183]'>
                        <span className='d-css bg-[#DE6D64] mr-[0.4rem]' />
                        30岁以下
                      </span>
                      <span className='text-[#666] text-[1rem]'>
                        {ageObj['30Num'] || 0}
                        <span className='text-[0.7rem]'>人</span>
                      </span>
                      <span className='text-[#666] text-[1rem]'>
                        {((ageObj['30NumZb'] || 0) * 100).toFixed(2)}%
                      </span>
                    </p>
                    <p>
                      <span className='text-[#456183]'>
                        <span className='d-css bg-[#FEB747] mr-[0.4rem]' />
                        30~45岁
                      </span>
                      <span className='text-[#666] text-[1rem]'>
                        {ageObj['345Num'] || 0}
                        <span className='text-[0.7rem]'>人</span>
                      </span>
                      <span className='text-[#666] text-[1rem]'>
                        {((ageObj['345NumZb'] || 0) * 100).toFixed(2)}%
                      </span>
                    </p>
                    <p>
                      <span className='text-[#456183]'>
                        <span className='d-css bg-[#7E5BF8] mr-[0.4rem]' />
                        45岁以上
                      </span>
                      <span className='text-[#666] text-[1rem]'>
                        {ageObj['45Num'] || 0}
                        <span className='text-[0.7rem]'>人</span>
                      </span>
                      <span className='text-[#666] text-[1rem]'>
                        {((ageObj['45NumZb'] || 0) * 100).toFixed(2)}%
                      </span>
                    </p>
                  </div>
                </div>
              </div>
              <div className='b2-card2 p-[0.8rem]'>
                <p className='card-title text-[#456183] text-[0.8rem] h-[1.8rem]'>
                  <BlockOutlined className='text-[1rem] text-[#F66357] mr-[0.2rem]' />
                  学历结构
                </p>
                <div className='echarts-box'>
                  <div className='echarts-con' ref={chartRef2} />
                  <div className='echarts-text'>
                    <p>
                      <span className='text-[#456183]'>
                        <span className='d-css bg-[#DE6D64] mr-[0.4rem]' />
                        大专
                      </span>
                      <span className='text-[#666] text-[1rem]'>
                        {eduObj['dzNum'] || 0}
                        <span className='text-[0.7rem]'>人</span>
                      </span>
                      <span className='text-[#666] text-[1rem]'>
                        {((eduObj['dzNumZb'] || 0) * 100).toFixed(2)}%
                      </span>
                    </p>
                    <p>
                      <span className='text-[#456183]'>
                        <span className='d-css bg-[#FEB747] mr-[0.4rem]' />
                        本科
                      </span>
                      <span className='text-[#666] text-[1rem]'>
                        {eduObj['bkNum'] || 0}
                        <span className='text-[0.7rem]'>人</span>
                      </span>
                      <span className='text-[#666] text-[1rem]'>
                        {((eduObj['bkNumZb'] || 0) * 100).toFixed(2)}%
                      </span>
                    </p>
                    <p>
                      <span className='text-[#456183]'>
                        <span className='d-css bg-[#7E5BF8] mr-[0.4rem]' />
                        研究生及以上
                      </span>
                      <span className='text-[#666] text-[1rem]'>
                        {eduObj['yjsNum'] || 0}
                        <span className='text-[0.7rem]'>人</span>
                      </span>
                      <span className='text-[#666] text-[1rem]'>
                        {((eduObj['yjsNumZb'] || 0) * 100).toFixed(2)}%
                      </span>
                    </p>
                    <p>
                      <span className='text-[#456183]'>
                        <span className='d-css bg-[#ADBCD7] mr-[0.4rem]' />
                        其他
                      </span>
                      <span className='text-[#666] text-[1rem]'>
                        {eduObj['qtNum'] || 0}
                        <span className='text-[0.7rem]'>人</span>
                      </span>
                      <span className='text-[#666] text-[1rem]'>
                        {((eduObj['qtNumZb'] || 0) * 100).toFixed(2)}%
                      </span>
                    </p>
                  </div>
                </div>
              </div>
              <div className='b2-card3 p-[0.8rem]'>
                <p className='card-title text-[#456183] text-[0.8rem] h-[1.8rem]'>
                  <BlockOutlined className='text-[1rem] text-[#F66357] mr-[0.2rem]' />
                  男女结构
                </p>
                <div className='echarts-box'>
                  <div className='echarts-con' ref={chartRef3} />
                  <div className='echarts-text'>
                    <p>
                      <span className='text-[#456183]'>
                        <span className='d-css bg-[#DE6D64] mr-[0.4rem]' />
                        男性
                      </span>
                      <span className='text-[#666] text-[1rem]'>
                        {sexObj['xNum'] || 0}
                        <span className='text-[0.7rem]'>人</span>
                      </span>
                      <span className='text-[#666] text-[1rem]'>
                        {((sexObj['xNumZb'] || 0) * 100).toFixed(2)}%
                      </span>
                    </p>
                    <p style={{ borderBottom: 'none' }}>
                      <span className='text-[#456183]'>
                        <span className='d-css bg-[#FEB747] mr-[0.4rem]' />
                        女性
                      </span>
                      <span className='text-[#666] text-[1rem]'>
                        {sexObj['oNum'] || 0}
                        <span className='text-[0.7rem]'>人</span>
                      </span>
                      <span className='text-[#666] text-[1rem]'>
                        {((sexObj['oNumZb'] || 0) * 100).toFixed(2)}%
                      </span>
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className='right-content p-[0.8rem]'>
          <p className='card-title text-[#456183] text-[0.8rem] h-[2rem]'>
            <BlockOutlined className='text-[1rem] text-[#F66357] mr-[0.2rem]' />
            系统待办
          </p>
          <div className='card-box'>
            <div className='card-div'>
              <Card className='card-css border-l-[#F6AB7F] border-l-[0.3rem]'>
                <ul>
                  <li>
                    <p className='text-[#456183]'>
                      <span className='ku bg-[#F6AB7F]' />
                      人员管理：张思思离职审批申请
                    </p>
                    <p className='li-con text-[#828282]'>
                      <span>上一步提交人：王阳</span>
                      <span>2024-08-21 14:18:23</span>
                    </p>
                  </li>
                  <li>
                    <p className='text-[#456183]'>
                      <span className='ku bg-[#F6AB7F]' />
                      人员管理：张思思离职审批申请
                    </p>
                    <p className='li-con text-[#828282]'>
                      <span>上一步提交人：王阳</span>
                      <span>2024-08-21 14:18:23</span>
                    </p>
                  </li>
                  <li>
                    <p className='text-[#456183]'>
                      <span className='ku bg-[#F6AB7F]' />
                      人员管理：张思思离职审批申请
                    </p>
                    <p className='li-con text-[#828282]'>
                      <span>上一步提交人：王阳</span>
                      <span>2024-08-21 14:18:23</span>
                    </p>
                  </li>
                </ul>
              </Card>
            </div>
            <div className='card-div'>
              <Card className='card-css border-l-[#7AC2FA] border-l-[0.3rem]'>
                <ul>
                  <li>
                    <p className='text-[#456183]'>
                      <span className='ku bg-[#7AC2FA]' />
                      人员管理：张思思离职审批申请
                    </p>
                    <p className='li-con text-[#828282]'>
                      <span>上一步提交人：王阳</span>
                      <span>2024-08-21 14:18:23</span>
                    </p>
                  </li>
                  <li>
                    <p className='text-[#456183]'>
                      <span className='ku bg-[#7AC2FA]' />
                      人员管理：张思思离职审批申请
                    </p>
                    <p className='li-con text-[#828282]'>
                      <span>上一步提交人：王阳</span>
                      <span>2024-08-21 14:18:23</span>
                    </p>
                  </li>
                  <li>
                    <p className='text-[#456183]'>
                      <span className='ku bg-[#7AC2FA]' />
                      人员管理：张思思离职审批申请
                    </p>
                    <p className='li-con text-[#828282]'>
                      <span>上一步提交人：王阳</span>
                      <span>2024-08-21 14:18:23</span>
                    </p>
                  </li>
                </ul>
              </Card>
            </div>
            <div className='card-div'>
              <Card className='card-css border-l-[#80DFC1] border-l-[0.3rem]'>
                <ul>
                  <li>
                    <p className='text-[#456183]'>
                      <span className='ku bg-[#80DFC1]' />
                      人员管理：张思思离职审批申请
                    </p>
                    <p className='li-con text-[#828282]'>
                      <span>上一步提交人：王阳</span>
                      <span>2024-08-21 14:18:23</span>
                    </p>
                  </li>
                  <li>
                    <p className='text-[#456183]'>
                      <span className='ku bg-[#80DFC1]' />
                      人员管理：张思思离职审批申请
                    </p>
                    <p className='li-con text-[#828282]'>
                      <span>上一步提交人：王阳</span>
                      <span>2024-08-21 14:18:23</span>
                    </p>
                  </li>
                  <li>
                    <p className='text-[#456183]'>
                      <span className='ku bg-[#80DFC1]' />
                      人员管理：张思思离职审批申请
                    </p>
                    <p className='li-con text-[#828282]'>
                      <span>上一步提交人：王阳</span>
                      <span>2024-08-21 14:18:23</span>
                    </p>
                  </li>
                </ul>
              </Card>
            </div>
          </div>
        </div>
      </div>

      <Modal
        title='- 添加菜单 -'
        destroyOnClose={true}
        open={addMenuVisible}
        centered
        className={styles.add_menu_modal}
        okText='保存'
        // okButtonProps={{
        //   disabled: this.initDisSave(),
        // }}
        onOk={handleOk}
        onCancel={cancelAddMenu}
        width={'40%'}
      >
        <div className={'add_menu_content'}>
          <Transfer
            dataSource={mockData}
            titles={['全部功能', '常用功能']}
            targetKeys={targetKeys}
            selectedKeys={selectedKeys}
            onChange={onChange}
            onSelectChange={onSelectChange}
            onScroll={onScroll}
            render={item => item.title}
          />
        </div>
      </Modal>
    </div>
  )
}

export default UserManagement
