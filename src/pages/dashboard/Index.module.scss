.add_menu_modal {
  :global {
    .ant-modal-title {
      text-align: center;
      line-height: 35px !important;
      font-size: 15px !important;
      color: #E60027 !important;
    }
    .ant-modal-footer {
      text-align: center;
    }
    .ant-transfer-list {
      height: 20rem;
    }
    .ant-transfer-list-header {
      background-color: #fafafa;
    }
    .ant-transfer {
      display: flex;
      justify-content: center;
    }
    .ant-transfer-list {
      width: 40%;
    }
  }

  .add_menu_content {
  }
}

.add_menu_transfer_wrap {
  display: flex;
  align-items: center;
  line-height: 25px;
  .img_css {
    width: 1rem;
    height: 1rem;
    background-size: cover;
    margin-right: 5px;
  }
  .menu34 {
    background-image: url("img/menu34.png");
  }
  .menu35 {
    background-image: url("img/menu35.png");
  }
  .menu36 {
    background-image: url("img/menu36.png");
  }
  .menu37 {
    background-image: url("img/menu37.png");
  }
  .menu38 {
    background-image: url("img/menu38.png");
  }
  .menu39 {
    background-image: url("img/menu39.png");
  }
  .menu40 {
    background-image: url("img/menu40.png");
  }
  .menu41 {
    background-image: url("img/menu41.png");
  }
  .menu42 {
    background-image: url("img/menu42.png");
  }
}
