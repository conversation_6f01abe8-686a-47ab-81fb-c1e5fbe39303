.dashboard-css {
  width: 100%;
  height: calc(100vh - 4rem);
  // display: flex;
  overflow: auto;
  .boxItem{
    display: flex;
    min-height: 100%;
    // height: 100%;
  }
  .left-content {
    width: 74%;
    // height: max-content;
    min-height: 100%;
    // height: 100%;

    .notice {
      width: calc(100% - 1rem);
      padding: 0.5rem 1rem;
      background: #E2EEF9;
      border-radius: 2.6rem;
      display: flex;
      align-items: center;
      margin-left: 0.5rem;

      .notice-icon {
        background: #fff;
        width: 1.9rem;
        height: 1.9rem;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .notice-text {
        flex: 1;
      }

      .notice-btn {
        float: right;
        width: 1.6rem;
        height: 1.6rem;
        background: #EB5957;
        border-radius: 6px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }

    .box {
      display: flex;
      flex-direction: column;
      // height: calc(100% - 2.6rem);
    }

    .left-box1 {
      display: flex;
      flex: 1;

      .b1-card1 {
        width: calc(33.33% - 1rem);
        border: 1px solid #fff;
        background: #fff;
        border-radius: 4px;
        margin: 0 0.5rem;
      }

      .b1-card2 {
        flex: 1;
        border: 1px solid #fff;
        background: #fff;
        border-radius: 4px;
        margin: 0 0.5rem;
      }

      .echarts-box {
        width: 100%;
        min-height: calc(100% - 1.8rem);

        .person-icon {
          height: 65%;
          text-align: center;
          display: flex;
          justify-content: center;
          align-items: center;
          flex-direction: column;
          img{
            width: 50%;
          }
        }

        .person-info {
          width: 80%;
          height: 25%;
          margin-left: 10%;
          display: flex;
          flex-wrap: wrap;

          p {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 50%;
          }
        }
      }

      .img-box {
        color: #4E6C8E;
        width: 4rem;
        text-align: center;
        float: left;
        margin-right: 2rem;
        cursor: pointer;

        .img-css {
          width: 4rem;
          height: 4rem;
          background-size: cover;
        }

        .img-css:hover {
          filter: brightness(0.9);
        }
        
        .dark_img {
          filter: brightness(0.7);
          cursor: no-drop;
        }

        .menu34 {
          background-image: url("img/menu34.png");
        }
        .menu35 {
          background-image: url("img/menu35.png");
        }
        .menu36 {
          background-image: url("img/menu36.png");
        }
        .menu37 {
          background-image: url("img/menu37.png");
        }
        .menu38 {
          background-image: url("img/menu38.png");
        }
        .menu39 {
          background-image: url("img/menu39.png");
        }
        .menu40 {
          background-image: url("img/menu40.png");
        }
        .menu41 {
          background-image: url("img/menu41.png");
        }
        .menu42 {
          background-image: url("img/menu42.png");
        }

        .img-rygl {
          background-image: url("img/rygl.png");
        }

        .img-xcgl {
          background-image: url("img/xcgl.png");
        }

        .img-gwgl {
          background-image: url("img/gwgl.png");
        }

        .img-tjgn {
          background-image: url("img/tjgn.png");
        }

        span {
          display: inline-block;
          width: 100%;
          margin-top: 0.2rem;
        }
      }

    }

    .left-box2 {
      display: flex;
      flex: 1;

      .b2-card1, .b2-card2, .b2-card3 {
        display: flex;
        flex-direction: column;
        flex: 1;
        border: 1px solid #fff;
        background: #fff;
        border-radius: 4px;
        margin: 0 0.5rem;
      }

      .echarts-box {
        flex: 1;
        width: 100%;
        display: flex;
        flex-direction: column;
        // height: calc(100% - 1.8rem);
      }

      .echarts-con {
        width: 100%;
        // height: 55%;
        height: 150px;
      }

      .echarts-text {
        // height: 45%;
        flex: 1;
        padding-top: 0.4rem;
        display: flex;
        flex-direction: column;

        p {
          border-bottom: 1px dashed #eee;
        }

        p:last-child {
          border-bottom: none;
        }

        p {
          padding: 0 10% 0 10%;
          display: flex;
          align-items: center;
          flex: 1;

          .d-css {
            display: inline-block;
            width: 0.5rem;
            height: 0.5rem;
            border-radius: 50%;
          }

          span:first-child {
            text-align: left;
          }

          span {
            text-align: center;
            flex: 1;
          }
        }
      }
    }
  }

  .right-content {
    width: 26%;
    border: 1px solid #fff;
    background: #fff;
    border-radius: 4px;
    margin-left: 1rem;
    // height: max-content;
    min-height: 100%;
    
    .card-box {
      // height: calc(100% - 2rem);
      display: flex;
      flex-direction: column;

      .card-div {
        flex: 1;
        margin-bottom: 10px;

        .card-css {
          // height: calc(100% - 0.8rem);

          .ku {
            width: 0.6rem;
            height: 0.6rem;
            display: inline-block;
            margin-right: 0.2rem;
          }
        }
      }
    }

    ul {
      li {
        margin-top: 1rem;
      }

      li:first-child {
        margin-top: 0 !important;
      }

      .li-con {
        display: flex;
        justify-content: space-between;
        margin-top: 0.2rem;
      }
    }
  }

}
