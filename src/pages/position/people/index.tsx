import { useEffect, useRef, useState } from 'react'
import {
  Form,
  Row,
  Col,
  Input,
  Select,
  Button,
  FormProps,
  Modal,
  Upload,
  message,
  Space,
  Spin,
  Cascader,
  GetProp,
  CascaderProps,
  Tooltip,
  Tag
} from 'antd'
import {
  UploadOutlined,
  DownloadOutlined,
  InboxOutlined,
  FullscreenExitOutlined,
  FullscreenOutlined
} from '@ant-design/icons'
import SvgIcon from '@/components/SvgIcons'
import peopleService from './service'
import { useRequest } from '@/hooks'
import styles from './Index.module.scss'
import type { TableColumnsType } from 'antd'
import { Enum, FieldType, FieldType2 } from './interface'
import { downFile, openNotification } from '@/utils/down.tsx'
import effectService from '@/pages/effect/employment/service.ts'
import dayjs from 'dayjs'
import { orgOption } from '@/pages/effect/employment/assest.ts'
import ResizableTable from '@/components/resizeTable/index.jsx'
type DefaultOptionType = GetProp<CascaderProps, 'options'>[number]
const { Dragger } = Upload

interface DataType {
  key: React.Key
  name: string
  age: number
  address: string
}

const data: DataType[] = []
for (let i = 0; i < 100; i++) {
  data.push({
    key: i,
    name: `Edward ${i}`,
    age: 32,
    address: `London Park no. ${i}`
  })
}

const People: React.FC = () => {
  const [form] = Form.useForm()
  const [editform] = Form.useForm()
  const [tableLoading, setTableLoading] = useState<boolean>(false)
  const [saveLoading, setSaveLoading] = useState<boolean>(false)
  const [editRecord, setEditRecord] = useState<any>(null)
  const [upModalOpen, setOpModalOpen] = useState<boolean>(false)
  const [fileList, setFileList] = useState([])
  // const [postList, setPostList] = useState<Enum[]>([])
  // const [levelList, setLevelList] = useState<Enum[]>([])
  // const [levelList2, setLevelList2] = useState<Enum[]>([])
  const [showTitle, setShowTitle] = useState<boolean>(true)
  const [orgValueArr, setOrgValueArr] = useState([])
  const [unitList, setUnitList] = useState<any[]>([])
  const [typeList, setTypeList] = useState<Enum[]>([])
  const [remarkList1, setRemarkList1] = useState<Enum[]>([])
  const [remarkList2, setRemarkList2] = useState<Enum[]>([])
  const [remarkList3, setRemarkList3] = useState<Enum[]>([])
  const [tableData, setTableData] = useState<DataType[]>([])
  const [pagination, setPagination] = useState({
    total: 0,
    pageNum: 1,
    pageSize: 50
  })

  // 计算高度相关
  const topRef = useRef(null)
  const contentRef = useRef(null)
  const tableTopRef = useRef(null)
  const [height, setHeight] = useState<number>(0)

  const { runAsync: getEnumType } = useRequest(peopleService.getEnumType, { manual: true })
  const { runAsync: getEmployeePostPag } = useRequest(peopleService.getEmployeePostPag, {
    manual: true
  })
  const { runAsync: downloadEmployeeTemplate } = useRequest(
    peopleService.downloadEmployeeTemplate,
    { manual: true }
  )
  const { runAsync: downloadEmployeeCompare } = useRequest(peopleService.downloadEmployeeCompare, {
    manual: true
  })
  const { runAsync: exportEmployeeExcel } = useRequest(peopleService.exportEmployeeExcel, {
    manual: true
  })
  const { runAsync: uploadEmployeeExcel } = useRequest(peopleService.uploadEmployeeExcel, {
    manual: true
  })
  const { runAsync: updateEmployeePost } = useRequest(peopleService.updateEmployeePost, {
    manual: true
  })
  const { runAsync: build4LevelOrgTree } = useRequest(effectService.build4LevelOrgTree, {
    manual: true
  })

  const colorMap = {
    正式: 'blue',
    紧密型外包: 'cyan',
    其他: 'orange'
  }
  const columns: TableColumnsType<any> = [
    {
      title: '员工编码',
      width: 75,
      minWidth: 75,
      dataIndex: 'employeeId',
      key: 'employeeId',
      align: 'center',
      fixed: 'left'
    },
    {
      title: '员工名称',
      width: 75,
      minWidth: 75,
      dataIndex: 'employeeName',
      key: 'employeeName',
      align: 'center',
      fixed: 'left'
    },
    {
      title: '所属单位',
      dataIndex: 'orgName5',
      key: 'orgName5',
      align: 'center',
      width: 150,
      minWidth: 150,
      // ellipsis: true,
      render: (text: string, _record: orgOption) => (
        <Tooltip title={text}>
          <div className={styles.over_ellipsis}>{text}</div>
        </Tooltip>
      )
    },
    {
      title: '所属区域',
      dataIndex: 'region',
      key: 'region',
      align: 'center',
      width: 90,
      minWidth: 90
    },
    {
      title: '年龄',
      dataIndex: 'age',
      key: 'age',
      align: 'center',
      width: 50,
      minWidth: 50
    },
    {
      title: '性别',
      dataIndex: 'gender',
      key: 'gender',
      align: 'center',
      width: 50,
      minWidth: 50
    },
    {
      title: '最高学历',
      dataIndex: 'edu',
      key: 'edu',
      align: 'center',
      width: 75,
      minWidth: 75
    },
    {
      title: '用工类型',
      dataIndex: 'employeeType',
      key: 'employeeType',
      align: 'center',
      width: 75,
      minWidth: 75,
      render: (text, _) => <Tag color={colorMap[text] || 'purple'}>{text}</Tag>
    },
    // {
    //   title: '岗位ID',
    //   dataIndex: 'postId',
    //   key: 'postId',
    //   width: 150
    // },
    {
      title: '岗位名称',
      dataIndex: 'postName',
      key: 'postName',
      align: 'center',
      width: 120,
      minWidth: 120
    },
    {
      title: '岗级',
      dataIndex: 'postLevel',
      key: 'postLevel',
      align: 'center',
      width: 50,
      minWidth: 50
    },
    {
      title: '备注1',
      dataIndex: 'remarkOne',
      key: 'remarkOne',
      align: 'center',
      width: 100,
      minWidth: 100
    },
    {
      title: '备注2',
      dataIndex: 'remarkTwo',
      key: 'remarkTwo',
      align: 'center',
      width: 100,
      minWidth: 100
    },
    {
      title: '备注3',
      dataIndex: 'remarkThree',
      key: 'remarkThree',
      align: 'center',
      width: 100,
      minWidth: 100
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      fixed: 'right',
      align: 'center',
      width: 100,
      minWidth: 100,
      render: (_, record) => (
        <span
          className='cursor-pointer'
          style={{ color: '#F14846' }}
          onClick={() => editItem(record)}
        >
          编辑
        </span>
      )
    }
  ]

  useEffect(() => {
    getEnumTypes()
    console.log(remarkList1, remarkList2, remarkList3)
    // 添加 resize 事件监听器
    window.addEventListener('resize', handleResize)
    // 清除事件监听器，防止内存泄漏
    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])
  const handleResize = () => {
    initHeight()
  }

  useEffect(() => {
    initHeight()
  }, [
    (document.querySelector('.position_people_table .ant-table-header') || {})['offsetHeight'],
    (document.querySelector('.position_people_table .ant-table-pagination') || {})['offsetHeight']
  ])

  useEffect(() => {
    if (!editRecord && typeList?.length > 0) {
      const params = {
        tag: '1',
        ...form.getFieldsValue()
      }
      queryTableData(params)
    }
  }, [editRecord])
  const initHeight = () => {
    const headerHeight =
      (document.querySelector('.position_people_table .ant-table-header') || {})['offsetHeight'] ||
      0
    const pageHeight =
      (document.querySelector('.position_people_table .ant-table-pagination') || {})[
        'offsetHeight'
      ] || 26
    if (headerHeight && pageHeight) {
      setHeight(
        contentRef.current?.offsetHeight -
          (tableTopRef.current.offsetHeight + headerHeight + pageHeight)
      )
    }
  }

  const handleDownload = async (type: number) => {
    try {
      // 调用接口获取 Blob 文件数据
      openNotification('正在导出', 0, 'loading')
      let response = null
      if (type === 1) {
        response = await downloadEmployeeTemplate({ tag: '1' })
      } else if (type === 2) {
        const params = form.getFieldsValue()
        const curOrg = orgValueArr ? orgValueArr[orgValueArr.length - 1] : {}
        response = await exportEmployeeExcel({
          ...params,
          tag: '1',
          unit: undefined,
          // regionId: Number(curOrg?.level) === 4 ? curOrg?.orgId : '',
          // unitId: Number(curOrg?.level) === 5 ? curOrg?.orgId : '',
          org4: Number(curOrg?.level) === 4 ? curOrg?.orgId : '',
          org5: Number(curOrg?.level) === 5 ? curOrg?.orgId : '',
          org6: Number(curOrg?.level) === 6 ? curOrg?.orgId : ''
        })
      } else if (type === 3) {
        response = await downloadEmployeeCompare({ tag: '1' })
      }
      downFile(response)
    } catch (error) {
      openNotification('导出失败', 1, 'error')
      console.error('Download failed:', error)
    }
  }

  // 点击查询
  const onFormFinish: FormProps<FieldType>['onFinish'] = values => {
    console.log('Success:', values)
    queryTableData({
      tag: '1',
      ...values
    })
  }

  // 点击保存
  const onFormFinish2: FormProps<FieldType2>['onFinish'] = async values => {
    console.log('Success:', values)
    setSaveLoading(true)
    const [error, res] = await updateEmployeePost({
      // postId: values.postId.split(',')[0],
      tag: '1',
      // postName: values?.postName.split(',')[1],
      postName: values?.postName,
      postLevel: values?.postLevel,
      remarkOne: values?.remark1,
      remarkTwo: values?.remark2,
      remarkThree: values?.remark3,
      employeeId: editRecord?.employeeId
    })
    setSaveLoading(false)
    if (error) {
      message.error('修改失败')
      return
    }
    if (res.STATUS === '0000') {
      if (res.DATA === '修改成功!') {
        message.success(res.DATA)
      } else {
        message.error(res.DATA)
      }
      setEditRecord(null)
    } else {
      message.error(res?.MESSAGE)
    }
  }

  // const getLevelData = async (type: number, postName?: string) => {
  //   let record = null
  //   if (type === 1) {
  //     const postId = form.getFieldsValue()?.postId
  //     record = postList?.find(item => `${item?.enumId},${item?.region}` === postId)
  //   } else {
  //     const postId = editform.getFieldsValue()?.postName
  //     if (postName) {
  //       record = postList?.find(item => item?.enumName === postName)
  //     } else {
  //       record = postList?.find(item => `${item?.enumId},${item?.region}` === postId)
  //     }
  //   }
  //   const [error, res] = await getEnumType({ code: '1003', region: record?.enumId })
  //   if (error) {
  //     return
  //   }
  //   if (res.STATUS === '0000') {
  //     if (type === 1) {
  //       setLevelList(res.DATA)
  //     } else {
  //       setLevelList2(res.DATA)
  //     }
  //   }
  // }

  const getEnumTypes = async () => {
    const [
      [postError, postData],
      [levelError, levelData],
      [unitError, unitData],
      [typeError, typeData],
      [remark1Error, remark1Data],
      [remark2Error, remark2Data],
      [remark3Error, remark3Data]
    ] = await Promise.all([
      getEnumType({ code: '1002', region: '' }),
      getEnumType({ code: '1003', region: '' }),
      build4LevelOrgTree({
        monthId: dayjs().format('YYYYMM'),
        tag: '1'
      }),
      getEnumType({ code: '1009', region: '' }),
      getEnumType({ code: '1006', region: '' }),
      getEnumType({ code: '1007', region: '' }),
      getEnumType({ code: '1008', region: '' })
    ])
    if (
      postError ||
      levelError ||
      unitError ||
      typeError ||
      remark1Error ||
      remark2Error ||
      remark3Error
    ) {
      return
    }
    if (postData.STATUS === '0000') {
      // setPostList(postData.DATA)
    }
    if (levelData.STATUS === '0000') {
      // setLevelList(levelData.DATA)
    }
    if (unitData.STATUS === '0000') {
      setUnitList(unitData.DATA?.filter(item => item?.orgId !== '49757'))
    }
    if (typeData.STATUS === '0000') {
      setTypeList(typeData.DATA)
    }
    if (remark1Data.STATUS === '0000') {
      setRemarkList1(remark1Data.DATA)
    }
    if (remark2Data.STATUS === '0000') {
      setRemarkList2(remark2Data.DATA)
    }
    if (remark3Data.STATUS === '0000') {
      setRemarkList3(remark3Data.DATA)
    }
    const params = {
      tag: '1',
      ...form.getFieldsValue()
    }
    queryTableData(params)
  }

  // const handleChangePost = () => {
  //   form.setFieldsValue({
  //     level: ''
  //   })
  //   getLevelData(1)
  // }

  // const handleChangePost2 = () => {
  //   editform.setFieldsValue({
  //     postLevel: ''
  //   })
  //   getLevelData(2)
  // }

  const queryTableData = async params => {
    setTableLoading(true)
    const curOrg = orgValueArr ? orgValueArr[orgValueArr.length - 1] : {}
    const [error, res] = await getEmployeePostPag({
      ...params,
      ...pagination,
      code: params?.code?.trim(),
      name: params?.name?.trim(),
      unit: undefined,
      // regionId: Number(curOrg?.level) === 4 ? curOrg?.orgId : '',
      // unitId: Number(curOrg?.level) === 5 ? curOrg?.orgId : '',
      org4: Number(curOrg?.level) === 4 ? curOrg?.orgId : '',
      org5: Number(curOrg?.level) === 5 ? curOrg?.orgId : '',
      org6: Number(curOrg?.level) === 6 ? curOrg?.orgId : ''
    })
    setTableLoading(false)
    if (error) {
      return
    }
    if (res.STATUS === '0000') {
      const {
        DATA: { data }
      } = res
      // 添加唯一的 key
      const dataWithKeys = data.map((item, index) => ({
        ...item,
        key: item.id || index // 使用数据中的唯一字段作为 key，或回退到索引
      }))
      setTableData(dataWithKeys)
      setPagination({
        ...pagination,
        total: res.DATA?.totalCount
      })
    }
  }

  const uploadFileFun = async () => {
    const formData = new FormData()
    fileList
      .map(file => file?.originFileObj)
      .forEach(file => {
        formData.append('file', file)
      })
    formData.append('tag', '1')
    const [error, res] = await uploadEmployeeExcel(formData)
    if (error) {
      return
    }
    if (res.STATUS === '0000') {
      setOpModalOpen(false)
      message.success(res?.DATA)
    } else {
      message.error(res?.MESSAGE)
    }
  }

  const editItem = record => {
    setEditRecord(record)
    // getLevelData(2, record?.postName)
    editform.setFieldsValue({
      ...record,
      remark1: record?.remarkOne || record?.remark1,
      remark2: record?.remarkTwo || record?.remark2,
      remark3: record?.remarkThree || record?.remark3
    })
  }

  // 上传
  const handleOk = () => {
    if (fileList?.length > 0) {
      uploadFileFun()
    } else {
      message.error('请先选择文件上传')
    }
  }

  const handleCancel = () => {
    setOpModalOpen(false)
  }

  // 切换组织
  const handleOrgChange = (_value, selectedOptions) => {
    setOrgValueArr(selectedOptions)
  }

  // 查询重置
  const onReset = () => {
    setOrgValueArr([])
    form.resetFields()
  }

  // 切换分页
  const onChangePage = page => {
    const newPage = {
      total: page?.total,
      pageNum: page?.current,
      pageSize: page?.pageSize
    }
    setPagination(newPage)
  }

  useEffect(() => {
    if (pagination?.total > 0) {
      const params = {
        tag: '1',
        ...form.getFieldsValue()
      }
      queryTableData(params)
    }
  }, [pagination.pageNum, pagination.pageSize])

  // // 搜索过滤空格
  // const handleSearchFilter = (input, option) => {
  //   const cleanedInput = input.trim() // 去除输入中的空格
  //   return option.children.toLowerCase().includes(cleanedInput.toLowerCase())
  // }

  // 搜索过滤空格
  const filter = (inputValue: string, path: DefaultOptionType[]) =>
    path.some(
      option =>
        (option.orgName as string).toLowerCase().indexOf(inputValue.trim().toLowerCase()) > -1
    )

  return (
    <div className={`${'pt-[0.5rem] px-[0rem] h-full flex flex-col'} ${styles.position_page}`}>
      {!editRecord ? (
        <>
          <div ref={topRef} className='bg-white pt-[0.2rem] px-[0.75rem] mb-[0.5rem]'>
            <Form
              form={form}
              labelCol={{ span: 6 }}
              onFinish={onFormFinish}
              initialValues={
                {
                  // code: '',
                  // name: '',
                  // type: '',
                  // unit: '',
                  // postId: '',
                  // level: ''
                }
              }
            >
              <Row gutter={24}>
                <Col span={6}>
                  <Form.Item name='unit' label='组织'>
                    <Cascader
                      allowClear
                      changeOnSelect
                      expandTrigger='hover'
                      displayRender={labels => labels[labels.length - 1]}
                      options={unitList}
                      onChange={handleOrgChange}
                      fieldNames={{
                        // value: 'orgId',
                        value: 'orgId',
                        label: 'orgName',
                        children: 'children'
                      }}
                      // showCheckedStrategy={Cascader.SHOW_CHILD}
                      // onChange={(value, selectedOptions) => {
                      //   console.log(value, selectedOptions)
                      //   setCascaderSelected(selectedOptions)
                      // }}
                      placeholder='请选择组织'
                      showSearch={{ filter }}
                      onSearch={value => console.log(value)}
                    />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item name='code' label='员工编码'>
                    <Input placeholder='请输入员工编码' autoComplete='off' allowClear />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item name='name' label='员工姓名'>
                    <Input placeholder='请输入员工姓名' autoComplete='off' allowClear />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item name='type' label='用工类型'>
                    <Select placeholder='请选择用工类型' allowClear>
                      {
                        // prettier-ignore
                        typeList.map(unit => {
                              const {enumId, enumName} = unit;
                              return <Select.Option key={enumId}
                                                    value={enumName}>{enumName}</Select.Option>
                            })
                      }
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item name='postName' label='岗位'>
                    <Input placeholder='请输入' allowClear />
                    {/*<Select*/}
                    {/*  placeholder='请选择岗位'*/}
                    {/*  allowClear*/}
                    {/*  showSearch*/}
                    {/*  onChange={handleChangePost}*/}
                    {/*>*/}
                    {/*  /!*<Select.Option key='all' value=''>*!/*/}
                    {/*  /!*  全部岗位*!/*/}
                    {/*  /!*</Select.Option>*!/*/}
                    {/*  {*/}
                    {/*    // prettier-ignore*/}
                    {/*    postList.map(unit => {*/}
                    {/*      const {enumId, region, enumName} = unit;*/}
                    {/*      return <Select.Option key={`${enumId},${region}`}*/}
                    {/*                            value={`${enumId},${region}`}>{enumName}</Select.Option>*/}
                    {/*    })*/}
                    {/*  }*/}
                    {/*</Select>*/}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    name='level'
                    label='岗级'
                    rules={[{ pattern: /^[+]{0,1}(\d+)$/, message: '只支持数字，请正确输入！' }]}
                  >
                    <Input placeholder='请输入' allowClear />
                    {/*<Select placeholder='请选择岗级' showSearch allowClear>*/}
                    {/*  /!*<Select.Option key='all' value=''>*!/*/}
                    {/*  /!*  全部岗级*!/*/}
                    {/*  /!*</Select.Option>*!/*/}
                    {/*  {*/}
                    {/*    // prettier-ignore*/}
                    {/*    levelList.map(unit => {*/}
                    {/*                                    const {enumId, enumName} = unit;*/}
                    {/*                                    return <Select.Option key={enumId}*/}
                    {/*                                                          value={enumId}>{enumName}</Select.Option>*/}
                    {/*                                })*/}
                    {/*  }*/}
                    {/*</Select>*/}
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <div className='text-right'>
                    <Space>
                      <Button type='primary' htmlType='submit'>
                        查询
                      </Button>
                      <Button onClick={() => onReset()}>重置</Button>
                      <Button icon={<DownloadOutlined />} onClick={() => handleDownload(2)}>
                        导出
                      </Button>
                    </Space>
                  </div>
                </Col>
              </Row>
            </Form>
          </div>
          <div
            ref={contentRef}
            className='relative bg-white px-5 pt-2.5'
            style={{ height: `calc(100% - ${topRef.current?.offsetHeight + 15}px)` }}
          >
            <div
              ref={tableTopRef}
              className={`${'flex justify-between items-center mb-2 overflow-hidden'} ${styles.animation_box} ${showTitle ? 'h-[1.6rem]' : 'h-0'}`}
            >
              <div className={'flex '}>
                {showTitle ? (
                  <FullscreenExitOutlined
                    className={`${styles.shousuo_icon} text-[1rem]`}
                    onClick={() => {
                      setShowTitle(false)
                      setTimeout(() => {
                        initHeight()
                      }, 200)
                    }}
                  />
                ) : (
                  <FullscreenOutlined
                    className={`${styles.shousuo_icon} ${'absolute top-[0.35rem] left-[0.3rem] text-[1rem] z-10'}`}
                    onClick={() => {
                      setShowTitle(true)
                      setTimeout(() => {
                        initHeight()
                      }, 200)
                    }}
                  />
                )}
                <div className='font-bold text-[0.8rem] ml-3'>数据列表</div>
              </div>
              <div className='flex gap-x-[0.75rem]'>
                <div className='flex items-center gap-x-[0.25rem]'>
                  <SvgIcon name='excel' width={20} height={20} />
                  <span className='text-[#E60027] cursor-pointer' onClick={() => handleDownload(1)}>
                    下载导入模版
                  </span>
                </div>
                {/*<div className='flex items-center gap-x-[0.25rem] ml-[0.4rem]'>*/}
                {/*  <SvgIcon name='excel' width={20} height={20}/>*/}
                {/*  <span*/}
                {/*      className='text-[#E60027] cursor-pointer'*/}
                {/*      onClick={() => handleDownload(3)}*/}
                {/*  >下载对照表</span>*/}
                {/*</div>*/}
                <Button
                  className='ml-[0.4rem]'
                  danger
                  ghost
                  icon={<UploadOutlined />}
                  onClick={() => setOpModalOpen(true)}
                >
                  导入
                </Button>
              </div>
            </div>
            <ResizableTable
              className='position_people_table'
              rowClassName={(_, i) => (i % 2 === 1 ? `customRow odd` : `customRow even`)}
              columns={columns}
              dataSource={tableData}
              loading={tableLoading}
              onChange={onChangePage}
              bordered
              scroll={{
                y: `calc(${height}px - 0.625rem - 1.6rem - 0.5rem)`
              }}
              pagination={{
                ...pagination,
                showSizeChanger: true,
                showQuickJumper: true,
                pageSizeOptions: ['10', '20', '50']
              }}
            />
          </div>
        </>
      ) : (
        <div className='bg-white h-[calc(100%-1rem)] pt-[1rem] px-[2.8rem]'>
          <Spin spinning={saveLoading}>
            <div className='text-[0.8rem] font-bold mb-[0.8rem]'>员工信息</div>
            <Form
              form={editform}
              labelCol={{ span: 6 }}
              onFinish={onFormFinish2}
              initialValues={{
                ...editRecord
              }}
            >
              <Row>
                <Col span={6}>
                  <Form.Item name='employeeName' label='员工姓名'>
                    <span>{editRecord.employeeName}</span>
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item name='employeeId' label='员工编号'>
                    <span>{editRecord.employeeId}</span>
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item name='unit' label='所属单位'>
                    <span>{editRecord.orgName5}</span>
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item name='region' label='所属区域'>
                    <span>{editRecord.region}</span>
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item name='age' label='年龄'>
                    <span>{editRecord.age}</span>
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item name='gender' label='性别'>
                    <span>{editRecord.gender}</span>
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item name='edu' label='学历'>
                    <span>{editRecord.edu}</span>
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item name='employeeType' label='用工类型'>
                    <span>{editRecord.employeeType}</span>
                  </Form.Item>
                </Col>
                {/*<Col span={6}>*/}
                {/*  <Form.Item*/}
                {/*    name='postId'*/}
                {/*    label='岗位ID'*/}
                {/*    rules={[{ required: true, message: '请选择岗位ID!' }]}*/}
                {/*  >*/}
                {/*    <Input placeholder='请输入' />*/}
                {/*    /!*<Select placeholder='请选择岗位ID' showSearch filterOption={handleSearchFilter}>*!/*/}
                {/*    /!*  {*!/*/}
                {/*    /!*    // prettier-ignore*!/*/}
                {/*    /!*    postList.map(unit => {*!/*/}
                {/*    /!*                                    const {enumId, region} = unit;*!/*/}
                {/*    /!*                                    return <Select.Option key={`${enumId},${region}`}*!/*/}
                {/*    /!*                                                          value={`${enumId},${region}`}>{enumId}</Select.Option>*!/*/}
                {/*    /!*                                })*!/*/}
                {/*    /!*  }*!/*/}
                {/*    /!*</Select>*!/*/}
                {/*  </Form.Item>*/}
                {/*</Col>*/}
                <Col span={6}>
                  <Form.Item
                    name='postName'
                    label='岗位名称'
                    rules={[{ required: true, message: '请输入岗位名称!' }]}
                  >
                    <Input placeholder='请输入岗位名称' allowClear />
                    {/*<Select*/}
                    {/*  placeholder='请选择岗位'*/}
                    {/*  showSearch*/}
                    {/*  filterOption={handleSearchFilter}*/}
                    {/*  onChange={handleChangePost2}*/}
                    {/*>*/}
                    {/*  {*/}
                    {/*    // prettier-ignore*/}
                    {/*    postList.map(unit => {*/}
                    {/*                                    const {enumId, region, enumName} = unit;*/}
                    {/*                                    return <Select.Option key={`${enumId},${region}`}*/}
                    {/*                                                          value={`${enumId},${region}`}>{enumName}</Select.Option>*/}
                    {/*                                })*/}
                    {/*  }*/}
                    {/*</Select>*/}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    name='postLevel'
                    label='岗级'
                    rules={[
                      { pattern: /^[+]{0,1}(\d+)$/, message: '只支持数字，请正确输入！' },
                      { required: true, message: '请输入岗级!' }
                    ]}
                  >
                    <Input placeholder='请输入岗级' allowClear />
                    {/*<Select placeholder='请选择岗级' showSearch filterOption={handleSearchFilter}>*/}
                    {/*  {*/}
                    {/*    // prettier-ignore*/}
                    {/*    levelList2.map(unit => {*/}
                    {/*                                    const {enumId, enumName} = unit;*/}
                    {/*                                    return <Select.Option key={enumId}*/}
                    {/*                                                          value={enumId}>{enumName}</Select.Option>*/}
                    {/*                                })*/}
                    {/*  }*/}
                    {/*</Select>*/}
                  </Form.Item>
                </Col>
                <Col span={6} />
                <Col span={6} />

                <Col span={6}>
                  <Form.Item
                    name='remark1'
                    label='备注1'
                    rules={[{ required: false, message: '请选择备注1!' }]}
                  >
                    <Input placeholder='请输入' allowClear />
                    {/*<Select placeholder='请选择备注'>*/}
                    {/*    {*/}
                    {/*        // prettier-ignore*/}
                    {/*        remarkList1.map(unit => {*/}
                    {/*            const {enumId, enumName} = unit;*/}
                    {/*            return <Select.Option key={enumId}*/}
                    {/*                                  value={enumName}>{enumName}</Select.Option>*/}
                    {/*        })*/}
                    {/*    }*/}
                    {/*</Select>*/}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item name='remark2' label='备注2'>
                    <Input placeholder='请输入' allowClear />
                    {/*<Select placeholder='请选择备注'>*/}
                    {/*    {*/}
                    {/*        // prettier-ignore*/}
                    {/*        remarkList2.map(unit => {*/}
                    {/*            const {enumId, enumName} = unit;*/}
                    {/*            return <Select.Option key={enumId}*/}
                    {/*                                  value={enumName}>{enumName}</Select.Option>*/}
                    {/*        })*/}
                    {/*    }*/}
                    {/*</Select>*/}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item name='remark3' label='备注3'>
                    <Input placeholder='请输入' allowClear />
                    {/*<Select placeholder='请选择备注'>*/}
                    {/*    {*/}
                    {/*        // prettier-ignore*/}
                    {/*        remarkList3.map(unit => {*/}
                    {/*            const {enumId, enumName} = unit;*/}
                    {/*            return <Select.Option key={enumId}*/}
                    {/*                                  value={enumName}>{enumName}</Select.Option>*/}
                    {/*        })*/}
                    {/*    }*/}
                    {/*</Select>*/}
                  </Form.Item>
                </Col>
                <Col span={24} className='text-center mt-[2rem]'>
                  <Space>
                    <Button
                      onClick={() => {
                        setEditRecord(null)
                        editform.resetFields()
                      }}
                    >
                      返回
                    </Button>
                    <Button type='primary' htmlType='submit'>
                      保存
                    </Button>
                  </Space>
                </Col>
              </Row>
            </Form>
          </Spin>
        </div>
      )}

      <Modal
        title='文件上传'
        destroyOnClose={true}
        open={upModalOpen}
        centered
        className={styles.detail_modal}
        okText='上传'
        onOk={handleOk}
        onCancel={handleCancel}
      >
        <div className='mt-4 mb-8'>
          <Row>
            <Col span={22} offset={1} className='h-[10rem]'>
              <Dragger
                {...{
                  // name: 'file',
                  // data: {
                  //     tag: "1"
                  // },
                  // action: `/zhyy/employee/uploadEmployeeExcel`,
                  action: '',
                  maxCount: 1,
                  multiple: false,
                  fileList,
                  beforeUpload(file, fileList) {
                    console.log(file, fileList)
                    return false
                    // let isError = true;
                    // form.validateFields((err, fieldsValue) => {
                    //   isError = !err;
                    // })
                    // return isError;
                  },
                  onChange(info) {
                    const { status } = info.file
                    if (status !== 'uploading') {
                      console.log(info.file, info.fileList)
                      setFileList(info.fileList)
                    }
                    // if (status === 'done') {
                    //     message.success(`${info.file.name} file uploaded successfully.`);
                    // } else if (status === 'error') {
                    //     message.error(`${info.file.name} file upload failed.`);
                    // }
                  }
                }}
              >
                <p className='ant-upload-drag-icon'>
                  <InboxOutlined style={{ color: '#F14846' }} />
                </p>
                <p className='ant-upload-text'>点击或将文件拖拽到这里上传</p>
                <p className='ant-upload-hint'>支持excel格式的文件。</p>
              </Dragger>
            </Col>
          </Row>
        </div>
      </Modal>
    </div>
  )
}
export default People
