import request from '@/request'

const perFilialeService = {
  // 查询枚举
  getEnumType: params => {
    return request.post<any>('/zhyy/employee/getEnumType', params)
  },

  // 人员岗位信息-列表查询
  getEmployeePostPag: params => {
    return request.post<any>('/zhyy/employee/getEmployeePostPag', params, {
      headers: {
        'Manager-Operate-Flag': 2
      }
    })
  },

  // 人员岗位信息-下载模板
  downloadEmployeeTemplate: params => {
    return request.post<any>('/zhyy/employee/downloadEmployeeTemplate', params, {
      responseType: 'blob',
      headers: {
        'Manager-Operate-Flag': 2
      }
    })
  },

  // 人员岗位信息-导出模板
  exportEmployeeExcel: params => {
    return request.post<any>('/zhyy/employee/exportEmployeeExcel', params, {
      responseType: 'blob',
      headers: {
        'Manager-Operate-Flag': 2
      }
    })
  },


  // 人员岗位信息-下载对照表
  downloadEmployeeCompare: params => {
    return request.post<any>('/zhyy/employee/downloadEmployeeCompare', params, {
      responseType: 'blob',
      headers: {
        'Manager-Operate-Flag': 2
      }
    })
  },

  // 人员岗位信息-导入
  uploadEmployeeExcel: params => {
    return request.post<any>('/zhyy/employee/uploadEmployeeExcel', params, {
      headers: {
        'Manager-Operate-Flag': 2
      }
    })
  },

  // 人员岗位信息-修改
  updateEmployeePost: params => {
    return request.post<any>('/zhyy/employee/updateEmployeePost', params, {
      headers: {
        'Manager-Operate-Flag': 2
      }
    })
  }
}

export default perFilialeService
