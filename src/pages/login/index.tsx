import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { Button, Form, Input } from 'antd'
import { useRequest } from '@/hooks/use-request'
import { encryptPassword, getFlatMenus, antdUtils, getFirstMenuRouter } from '@/utils'
import { useGlobalStore } from '@/store/global'
import { useUserStore } from '@/store/user'
import loginService from './service'
import Crypoto from '@/utils/aes'

import type { FormProps } from 'antd'
import type { Account } from '@/types'

import './index.scss'

type FieldType = {
  userName?: string
  password?: string
  captcha?: string
}

const crypoto = new Crypoto()

const onFinishFailed: FormProps<FieldType>['onFinishFailed'] = errorInfo => {
  console.log('Failed:', errorInfo)
}

const getBase64 = (img: Blob, callback: (buffer: any) => void) => {
  const reader = new FileReader()
  reader.addEventListener('load', () => callback(reader.result))
  reader.readAsDataURL(img)
}

const Login: React.FC = () => {
  const [captchaBase64, setCaptchaBase64] = useState<string>('')
  const { data: captcha, refresh: refreshCaptcha } = useRequest(loginService.getCaptcha)
  const { runAsync: login } = useRequest(loginService.login, { manual: true })
  const navigate = useNavigate()
  const { setToken } = useGlobalStore()
  const { setCurrentUser } = useUserStore()

  useEffect(() => {
    if (captcha) {
      const blob = new Blob([captcha], { type: captcha.type })
      getBase64(blob, buffer => setCaptchaBase64(buffer))
    }
  }, [captcha])

  const onFinish: FormProps<FieldType>['onFinish'] = async values => {
    const { userName, password, captcha } = values

    // 打印原始密码
    console.log('原始密码:', password)

    // 进行RSA加密
    const encryptedPassword = encryptPassword(password)
    console.log('RSA加密后的密码:', encryptedPassword)

    const params = {
      userName: crypoto.encryptBy3DES(userName),
      password: encryptedPassword,
      captcha
    }

    console.log('登录参数:', params)
    const [loginError, data] = await login(params)

    if (loginError) {
      return
    }

    if (data.STATUS !== '0000') {
      antdUtils.message.error(data.MESSAGE)
      // 刷新验证码
      refreshCaptcha()
      return
    }

    const { token, menuInfo, userInfo, extraInfo, roleInfo } = data.DATA

    setToken(token)

    const treeMenus = menuInfo?.treeMenus
    const currentUserDetail: Account = {
      userInfo: { userId: userInfo?.userId, userName: userInfo?.userName },
      menus: treeMenus,
      flatMenus: getFlatMenus(treeMenus),
      currentMenuCode: treeMenus[0]?.menuCode,
      orgInfo: extraInfo.orga,
      roleInfo: { roleCode: roleInfo?.roles?.map(role => role.roleCode) }
    }
    setCurrentUser(currentUserDetail)

    navigate(getFirstMenuRouter(treeMenus))
  }

  return (
    <div className='login-wrapper relative w-screen h-screen'>
      <div className='absolute scale-[1.4] opacity-80 left-1/2 top-[5%]'>
        <div className='w-[8.5rem] h-[8.5rem] rounded-full bg-[#1890ffcc]' />
        <div className='absolute w-[5rem] h-[5rem] rounded-full bg-[#1890ff66] left-[5rem] top-[5rem]' />
      </div>
      <div className='absolute scale-[2] rotate-180 opacity-60 -right-4 -bottom-2'>
        <div className='w-[8.5rem] h-[8.5rem] rounded-full bg-[#1890ffcc]' />
        <div className='absolute w-[5rem] h-[5rem] rounded-full bg-[#1890ff66] left-[5rem] top-[5rem]' />
      </div>
      <div className='absolute scale-[1] -rotate-90 opacity-60 left-10 bottom-10'>
        <div className='w-[8.5rem] h-[8.5rem] rounded-full bg-[#1890ffcc]' />
        <div className='absolute w-[5rem] h-[5rem] rounded-full bg-[#1890ff66] left-[5rem] top-[5rem]' />
      </div>
      <div className='absolute left-8 top-6 text-[#1890ff]'>
        <div className='text-[2rem] font-[600]'>天津人力资源数字化管理平台</div>
        <div className='text-[1rem]'>Digital Human Resources Management Platform</div>
      </div>
      <div className='absolute left-1/2 top-1/2 w-[40rem] h-[20rem] -translate-x-[20rem] -translate-y-[10rem] bg-white shadow-lg flex items-center justify-around'>
        <div className='flex-[0_0_15rem]'>
          <div className='login-img' />
        </div>
        <div className='flex-[0_0_15rem]'>
          <Form
            name='basic'
            layout='vertical'
            wrapperCol={{ span: 24 }}
            style={{ maxWidth: 600 }}
            onFinish={onFinish}
            onFinishFailed={onFinishFailed}
            autoComplete='off'
          >
            <Form.Item<FieldType>
              label='用户名'
              name='userName'
              rules={[{ required: true, message: '用户名不能为空，请输入用户名！' }]}
            >
              <Input placeholder='请输入用户名' />
            </Form.Item>
            <Form.Item<FieldType>
              label='密码'
              name='password'
              rules={[{ required: true, message: '密码不能为空，请输入密码！' }]}
            >
              <Input.Password placeholder='请输入密码' />
            </Form.Item>
            <Form.Item<FieldType> label='验证码'>
              <div style={{ display: 'flex', columnGap: 8 }}>
                <Form.Item
                  name='captcha'
                  noStyle
                  rules={[{ required: true, message: '验证码不能为空，请输入验证码！' }]}
                >
                  <Input placeholder='请输入验证码' />
                </Form.Item>
                <img
                  src={captchaBase64}
                  className='captcha-img cursor-pointer'
                  onClick={() => refreshCaptcha()}
                />
              </div>
            </Form.Item>
            <Form.Item wrapperCol={{ span: 24 }}>
              <Button className='w-full' type='primary' htmlType='submit'>
                登录
              </Button>
            </Form.Item>
          </Form>
        </div>
      </div>
    </div>
  )
}
export default Login
