.ant-dropdown-arrow {
  display: none !important;
}
.ant-menu-submenu-popup .ant-menu-vertical >.ant-menu-item {
  display: flex !important;
  align-items: center !important;
}

.menu-wrapper.ant-menu-vertical{
  .ant-menu-submenu-title{
    display: flex !important;
    align-items: center !important;
  }
}
.menu-wrapper.ant-menu-inline-collapsed{
  .ant-menu-submenu-title{
    display: block !important;
  }
}

.switch-wrapper {
  &.ant-switch-checked {
    background: #e60027 !important;
  }
}

.switch-wrapper.dark {
  background: #d6d6d6 !important;
  &.ant-switch-checked {
    background: #fff !important;
  }
  .ant-switch-inner {
    .ant-switch-inner-checked {
      color: #304156 !important;
    }
    .ant-switch-inner-unchecked {
      color: #fff !important;
    }
  }
}

.input-wrapper {
  input::-webkit-input-placeholder {
    color: #aeb1b4;
  }
}
