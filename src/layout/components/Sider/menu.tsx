import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { Link, useMatches } from 'react-router-dom'
import { Menu, Input, ConfigProvider, Switch, Dropdown, theme, MenuProps, Tooltip } from 'antd'
import { MenuFoldOutlined, SearchOutlined, MenuOutlined } from '@ant-design/icons'
import SvgIcon from '@/components/SvgIcons'
import { useUserStore } from '@/store/user'
import { useGlobalStore } from '@/store/global'
import './menu.scss'
import type { ItemType } from 'antd/es/menu/interface'
import type { Menu as MenuType } from '@/types'

const { useToken } = theme

const SiderMenu: React.FC = () => {
  const { token } = useToken()

  const matches = useMatches()
  const [selectKeys, setSelectKeys] = useState<string[]>([])
  const [openKeys, setOpenKeys] = useState<string[]>([])

  const { currentUser } = useUserStore()
  const { collapsed, setCollapsed, darkMode, setDarkMode, menuMode, setMenuMode } = useGlobalStore()

  useEffect(() => {
    initMenuStatus()
  }, [matches])

  const initMenuStatus = () => {
    const [match] = matches
    if (match) {
      // 获取当前匹配的路由，默认为最后一个
      const route = matches.at(-1)
      // 从匹配的路由中取出自定义参数
      const handle = route?.handle as any
      //   console.log(matches,route,handle)
      //   // 从自定义参数中取出上级path，让菜单自动展开
        setOpenKeys(handle?.parentPaths || [])
      // 让当前菜单和所有上级菜单高亮显示
      setSelectKeys([...(handle?.parentPaths || []), handle?.path])
    }
  }

  const getMenuTitle = (menu: MenuType) => {
    if (menu?.children?.length) {
      return menu.menuName
    }
    return (
        <Tooltip title={menu?.menuName}>
            <Link to={menu.path}>
                <span className='text-[0.7rem]'>{menu.menuName}</span>
            </Link>
        </Tooltip>
    )
  }

  const treeMenuData = useCallback((menus: MenuType[]): ItemType[] => {
    return menus.map((menu: MenuType) => {
      const children = menu?.children || []
      return {
        key: menu.path,
        label: getMenuTitle(menu),
        children: children.length ? treeMenuData(children || []) : null,
        icon: menu?.tag && <SvgIcon name={menu?.tag} size={20} color='#F66A5C' />
      }
    })
  }, [])

  const headerMenuData = currentUser?.menus?.filter(menu => 
    menu.menuCode === currentUser?.currentMenuCode
  )[0]

  const siderMenuData = useMemo(() => {
    return treeMenuData(headerMenuData?.children || [])
  }, [currentUser?.currentMenuCode])

  const unit: number = useMemo(() => {
    return !collapsed ? 24 : 32
  }, [collapsed])

  /** 切换侧边栏样式 */
  const changeDarkMode = (checked: boolean) => {
    setDarkMode(!checked)
  }

  /** 切换侧边栏菜单展示方式 */
  const changeMenuMode = (checked: boolean) => {
    setMenuMode(checked ? 'inline' : 'vertical')
    initMenuStatus()
  }

  const items: MenuProps['items'] = [
    {
      key: '1',
      label: (
        <Switch
          className={`switch-wrapper ${darkMode && 'dark'}`}
          checked={menuMode === 'inline'}
          checkedChildren='模式'
          unCheckedChildren='模式'
          defaultChecked
          onChange={changeMenuMode}
        />
      )
    },
    {
      key: '2',
      label: (
        <Switch
          className={`switch-wrapper ${darkMode && 'dark'}`}
          checked={!darkMode}
          checkedChildren='风格'
          unCheckedChildren='风格'
          defaultChecked
          onChange={changeDarkMode}
        />
      )
    }
  ]

  // @ts-ignore
  return (
    <div className='relative h-full'>
      <div
        style={{ justifyContent: !collapsed ? 'flex-start' : 'center' }}
        className='inline-flex gap-x-[0.4rem] w-full pt-[1rem] px-[0.8rem]'
      >
        <SvgIcon
          name={headerMenuData?.tag}
          color={darkMode ? '#fff' : '#E60027'}
          width={unit}
          height={unit}
        />
        {!collapsed && (
          <span className='text-[0.8rem] font-bold' style={{ color: darkMode && '#fff' }}>
            {headerMenuData?.menuName}
          </span>
        )}
      </div>
      <div className='my-[0.75rem] mx-[0.5rem] text-center'>
        {!collapsed ? (
          <Input
            className={darkMode && 'input-wrapper'}
            style={{
              borderRadius: 0,
              background: darkMode ? '#304156' : '#fff'
            }}
            styles={{
              suffix: { marginInlineStart: 0 }
            }}
            placeholder='搜索'
            suffix={<SearchOutlined style={{ color: darkMode ? '#fff' : '#909399' }} />}
          />
        ) : (
          <SearchOutlined style={{ color: '#00000073' }} />
        )}
      </div>
      <ConfigProvider
        theme={{
          components: {
            Menu: {
              itemColor: darkMode ? '#fff' : '#666',
              itemBg: darkMode ? '#304156' : '#F7F7F7',
              itemSelectedColor: darkMode ? '#fff' : '#E60027',
              itemSelectedBg: darkMode ? '#233449' : '#FDE7EA',
              itemHoverColor: darkMode ? '#fff' : '#666',
              itemHoverBg: darkMode ? '#233449' : '#FDE7EA',
              itemActiveBg: darkMode ? '#304156' : '#F7F7F7',
              itemMarginBlock: 0,
              itemMarginInline: 0,
              itemBorderRadius: 0,
              popupBg: darkMode ? '#304156' : '#fff'
            }
          }
        }}
      >
        <Menu
          className={'menu-wrapper'}
          mode={menuMode}
          selectedKeys={selectKeys}
          style={{ borderRight: 0 }}
          inlineCollapsed={collapsed}
          items={siderMenuData}
          openKeys={openKeys}
          onOpenChange={val => {
            console.log('val', val)
            setOpenKeys(val)
          }}
        />
      </ConfigProvider>
      <div
        className='absolute left-0 bottom-0 right-0 border-t-[1px] leading-[2rem]'
        style={{ padding: !collapsed ? '0 0.5rem' : '0 0.25rem' }}
      >
        <div className='my-[0.4rem]'>
          {collapsed && (
            <Dropdown
              placement='topRight'
              arrow
              menu={{ items }}
              dropdownRender={menu => (
                <div
                  style={{
                    position: 'relative',
                    left: 80,
                    top: 20,
                    backgroundColor: token.colorBgElevated,
                    borderRadius: token.borderRadiusLG,
                    boxShadow: token.boxShadowSecondary
                  }}
                >
                  {React.cloneElement(menu as React.ReactElement, {
                    style: {
                      backgroundColor: darkMode ? '#304156' : '#fff',
                      boxShadow: 'none'
                    }
                  })}
                  {/*<Button type="primary">Click me!</Button>*/}
                </div>
              )}
            >
              <MenuOutlined
                className='flex-auto flex items-center justify-end font-size text-[1rem]'
                style={{ color: darkMode ? '#ffffff73' : '#00000073', cursor: 'pointer' }}
              />
            </Dropdown>
          )}
        </div>

        <div className='flex mb-[0.4rem]'>
          {!collapsed && (
            <div className='flex-auto flex items-center gap-[1.1rem]'>
              <ConfigProvider
                theme={{ components: { Switch: { handleBg: darkMode ? '#304156' : '#fff' } } }}
              >
                <Switch
                  className={`switch-wrapper ${darkMode && 'dark'}`}
                  checked={menuMode === 'inline'}
                  checkedChildren='模式'
                  unCheckedChildren='模式'
                  defaultChecked
                  onChange={changeMenuMode}
                />
                <Switch
                  className={`switch-wrapper ${darkMode && 'dark'}`}
                  checked={!darkMode}
                  checkedChildren='风格'
                  unCheckedChildren='风格'
                  defaultChecked
                  onChange={changeDarkMode}
                />
              </ConfigProvider>
            </div>
          )}
          <div className='flex-auto flex items-center justify-end font-size text-[1rem]'>
            <MenuFoldOutlined
              style={{ color: darkMode ? '#ffffff73' : '#00000073', cursor: 'pointer' }}
              onClick={() => {
                setCollapsed(!collapsed)
              }}
            />
          </div>
        </div>
      </div>
    </div>
  )
}
export default SiderMenu
