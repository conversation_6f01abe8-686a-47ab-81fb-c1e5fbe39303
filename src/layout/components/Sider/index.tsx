import React, { useMemo } from 'react'
import SiderMenu from './menu'
import { useMatchRoute } from '@/hooks/use-match-router'
import { useGlobalStore } from '@/store/global'

const Sider: React.FC = () => {
  const matchRoute = useMatchRoute()
  const { collapsed, darkMode } = useGlobalStore()

  const showSider = useMemo(() => {
    return matchRoute && matchRoute.showSider
  }, [matchRoute])

  const width: number = useMemo(() => {
    if (showSider) {
      return collapsed ? 80 : 170
    }
    return 0
  }, [showSider, collapsed])

  function renderMenu() {
    return <SiderMenu />
  }

  return (
    <div
      style={{ width, background: darkMode && '#304156' }}
      className='top-[4rem] fixed box-border left-0 bottom-0 overflow-y-auto shadow-[2px_0_6px_0_#0015291f] z-20'
    >
      {renderMenu()}
    </div>
  )
}
export default Sider
