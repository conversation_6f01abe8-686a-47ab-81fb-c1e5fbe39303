import { Suspense, useMemo } from 'react'
import { useMatchRoute } from '@/hooks/use-match-router'
import { useGlobalStore } from '@/store/global'

const Content: React.FC<any> = ({ children }) => {
  const matchRoute = useMatchRoute()
  const { collapsed } = useGlobalStore()

  const showSider = useMemo(() => {
    return matchRoute && matchRoute.showSider
  }, [matchRoute])

  const marginLeft: number = useMemo(() => {
    if (showSider) {
      return collapsed ? 80 : 170
    }
    return 0
  }, [showSider, collapsed])

  return (
    <div
      className='mt-[4rem] w-[100%] bg-[#f3f5fa]'
      style={{
        borderRadius: '8px',
        marginLeft,
        height: 'calc(100vh - 4rem)',
        width: `calc(100vw - ${marginLeft}px)`
      }}
    >
      <div className='m-0 rounded-md h-[100%] z-10'>
        <Suspense fallback={<div>loading...</div>}>{children}</Suspense>
      </div>
    </div>
  )
}
export default Content
