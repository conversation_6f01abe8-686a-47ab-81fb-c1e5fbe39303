import { useEffect } from 'react'
import {useRequest} from "@/hooks";
import globalService from "@/service/service.ts";
import { useNavigate, useLocation, useNavigationType } from "react-router-dom";

const useBeforeUnload = (onBeforeUnload?: () => void) => {
  const navigate = useNavigate();
  const location = useLocation(); // 获取当前路由
  const navigationType = useNavigationType();

  const {runAsync: logout} = useRequest(globalService.logout, {
    manual: true
  })

  useEffect(() => {
    console.log(11111,navigate);
  }, [location.pathname])

  useEffect(() => {
    if (window.name === 'isRefreshed') {
      console.log('页面被刷新');
    } else {
      console.log('首次加载页面');
      window.name = 'isRefreshed';
    }
  }, [])

  useEffect(() => {
    const handleBeforeUnload = event => {

      if (window.name === 'isRefreshed') {
        console.log('页面被刷新');
        console.log(22222, window.name);
        if (navigationType !== 'PUSH' && navigationType !== 'POP') {
          if (onBeforeUnload) {
            onBeforeUnload()
          } else {
            // alert('页面被刷新')
            logout({logoutType: "02"})

            // 这里可以设置返回提示，某些浏览器会显示该提示
            // const message = 'Are you sure you want to leave?'
            // event.returnValue = message // 现代浏览器
            // return message // 某些旧版浏览器
          }
        }
      }

      console.log(event);
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
    }
  }, [onBeforeUnload])

}

export default useBeforeUnload
