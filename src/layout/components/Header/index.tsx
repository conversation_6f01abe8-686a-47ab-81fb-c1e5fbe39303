import { useMemo } from 'react'
import { Link } from 'react-router-dom'
import { Menu, ConfigProvider, Dropdown } from 'antd'
import { useUserStore } from '@/store/user'
import { useGlobalStore } from '@/store/global'
import SvgIcon from '@/components/SvgIcons'
import './index.scss'
import globalService from "@/service/service.ts";
import {useRequest} from "@/hooks";
import usePageTracking from "@/hooks/page-track";
// import useBeforeUnload from "@/layout/components/Function/Level.tsx";

const Header: React.FC = () => {
  const { currentUser, setCurrentUser, reset: resetUser } = useUserStore()
  const { reset: resetGlobal } = useGlobalStore()
  const {runAsync: logout} = useRequest(globalService.logout, {manual: true})

  const menuData = useMemo(() => {
    return currentUser?.menus?.map(item => {
      const { menuCode, menuName, tag, showType, path } = item
      // 测试先写死，后面改造
      return {
        key: menuCode,
        label: (
          <div className='flex items-center gap-x-[0.4rem]'>
            <SvgIcon name={tag} />
            <span style={{ fontSize: '0.9rem' }}>
              {
                // prettier-ignore
                showType === '4' ? <Link to={showType === '4' && path}>
                  {menuName}
                </Link> : <span>{menuName}</span>
              }
            </span>
          </div>
        )
      }
    })
  }, [currentUser])

  // // 登出
  // useBeforeUnload();

    // 进入离开页面埋点
    usePageTracking();

  const handleMenuClick = menuItem => {
    const { key } = menuItem
    currentUser.currentMenuCode = key
    setCurrentUser(currentUser)
  }

  /** 退出登录 */
  const logoutFun = () => {
    resetUser()
    resetGlobal()
    logout({logoutType: "01"})
  }

  return (
    <div className='header-wrapper z-[998] h-[4rem] pl-[1.2rem] pr-[1.4rem] flex items-center fixed top-0 right-0 left-0'>
      <div className='flex-none flex items-center gap-x-[1rem] mr-[3rem] text-white'>
        <div className='logo' />
        <div className='w-[1px] h-[1.5rem] bg-white' />
        <div className='text-[1.05rem]'>天津人力资源数字化管理平台</div>
      </div>
      <ConfigProvider
        theme={{
          components: {
            Menu: {
              itemBg: 'transparent',
              itemColor: '#fff',
              itemHeight: '2.25rem',
              itemHoverColor: '#fff',
              horizontalLineHeight: '2.25rem',
              horizontalItemSelectedBg: '#fff',
              horizontalItemSelectedColor: '#E60027',
              horizontalItemHoverBg: '#F24F4B',
              horizontalItemBorderRadius: 4,
              activeBarHeight: 0,
              popupBg: '#FF7A68'
            }
          }
        }}
      >
        <Menu
          style={{ flex: 'auto', minWidth: 0, border: 0 }}
          selectedKeys={[currentUser?.currentMenuCode]}
          items={menuData}
          mode='horizontal'
          onClick={handleMenuClick}
        />
      </ConfigProvider>
      <div className='flex-none flex items-center justify-end gap-x-[0.5rem]'>
        <SvgIcon name='man' width={46} height={46} />
        <Dropdown
          trigger={['click']}
          placement='bottomLeft'
          getPopupContainer={node => node.parentElement!}
          dropdownRender={() => {
            return (
              <div className='bg-white rounded-lg w-[200px] shadow-[2px_0_6px_0_#0015291f]'>
                <div className='p-[16px] text-center text-[rgb(242_79_75)] cursor-pointer'>
                  <div onClick={logoutFun}>退出登录</div>
                </div>
              </div>
            )
          }}
          arrow
        >
          <div className='flex items-center gap-x-[0.25rem] h-[4rem] cursor-pointer'>
            <span className='text-[#ffffffa1] text-[0.7rem]'>
              {currentUser?.userInfo?.userName}
            </span>
            <SvgIcon name='logout' color='#ffffffa3' width={14} height={14} />
          </div>
        </Dropdown>
      </div>
    </div>
  )
}

export default Header
