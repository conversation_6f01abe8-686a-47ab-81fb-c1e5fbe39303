import React, {useMemo, useCallback} from 'react'
import {Tabs, Dropdown, ConfigProvider} from 'antd'
import {CloseOutlined} from '@ant-design/icons'
import {KeepAliveTab, useTabs, useMatchRoute} from '@/hooks'
import {useUserStore} from '@/store/user'
import {getFlatMenus} from '@/utils'
import {router} from '@/router'

import './index.scss'

import type {MenuItemType} from 'antd/es/menu/interface'
import themeObj from './themeConfig'
import {Outlet} from "react-router-dom";

enum OperationType {
    REFRESH = 'refresh',
    CLOSE = 'close',
    CLOSEOTHER = 'close-other'
}

const TabsLayout: React.FC = () => {
    const {activeTabRoutePath, tabs, closeTab, closeOtherTab, refreshTab} = useTabs()
    const {currentUser, setCurrentUser, setTabUrl} = useUserStore()
    const matchRoute = useMatchRoute()

    // prettier-ignore
    const menuItems: MenuItemType[] = useMemo(
        () => [
            {label: '刷新', key: OperationType.REFRESH},
            tabs.length <= 1 ? null : {label: '关闭', key: OperationType.CLOSE},
            tabs.length <= 1 ? null : {label: '关闭其他', key: OperationType.CLOSEOTHER}
        ].filter(o => o !== null) as MenuItemType[],
        [tabs]
    );

    const menuClick = useCallback(
        ({key, domEvent}: any, tab: KeepAliveTab) => {
            domEvent.stopPropagation()

            if (key === OperationType.REFRESH) {
                refreshTab(tab.routePath)
            } else if (key === OperationType.CLOSE) {
                closeTab(tab.routePath)
            } else if (key === OperationType.CLOSEOTHER) {
                closeOtherTab(tab.routePath)
            }
        },
        [closeOtherTab, closeTab, refreshTab]
    )

    // prettier-ignore
    const renderTabTitle = useCallback(
        (tab: KeepAliveTab) => {
            return (
                <Dropdown
                    menu={{items: menuItems, onClick: e => menuClick(e, tab)}}
                    trigger={['contextMenu']}
                >
                    <div
                        className='flex items-center justify-center text-[0.7rem] gap-x-[0.5rem] h-[1.6rem] px-[0.7rem] py-[0.3rem] rounded-[0.4rem]'
                        style={activeTabRoutePath === tab?.routePath ? {background: '#F24F4B'} : {background: '#fff'}}
                    >
                        <div
                            className='w-[0.4rem] h-[0.4rem] rounded-[50%]'
                            style={activeTabRoutePath === tab?.routePath ? {background: '#fff'} : {background: '#00000057'}}
                        />
                        <span>{tab.title}</span>
                        {tabs.length > 1 && <CloseOutlined onClick={(event) => {
                            event.stopPropagation();
                            closeTab(tab.routePath);
                        }}/>}
                    </div>
                </Dropdown>
            )
        },
        [menuItems, activeTabRoutePath]
    )

    const tabItems = useMemo(() => {
        return tabs.map(tab => {
            return {
                key: tab.routePath,
                label: renderTabTitle(tab),
                children: (
                    <ConfigProvider theme={themeObj}>
                        <div key={tab.key} className='h-[calc(100vh-6.4rem)] overflow-y-hidden'>
                            {tab.children}
                        </div>
                    </ConfigProvider>
                ),
                closable: tabs.length > 1 // 剩最后一个就不能删除了
            }
        })
    }, [tabs, activeTabRoutePath])

    const onTabsChange = useCallback((tabRoutePath: string) => {
        const flatMenus = getFlatMenus(currentUser?.menus)
        const menu = flatMenus.find(menu => menu?.path === tabRoutePath)
        const newCurrentMenuCode = menu?.menuCode?.slice(0, 2)
        if (currentUser?.currentMenuCode === newCurrentMenuCode) {
            router.navigate(tabRoutePath)
        } else {
            currentUser.currentMenuCode = newCurrentMenuCode
            setTabUrl(tabRoutePath)
            setCurrentUser(currentUser)
        }
    }, [])

    const onTabEdit = (
        targetKey: React.MouseEvent | React.KeyboardEvent | string,
        action: 'add' | 'remove'
    ) => {
        if (action === 'remove') {
            closeTab(targetKey as string)
        }
    }

    return (
        <div>
            {matchRoute?.showSider ? (
                <ConfigProvider
                    theme={{
                        components: {
                            Tabs: {
                                itemColor: '#00000073',
                                itemActiveColor: '#00000073',
                                itemHoverColor: '#00000073',
                                itemSelectedColor: '#fff',
                                cardPaddingSM: '0 0',
                                horizontalMargin: '0 0 0 0',
                                inkBarColor: 'transparent'
                            }
                        }
                    }}
                >
                    <Tabs
                        className='tabs-container'
                        activeKey={activeTabRoutePath}
                        items={tabItems}
                        onChange={onTabsChange}
                        onEdit={onTabEdit}
                        hideAdd
                        size='small'
                    />
                </ConfigProvider>
            ) : (
                <ConfigProvider theme={themeObj}>
                    <Outlet/>
                </ConfigProvider>
            )}
        </div>
    )
}
export default TabsLayout
