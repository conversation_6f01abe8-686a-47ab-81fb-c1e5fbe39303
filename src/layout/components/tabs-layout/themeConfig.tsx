const defaultColor = {
  colorPrimary: '#F14846',
  colorPrimaryHover: '#F66A5C'
}

const themeObj = {
  components: {
    Tabs: {
      itemColor: '#F14846',
      itemActiveColor: '#F66A5C',
      itemHoverColor: '#F66A5C',
      itemSelectedColor: '#F14846',
      inkBarColor: '#F14846'
    },
    Button: {
      ...defaultColor,
      // colorPrimary: 'linear-gradient(to right, #F66A5C, #F14846)',
      // colorPrimaryHover: 'linear-gradient(to right, #f66556, #fa2724)',
      // colorPrimaryActive: 'linear-gradient(to right, #F66A5C, #F14846)',
      colorLink: '#F14846',
      colorLinkHover: '#F66A5C',
      colorLinkActive: '#F66A5C',
      borderRadius: 2
    },
    Switch: {
      ...defaultColor,
      borderRadius: 2
    },
    Radio: {
      ...defaultColor,
      borderRadius: 2
    },
    Checkbox: {
      ...defaultColor
    },
    Tree: {
      ...defaultColor
    },
    Input: {
      borderRadius: 2
    },
    Select: {
      borderRadius: 2
    },
    Table: {
      borderRadius: 2,
      headerBorderRadius: 2
    }
  }
}
export default themeObj
