import { useEffect, useState, lazy } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import Header from './components/Header'
import Sider from './components/Sider'
import Content from './components/content'
import TabsLayout from './components/tabs-layout'
import GloablLoading from '@/components/global-loading'
import { useUserStore } from '@/store/user'
import { useGlobalStore } from '@/store/global'
import { replaceRoutes, router } from '@/router'
import { components } from '@/config'
import { getFlatMenus } from '@/utils'
import Watermark from '@/components/waterMark/index'
import dayjs from 'dayjs'

import type { Menu } from '@/types'

const Layout: React.FC = () => {
  const [loading, setLoading] = useState(true)
  const { currentUser, setCurrentUser, tabUrl, setTabUrl } = useUserStore()
  const { token } = useGlobalStore()
  const location = useLocation()
  const navigate = useNavigate()

  const formatMenus = (
    menus: Menu[],
    menuGroup: Record<string, Menu[]>,
    routes: Menu[],
    parentMenu?: Menu
  ): Menu[] => {
    return menus.map(menu => {
      const children = menuGroup[menu.menuCode]

      const parentPaths = parentMenu?.parentPaths || []
      const path = (parentMenu ? `${parentPaths.at(-1)}${menu.url}` : menu.url) || ''
      routes.push({
        ...menu,
        path,
        parentPaths:
          parentPaths.length > 2 ? [parentPaths.at(-1), parentPaths.at(-2)] : [parentPaths.at(-1)],
        filePath: path
      })

      return {
        ...menu,
        path,
        parentPaths,
        children: children?.length
          ? formatMenus(children, menuGroup, routes, {
              ...menu,
              parentPaths: [...parentPaths, path || ''].filter(o => o)
            })
          : undefined
      }
    })
  }

  useEffect(() => {
    if (!token) {
      navigate('/login')
    }
  }, [navigate, token])

  useEffect(() => {
    if (currentUser) {
      const { menus, currentMenuCode } = currentUser
      const filterMenus = menus.filter(menu => menu?.menuCode === currentMenuCode)
      const flatMenus = getFlatMenus(filterMenus)
      const maxLength = flatMenus[flatMenus.length - 1]?.menuCode.length
      const menuData = flatMenus.filter(menu => menu?.menuCode?.length === maxLength)[0]
      console.log('maxLength', menus, maxLength, tabUrl)
      // navigate(menuData?.path)
      if (tabUrl) {
        navigate(tabUrl)
        setTabUrl('')
      } else {
        navigate(menuData?.path)
      }
    }
  }, [currentUser?.currentMenuCode])

  useEffect(() => {
    if (!currentUser) return

    const { flatMenus = [] } = currentUser

    const menuGroup = flatMenus.reduce<Record<string, Menu[]>>((prev, menu) => {
      if (!menu.parentCode) {
        return prev
      }

      if (!prev[menu.parentCode]) {
        prev[menu.parentCode] = []
      }

      prev[menu.parentCode].push(menu)
      return prev
    }, {})

    const routes: Menu[] = []

    const newMenus = formatMenus(
      flatMenus.filter(o => !o.parentCode),
      menuGroup,
      routes
    )

    currentUser.menus = newMenus
    console.log('newMenus', newMenus)
    replaceRoutes('*', [
      ...routes.map(menu => {
        return {
          path: `/*${menu.path}`,
          Component: menu.filePath
            ? lazy(
                components[`${menu.filePath}/index.jsx`] || components[`${menu.filePath}/index.tsx`]
              )
            : null,
          id: `/*${menu.path}`,
          handle: {
            parentPaths: menu.parentPaths,
            path: menu.path,
            name: menu.menuName,
            icon: menu.iconClass,
            showSider: menu.showType !== '4'
          }
        }
      }),
      {
        id: '*',
        path: '*',
        Component: null,
        handle: {
          path: '404',
          name: '404'
        }
      }
    ])

    setCurrentUser(currentUser)
    setLoading(false)
    // replace一下当前路由，为了触发路由匹配
    router.navigate(`${location.pathname}`, { replace: true })
  }, [currentUser, setCurrentUser])

  /** 动态路由加载完成后，才开始渲染页面，否则会出现白屏问题 */
  if (loading || !currentUser) {
    return <GloablLoading />
  }
  let text = ''
  const currentYear = dayjs().year() // 获取当前年份
  const currentMonth = dayjs().month() + 1 // 获取当前月份（注意：month() 从 0 开始，需要 +1）
  const currentDate = dayjs().date() // 获取当前日期
  if (currentUser.userInfo) {
    text = `${currentUser.userInfo.userName} ${currentYear}-${currentMonth}-${currentDate}`
  } else {
    text = `中国联通 ${currentYear}-${currentMonth}-${currentDate}}`
  }
  return (
    <div className='overflow-hidden'>
      <Watermark text={text} fontSize={40} gap={100} color='rgba(0,0,0,0.05)' />
      <Header />
      <Sider />
      <Content>
        <TabsLayout />
      </Content>
    </div>
  )
}
export default Layout
