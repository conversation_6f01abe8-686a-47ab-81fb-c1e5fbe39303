import { JSEncrypt } from 'jsencrypt'
import { md5 } from 'js-md5'
const PUBLIC_KEY = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAuY8XiCyGln/KRAHCJI+E
JlqZgq4E6ws26uwkFwTUuuff7lUTkuyffMwL+6cBBIvb2Qm20GB+ngaw+CBC67JU
SnWgKLIrmOzp3hvdt8PT6Ct3fToOD6aqvkCsOqfE36pZ54ZnXaq02AkOGbCFXbNh
Ds5hxuNTf0x8NOOa547X1tu56Kle/FW1ucaVT3LDWct9cNObHwh0Kx/dnJuHArKc
akL+5oEgkoLdfnbJY46wz5G/ckqHuANqFOBVO536SF9OCEtWi73adbxthq/+R7NZ
dIA6anPtRBMC6RtoSzEN88aL9o7rU+BuX7VvFuwt7LLgx/g3lHAkjIcqw/ctS8JG
vQIDAQAB
-----END PUBLIC KEY-----`

export class RSAUtils {
  private encryptor: JSEncrypt

  constructor() {
    this.encryptor = new JSEncrypt()
    this.encryptor.setPublicKey(PUBLIC_KEY)
  }

  encrypt(text: string): string {
    try {
      const encrypted = this.encryptor.encrypt(text)
      if (!encrypted) {
        throw new Error('加密失败')
      }
      return encrypted
    } catch (error) {
      console.error('RSA加密失败:', error)
      throw new Error('RSA加密失败')
    }
  }
}

const rsaUtils = new RSAUtils()

export const encryptPassword = (password: string): string => {
  return rsaUtils.encrypt(md5(password))
}

export default rsaUtils
