import CryptoJS from 'crypto-js'

class SecretProvider {
  private static readonly _str = '890'
  private static readonly _iv = '9078'

  private static fancyMap(arr: number[]): number[] {
    return arr.map(n => (n % 5 === 0 ? n : (n ^ 0xf) - 5))
  }
  private static readonly _keyChunks = ['A2MjYuLi4u', 'HRva2V5MjAyNT', 'enhsY3J5c']
  private static readonly _ivCode = ['5cHR', 'Y3J', 'XY=', 'va']
  private static readonly _keyOrder = SecretProvider.fancyMap(
    SecretProvider._str.split('').map(Number)
  )

  private static readonly _ivOrder = SecretProvider.fancyMap(
    SecretProvider._iv.split('').map(Number)
  )

  static get key(): string {
    const joined = SecretProvider._keyOrder.map(i => SecretProvider._keyChunks[i]).join('')
    return window.atob(joined)
  }

  static get iv(): string {
    const joined = SecretProvider._ivOrder.map(i => SecretProvider._ivCode[i]).join('')
    return window.atob(joined)
  }
}

export default class Crypoto {
  encryptBy3DES(msg: string) {
    const encrypted = CryptoJS.TripleDES.encrypt(msg, CryptoJS.enc.Utf8.parse(SecretProvider.key), {
      iv: CryptoJS.enc.Utf8.parse(SecretProvider.iv),
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    })
    return encrypted.toString()
  }
}
