import { notification, Spin } from 'antd'

/**
 *    自动补全尺寸
 *    @param size css size
 *    @param unit 自动填充 css 尺寸单位
 */
export const downFile = (resData: any) => {
  try {
    const blob = resData[1] || {}
    const response = resData[2] || {}
    const contentDisposition = response?.headers['content-disposition']
    let fileName = 'downloaded-file' // 默认文件名
    if (contentDisposition) {
      // 使用正则表达式解析文件名
      const fileNameMatch = contentDisposition.match(/filename="?([^"]+)"?/)
      if (fileNameMatch && fileNameMatch.length > 1) {
        fileName = decodeURIComponent(fileNameMatch[1]) // 获取文件名并解码
      }
    }
    // 创建下载链接并自动触发下载
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = fileName // 自定义文件名
    document.body.appendChild(a)
    a.click()
    a.remove() // 移除临时的 <a> 标签
    // 释放 URL 对象
    window.URL.revokeObjectURL(url)
    openNotification('导出成功', 1, 'success')
  } catch (error) {
    openNotification('导出失败', 1, 'error')
    console.error('Download failed:', error)
  }
}

/**
 * 导入/导出 loading方法
 */
export const openNotification = (text, time, flag) => {
  if (flag === 'success') {
    notification.success({
      key: 'upLoad',
      message: text,
      duration: time, // 若不配置，默认4.5秒后自动关闭，配置为null则不自动关闭
      onClick: () => {
        // console.log('Notification Clicked!');
      }
    })
  } else if (flag === 'error') {
    notification.error({
      key: 'upLoad',
      message: text,
      duration: time,
      onClick: () => {
        // console.log('Notification Clicked!');
      }
    })
  } else {
    notification.open({
      key: 'upLoad',
      message: (
        <div>
          <Spin style={{ marginRight: 5 }} />
          {text}
        </div>
      ),
      duration: time,
      onClick: () => {
        // console.log('Notification Clicked!');
      }
    })
  }
}
