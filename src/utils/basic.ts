import type { BasicTypes, ValidateValueType, Menu } from '@/types'
import { md5 } from 'js-md5'

export const md5Encryption = (value: string) => {
  return md5(value)
}

/**
 *
 * @param value 目标值
 * @param type 类型
 *
 * @example
 * isValueType<string>('123', 'String') // true
 * isValueType<object>({}, 'Object') // true
 * isValueType<number>([], 'Array') // true
 * isValueType<number>([], 'Object') // false
 */
export const isValueType = <T extends BasicTypes>(
  value: unknown,
  type: ValidateValueType
): value is T => {
  const valid = Object.prototype.toString.call(value)

  return valid.includes(type)
}

/** 将多维数组（对象含children）转为一维数组 */
export const getFlatMenus = (menus: Menu[]): Menu[] => {
  return menus.reduce((prev, menu) => {
    if (Array.isArray(menu.children)) {
      const copyCur = { ...menu }
      delete copyCur.children
      return prev.concat(copyCur, getFlatMenus(menu.children))
    } else {
      return prev.concat(menu)
    }
  }, [])
}

/** 获取角色第一个菜单路由 */
export const getFirstMenuRouter = (menus: Menu[]): string => {
  const firstMenu = menus[0];
  let firstRouter = '';
  const getUrl = (menu) => {
    firstRouter += menu?.url;
    if (menu?.children && menu.children.length > 0) {
      getUrl(menu.children[0]);
    }
  }
  getUrl(firstMenu);
  return firstRouter;
}
