/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 30/05/22.
 */


/**
 * value: 没值显示  none || '--'； 带百分比的值直接展示原value
 * type: 无或0：将value*100加‘%’； 1：加‘%’； 2：将value*100； 3：保留原样
 * none: 无数据的样式
 */
export function calculatePercentage(value, type = 0, none) {
  return (value || value === 0) && value !== '--'
    ? value.toString().includes('%')
      ? value
      : type === 1
        ? `${value}%`
        : type === 2
          ? parseFloat((value * 100).toFixed(2))
          : type === 3
            ? value
            : `${parseFloat((value * 100).toFixed(2))}%`
    : (none || none === '' || none === 0) ? none : '--';
}
