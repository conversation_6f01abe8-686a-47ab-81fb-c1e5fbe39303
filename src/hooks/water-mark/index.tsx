import { useEffect, useState } from 'react'

// 生成水印背景图的函数
function generateWatermark({ text, fontSize, gap, color }) {
  const canvas = document.createElement('canvas')
  const devicePixelRatio = window.devicePixelRatio || 1
  const adjustedFontSize = fontSize * devicePixelRatio
  const font = `${adjustedFontSize}px serif`
  const ctx = canvas.getContext('2d')

  ctx.font = font
  const { width } = ctx.measureText(text)
  const canvasSize = Math.max(100, width) + gap * devicePixelRatio
  canvas.width = canvasSize
  canvas.height = canvasSize

  ctx.translate(canvas.width / 2, canvas.height / 2)
  ctx.rotate((Math.PI / 180) * -25)
  ctx.fillStyle = color
  ctx.font = font
  ctx.textAlign = 'center'
  ctx.textBaseline = 'middle'
  ctx.fillText(text, 0, 0)

  return {
    base64: canvas.toDataURL(),
    size: canvasSize / devicePixelRatio
  }
}

export default function useWatermark({
  text = '中国联通',
  fontSize = 20,
  gap = 100,
  color = 'rgba(0,0,0,0.1)'
} = {}) {
  const [watermarkProps, setWatermarkProps] = useState({ text, fontSize, gap, color })

  useEffect(() => {
    // 创建水印容器
    const watermarkDiv = document.createElement('div')
    Object.assign(watermarkDiv.style, {
      position: 'fixed',
      top: '0',
      left: '0',
      width: '100%',
      height: '100%',
      pointerEvents: 'none',
      zIndex: '9999',
      backgroundRepeat: 'repeat',
      opacity: '1',
      display: 'block',
      userSelect: 'none',
      WebkitUserSelect: 'none',
      MozUserSelect: 'none',
      msUserSelect: 'none'
    })

    // 生成水印背景图
    const { base64, size } = generateWatermark(watermarkProps)
    watermarkDiv.style.backgroundImage = `url(${base64})`
    watermarkDiv.style.backgroundSize = `${size}px ${size}px`

    // 将水印容器添加到 body
    document.body.appendChild(watermarkDiv)

    // 获取 body 的原始 position 样式
    const bodyStyle = window.getComputedStyle(document.body)
    const originalBodyPosition = bodyStyle.position
    if (originalBodyPosition === 'static') {
      document.body.style.position = 'relative'
    }

    // 监听水印样式变化
    const observer = new MutationObserver(mutations => {
      mutations.forEach(mutation => {
        // 如果水印容器样式发生变化，重新生成水印
        if (mutation.type === 'attributes' && mutation.target === watermarkDiv) {
          const { base64, size } = generateWatermark(watermarkProps)
          Object.assign(watermarkDiv.style, {
            position: 'fixed',
            top: '0',
            left: '0',
            width: '100%',
            height: '100%',
            pointerEvents: 'none',
            zIndex: '9999',
            backgroundRepeat: 'repeat',
            opacity: '1',
            display: 'block',
            userSelect: 'none',
            WebkitUserSelect: 'none',
            MozUserSelect: 'none',
            msUserSelect: 'none'
          })
          watermarkDiv.style.backgroundImage = `url(${base64})`
          watermarkDiv.style.backgroundSize = `${size}px ${size}px`
        }
      })
    })

    // 观察水印 div 的变化
    observer.observe(watermarkDiv, {
      attributes: true
    })

    // 定期检查水印的可见性和样式
    const interval = setInterval(() => {
      if (!document.body.contains(watermarkDiv)) {
        document.body.appendChild(watermarkDiv)
      }
      const computedStyle = window.getComputedStyle(watermarkDiv)
      if (computedStyle.display === 'none' || parseFloat(computedStyle.opacity) < 0.1) {
        watermarkDiv.style.display = 'block'
        watermarkDiv.style.opacity = '1'
      }
      const parentDisplay = window.getComputedStyle(document.body).display
      if (parentDisplay === 'none') {
        document.body.style.display = 'block'
      }
    }, 1000) // 每秒检查一次

    // 清理函数
    return () => {
      observer.disconnect()
      clearInterval(interval)
      if (watermarkDiv.parentNode === document.body) {
        document.body.removeChild(watermarkDiv)
      }
      if (originalBodyPosition === 'static') {
        document.body.style.position = originalBodyPosition
      }
    }
  }, [watermarkProps]) // 监听 watermarkProps 变化

  // 当外部组件传入的新值发生变化时，更新 watermarkProps
  useEffect(() => {
    setWatermarkProps({ text, fontSize, gap, color })
  }, [text, fontSize, gap, color])

  return null
}
