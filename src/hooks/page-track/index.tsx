// src/hooks/usePageTracking.js
import {useEffect, useRef} from 'react';
import { useLocation } from 'react-router-dom';
import {useRequest} from "@/hooks";
import globalService from "@/service/service.ts";
import {useUserStore} from "@/store/user.ts";

const usePageTracking = () => {
  const { currentUser} = useUserStore()
  const location = useLocation();
  const previousLocation = useRef(location);
  const { runAsync: traceVisitMenu } = useRequest(globalService.traceVisitMenu, {
    manual: true
  })

  useEffect(() => {
    // 页面打开时调用接口
    const trackPageView = async (visitType: number, menuCode, menuName) => {
      try {
        // 调用接口记录埋点
        const [error, res] = await traceVisitMenu({
          visitType,
          menuCode,
          menuName
        })
        if (error) {
          return
        }
        if (res.STATUS === '0000') {
        }
      } catch (error) {
        console.error('Error tracking page view:', error);
      }
    };

    const {flatMenus} = currentUser;
    const curPath = location.pathname?.substring(location.pathname.lastIndexOf('/'));
    const prePath = previousLocation.current.pathname?.substring(previousLocation.current.pathname.lastIndexOf('/'));
    const curRouter = flatMenus.find(item => item.url === curPath);
    const preRouter = flatMenus.find(item => item.url === prePath);
    // 页面关闭时调用接口
    if (previousLocation.current.pathname !== location.pathname) {
      trackPageView(1, curRouter?.menuCode, curRouter?.menuName); // 1 表示打开菜单
      trackPageView(2, preRouter?.menuCode, preRouter?.menuName); // 1 表示打开菜单
      previousLocation.current = location; // 更新之前的路由
    } else {
      trackPageView(1, curRouter?.menuCode, curRouter?.menuName); // 1 表示打开菜单
    }

    // trackPageView(1);

    // const handleBeforeUnload = async () => {
    //   await trackPageView(2); // 2 表示关闭菜单
    // };
    //
    // window.addEventListener('beforeunload', handleBeforeUnload);
    //
    // return () => {
    //   window.removeEventListener('beforeunload', handleBeforeUnload);
    // };
  }, [location]);
};

export default usePageTracking;
