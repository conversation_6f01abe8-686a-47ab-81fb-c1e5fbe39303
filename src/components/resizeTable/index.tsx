import React, { useEffect } from 'react'
import { Table } from 'antd'
import type { TableProps, ColumnsType, ColumnType } from 'antd/lib/table'
import { Resizable } from 'react-resizable'
import 'react-resizable/css/styles.css'
import './resize-table.css' // 引入 CSS 文件

// 定义表头单元格组件，支持调整大小
const TableHeaderCell: React.FC<Record<string, any>> = props => {
  const { onResize, width, ...restProps } = props
  if (!width) return <th {...restProps} />

  return (
    <Resizable
      className='resize-cell' // 使用 CSS 类
      width={width}
      height={0}
      onResize={onResize as any} // 强制转换类型为 any 以符合类型要求
      draggableOpts={{ enableUserSelectHack: false }}
    >
      <th {...restProps} />
    </Resizable>
  )
}

const ResizeTable: React.FC<TableProps<any>> = props => {
  const { columns = [], ...rest } = props

  const [tableColumns, setTableColumns] = React.useState<ColumnsType<any>>(columns)

  // 根据列的 key 处理列宽调整
  const handleResize =
    (key: string) =>
    (_: any, { size }: any) => {
      setTableColumns(prevColumns => {
        // 递归更新列宽
        const updateColumns = (cols: ColumnsType<any>): ColumnsType<any> => {
          return cols.map(col => {
            if ('key' in col && col.key === key) {
              return { ...col, width: size.width }
            }
            if ('children' in col) {
              return { ...col, children: updateColumns(col.children) }
            }
            return col
          })
        }
        return updateColumns(prevColumns)
      })
    }

  // 递归地为叶子列添加 onHeaderCell 属性
  const makeResizable = (cols: any): ColumnsType<any> => {
    return cols.map(col => {
      // 这里统一给列加上 onHeaderCell
      // 如果你只希望“叶子列”可拖拽，可做一些条件判断，比如没有 children 并且自己有 width 时再加 onResize
      const newCol = {
        ...col,
        onHeaderCell: (column: ColumnType<any>) => ({
          width: column.width,
          onResize: handleResize(column.key as string)
        })
      }

      if (col.children && col.children.length) {
        return {
          ...newCol,
          children: makeResizable(col.children)
        }
      }
      return newCol
    })
  }

  // 构建动态列配置
  const mergedColumns = makeResizable(tableColumns)

  // 监听 columns 变化并更新 state
  useEffect(() => {
    setTableColumns(columns)
  }, [columns])

  return (
    <Table
      {...rest}
      components={{
        header: {
          cell: TableHeaderCell
        }
      }}
      columns={mergedColumns as ColumnsType<any>} // 确保类型匹配
    />
  )
}

export default ResizeTable
