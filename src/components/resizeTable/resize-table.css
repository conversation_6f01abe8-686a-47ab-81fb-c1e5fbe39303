/* resize-table.css */
.resize-cell {
  position: relative;
  background-clip: padding-box;
  overflow: hidden;
}

.resize-cell::before {
  position: absolute;
  top: 50%;
  right: 0;
  width: 1px;
  height: 1.6em;
  background-color: rgba(0, 0, 0, .06);
  transform: translateY(-50%);
  transition: background-color .3s;
  content: "";
}

.resize-cell:last-child::before {
  display: none;
}

.react-resizable-handle {
  position: absolute;
  width: 10px;
  height: 100%;
  bottom: 0;
  right: -5px;
  cursor: col-resize;
  background-image: none;
  z-index: 1;
}
