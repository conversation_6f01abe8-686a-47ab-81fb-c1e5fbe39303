import {useEffect, useRef, useState} from 'react'
import {Button, Form, Input, Table, Cascader, GetProp, CascaderProps, Select} from 'antd'
import {PlusOutlined} from '@ant-design/icons'
import styles from './Index.module.scss'

type DefaultOptionType = GetProp<CascaderProps, 'options'>[number]
const EditTabel = props => {
    // const defaultColumns = props.columns

    const [formRef] = Form.useForm()
    const tableDataRef = useRef([])
    const [tableData, setTableData] = useState([])
    const [tableColumns, setTableColumns] = useState(props.columns)

    useEffect(() => {
        setTableColumns([
            ...initColumns(props.columns),
            {
                title: '操作',
                dataIndex: 'action',
                width: 80,
                fixed: 'right',
                render: (_text, record) => (
                    <div className='action'>
                        {/*<Button*/}
                        {/*    type='dashed'*/}
                        {/*    onClick={() => deleteRecord(record)}*/}
                        {/*    style={{margin: 'auto', width: '50%', marginLeft: '25%'}}*/}
                        {/*>*/}
                        {/*    删除*/}
                        {/*</Button>*/}
                        <Button type='link' onClick={() => deleteRecord(record)}>删除</Button>
                    </div>
                )
            }
        ]);
    }, [])

    // useEffect(() => {
    //     setTableData(tableDataRef.current)
    // }, [JSON.stringify(tableDataRef.current)])

    const initColumns = (columns) => {
        return columns?.filter(cloumn => cloumn?.dataIndex !== 'cycleId')?.map(item => {
            if (item?.children?.length > 0) {
                return ({
                    ...item,
                    width: (item?.width && item.width > 100) ? item?.width : 100,
                    children: initColumns(item.children),
                })
            } else {
                return ({
                    ...item,
                    width: (item?.width && item.width > 120) ? item?.width : 120,
                    render: (text, record, index) => (
                        <Form.Item
                            key={text + index}
                            name={record.dataIndex}
                            rules={[
                                {
                                    required: true,
                                    message: `请输入${record.title}`
                                }
                            ]}
                        >
                            {
                                item?.actionType === 'cascader' ?
                                    <Cascader
                                        allowClear={false}
                                        changeOnSelect
                                        expandTrigger='hover'
                                        displayRender={labels => labels[labels.length - 1]}
                                        options={props?.cascaderOption || []}
                                        fieldNames={{
                                            value: 'orgId',
                                            label: 'orgName',
                                            children: 'children'
                                        }}
                                        placeholder={`请选择${item?.title}`}
                                        showSearch={{filter}}
                                        onSearch={value => console.log(value)}
                                        value={text?.map(item => item?.orgId)}
                                        onChange={(_value, option) => handleEditRecord(record, option, item)}
                                    /> :
                                    item?.actionType === 'select' ?
                                        <>
                                            <Select placeholder={`请选择${item?.title}`} allowClear
                                                    value={text}
                                                    onChange={value => handleEditRecord(record, value, item)}>
                                                {
                                                    (props[item?.actionOptionName] || []).map(item => (
                                                        <Select.Option key={item?.enumId} value={item?.enumId}>{item?.enumName}</Select.Option>
                                                    ))
                                                }
                                            </Select>
                                        </>
                                        :
                                        <Input placeholder={`请输入${item.title}`}
                                               value={text}
                                               onChange={e => handleEditRecord(record, e.target.value, item)}/>
                            }
                        </Form.Item>
                    )
                })
            }
        })
    }

    const handleEditRecord = (record, value, column) => {
        tableDataRef.current = tableDataRef.current?.map(item => {
            const data = {...item};
            if (item?.key === record?.key) {
                // if (column?.actionType === 'cascader') {
                //     data[column?.dataIndex] = value;
                // } else {
                //     data[column?.dataIndex] = value;
                // }
                data[column?.dataIndex] = value;
            }
            return data;
        });
        // setTableData(list)
        console.log('修改：', tableDataRef.current)
    };

    const deleteRecord = (record) => {
        tableDataRef.current = tableDataRef.current?.filter(item => item?.key !== record?.key)
        setTableData(tableDataRef.current)
    }

    const onFinish = () => {
        props?.submitData(tableDataRef.current);
    }

    const addRecord = () => {
        tableDataRef.current = [...tableDataRef.current, {
            key: new Date().getTime(),
        }]
        setTableData(tableDataRef.current)
    }

    const filter = (inputValue: string, path: DefaultOptionType[]) =>
        path.some(
            option =>
                (option.orgName as string).toLowerCase().indexOf(inputValue.trim().toLowerCase()) > -1
        )

    return (
        <div className={styles.add}>
            {/*{initHeader()}*/}
            <Form
                name='dynamic_form_nest_item'
                form={formRef}
                onFinish={onFinish}
                style={{
                    width: '100%',
                    marginTop: 16,
                    maxHeight: '500px'
                }}
                autoComplete='off'
            >
                {/*<Form.List name='users'>*/}
                <Table
                    style={{marginBottom: 20}}
                    className='edit-table'
                    columns={tableColumns}
                    dataSource={tableData}
                    bordered
                    scroll={{
                        // x: 'max-content',
                        y: `20rem`
                    }}
                    pagination={false}
                />

                <Form.Item
                    style={{width: '100%', display: 'flex', justifyContent: 'right'}}
                >
                    <Button
                        // type='dashed'
                        onClick={() => addRecord()}
                        danger
                        block
                        icon={<PlusOutlined/>}
                        style={{width: 150}}
                    >
                        新增一条数据
                    </Button>
                </Form.Item>
                {/*</Form.List>*/}

                <Form.Item
                    style={{
                        textAlign: 'center',
                        width: '100%',
                        background: '#fff'
                    }}
                >
                    <Button type='primary' htmlType='submit' style={{}}>
                        提交
                    </Button>
                </Form.Item>
            </Form>
        </div>
    )
}
export default EditTabel
