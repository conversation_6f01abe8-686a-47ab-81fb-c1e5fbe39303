.loading {
  display: block;
  position: relative;
  width: 6px;
  height: 10px;
  border-radius: 2px;
  animation: rectangle infinite 1s ease-in-out -0.2s;
  background-color: #673ab7;
}

.loading:before,
.loading:after {
  position: absolute;
  width: 6px;
  height: 10px;
  content: '';
  background-color: #673ab7;
  border-radius: 2px;
}

.loading:before {
  left: -14px;
  animation: rectangle infinite 1s ease-in-out -0.4s;
}

.loading:after {
  right: -14px;
  animation: rectangle infinite 1s ease-in-out;
}

@keyframes rectangle {
  0%,
  80%,
  100% {
    height: 20px;
    box-shadow: 0 0 #673ab7;
  }
  40% {
    height: 30px;
    box-shadow: 0 -20px #673ab7;
  }
}
