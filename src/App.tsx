import { px2remTransformer, StyleProvider } from '@ant-design/cssinjs'
import { ConfigProvider, App as AntdApp } from 'antd'
import zhCN from 'antd/locale/zh_CN'
import Router from '@/router'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
// import useBeforeUnload from "@/layout/components/Function/Level.tsx";
dayjs.locale('zh-cn')

const px2rem = px2remTransformer({
  rootValue: 20,
  precision: 2
})


const App: React.FC = () => {
  // useBeforeUnload();

  return (
    <ConfigProvider locale={zhCN}>
      <StyleProvider transformers={[px2rem]}>
        <AntdApp>
          <Router />
        </AntdApp>
      </StyleProvider>
    </ConfigProvider>
  )
}

export default App
