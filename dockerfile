FROM harbor.dcos.xixian.unicom.local/common/nginx:1.20.1

#RUN  sed -i "3i\ nameserver 10.172.49.5" /etc/resolv.conf  
#RUN  sed -i "4i\ nameserver 10.172.49.6" /etc/resolv.conf  
#RUN echo "$(sed -i "3i\ nameserver 10.172.49.5" /etc/resolv.conf)" > /etc/resolv.conf
#RUN echo "$(sed -i "4i\ nameserver 10.172.49.6" /etc/resolv.conf)" > /etc/resolv.conf
RUN rm -rf  /etc/nginx/nginx.conf
RUN rm -rf /etc/localtime
RUN ln -s /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
COPY nginx.conf /etc/nginx/nginx.conf
COPY start.sh /etc/nginx/
#Start  
WORKDIR /usr/share/nginx/html
COPY html .
#ENTRYPOINT echo "nameserver 10.172.49.5\nnameserver 10.172.49.6" > /etc/resolv.conf
CMD ["sh","/etc/nginx/start.sh"]
