import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'
import path from 'node:path'
import tailwindcss from 'tailwindcss'
import autoprefixer from 'autoprefixer'

// https://vitejs.dev/config/
export default defineConfig({
  base: '/efop',
  resolve: {
    /**
     * 预设别名
     * - @: src 根目录
     */
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@mock': path.resolve(__dirname, './mock')
    }
  },
  define: {
    global: 'globalThis',
    'process.env': {}
  },
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@use "./src/styles/mixins.scss" as *;`
      }
    },
    postcss: {
      plugins: [tailwindcss, autoprefixer()]
    }
  },
  // optimizeDeps: {
  //   exclude: ['lodash-es'],
  // },
  plugins: [
    react(),
    /** 创建雪碧图 */
    createSvgIconsPlugin({
      /** 指定需要缓存的图标文件夹 */
      iconDirs: [path.resolve(process.cwd(), 'src/icons')],
      /** 指定symbolId格式 */
      symbolId: 'icon-[dir]-[name]',
      /** 自定义插入的位置 默认body-last*/
      inject: 'body-last',
      /** 自定义dom的id */
      customDomId: '__svg__icons__dom__'
    })
  ],
  /** 开发服务器配置 */
  server: {
    host: '0.0.0.0',
    port: 9090,
    open: true,
    proxy: {
      '/zhyy': {
        // target: 'https://************:28081/',
        // target: 'http://*************:8083/',
        target: 'http://***********:8083/',
        // target: 'http://127.0.0.1:8083/',
        changeOrigin: true,
        secure: false
      }
    }
  }
})
