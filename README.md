# ZXL Front PC

一个基于 React 的现代化前端项目，使用 Vite 作为构建工具，集成了多种前端技术栈。

## 项目概览

这是一个基于 React 的现代化前端项目，使用 Vite 作为构建工具，TypeScript 作为编程语言，并集成了多种现代前端技术栈。项目定位为 PC 端应用，基路径为 `/efop`。

## 技术栈

- **核心框架**: React 18
- **构建工具**: Vite 5
- **样式解决方案**: 
  - Tailwind CSS
  - SCSS
  - Ant Design CSS in JS
- **UI 组件库**: Ant Design 5
- **状态管理**: Zustand
- **路由管理**: React Router 6
- **HTTP 请求**: Axios
- **图表库**: ECharts
- **日期处理**: Day.js
- **图标处理**: @ant-design/icons + SVG 图标
- **工具库**: classnames、crypto-js、js-md5 等

## 项目结构

```
src/
├── assets/       # 静态资源
├── components/   # 可复用组件
├── config/       # 配置文件
├── hooks/        # 自定义 React Hooks
├── icons/        # SVG 图标 (用于雪碧图)
├── layout/       # 布局组件
├── pages/        # 页面组件
│   ├── dashboard/  # 首页/仪表盘
│   ├── effect/     # 效能相关页面
│   ├── empty/      # 空白页面
│   ├── login/      # 登录页面
│   ├── position/   # 岗位相关页面
│   ├── salary/     # 薪资相关页面
│   └── system/     # 系统管理页面
├── request/      # API 请求处理
├── router/       # 路由配置
├── service/      # 服务接口
├── store/        # 状态管理
│   ├── global.ts   # 全局状态
│   └── user.ts     # 用户状态
├── styles/       # 样式文件
├── types/        # TypeScript 类型定义
├── utils/        # 工具函数
├── App.tsx       # 应用入口组件
├── main.tsx      # 应用入口文件
└── vite-env.d.ts # Vite 环境类型声明
```

## 核心功能模块

### 状态管理

项目使用 Zustand 进行状态管理，主要分为：

- **全局状态 (global.ts)**:
  - 令牌管理
  - UI 主题设置（暗色模式）
  - 侧边栏折叠状态
  - 菜单模式设置

- **用户状态 (user.ts)**:
  - 用户信息与权限管理

Zustand 利用中间件提供了持久化存储、开发工具集成等功能。

### 路由系统

使用 React Router 6 进行路由管理，支持：

- 嵌套路由结构
- 动态路由添加与替换
- 路由错误处理
- 路由鉴权（通过布局组件实现）

主要路由包括登录页、仪表盘、效能、岗位、薪资和系统管理等模块。

### 布局系统

项目有统一的布局管理，支持：

- 顶部导航与侧边栏布局
- 响应式设计
- 菜单折叠功能
- 用户界面主题切换

### 请求处理

基于 Axios 封装了 HTTP 请求库，特点：

- 统一的错误处理
- 请求/响应拦截
- 认证令牌管理
- 代理配置（'/zhyy' 接口代理至 'https://************:28081/'）

### UI 框架

使用 Ant Design 作为主要 UI 组件库：

- 配置中文本地化
- 集成 CSS-in-JS 解决方案
- 像素转 rem 单位适配（基准值为 20px）
- 自定义主题样式

### 样式处理

多层次样式解决方案：

- Tailwind CSS 用于原子化 CSS
- SCSS 用于复杂样式和混合器
- CSS-in-JS 用于组件级样式

### 图标系统

使用 SVG 图标系统：

- 通过 vite-plugin-svg-icons 插件创建 SVG 雪碧图
- 支持自定义图标目录
- 统一的图标命名规范

## 开发规范与工具

- **代码质量工具**:
  - ESLint + TypeScript ESLint
  - Prettier
  - Husky (Git Hooks)
  - lint-staged
  - commitlint

- **构建与部署**:
  - 开发环境: `pnpm dev`
  - 生产构建: `pnpm build`
  - 代码预检查: `pnpm pre-check`

## 项目特色

1. **现代化技术栈**: 采用最新的 React 18、TypeScript 5 和 Vite 5
2. **完备的状态管理**: 轻量级 Zustand 状态库与持久化方案
3. **灵活的路由系统**: 支持动态路由与权限控制
4. **优秀的组件化结构**: 清晰的目录结构与功能分层
5. **多样化的业务模块**: 包含仪表盘、系统管理等多个业务模块
6. **完善的工程化措施**: 从代码规范到提交规范的全方位质量保障

## 开发环境配置

- 开发服务器：默认端口 9090
- 代理配置：'/zhyy' → 'https://************:28081/'
- 资源别名：'@' → './src'、'@mock' → './mock'

## 快速开始

### 环境准备

- Node.js >= 16
- pnpm >= 8

### 安装依赖

```bash
pnpm install
```

### 开发模式

```bash
pnpm dev
```

### 构建生产版本

```bash
pnpm build
```

### 预览生产版本

```bash
pnpm preview
```
